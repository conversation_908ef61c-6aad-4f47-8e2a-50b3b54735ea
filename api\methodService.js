const saveMethod = async (methodData) => {
  try {
    // Asegurarnos de que enviamos todos los datos necesarios al backend
    const response = await fetch('/api/methods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...methodData,
        // Asegurarse de incluir explícitamente los registros de tiempo y suplementos
        timeRecords: methodData.timeRecords || [],
        supplements: methodData.supplements || []
      }),
    });
    
    if (!response.ok) {
      throw new Error('Error al guardar el método');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error guardando método:', error);
    throw error;
  }
}; 