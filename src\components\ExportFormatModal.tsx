import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getCurrentFormatInfo } from '../utils/numberFormat';
import { X, FileDown, Globe, Settings } from 'lucide-react';

interface ExportFormatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (format: 'auto' | 'comma' | 'dot') => void;
  filename?: string;
}

export const ExportFormatModal: React.FC<ExportFormatModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  filename = 'archivo.xlsx'
}) => {
  const { t, i18n } = useTranslation('common');
  const [selectedFormat, setSelectedFormat] = useState<'auto' | 'comma' | 'dot'>('auto');
  
  const formatInfo = getCurrentFormatInfo();

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm(selectedFormat);
    onClose();
  };

  const getFormatDescription = (format: 'auto' | 'comma' | 'dot') => {
    switch (format) {
      case 'auto':
        return t('exportFormat.autoDescription');
      case 'comma':
        return t('exportFormat.commaDescription');
      case 'dot':
        return t('exportFormat.dotDescription');
      default:
        return '';
    }
  };

  const getFormatExample = (format: 'auto' | 'comma' | 'dot') => {
    const sampleNumber = 1234.567;
    switch (format) {
      case 'auto':
        return formatInfo.isSpanish ? '1234,567' : '1234.567';
      case 'comma':
        return '1234,567';
      case 'dot':
        return '1234.567';
      default:
        return '';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <FileDown className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              {t('exportFormat.title')}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Cerrar"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          <div className="text-gray-600">
            <p>{t('exportFormat.description')}</p>
            <p className="text-sm mt-2 font-mono bg-gray-100 p-2 rounded">
              📄 {filename}
            </p>
          </div>

          {/* Opciones de formato */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900 flex items-center gap-2">
              <Settings className="w-4 h-4" />
              {t('exportFormat.formatOptions')}
            </h3>

            {/* Automático */}
            <label className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
              <input
                type="radio"
                name="format"
                value="auto"
                checked={selectedFormat === 'auto'}
                onChange={(e) => setSelectedFormat(e.target.value as 'auto')}
                className="mt-1"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-blue-600" />
                  <span className="font-medium">{t('exportFormat.auto')}</span>
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                    {t('exportFormat.recommended')}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {getFormatDescription('auto')}
                </p>
                <p className="text-xs font-mono bg-gray-100 px-2 py-1 rounded mt-2">
                  {t('exportFormat.example')}: {getFormatExample('auto')}
                </p>
              </div>
            </label>

            {/* Coma decimal */}
            <label className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
              <input
                type="radio"
                name="format"
                value="comma"
                checked={selectedFormat === 'comma'}
                onChange={(e) => setSelectedFormat(e.target.value as 'comma')}
                className="mt-1"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-lg">🇪🇸</span>
                  <span className="font-medium">{t('exportFormat.comma')}</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {getFormatDescription('comma')}
                </p>
                <p className="text-xs font-mono bg-gray-100 px-2 py-1 rounded mt-2">
                  {t('exportFormat.example')}: {getFormatExample('comma')}
                </p>
              </div>
            </label>

            {/* Punto decimal */}
            <label className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
              <input
                type="radio"
                name="format"
                value="dot"
                checked={selectedFormat === 'dot'}
                onChange={(e) => setSelectedFormat(e.target.value as 'dot')}
                className="mt-1"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-lg">🇺🇸</span>
                  <span className="font-medium">{t('exportFormat.dot')}</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {getFormatDescription('dot')}
                </p>
                <p className="text-xs font-mono bg-gray-100 px-2 py-1 rounded mt-2">
                  {t('exportFormat.example')}: {getFormatExample('dot')}
                </p>
              </div>
            </label>
          </div>

          {/* Info adicional */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">
              {t('exportFormat.currentLanguage')}
            </h4>
            <p className="text-sm text-blue-700">
              {t('exportFormat.detectedLanguage')}: <span className="font-mono">{formatInfo.language}</span>
            </p>
            <p className="text-sm text-blue-700">
              {t('exportFormat.detectedSeparator')}: <span className="font-mono">"{formatInfo.decimalSeparator}"</span>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50 rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 hover:text-gray-900 transition-colors"
          >
            {t('exportFormat.cancel')}
          </button>
          <button
            onClick={handleConfirm}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <FileDown className="w-4 h-4" />
            {t('exportFormat.export')}
          </button>
        </div>
      </div>
    </div>
  );
};

// Traducciones adicionales que necesitamos agregar
export const exportFormatTranslations = {
  es: {
    'exportFormat.title': 'Formato de decimales para Excel',
    'exportFormat.description': 'Elige cómo quieres que se muestren los números decimales en el archivo Excel:',
    'exportFormat.tip': 'Recomendación',
    'exportFormat.tipDescription': 'En España se usa la coma (,) como separador decimal. En países anglosajones se usa el punto (.).',
    'exportFormat.auto': 'Automático (según idioma)',
    'exportFormat.comma': 'Coma como separador',
    'exportFormat.dot': 'Punto como separador',
    'exportFormat.export': 'Exportar Excel',
    'exportFormat.currentLanguage': 'Idioma actual',
    'exportFormat.filename': 'Archivo',
    'formatAuto': 'Automático según idioma',
    'formatComma': 'Formato español',
    'formatDot': 'Formato internacional'
  },
  en: {
    'exportFormat.title': 'Excel decimal format',
    'exportFormat.description': 'Choose how you want decimal numbers to be displayed in the Excel file:',
    'exportFormat.tip': 'Recommendation',
    'exportFormat.tipDescription': 'In Spain, comma (,) is used as decimal separator. In English-speaking countries, dot (.) is used.',
    'exportFormat.auto': 'Automatic (by language)',
    'exportFormat.comma': 'Comma as separator',
    'exportFormat.dot': 'Dot as separator',
    'exportFormat.export': 'Export Excel',
    'exportFormat.currentLanguage': 'Current language',
    'exportFormat.filename': 'File',
    'formatAuto': 'Automatic by language',
    'formatComma': 'Spanish format',
    'formatDot': 'International format'
  }
}; 