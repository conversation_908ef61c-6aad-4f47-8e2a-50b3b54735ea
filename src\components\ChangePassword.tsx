import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { useToast } from './ui/use-toast';
import { supabase } from '../lib/supabase';

export const ChangePassword: React.FC = () => {
  const { t } = useTranslation(['profile', 'common']);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (newPassword !== confirmPassword) {
      toast({
        description: t('profile:changePassword.passwordMismatch'),
        variant: "destructive"
      });
      return;
    }

    if (newPassword.length < 6) {
      toast({
        description: t('profile:changePassword.passwordTooShort'),
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // Primero, verificar la contraseña actual
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: (await supabase.auth.getUser()).data.user?.email || '',
        password: currentPassword
      });

      if (signInError) {
        throw new Error('Invalid current password');
      }

      // Si la contraseña actual es correcta, actualizar la contraseña
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (updateError) throw updateError;

      toast({
        description: t('profile:changePassword.passwordUpdateSuccess')
      });

      // Limpiar los campos
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      console.error('Error updating password:', error);
      toast({
        description: error.message === 'Invalid current password' 
          ? t('profile:changePassword.invalidCurrentPassword')
          : t('profile:changePassword.passwordUpdateError'),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Contraseña actual */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center mb-2">
          <span className="text-red-600 mr-2">🔒</span>
          <label className="block text-sm font-semibold text-red-800">
            {t('profile:changePassword.currentPassword')}
          </label>
        </div>
        <Input
          type="password"
          value={currentPassword}
          onChange={(e) => setCurrentPassword(e.target.value)}
          className="bg-white border-red-300 focus:border-red-500 focus:ring-red-500"
          placeholder="••••••••"
          required
        />
        <p className="text-xs text-red-600 mt-1">
          {t('profile:changePassword.currentPasswordHint', { defaultValue: 'Ingresa tu contraseña actual para verificar tu identidad' })}
        </p>
      </div>

      {/* Separador visual */}
      <div className="flex items-center">
        <div className="flex-grow border-t border-gray-300"></div>
        <span className="flex-shrink-0 px-4 text-sm text-gray-500 bg-gray-50 rounded-full">
          {t('profile:changePassword.newPasswordSection', { defaultValue: 'Nueva contraseña' })}
        </span>
        <div className="flex-grow border-t border-gray-300"></div>
      </div>

      {/* Nueva contraseña */}
      <div className="space-y-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <span className="text-green-600 mr-2">🔑</span>
            <label className="block text-sm font-semibold text-green-800">
              {t('profile:changePassword.newPassword')}
            </label>
          </div>
          <Input
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            className="bg-white border-green-300 focus:border-green-500 focus:ring-green-500"
            placeholder="••••••••"
            required
          />
          <p className="text-xs text-green-600 mt-1">
            {t('profile:changePassword.newPasswordHint', { defaultValue: 'Mínimo 6 caracteres' })}
          </p>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <span className="text-green-600 mr-2">✅</span>
            <label className="block text-sm font-semibold text-green-800">
              {t('profile:changePassword.confirmPassword')}
            </label>
          </div>
          <Input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className="bg-white border-green-300 focus:border-green-500 focus:ring-green-500"
            placeholder="••••••••"
            required
          />
          <p className="text-xs text-green-600 mt-1">
            {t('profile:changePassword.confirmPasswordHint', { defaultValue: 'Repite la nueva contraseña' })}
          </p>
        </div>
      </div>

      <Button
        type="submit"
        disabled={isLoading}
        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {t('common:loading')}
          </div>
        ) : (
          <div className="flex items-center justify-center">
            <span className="mr-2">🔄</span>
            {t('profile:changePassword.submit')}
          </div>
        )}
      </Button>
    </form>
  );
}; 