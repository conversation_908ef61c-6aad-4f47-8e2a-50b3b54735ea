export default {
  repetitiveElements: 'Repetitive Elements',
  start: 'Start',
  stop: 'Stop',
  reset: 'Reset',
  lap: 'Lap',
  element: 'Element',
  time: 'Time',
  rating: 'Rating',
  observations: 'Observations',
  cycle: 'Cycle',
  currentElement: 'Current Element',
  nextElement: 'Save lap',
  previousElement: 'Previous Element',
  totalTime: 'Total Time',
  averageTime: 'Average Time',
  standardTime: 'Standard Time',
  normalTime: 'Normal Time',
  cycles: 'Cycles',
  saveReading: 'Save Reading',
  discardReading: 'Discard Reading',
  confirmDiscard: 'Are you sure you want to discard this reading?',
  readingSaved: 'Reading saved successfully',
  readingError: 'Error saving reading',
  noReadings: 'No readings available',
  loadError: 'Error loading readings',
  description: 'Description',
  noStudySelected: 'No study selected',
  takes: 'Takes',
  averageActivity: 'Average Activity',
  remainingTakes: 'Remaining Takes',
  addComment: 'Add Comment',
  editRecord: 'Edit Record',
  activity: 'Activity',
  comment: 'Comment',
  deleteAllRecords: 'Delete All Records',
  deleteAllRecordsConfirmation: 'Are you sure you want to delete all records for this element? This action cannot be undone.',
  increaseActivity: 'Increase Activity',
  decreaseActivity: 'Decrease Activity',
  voiceInputTip: 'Use the microphone button to add more text to the description',
  addVoiceDescription: 'Add voice description',
  clearDescription: 'Clear description',
  descriptionPlaceholder: 'Element or activity description...',
  errors: {
    loadingRecords: 'Error loading records',
    savingRecord: 'Error saving record',
    updatingRecord: 'Error updating record',
    deletingRecord: 'Error deleting record',
    deletingAllRecords: 'Error deleting all records'
  }
};
