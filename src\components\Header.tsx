import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { Home, Search, Menu, LogOut, Globe, X, Info, ListTodo, Timer, Gauge, Clock, UserPlus, FileText, BookOpen, Watch, User, Download, Upload } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { useStudyStore } from '../store/studyStore';
import { useSupplementTablesStore } from '../store/supplementTablesStore';
import { useSearchStore } from '../store/searchStore';
import { exportStudies, importStudies } from '../utils/studyExportImport';
import { supabase } from '../lib/supabaseClient';

interface HeaderProps {
  showSearch?: boolean;
  showMenu?: boolean;
  onSearch?: (query: string) => void;
  title?: string;
  subtitle?: string;
  showActions?: boolean;
  onBack?: () => void;
  leftIcon?: React.ReactNode;
  onLeftIconClick?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  showSearch = true,
  showMenu = true,
  onSearch,
  title,
  subtitle,
  showActions,
  onBack,
  leftIcon,
  onLeftIconClick,
}: HeaderProps) => {
  const { t, i18n } = useTranslation('common');
  const navigate = useNavigate();
  const location = useLocation();
  const { signOut } = useAuthStore();
  const { selectedStudy, studies, fetchStudies } = useStudyStore();
  const [showSearchInput, setShowSearchInput] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const { searchQuery, setSearchQuery } = useSearchStore();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // Determinar si estamos en la página principal
  const isHomePage = location.pathname === '/';

  const handleExportStudy = async () => {
    if (!selectedStudy) {
      console.error('No hay estudio seleccionado');
      alert(t('exportError', { ns: 'study' }) + ': No hay estudio seleccionado');
      return;
    }

    try {
      await exportStudies(selectedStudy);
    } catch (error) {
      console.error('Error detallado al exportar:', error);
      alert(t('exportError', { ns: 'study' }) + ': ' + (error instanceof Error ? error.message : 'Error desconocido'));
    }
  };

  const handleExportAllStudies = async () => {
    if (!studies || studies.length === 0) {
      console.error('No hay estudios para exportar');
      alert(t('exportError', { ns: 'study' }) + ': ' + t('no_studies', { ns: 'study' }));
      return;
    }

    try {
      await exportStudies(studies);
    } catch (error) {
      console.error('Error detallado al exportar todos los estudios:', error);
      alert(t('exportError', { ns: 'study' }) + ': ' + (error instanceof Error ? error.message : 'Error desconocido'));
    }
  };

  const handleImportStudy = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const importedStudies = await importStudies(file);
          console.log('Estudios importados:', importedStudies);
          
          if (importedStudies && importedStudies.length > 0) {
            // Seleccionar el último estudio importado
            const lastStudy = importedStudies[importedStudies.length - 1];
            // Navegar a la página principal con el ID del estudio a seleccionar
            navigate('/', { state: { selectStudyId: lastStudy.id } });
            alert(t('importSuccess', { ns: 'study', count: importedStudies.length }));
          } else {
            throw new Error('No se importaron estudios');
          }
        } catch (error) {
          console.error('Error detallado al importar:', error);
          const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
          alert(t('importError', { ns: 'study' }) + ': ' + errorMessage);
        }
      }
    };
    input.click();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    if (onSearch) {
      onSearch('');
    }
    setShowSearchInput(false);
  };

  const handleSearchButtonClick = () => {
    if (searchQuery) {
      handleClearSearch();
    } else {
      setShowSearchInput(true);
      if (onSearch) {
        onSearch('');
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setShowSearchInput(false);
    } else if (e.key === 'Escape') {
      handleClearSearch();
    }
  };

  const handleClickOutside = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.classList.contains('modal-overlay')) {
      setShowSearchInput(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchInputRef.current && !searchInputRef.current.contains(event.target as Node)) {
        setShowSearchInput(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = async () => {
    await signOut();
    navigate('/login');
  };

  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang);
    setShowLanguageMenu(false);
  };

  const getNavigationItems = useCallback(() => {
    if (!selectedStudy) return [];

    const hasRepetitiveElements = selectedStudy.elements.some(e => 
      e.repetition_type === 'repetitive-type' || e.repetition_type === 'repetitive'
    );
    const hasMachineElements = selectedStudy.elements.some(e => 
      e.type === 'machine-time' || e.repetition_type === 'machine-type'
    );
    const hasFrequentialElements = selectedStudy.elements.some(e => 
      e.repetition_type === 'frequency-type' || e.repetition_type === 'frequency'
    );

    const items = [
      { path: `/study/${selectedStudy?.id}/info`, label: 'info', icon: Info, visible: true },
      { path: `/study/${selectedStudy?.id}/method`, label: 'method', icon: ListTodo, visible: true }
    ];

    if (hasRepetitiveElements) {
      items.push({ path: `/study/${selectedStudy?.id}/repetitive`, label: 'repetitive', icon: Timer, visible: true, ns: 'study' });
    }

    items.push({ path: `/study/${selectedStudy?.id}/cronoseguido`, label: 'cronoseguido', icon: Watch, visible: true, ns: 'cronoSeguido' });

    if (hasMachineElements) {
      items.push({ path: `/study/${selectedStudy?.id}/machine`, label: 'machineTimes', icon: Gauge, visible: true });
    }

    if (hasFrequentialElements) {
      items.push({ path: `/study/${selectedStudy?.id}/frequency`, label: 'frequencyTimes', icon: Clock, visible: true });
    }

    if (selectedStudy.elements.length > 0) {
      items.push({ path: `/study/${selectedStudy?.id}/supplements`, label: 'supplements', icon: UserPlus, visible: true, ns: 'study' });

      // Verificar si todos los elementos tienen tiempos asignados
      let allElementsHaveTime = true;

      for (const element of selectedStudy.elements) {
        const elementTimes = selectedStudy.time_records?.[element.id] || [];
        const hasTime = elementTimes.length > 0;

        if (!hasTime) {
          allElementsHaveTime = false;
          break;
        }
      }

      // Verificar si hay suplementos configurados
      const hasSupplements = selectedStudy.supplements && 
        selectedStudy.elements.every(element => 
          selectedStudy.supplements[element.id]?.percentage > 0
        );

      // Mostrar el informe solo si todos los elementos tienen tiempos y hay suplementos
      if (allElementsHaveTime && hasSupplements) {
        items.push({ 
          path: `/study/${selectedStudy?.id}/report`, 
          label: 'report', 
          icon: FileText, 
          visible: true,
          ns: 'study'
        });
      }
    }

    return items;
  }, [selectedStudy]);

  const [navigationItems, setNavigationItems] = useState([]);

  useEffect(() => {
    setNavigationItems(getNavigationItems());
  }, [selectedStudy, getNavigationItems]);

  return (
    <header className="bg-gradient-to-r from-purple-700 to-purple-900 text-white p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Botón del menú móvil */}
          <button
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className="lg:hidden p-2 hover:bg-purple-600 rounded-full transition-colors"
          >
            <Menu className="w-5 h-5" />
          </button>
          {leftIcon && onLeftIconClick ? (
            <button
              onClick={onLeftIconClick}
              className="hover:bg-purple-600 rounded-lg px-3 py-2 transition-colors flex flex-col items-start"
            >
              <div className="flex items-center">
                {leftIcon}
                <h1 className="text-xl font-semibold ml-2">
                  {title || t('appName')}
                </h1>
              </div>
              {subtitle && (
                <div className="text-sm opacity-80 mt-1">
                  {subtitle}
                </div>
              )}
            </button>
          ) : onBack ? (
            <button
              onClick={onBack}
              className="hover:bg-purple-600 rounded-lg px-3 py-2 transition-colors flex flex-col items-start"
            >
              <h1 className="text-xl font-semibold">
                {title || t('appName')}
              </h1>
              {subtitle && (
                <div className="text-sm opacity-80 mt-1">
                  {subtitle}
                </div>
              )}
            </button>
          ) : (
            <button
              onClick={() => navigate('/')}
              className="hover:bg-purple-600 rounded-lg px-3 py-2 transition-colors flex flex-col items-start"
            >
              <h1 className="text-xl font-semibold">
                {title || t('appName')}
              </h1>
              {subtitle ? (
                <div className="text-sm opacity-80 mt-1">
                  {subtitle}
                </div>
              ) : (
                selectedStudy && selectedStudy.required_info?.name && (
                  <div className="text-sm opacity-80 mt-1">
                    {selectedStudy.required_info.name}
                  </div>
                )
              )}
            </button>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {showSearch && isHomePage && (
            <div className="relative flex-grow max-w-md">
              <div className="relative flex items-center">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder={t('search.placeholder', { ns: 'common' })}
                  className="w-full px-4 py-2 pl-10 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900"
                  autoFocus
                />
                {searchQuery && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  >
                    <X className="h-4 w-4 text-gray-400" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Acciones adicionales */}
          {showActions !== false && (
            <>
              {/* Changelog Link */}
              <button
                onClick={() => navigate('/changelog')}
                className="hidden lg:flex items-center space-x-1 px-3 py-2 hover:bg-purple-600 rounded-lg transition-colors"
                title={t('changelog', { ns: 'common' })}
              >
                <FileText className="w-5 h-5" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Menú móvil */}
      <div 
        className={`lg:hidden fixed inset-0 z-50 transition-opacity duration-300 ease-in-out ${
          showMobileMenu ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
        }`}
      >
        <div 
          className={`absolute inset-0 bg-black transition-opacity duration-300 ease-in-out ${
            showMobileMenu ? 'opacity-50' : 'opacity-0'
          }`} 
          onClick={() => setShowMobileMenu(false)}
        />
        <div 
          className={`absolute left-0 top-0 h-full w-64 bg-purple-900 p-4 transform transition-all duration-300 ease-in-out ${
            showMobileMenu ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'
          }`}
        >
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setShowMobileMenu(false)}
              className="p-2 hover:bg-purple-600 rounded-full transition-colors duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className={`flex flex-col space-y-2 transition-all duration-300 delay-100 ${
            showMobileMenu ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'
          }`}>
            {navigationItems.map((item) => {
              if (!item.visible) return null;
              
              const label = item.ns ? t(item.label, { ns: item.ns }) : t(item.label, { ns: 'study' });
              
              return (
                <button
                  key={item.path}
                  onClick={() => {
                    navigate(item.path);
                    setShowMobileMenu(false);
                  }}
                  className="px-3 py-2 text-white hover:bg-purple-600 rounded-lg flex items-center space-x-2"
                >
                  <item.icon className="w-5 h-5" />
                  <span>{label}</span>
                </button>
              );
            })}
          </div>
          <div className="mt-4 pt-4 border-t border-purple-600 space-y-2">
            <div className="flex items-center space-x-2">
              <Globe className="w-4 h-4" />
              <select
                value={i18n.language || 'es'}
                onChange={(e) => {
                  i18n.changeLanguage(e.target.value);
                  useSupplementTablesStore.getState().updateDescriptions();
                }}
                className="bg-transparent text-white focus:outline-none cursor-pointer"
              >
                <option value="es" className="text-gray-900">Español</option>
                <option value="en" className="text-gray-900">English</option>
              </select>
            </div>
            <button
              onClick={() => {
                navigate('/library');
                setShowMobileMenu(false);
              }}
              className="w-full flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
            >
              <BookOpen className="w-5 h-5" />
              <span>{t('library:title')}</span>
            </button>
            {selectedStudy && (
              <button
                onClick={() => {
                  handleExportStudy();
                  setShowMobileMenu(false);
                }}
                className="w-full flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
                title={t('exportStudy', { ns: 'study' })}
              >
                <Download className="w-5 h-5" />
                <span>{t('export', { ns: 'study' })}</span>
              </button>
            )}
            {/* Botón para exportar todos los estudios (solo en menú móvil y si hay estudios) */}
            {isHomePage && studies && studies.length > 0 && (
              <button
                onClick={() => {
                  handleExportAllStudies();
                  setShowMobileMenu(false);
                }}
                className="w-full flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
                title={t('exportAllStudies', { ns: 'study' })}
              >
                <Download className="w-5 h-5" />
                <span>{t('exportAllStudies', { ns: 'study' })}</span>
              </button>
            )}
            <button
              onClick={() => {
                handleImportStudy();
                setShowMobileMenu(false);
              }}
              className="w-full flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
              title={t('importStudy', { ns: 'study' })}
            >
              <Upload className="w-5 h-5" />
              <span>{t('import', { ns: 'study' })}</span>
            </button>
            <button
              onClick={() => {
                navigate('/profile');
                setShowMobileMenu(false);
              }}
              className="w-full flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
            >
              <User className="w-5 h-5" />
              <span>{t('header.profile')}</span>
            </button>
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
            >
              <LogOut className="w-5 h-5" />
              <span>{t('header.logout')}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Menú de escritorio */}
      <div className="hidden lg:flex flex-col mt-4">
        <div className="flex flex-wrap justify-between items-center gap-2">
          {navigationItems.map((item) => {
            if (!item.visible) return null;
            
            const label = item.ns ? t(item.label, { ns: item.ns }) : t(item.label, { ns: 'study' });
            
            return (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className="px-3 py-2 text-white hover:bg-purple-600 rounded-lg flex flex-col items-center space-y-1"
              >
                <item.icon className="w-5 h-5" />
                <span className="text-sm">{label}</span>
              </button>
            );
          })}
        </div>

        <div className="flex justify-end items-center space-x-6 mt-4 pt-4 border-t border-purple-600">
          <div className="flex items-center space-x-2">
            <Globe className="w-4 h-4" />
            <select
              value={i18n.language || 'es'}
              onChange={(e) => {
                i18n.changeLanguage(e.target.value);
                useSupplementTablesStore.getState().updateDescriptions();
              }}
              className="bg-transparent text-white focus:outline-none cursor-pointer"
            >
              <option value="es" className="text-gray-900">Español</option>
              <option value="en" className="text-gray-900">English</option>
            </select>
          </div>
          <button
            onClick={() => navigate('/library')}
            className="flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
          >
            <BookOpen className="w-5 h-5" />
            <span>{t('library:title')}</span>
          </button>
          {selectedStudy && (
            <button
              onClick={handleExportStudy}
              className="flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
              title={t('exportStudy', { ns: 'study' })}
            >
              <Download className="w-5 h-5" />
              <span>{t('export', { ns: 'study' })}</span>
            </button>
          )}
          {/* Botón para exportar todos los estudios */}
          {isHomePage && studies && studies.length > 0 && (
            <button
              onClick={() => {
                handleExportAllStudies();
                setShowMobileMenu(false);
              }}
              className="flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
              title={t('exportAllStudies', { ns: 'study' })}
            >
              <Download className="w-5 h-5" /> 
              <span>{t('exportAllStudies', { ns: 'study' })}</span>
            </button>
          )}
          <button
            onClick={handleImportStudy}
            className="flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
            title={t('importStudy', { ns: 'study' })}
          >
            <Upload className="w-5 h-5" />
            <span>{t('import', { ns: 'study' })}</span>
          </button>
          <button
            onClick={() => navigate('/profile')}
            className="flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
          >
            <User className="w-5 h-5" />
            <span>{t('header.profile')}</span>
          </button>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 hover:bg-purple-600 rounded-lg px-3 py-2"
          >
            <LogOut className="w-5 h-5" />
            <span>{t('header.logout')}</span>
          </button>
        </div>
      </div>
    </header>
  );
};