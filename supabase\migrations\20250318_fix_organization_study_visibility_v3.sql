-- Drop existing policies to reapply them with the correct logic
DROP POLICY IF EXISTS "Users can view their own studies" ON studies;

-- <PERSON>reate updated policy with fixed logical structure and proper organization visibility
CREATE POLICY "Users can view their own studies"
ON studies
FOR SELECT
TO authenticated
USING (
    -- User owns the study
    user_id = auth.uid()
    -- OR user is a member of the organization that owns the study
    OR (
        organization_id IN (
            SELECT organization_id
            FROM organization_members
            WHERE user_id = auth.uid()
        )
        AND organization_id IS NOT NULL
    )
    -- OR the study is explicitly shared
    OR is_shared = true
);

-- Add index to improve performance
CREATE INDEX IF NOT EXISTS idx_studies_organization_id_user_id ON studies(organization_id, user_id);

-- Add comment to explain the fix
COMMENT ON POLICY "Users can view their own studies" ON studies IS 'Fixed policy to properly show all studies in organizations with improved organization_id handling';
