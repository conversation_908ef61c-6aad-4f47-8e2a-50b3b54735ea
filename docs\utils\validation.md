# Validation Utilities

## Descripción
Utilidades para validación de datos.

## Funciones

### validateStudyData
```typescript
function validateStudyData(data: Partial<Study>): boolean
```
Valida datos de estudio.

### validateElement
```typescript
function validateElement(element: Partial<WorkElement>): boolean
```
Valida configuración de elemento.

### validateTimeRecord
```typescript
function validateTimeRecord(record: Partial<TimeRecord>): boolean
```
Valida registro de tiempo.

## Uso
```typescript
// Validar estudio
const isValidStudy = validateStudyData({
  name: "Estudio 1",
  company: "Empresa",
  date: "2024-03-26"
});

// Validar elemento
const isValidElement = validateElement({
  description: "Elemento 1",
  type: "machine-stopped"
});
```