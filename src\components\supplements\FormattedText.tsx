import React from 'react';

interface FormattedTextProps {
  text: string;
  className?: string;
}

export const FormattedText: React.FC<FormattedTextProps> = ({ text, className = '' }) => {
  // Divide el texto en líneas
  const lines = text.split('\n');

  return (
    <pre className={`web-mono print-mono print-explanation ${className}`}>
      {lines.map((line, index) => {
        // Maneja los encabezados
        if (line.includes('explanation-header')) {
          const headerText = line.replace(/<span class="explanation-header">|<\/span>/g, '').trim();
          return (
            <div key={index} className="font-bold mt-4 mb-2">
              {headerText}
            </div>
          );
        }
        
        // Maneja las líneas horizontales
        if (line.includes('<hr>')) {
          return <div key={index} className="my-4 border-t border-gray-300" />;
        }

        // Maneja las viñetas
        if (line.startsWith('▸')) {
          return (
            <div key={index} className="ml-4 my-1">
              {line}
            </div>
          );
        }

        // Maneja los espacios en blanco
        if (line.trim() === '') {
          return <div key={index}>&nbsp;</div>;
        }

        // Texto normal
        return <div key={index} className="my-1">{line}</div>;
      })}
    </pre>
  );
};
