import React from 'react';
import { cn } from '../../lib/utils';

// Skeleton básico
export const Skeleton: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ 
  className, 
  ...props 
}) => {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%]",
        "animate-[shimmer_1.5s_ease-in-out_infinite]",
        className
      )}
      style={{
        backgroundImage: 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
        backgroundSize: '200% 100%',
        animation: 'shimmer 1.5s ease-in-out infinite'
      }}
      {...props}
    />
  );
};

// Skeleton para tarjetas de organizaciones
export const OrganizationCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg border p-4 shadow-sm space-y-3">
      <div className="flex items-start justify-between">
        <div className="space-y-2 flex-1">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <Skeleton className="h-8 w-20" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-1/4" />
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
      <div className="flex space-x-2">
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-24" />
      </div>
    </div>
  );
};

// Skeleton para tabla de miembros
export const MemberTableSkeleton: React.FC = () => {
  return (
    <div className="space-y-3">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-8 w-16" />
          </div>
        </div>
      ))}
    </div>
  );
};

// Skeleton para solicitudes pendientes
export const JoinRequestSkeleton: React.FC = () => {
  return (
    <div className="space-y-3">
      {[...Array(2)].map((_, index) => (
        <div key={index} className="p-4 bg-gray-50 rounded border">
          <div className="flex justify-between items-center">
            <div className="space-y-2">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Skeleton para lista de solicitudes enviadas
export const SentRequestSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      {[...Array(2)].map((_, index) => (
        <div key={index} className="p-4 border rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <Skeleton className="h-5 w-44" />
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-4 w-28" />
            </div>
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
        </div>
      ))}
    </div>
  );
};

// Loading state con mensaje personalizado
export const LoadingState: React.FC<{
  message?: string;
  icon?: string;
  variant?: 'default' | 'organizations' | 'members' | 'requests';
}> = ({ 
  message = "Cargando...", 
  icon = "⏳", 
  variant = 'default' 
}) => {
  const getSkeletonComponent = () => {
    switch (variant) {
      case 'organizations':
        return <OrganizationCardSkeleton />;
      case 'members':
        return <MemberTableSkeleton />;
      case 'requests':
        return <JoinRequestSkeleton />;
      default:
        return (
          <div className="space-y-3">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Mensaje de carga */}
      <div className="flex items-center justify-center p-6">
        <div className="text-center">
          <div className="text-3xl mb-2 animate-bounce">{icon}</div>
          <p className="text-gray-600 font-medium">{message}</p>
        </div>
      </div>
      
      {/* Skeleton apropiado */}
      {getSkeletonComponent()}
    </div>
  );
};

// CSS personalizado para la animación shimmer
const shimmerKeyframes = `
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

// Inyectar CSS si no existe
if (typeof document !== 'undefined') {
  const styleId = 'skeleton-shimmer-styles';
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = shimmerKeyframes;
    document.head.appendChild(style);
  }
} 