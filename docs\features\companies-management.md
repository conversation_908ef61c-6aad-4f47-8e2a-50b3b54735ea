# Gestión de Empresas/Departamentos

## Descripción

Esta funcionalidad permite a los usuarios gestionar una lista de empresas y departamentos personalizados desde su perfil, y utilizarlos rápidamente al crear nuevos estudios de tiempos.

## Funcionalidades

### 1. Gestión desde el Perfil

Los usuarios pueden acceder a la gestión de empresas desde la página de perfil:

- **Agregar empresas/departamentos**: Escribir y agregar nuevas empresas a la lista personal
- **Eliminar empresas**: Remover empresas que ya no se usan
- **Establecer empresa por defecto**: Seleccionar una empresa que se use automáticamente en nuevos estudios

### 2. Uso en Estudios

Al crear o editar un estudio:

- El campo empresa se convierte en un combobox
- Muestra las empresas guardadas como opciones rápidas
- Permite escribir una nueva empresa si no está en la lista
- Si hay una empresa por defecto configurada, se pre-selecciona automáticamente

## Estructura de Datos

### Base de Datos

```sql
-- Nuevos campos en la tabla profiles
ALTER TABLE public.profiles 
ADD COLUMN companies_list JSONB DEFAULT '[]'::jsonb,
ADD COLUMN default_company TEXT;
```

### TypeScript

```typescript
interface UserProfile {
  // ... campos existentes
  companies_list?: string[];        // Lista de empresas guardadas
  default_company?: string | null;  // Empresa por defecto
}
```

## Componentes Modificados

### 1. ProfilePreferences.tsx

Agrega la sección de gestión de empresas:

- **Lista de empresas**: Muestra las empresas guardadas con opción de eliminar
- **Agregar empresa**: Campo para añadir nuevas empresas
- **Empresa por defecto**: Selector para elegir la empresa por defecto

### 2. StudyForm.tsx

Modifica el campo empresa:

- **Combobox con datalist**: Permite seleccionar de la lista o escribir nuevo
- **Auto-completado**: Facilita la selección de empresas existentes
- **Valor por defecto**: Pre-llena la empresa por defecto del usuario

## Traducciones

### Español
```typescript
preferences: {
  companiesList: 'Empresas / Departamentos',
  noCompanies: 'No hay empresas/departamentos guardados',
  addCompanyPlaceholder: 'Agregar empresa/departamento...',
  addCompany: 'Agregar empresa',
  removeCompany: 'Eliminar empresa',
  add: 'Agregar',
  defaultCompany: 'Empresa/Departamento por defecto',
  noDefaultCompany: 'Ninguna (escribir manualmente)',
  defaultCompanyHelp: 'Esta empresa se seleccionará automáticamente en nuevos estudios'
}
```

### Inglés
```typescript
preferences: {
  companiesList: 'Companies / Departments',
  noCompanies: 'No companies/departments saved',
  addCompanyPlaceholder: 'Add company/department...',
  addCompany: 'Add company',
  removeCompany: 'Remove company',
  add: 'Add',
  defaultCompany: 'Default Company/Department',
  noDefaultCompany: 'None (type manually)',
  defaultCompanyHelp: 'This company will be automatically selected in new studies'
}
```

## Flujo de Uso

### Configuración Inicial

1. Usuario accede a su perfil
2. Navega a la sección "Empresas / Departamentos"
3. Agrega las empresas que usa frecuentemente
4. Opcionalmente selecciona una empresa por defecto

### Creación de Estudios

1. Usuario crea un nuevo estudio
2. El campo empresa se pre-llena con la empresa por defecto (si está configurada)
3. Usuario puede:
   - Mantener la empresa por defecto
   - Seleccionar otra empresa de la lista
   - Escribir una nueva empresa

### Beneficios

- **Eficiencia**: Reduce tiempo de escritura en estudios frecuentes
- **Consistencia**: Evita variaciones en nombres de empresas
- **Flexibilidad**: Permite agregar nuevas empresas cuando sea necesario
- **Personalización**: Cada usuario mantiene su propia lista

## Migración

Para aplicar esta funcionalidad en una base de datos existente:

```sql
-- Ejecutar el archivo de migración
\i supabase/migrations/20250130_add_companies_management.sql
```

O ejecutar directamente en Supabase SQL Editor:

1. Copiar el contenido de `20250130_add_companies_management.sql`
2. Ejecutar en el SQL Editor del dashboard de Supabase
3. Verificar que los campos se agregaron correctamente 