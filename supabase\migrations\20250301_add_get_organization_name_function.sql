-- Create a function to get organization name by ID
-- This function will be accessible to authenticated users and will return the name of an organization
-- even if the user doesn't have direct access to the organizations table

CREATE OR REPLACE FUNCTION get_organization_name(p_organization_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_name TEXT;
BEGIN
    -- Get the organization name
    SELECT name INTO v_name
    FROM organizations
    WHERE id = p_organization_id;
    
    RETURN v_name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_organization_name(UUID) TO authenticated;