-- Add elements, time_records and supplements columns to studies table
ALTER TABLE public.studies
ADD COLUMN IF NOT EXISTS elements JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS time_records JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS supplements JSONB DEFAULT '{}'::jsonb;

-- Add comment to explain the structure of each JSONB column
COMMENT ON COLUMN public.studies.elements IS 'Array of study elements. Example: [{"id": "uuid", "type": "machine-stopped", "position": 2, "study_id": "uuid", "description": "Element description", "repetition_type": "repetitive", "frequency_cycles": 1, "frequency_repetitions": 1}]';

COMMENT ON COLUMN public.studies.time_records IS 'Object mapping element IDs to arrays of time records. Example: {"element_id": [{"id": "uuid", "time": 7.554, "comment": "", "activity": 100, "elementId": "element_id", "timestamp": "2024-11-19T20:10:12.691Z"}]}';

COMMENT ON COLUMN public.studies.supplements IS 'Object containing supplementary study data';

-- Add RLS policies for the new columns
ALTER POLICY "Users can update their own studies"
    ON public.studies
    USING (auth.uid() = user_id);

-- Ensure the new columns are included in the select policy
ALTER POLICY "Users can view their own studies"
    ON public.studies
    USING (auth.uid() = user_id);
