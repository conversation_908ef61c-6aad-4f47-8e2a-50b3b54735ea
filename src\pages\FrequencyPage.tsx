import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle } from 'lucide-react';
import { Header } from '../components/Header';
import { ElementStats } from '../components/ElementStats';
import { DeleteConfirmModal } from '../components/DeleteConfirmModal';
import { FrequencyElementList } from '../components/FrequencyElementList';
import { FrequencyTimer } from '../components/FrequencyTimer';
import { FrequencyRecordList } from '../components/FrequencyRecordList';
import { useStudyStore } from '../store/studyStore';
import { useTimeRecordStore } from '../store/timeRecordStore';
import { useTimeRecords } from '../hooks/useTimeRecords';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl';
import { ElementInstance, TimeRecord } from '../types';
import { useNavigate } from 'react-router-dom';
import { VoiceInputModal } from '../components/VoiceInputModal';
import { useAuthStore } from '../store/authStore';

interface FrequencyPageProps {
  onBack: () => void;
}

export const FrequencyPage: React.FC<FrequencyPageProps> = ({ onBack }) => {
  const { t, i18n } = useTranslation(['frequency', 'common', 'study']);
  const navigate = useNavigate();
  const study = useStudyFromUrl();
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const isLoading = useStudyStore(state => state.isLoading);
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);
  const { user } = useAuthStore();
  const [isSharedStudy, setIsSharedStudy] = useState(false);

  useEffect(() => {
    // Mark that we've attempted to load when loading state changes from true to false
    if (!isLoading) {
      setHasAttemptedLoad(true);
    }
  }, [isLoading]);

  useEffect(() => {
    // Only redirect if:
    // 1. We're not currently loading
    // 2. We've attempted to load at least once
    // 3. We don't have a study
    if (!isLoading && hasAttemptedLoad && !study && !selectedStudy) {
      navigate('/');
    }
  }, [study, selectedStudy, isLoading, hasAttemptedLoad, navigate]);

  useEffect(() => {
    if (selectedStudy && user) {
      setIsSharedStudy(selectedStudy.user_id !== user.id);
    }
  }, [selectedStudy, user]);

  const elements = selectedStudy?.elements || [];
  const { saveRecords, deleteRecord, deleteAllRecords, updateRecord } = useTimeRecordStore();
  
  const frequencyElements = useMemo(() => 
    elements.filter(e => 
      e.repetition_type === 'frequency-type' || e.repetition_type === 'frequency'
    ),
    [elements]
  );

  const [selectedElement, setSelectedElement] = useState<ElementInstance | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [time, setTime] = useState(0);
  const [activity, setActivity] = useState(selectedStudy?.required_info?.activity_scale?.normal || 100);
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const [comment, setComment] = useState('');
  const [editingRecord, setEditingRecord] = useState<TimeRecord | null>(null);
  const [error, setError] = useState<string | null>(null);

  const timerRef = useRef<number>();
  const startTimeRef = useRef<number>();

  const selectedElementStats = useTimeRecords(
    selectedElement ? selectedStudy?.time_records[selectedElement.id] || [] : []
  );

  const handleElementClick = (element: ElementInstance) => {
    setSelectedElement(element);
    setTime(0);
    setIsRunning(false);
    setIsPaused(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  const handleStart = () => {
    if (!selectedElement) return;

    startTimeRef.current = Date.now() - time;
    timerRef.current = window.setInterval(() => {
      setTime(Date.now() - startTimeRef.current!);
    }, 10);
    setIsRunning(true);
    setIsPaused(false);
  };

  const handlePause = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = undefined;
    }
    setIsPaused(true);
    setIsRunning(false);
  };

  const handleStop = async () => {
    if (!selectedElement || !isRunning) return;

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    try {
      const record: Omit<TimeRecord, 'id'> = {
        elementId: selectedElement.id,
        time: time / 1000,
        original_time: time / 1000,
        activity,
        timestamp: new Date().toISOString(),
        description: selectedElement.description,
        addedToMethod: false,
        comment: ''
      };

      await saveRecords([record]);
      setTime(0);
      setIsRunning(false);
      setIsPaused(false);
    } catch (err) {
      console.error('Error saving record:', err);
      setError(t('errors.savingRecord', { ns: 'common' }));
      setIsRunning(true);
      setIsPaused(false);
      startTimeRef.current = Date.now() - time;
      timerRef.current = window.setInterval(() => {
        setTime(Date.now() - startTimeRef.current!);
      }, 10);
    }
  };

  const adjustActivity = (increment: boolean) => {
    const activityScale = selectedStudy?.required_info?.activity_scale;
    if (!activityScale) return;

    setActivity(prev => {
      const step = 5;
      const optimal = activityScale.optimal;
      const normal = activityScale.normal;
      const min = Math.max(50, normal * 0.5);
      
      let newActivity;
      if (increment) {
        if (prev === 130) return optimal;
        newActivity = Math.floor(prev / step) * step + step;
      } else {
        if (prev === optimal) return 130;
        newActivity = Math.ceil(prev / step) * step - step;
      }
      
      return Math.min(Math.max(newActivity, min), optimal);
    });
  };

  const handleUpdateRecord = async (record: TimeRecord) => {
    try {
      await updateRecord(record.id, record);
      setEditingRecord(null);
      setShowEditModal(false);
    } catch (err) {
      console.error('Error updating record:', err);
      setError(t('errors.updatingRecord', { ns: 'common' }));
    }
  };

  const handleDeleteRecord = async (recordId: string) => {
    try {
      await deleteRecord(recordId);
    } catch (err) {
      console.error('Error deleting record:', err);
      setError(t('errors.deletingRecord', { ns: 'common' }));
    }
  };

  const handleDeleteAllRecords = async () => {
    if (!selectedElement) return;

    try {
      await deleteAllRecords(selectedElement.id);
      setShowDeleteAllModal(false);
    } catch (err) {
      console.error('Error deleting all records:', err);
      setError(t('errors.deletingAllRecords', { ns: 'common' }));
    }
  };

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Mostrar un loader mientras se carga el estudio
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <Header
          title={t('title', { ns: 'frequency' })}
          showSearch={false}
          showActions={false}
          onBack={onBack}
        />
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
            <div className="text-lg font-medium text-gray-900">
              {t('loading', { ns: 'common' })}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Si no hay estudio después de cargar, redirigir (no mostrar mensaje)
  if (!selectedStudy) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('title', { ns: 'frequency' })}
        subtitle={selectedStudy.required_info?.name || ''}
        showSearch={false}
        showActions={true}
        onBack={onBack}
      />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <span>{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-500 hover:text-red-700"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}

          {isSharedStudy && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <span>{t('permission_error.message', { ns: 'study' })}</span>
              <button
                onClick={() => setIsSharedStudy(false)}
                className="ml-auto text-amber-500 hover:text-amber-700"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}

          {!selectedElement ? (
            <FrequencyElementList
              elements={frequencyElements}
              allElements={elements}
              records={selectedStudy.time_records}
              isLoading={false}
              onElementClick={handleElementClick}
            />
          ) : (
            <>
              <ElementStats 
                {...selectedElementStats}
                element={selectedElement}
                timeRecords={selectedStudy.time_records[selectedElement.id] || []}
              />

              <FrequencyTimer
                element={selectedElement}
                time={time}
                activity={activity}
                isRunning={isRunning}
                onStart={handleStart}
                onPause={handlePause}
                onStop={handleStop}
                onActivityChange={adjustActivity}
              />

              <FrequencyRecordList
                element={selectedElement}
                records={selectedStudy.time_records[selectedElement.id] || []}
                onDeleteRecord={handleDeleteRecord}
                onEditRecord={(record) => {
                  setEditingRecord({
                    ...record,
                    frequency_repetitions: selectedElement?.frequency_repetitions || 1,
                    frequency_cycles: selectedElement?.frequency_cycles || 1
                  });
                  setShowEditModal(true);
                }}
                onAddComment={(record) => {
                  setComment(record.comment || '');
                  setEditingRecord(record);
                  setShowCommentModal(true);
                }}
                onDeleteAll={() => setShowDeleteAllModal(true)}
              />

              <button
                onClick={() => setSelectedElement(null)}
                className="fixed bottom-6 right-6 w-14 h-14 bg-purple-600 text-white rounded-full shadow-lg flex items-center justify-center hover:bg-purple-700 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </>
          )}
        </div>
      </main>

      {showCommentModal && editingRecord && (
        <VoiceInputModal
          isOpen={showCommentModal}
          onClose={() => setShowCommentModal(false)}
          onTranscript={(text) => {
            handleUpdateRecord({
              ...editingRecord,
              comment: text
            });
            setShowCommentModal(false);
          }}
          title={t('addComment', { ns: 'common' })}
        />
      )}

      {showEditModal && editingRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-bold mb-4">{t('editRecord', { ns: 'frequency' })}</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('time', { ns: 'frequency' })}
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editingRecord.time}
                  onChange={(e) => setEditingRecord({
                    ...editingRecord,
                    time: parseFloat(e.target.value)
                  })}
                  className="w-full p-2 border rounded"
                  title={t('time', { ns: 'frequency' })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('activity', { ns: 'frequency' })}
                </label>
                <input
                  type="number"
                  value={editingRecord.activity}
                  onChange={(e) => setEditingRecord({
                    ...editingRecord,
                    activity: parseInt(e.target.value)
                  })}
                  className="w-full p-2 border rounded"
                  title={t('activity', { ns: 'frequency' })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('frequency', { ns: 'frequency' })}
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    min="1"
                    value={editingRecord.frequency_repetitions || 1}
                    onChange={(e) => {
                      const repetitions = parseInt(e.target.value) || 1;
                      const cycles = editingRecord.frequency_cycles || 1;
                      // Calculamos el factor de ajuste basado en la cantidad esperada vs la real
                      const expectedPieces = selectedElement?.frequency_cycles || 1;
                      const actualPieces = cycles;
                      const adjustmentFactor = expectedPieces / actualPieces;
                      // Ajustamos el tiempo observado según la fórmula T'_o = T_o × (N_e/N_r)
                      // Usamos el tiempo original del registro para el cálculo
                      const originalTime = editingRecord.original_time || editingRecord.time;
                      const adjustedTime = originalTime * adjustmentFactor;
                      setEditingRecord({
                        ...editingRecord,
                        frequency_repetitions: repetitions,
                        time: adjustedTime,
                        original_time: originalTime // Guardamos el tiempo original
                      });
                    }}
                    className="w-1/2 p-2 border rounded"
                    title={t('repetitions', { ns: 'frequency' })}
                  />
                  <span className="text-gray-500">/</span>
                  <input
                    type="number"
                    min="1"
                    value={editingRecord.frequency_cycles || 1}
                    onChange={(e) => {
                      const cycles = parseInt(e.target.value) || 1;
                      const repetitions = editingRecord.frequency_repetitions || 1;
                      // Calculamos el factor de ajuste basado en la cantidad esperada vs la real
                      const expectedPieces = selectedElement?.frequency_cycles || 1;
                      const actualPieces = cycles;
                      const adjustmentFactor = expectedPieces / actualPieces;
                      // Ajustamos el tiempo observado según la fórmula T'_o = T_o × (N_e/N_r)
                      // Usamos el tiempo original del registro para el cálculo
                      const originalTime = editingRecord.original_time || editingRecord.time;
                      const adjustedTime = originalTime * adjustmentFactor;
                      setEditingRecord({
                        ...editingRecord,
                        frequency_cycles: cycles,
                        time: adjustedTime,
                        original_time: originalTime // Guardamos el tiempo original
                      });
                    }}
                    className="w-1/2 p-2 border rounded"
                    title={t('cycles', { ns: 'frequency' })}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 text-blue-700">
                  {t('comment', { ns: 'common', defaultValue: 'Comentario' })}
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-start space-x-2">
                    <textarea
                      value={editingRecord.comment || ''}
                      onChange={(e) => setEditingRecord({
                        ...editingRecord,
                        comment: e.target.value
                      })}
                      className="w-full rounded-lg border-2 border-blue-300 focus:border-blue-500 focus:ring-blue-500 bg-blue-50 p-2"
                      rows={2}
                      placeholder={t('addCommentPlaceholder', { ns: 'common', defaultValue: 'Agregar comentario sobre este registro...' })}
                    />
                    <button
                      type="button"
                      onClick={() => setEditingRecord({
                        ...editingRecord,
                        comment: ''
                      })}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
                      title={t('clearComment', { ns: 'common', defaultValue: 'Limpiar comentario' })}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  {editingRecord.comment && (
                    <p className="text-xs text-blue-600">
                      {t('commentWillExport', { ns: 'common', defaultValue: 'Este comentario aparecerá en el informe Excel' })}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
                title={t('cancel', { ns: 'frequency' })}
              >
                {t('cancel', { ns: 'frequency' })}
              </button>
              <button
                onClick={() => handleUpdateRecord(editingRecord)}
                className="px-4 py-2 bg-purple-600 text-white rounded"
                title={t('save', { ns: 'frequency' })}
              >
                {t('save', { ns: 'frequency' })}
              </button>
            </div>
          </div>
        </div>
      )}

      <DeleteConfirmModal
        isOpen={showDeleteAllModal}
        onClose={() => setShowDeleteAllModal(false)}
        onConfirm={handleDeleteAllRecords}
        title={t('deleteAllRecords', { ns: 'frequency' })}
        message={t('deleteAllRecordsConfirmation', { ns: 'frequency' })}
        cancelText={t('cancel', { ns: 'frequency' })}
        deleteText={t('delete', { ns: 'frequency' })}
      />
    </div>
  );
};