import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { ArrowLeft } from 'lucide-react';
import { LanguageSelector } from '../components/LanguageSelector';

interface SectionProps {
  title: string;
  content: string;
  children?: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({ title, content, children }) => (
  <section className="mb-8">
    <h2 className="text-2xl font-semibold mb-4 text-gray-800">{title}</h2>
    <p className="text-gray-600 mb-4">{content}</p>
    {children}
  </section>
);

interface BulletListProps {
  items: string[];
}

const BulletList: React.FC<BulletListProps> = ({ items }) => (
  <ul className="list-disc list-inside space-y-2 text-gray-600 ml-4">
    {items.map((item, index) => (
      <li key={index}>{item}</li>
    ))}
  </ul>
);

export const PrivacyPolicyPage: React.FC = () => {
  const { t, i18n } = useTranslation(['privacy', 'common']);
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50">
      <Helmet>
        <title>{t('meta.title', { ns: 'privacy' })}</title>
        <meta name="description" content={t('meta.description', { ns: 'privacy' })} />
        <meta name="keywords" content={t('meta.keywords', { ns: 'privacy' })} />
      </Helmet>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-purple-600 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {t('back', { ns: 'common' })}
            </button>
            <LanguageSelector />
          </div>

          <h1 className="text-3xl font-bold mb-6 text-gray-900">{t('title', { ns: 'privacy' })}</h1>
          
          <p className="text-gray-600 mb-8">
            {t('introduction', { ns: 'privacy' })}
          </p>

          <div className="flex flex-col space-y-2 mb-8 text-sm text-gray-500">
            <p>{t('lastUpdate', { ns: 'privacy', date: new Date('2024-12-02').toLocaleDateString(i18n.language) })}</p>
            <p>{t('effectiveDate', { ns: 'privacy', date: new Date('2024-12-02').toLocaleDateString(i18n.language) })}</p>
          </div>

          <p className="text-gray-600 mb-8 italic">
            {t('changes', { ns: 'privacy' })}
          </p>

          <Section
            title={t('sections.dataCollection.title', { ns: 'privacy' })}
            content={t('sections.dataCollection.content', { ns: 'privacy' })}
          >
            <BulletList items={[t('sections.dataCollection.email', { ns: 'privacy' }), t('sections.dataCollection.name', { ns: 'privacy' })]} />
          </Section>

          <Section
            title={t('sections.howWeCollect.title', { ns: 'privacy' })}
            content={t('sections.howWeCollect.content', { ns: 'privacy' })}
          >
            <BulletList items={[t('sections.howWeCollect.registration', { ns: 'privacy' })]} />
          </Section>

          <Section
            title={t('sections.howWeUse.title', { ns: 'privacy' })}
            content={t('sections.howWeUse.content', { ns: 'privacy' })}
          >
            <BulletList
              items={[
                t('sections.howWeUse.productInfo', { ns: 'privacy' }),
                t('sections.howWeUse.account', { ns: 'privacy' }),
                t('sections.howWeUse.testimonials', { ns: 'privacy' }),
                t('sections.howWeUse.administration', { ns: 'privacy' }),
                t('sections.howWeUse.management', { ns: 'privacy' }),
                t('sections.howWeUse.communications', { ns: 'privacy' })
              ]}
            />
            <p className="mt-4 text-gray-600 italic">{t('sections.howWeUse.consent', { ns: 'privacy' })}</p>
          </Section>

          <Section
            title={t('sections.sharing.title', { ns: 'privacy' })}
            content={t('sections.sharing.content', { ns: 'privacy' })}
          >
            <BulletList
              items={[
                t('sections.sharing.analytics', { ns: 'privacy' }),
                t('sections.sharing.legal', { ns: 'privacy' }),
                t('sections.sharing.enforcement', { ns: 'privacy' }),
                t('sections.sharing.rights', { ns: 'privacy' }),
                t('sections.sharing.merger', { ns: 'privacy' })
              ]}
            />
          </Section>

          <Section
            title={t('sections.retention.title', { ns: 'privacy' })}
            content={t('sections.retention.content', { ns: 'privacy' })}
          >
            <p className="mt-4 text-gray-600">{t('sections.retention.details', { ns: 'privacy' })}</p>
          </Section>

          <Section
            title={t('sections.rights.title', { ns: 'privacy' })}
            content={t('sections.rights.content', { ns: 'privacy' })}
          >
            <BulletList
              items={[
                t('sections.rights.access', { ns: 'privacy' }),
                t('sections.rights.copy', { ns: 'privacy' }),
                t('sections.rights.restrict', { ns: 'privacy' }),
                t('sections.rights.withdraw', { ns: 'privacy' })
              ]}
            />
            <p className="mt-4 text-gray-600">{t('sections.rights.exercise', { ns: 'privacy' })}</p>
          </Section>

          <Section
            title={t('sections.billing.title', { ns: 'privacy' })}
            content={t('sections.billing.content', { ns: 'privacy' })}
          />

          <Section
            title={t('sections.minors.title', { ns: 'privacy' })}
            content={t('sections.minors.content', { ns: 'privacy' })}
          />

          <Section
            title={t('sections.cookies.title', { ns: 'privacy' })}
            content={t('sections.cookies.content', { ns: 'privacy' })}
          />

          <Section
            title={t('sections.security.title', { ns: 'privacy' })}
            content={t('sections.security.content', { ns: 'privacy' })}
          />

          <Section
            title={t('sections.contact.title', { ns: 'privacy' })}
            content={t('sections.contact.content', { ns: 'privacy' })}
          />
        </div>

        <div className="mt-8 flex justify-center gap-6 text-sm">
          <a
            href="https://cronometras.com/privacy/"
            className="text-purple-600 hover:text-purple-700 transition-colors"
            target="_blank"
            rel="noopener noreferrer"
          >
            Política de Privacidad
          </a>
          <a
            href="https://cronometras.com/cookies/"
            className="text-purple-600 hover:text-purple-700 transition-colors"
            target="_blank"
            rel="noopener noreferrer"
          >
            Política de Cookies
          </a>
        </div>
      </div>
    </div>
  );
};
