import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';
import { useAuthStore } from '../../store/authStore';
import { OrganizationMember } from '../../types/organization';
import { CleanOrphanedStudiesButton } from '../organization/CleanOrphanedStudiesButton';
import { LoadingState } from '../ui/skeleton';
import { MemberCard } from '../ui/enhanced-card';

interface OrganizationMembersProps {
  organizationId?: string;
  selectedOrganizationId?: string;
  onSelectOrganization?: (organizationId: string) => void;
}

export const OrganizationMembers: React.FC<OrganizationMembersProps> = ({ 
  organizationId, 
  selectedOrganizationId,
  onSelectOrganization 
}) => {
  const { t } = useTranslation('common');
  const { toast } = useToast();
  const { user } = useAuthStore();
  const { 
    members, 
    fetchMembers, 
    removeMember, 
    promoteMember, 
    demoteMember,
    isLoading,
    organizations
  } = useOrganizationStore();
  const [showMembers, setShowMembers] = useState(false); // Oculto por defecto
  const [memberCount, setMemberCount] = useState(0);
  const [membersLoaded, setMembersLoaded] = useState(false);
  const [activeOrganizationId, setActiveOrganizationId] = useState<string | undefined>(organizationId);

  // Si se proporciona un organizationId específico, usarlo
  // De lo contrario, usar el seleccionado o el primero de la lista
  useEffect(() => {
    if (organizationId) {
      setActiveOrganizationId(organizationId);
    } else if (selectedOrganizationId) {
      setActiveOrganizationId(selectedOrganizationId);
    } else if (organizations.length > 0 && !activeOrganizationId) {
      setActiveOrganizationId(organizations[0].id);
    }
  }, [organizationId, selectedOrganizationId, organizations, activeOrganizationId]);

  // Obtener los miembros de la organización solo una vez al montar el componente
  useEffect(() => {
    const loadMembers = async () => {
      if (activeOrganizationId && !membersLoaded) {
        console.log('Loading members for organization (first time):', activeOrganizationId);
        await fetchMembers(activeOrganizationId);
        setMembersLoaded(true);
      }
    };
    
    loadMembers();
  }, [activeOrganizationId, fetchMembers, membersLoaded]);

  // Actualizar el contador de miembros cuando cambie la lista
  useEffect(() => {
    if (activeOrganizationId) {
      const organizationMembers = members[activeOrganizationId] || [];
      setMemberCount(organizationMembers.length);
    }
  }, [members, activeOrganizationId]);

  const handleRemoveMember = async (memberId: string) => {
    if (!activeOrganizationId) return;
    
    // No permitir expulsar al propietario o a uno mismo
    const organizationMembers = members[activeOrganizationId] || [];
    const memberToRemove = organizationMembers.find(m => m.user_id === memberId);
    
    if (!memberToRemove) {
      return;
    }
    
    if (memberToRemove.role === 'owner') {
      toast({
        title: t('errors.error'),
        description: t('organizationMembers.cannotRemoveOwner'),
        variant: 'destructive',
      });
      return;
    }
    
    if (memberId === user?.id) {
      toast({
        title: t('errors.error'),
        description: t('organizationMembers.cannotRemoveSelf'),
        variant: 'destructive',
      });
      return;
    }
    
    if (!confirm(t('organizationMembers.removeConfirm'))) {
      return;
    }
    
    try {
      await removeMember(activeOrganizationId, memberId);
      
      toast({
        title: t('success'),
        description: t('organizationMembers.removeSuccess'),
      });
    } catch (error) {
      toast({
        title: t('errors.error'),
        description: t('organizationMembers.removeError'),
        variant: 'destructive',
      });
    }
  };

  const handlePromoteMember = async (memberId: string) => {
    if (!activeOrganizationId) return;
    
    try {
      await promoteMember(activeOrganizationId, memberId);
      toast({
        title: t('success'),
        description: t('organizationMembers.promoteSuccess'),
      });
    } catch (error) {
      toast({
        title: t('errors.error'),
        description: (error as Error).message,
        variant: 'destructive',
      });
    }
  };

  const handleDemoteMember = async (memberId: string) => {
    if (!activeOrganizationId) return;
    
    try {
      await demoteMember(activeOrganizationId, memberId);
      toast({
        title: t('success'),
        description: t('organizationMembers.demoteSuccess'),
      });
    } catch (error) {
      toast({
        title: t('errors.error'),
        description: (error as Error).message,
        variant: 'destructive',
      });
    }
  };

  const getRoleTranslation = (role: string) => {
    switch (role) {
      case 'owner':
        return t('organizationMembers.owner');
      case 'admin':
        return t('organizationMembers.admin');
      case 'member':
        return t('organizationMembers.member');
      default:
        return role;
    }
  };

  // Verificar si el usuario actual es admin o propietario
  const isAdminOrOwner = user && members[activeOrganizationId] && members[activeOrganizationId].some(m => 
    m.user_id === user.id && (m.role === 'admin' || m.role === 'owner')
  );

  // Verificar si el usuario actual es propietario
  const isOwner = user && members[activeOrganizationId] && members[activeOrganizationId].some(m => 
    m.user_id === user.id && m.role === 'owner'
  );

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">{t('organizationMembers.title')}</h2>
          <div className="flex space-x-2">
            {/* Selector de organización si hay más de una y no se proporciona un ID específico */}
            {!organizationId && organizations.length > 1 && (
              <select 
                className="text-sm border rounded px-2 py-1"
                value={activeOrganizationId}
                title="Seleccionar organización"
                onChange={(e) => {
                  const newOrgId = e.target.value;
                  setActiveOrganizationId(newOrgId);
                  setMembersLoaded(false); // Forzar recarga de miembros
                  if (onSelectOrganization) {
                    onSelectOrganization(newOrgId);
                  }
                }}
              >
                {organizations.map(org => (
                  <option key={org.id} value={org.id}>{org.name}</option>
                ))}
              </select>
            )}
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowMembers(!showMembers)}
            >
              {showMembers ? t('hide') : t('show')}
            </Button>
          </div>
        </div>

        {activeOrganizationId && (
          <div className="text-sm text-gray-600 mb-2">
            <p>{t('organizationMembers.selectedOrganization')}: <strong>{organizations.find(org => org.id === activeOrganizationId)?.name}</strong></p>
            <p className="mt-1">{t('organizationMembers.shareStudiesHint')}</p>
            
            {/* Botón para limpiar estudios huérfanos - solo visible para propietarios */}
            {isOwner && (
              <div className="mt-3">
                <CleanOrphanedStudiesButton organizationId={activeOrganizationId} />
              </div>
            )}
          </div>
        )}
        
        {showMembers && (
          <div className="space-y-4">
            {isLoading ? (
              <LoadingState 
                message="Cargando miembros de la organización..." 
                icon="👥" 
                variant="members"
              />
            ) : members[activeOrganizationId] && members[activeOrganizationId].length === 0 ? (
              <div className="text-center py-10 px-6 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl border-2 border-dashed border-blue-200">
                <div className="max-w-md mx-auto">
                  {/* Ilustración con iconos */}
                  <div className="flex justify-center mb-6">
                    <div className="relative">
                      <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-4xl">👥</span>
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <span className="text-lg">❓</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Texto principal */}
                  <h4 className="text-xl font-bold text-blue-900 mb-3">
                    ¡Tu organización está esperando!
                  </h4>
                  <p className="text-blue-700 mb-6 leading-relaxed">
                    Parece que aún no hay miembros en esta organización. Invita a tu equipo para empezar a colaborar y compartir estudios.
                  </p>
                  
                  {/* Pasos a seguir */}
                  <div className="bg-white rounded-lg p-4 mb-6 border border-blue-200">
                    <h5 className="text-sm font-semibold text-blue-900 mb-3">¿Cómo invitar miembros?</h5>
                    <div className="space-y-2 text-left">
                      <div className="flex items-center text-sm text-blue-700">
                        <span className="mr-2">1️⃣</span>
                        Comparte el código de invitación con tu equipo
                      </div>
                      <div className="flex items-center text-sm text-blue-700">
                        <span className="mr-2">2️⃣</span>
                        Ellos pueden usar el código en "Unirse a organización"
                      </div>
                      <div className="flex items-center text-sm text-blue-700">
                        <span className="mr-2">3️⃣</span>
                        Aprueba sus solicitudes desde las notificaciones
                      </div>
                    </div>
                  </div>
                  
                  {/* Código de invitación */}
                  {organizations.find(org => org.id === activeOrganizationId) && (
                    <div className="bg-blue-100 rounded-lg p-4 border border-blue-200">
                      <p className="text-sm font-medium text-blue-900 mb-2">Código de invitación:</p>
                      <div className="flex items-center justify-center space-x-2">
                        <code className="px-3 py-2 bg-white rounded border text-blue-900 font-mono">
                          {organizations.find(org => org.id === activeOrganizationId)?.invite_code}
                        </code>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-blue-300 text-blue-700 hover:bg-blue-50"
                          onClick={() => {
                            const inviteCode = organizations.find(org => org.id === activeOrganizationId)?.invite_code;
                            if (inviteCode) {
                              navigator.clipboard.writeText(inviteCode);
                              toast({
                                description: 'Código copiado al portapapeles',
                              });
                            }
                          }}
                        >
                          <span className="mr-1">📋</span>
                          Copiar
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {members[activeOrganizationId] && members[activeOrganizationId].map((member: OrganizationMember) => (
                  <MemberCard
                    key={member.user_id}
                    member={member}
                    onRemove={() => handleRemoveMember(member.user_id)}
                    onPromote={() => handlePromoteMember(member.user_id)}
                    onDemote={() => handleDemoteMember(member.user_id)}
                    canManage={isAdminOrOwner}
                    isCurrentUser={member.user_id === user?.id}
                    className="h-full"
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
