import React from 'react';
import { LoginForm } from '../components/LoginForm';
import { useAuthStore } from '../store/authStore';
import { Navigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export const LoginPage: React.FC = () => {
  const { t } = useTranslation('login');
  const user = useAuthStore(state => state.user);

  if (user) {
    return <Navigate to="/" />;
  }

  return <LoginForm />;
};
