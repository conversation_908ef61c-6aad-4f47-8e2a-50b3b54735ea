-- Drop existing policies
drop policy if exists "Organization admins can manage members" on organization_members;
drop policy if exists "Users can view members of their organizations" on organization_members;

-- Create new policies
create policy "Users can view their own memberships"
    on organization_members for select
    to authenticated
    using (user_id = auth.uid());

create policy "Organization admins can manage members"
    on organization_members for all
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organization_members.organization_id
            and user_id = auth.uid()
            and role in ('owner', 'admin')
        )
        and user_id != auth.uid() -- No pueden modificar su propia membresía
    );

-- Política específica para que los owners puedan modificar cualquier membresía
create policy "Organization owners can manage all members"
    on organization_members for all
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organization_members.organization_id
            and user_id = auth.uid()
            and role = 'owner'
        )
    );

-- Política para permitir la inserción de nuevos miembros
create policy "Allow member creation through join request process"
    on organization_members for insert
    to authenticated
    with check (
        exists (
            select 1 from organization_join_requests
            where organization_id = organization_members.organization_id
            and user_id = organization_members.user_id
            and status = 'approved'
        )
        or (
            -- Permitir la creación del owner inicial
            not exists (
                select 1 from organization_members
                where organization_id = organization_members.organization_id
            )
            and exists (
                select 1 from organizations
                where id = organization_members.organization_id
                and created_by = auth.uid()
            )
        )
    );
