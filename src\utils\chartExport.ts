import { Chart } from 'chart.js';

export interface ChartImageData {
  title: string;
  imageData: string;
  width: number;
  height: number;
}

export interface ChartExportData {
  machineTypes?: ChartImageData;
  saturation?: ChartImageData;
  elements?: ChartImageData;
  supplements?: ChartImageData;
  cycleDistribution?: ChartImageData;
  // New flowchart symbol charts
  flowchartSymbols?: ChartImageData;
  operationPercentages?: ChartImageData;
  operationCount?: ChartImageData;
  summary?: {
    totalElements: number;
    optimalSaturation: number;
    normalSaturation: number;
    maxActivity: number;
  };
}

/**
 * Captura una gráfica de Chart.js como imagen base64
 */
export const captureChartAsImage = (chartId: string, title: string): Promise<ChartImageData | null> => {
  return new Promise((resolve) => {
    try {
      const canvas = document.querySelector(`#${chartId} canvas`) as HTMLCanvasElement;
      if (!canvas) {
        console.warn(`No se encontró el canvas para la gráfica: ${chartId}`);
        resolve(null);
        return;
      }

      // Crear un canvas temporal con fondo blanco
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      
      if (!tempCtx) {
        resolve(null);
        return;
      }

      tempCanvas.width = canvas.width;
      tempCanvas.height = canvas.height;

      // Llenar con fondo blanco
      tempCtx.fillStyle = '#ffffff';
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Dibujar la gráfica encima
      tempCtx.drawImage(canvas, 0, 0);

      const imageData = tempCanvas.toDataURL('image/png');
      
      resolve({
        title,
        imageData,
        width: canvas.width,
        height: canvas.height
      });
    } catch (error) {
      console.error(`Error capturando gráfica ${chartId}:`, error);
      resolve(null);
    }
  });
};

/**
 * Captura todas las gráficas del análisis visual
 */
export const captureAllCharts = async (
  stats: any,
  elements: any[],
  t: any
): Promise<ChartExportData> => {
  const chartData: ChartExportData = {};

  // Verificar si hay datos para mostrar gráficas
  const hasMachineData = stats.machineStoppedTime > 0 || stats.machineRunningTime > 0 || stats.machineTime > 0;
  const hasElementData = elements.length > 0;

  if (!hasElementData) {
    return chartData;
  }

  try {
    // Esperar un poco para asegurar que las gráficas estén renderizadas
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Capturar gráfica de tipos de máquina
    if (hasMachineData) {
      const machineTypesChart = await captureChartAsImage(
        'machine-types-chart',
        t('charts.machineTypes.title', { defaultValue: 'Distribución por Tipo de Máquina' })
      );
      if (machineTypesChart) {
        chartData.machineTypes = machineTypesChart;
      }
    }

    // Capturar gráfica de saturación
    const saturationChart = await captureChartAsImage(
      'saturation-chart',
      t('charts.saturation.title', { defaultValue: 'Saturación/Rendimiento Normal' })
    );
    if (saturationChart) {
      chartData.saturation = saturationChart;
    }

    // Capturar gráfica de elementos
    const elementsChart = await captureChartAsImage(
      'elements-chart',
      t('charts.elements.title', { defaultValue: 'Tiempos por Elemento' })
    );
    if (elementsChart) {
      chartData.elements = elementsChart;
    }

    // Capturar gráfica de suplementos
    const supplementsChart = await captureChartAsImage(
      'supplements-chart',
      t('charts.supplements.title', { defaultValue: 'Suplementos por Elemento' })
    );
    if (supplementsChart) {
      chartData.supplements = supplementsChart;
    }

    // Activity chart removed as requested

    // Capturar gráfica de distribución del ciclo
    const cycleDistributionChart = await captureChartAsImage(
      'cycle-distribution-chart',
      t('charts.cycleDistribution.title', { defaultValue: 'Distribución del Tiempo de Ciclo' })
    );
    if (cycleDistributionChart) {
      chartData.cycleDistribution = cycleDistributionChart;
    }

    // Capturar nuevas gráficas de símbolos de diagrama de flujo
    const flowchartSymbolsChart = await captureChartAsImage(
      'flowchart-symbols-chart',
      t('charts.flowchartSymbols.distribution', { defaultValue: 'Distribución por Tipo de Operación' })
    );
    if (flowchartSymbolsChart) {
      chartData.flowchartSymbols = flowchartSymbolsChart;
    }

    const operationPercentagesChart = await captureChartAsImage(
      'operation-percentages-chart',
      t('charts.flowchartSymbols.percentages', { defaultValue: 'Porcentajes por Tipo de Operación' })
    );
    if (operationPercentagesChart) {
      chartData.operationPercentages = operationPercentagesChart;
    }

    const operationCountChart = await captureChartAsImage(
      'operation-count-chart',
      t('charts.flowchartSymbols.countTitle', { defaultValue: 'Cantidad de Operaciones por Tipo' })
    );
    if (operationCountChart) {
      chartData.operationCount = operationCountChart;
    }

    // Agregar datos del resumen estadístico
    chartData.summary = {
      totalElements: elements.filter(e => !e.concurrent_machine_time).length,
      optimalSaturation: stats.optimalSaturation || 0,
      normalSaturation: stats.normalSaturation || 0,
      maxActivity: stats.maxActivity || 0
    };

  } catch (error) {
    console.error('Error capturando gráficas:', error);
  }

  return chartData;
};

/**
 * Genera IDs únicos para las gráficas
 */
export const generateChartIds = () => {
  const timestamp = Date.now();
  return {
    machineTypes: `machine-types-chart-${timestamp}`,
    saturation: `saturation-chart-${timestamp}`,
    elements: `elements-chart-${timestamp}`,
    supplements: `supplements-chart-${timestamp}`,
    cycleDistribution: `cycle-distribution-chart-${timestamp}`,
    // New flowchart symbol charts
    flowchartSymbols: `flowchart-symbols-chart-${timestamp}`,
    operationPercentages: `operation-percentages-chart-${timestamp}`,
    operationCount: `operation-count-chart-${timestamp}`
  };
};