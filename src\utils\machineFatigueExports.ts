import jsPDF from 'jspdf';
import { Workbook } from 'exceljs';
import { supabase } from '../lib/supabase';
import { formatNumberForExcel, formatPercentageForExcel, formatNumberAsString } from './numberFormat';

interface CalculationResults {
  totalCycleTime: number;
  baseTime: number;
  personalNeedsSupplement: number;
  fatigueSupplement: number;
  remainingFatigue?: number;
  explanation: {
    supplementsExplanation: string;
    baseTimeCalculation: string;
    personalNeedsCalculation: string;
    fatigueCalculation: string;
    totalTimeCalculation: string;
    remainingFatigueCalculation?: string;
  };
}

interface ExportData {
  results: CalculationResults;
  machineStopTime: number;
  machineRunTime: number;
  machineTime: number;
  inactivityTime: number;
  personalNeedsPercentage: number;
  fatiguePercentage: number;
  calculationCase: string;
  fatigueExplanationDetails?: string;
  studyName?: string;
  timestamp?: string;
}

export const exportToPDF = (data: ExportData) => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 20;
  let yPosition = margin;

  // Helper functions
  const checkNewPage = (requiredSpace: number = 30) => {
    if (yPosition + requiredSpace > pageHeight - margin - 20) {
      doc.addPage();
      yPosition = margin;
      return true;
    }
    return false;
  };

  const addText = (text: string, fontSize: number = 12, isBold: boolean = false, color: number[] = [0, 0, 0]) => {
    doc.setFontSize(fontSize);
    doc.setFont('helvetica', isBold ? 'bold' : 'normal');
    doc.setTextColor(color[0], color[1], color[2]);

    const lines = doc.splitTextToSize(text, pageWidth - 2 * margin);
    lines.forEach((line: string) => {
      checkNewPage();
      doc.text(line, margin, yPosition);
      yPosition += fontSize * 0.6;
    });
    yPosition += 5;
  };

  const addSectionTitle = (title: string, color: number[] = [70, 130, 180]) => {
    checkNewPage(25);
    yPosition += 5;

    // Add background rectangle
    doc.setFillColor(color[0], color[1], color[2]);
    doc.rect(margin, yPosition - 5, pageWidth - 2 * margin, 15, 'F');

    // Add title text
    doc.setTextColor(255, 255, 255);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text(title, margin + 5, yPosition + 5);

    yPosition += 20;
  };

  const addValueRow = (label: string, value: string, unit: string = '') => {
    checkNewPage(20);

    // Add border
    doc.setDrawColor(200, 200, 200);
    doc.setLineWidth(0.5);
    doc.rect(margin, yPosition, pageWidth - 2 * margin, 15);

    // Add light background
    doc.setFillColor(248, 249, 250);
    doc.rect(margin + 0.5, yPosition + 0.5, pageWidth - 2 * margin - 1, 14, 'F');

    // Add label
    doc.setTextColor(60, 60, 60);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    doc.text(label, margin + 5, yPosition + 10);

    // Add value
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(11);
    doc.setTextColor(20, 20, 20);
    const valueText = `${value} ${unit}`;
    const valueWidth = doc.getTextWidth(valueText);
    doc.text(valueText, pageWidth - margin - valueWidth - 5, yPosition + 10);

    yPosition += 16;
  };

  const addSeparator = () => {
    checkNewPage(10);
    yPosition += 5;
    doc.setDrawColor(180, 180, 180);
    doc.setLineWidth(1);
    doc.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;
  };

  // Helper function to clean text for PDF
  const cleanTextForPDF = (text: string) => {
    return text
      // Remove HTML elements
      .replace(/<hr>/g, '')
      .replace(/<\/?\w+>/g, '') // Remove any other HTML tags
      .replace(/▸/g, '• ')

      // Handle percentage symbols that are used as decorative elements
      .replace(/%{4,}/g, '────────────') // Replace sequences of % with lines
      .replace(/%%+/g, '') // Remove double or more %

      // Clean up special characters
      .replace(/¸/g, '') // Remove cedilla characters
      .replace(/[^\x00-\x7F]/g, function(char) {
        // Replace non-ASCII characters with safe alternatives
        const charCode = char.charCodeAt(0);
        if (charCode === 8226) return '• '; // Bullet point
        if (charCode === 8211 || charCode === 8212) return '-'; // En/Em dash
        if (charCode === 8220 || charCode === 8221) return '"'; // Smart quotes
        if (charCode === 8216 || charCode === 8217) return "'"; // Smart apostrophes
        return ''; // Remove other problematic characters
      })

      // Clean up malformed bullet points and colons
      .replace(/•\s*%+\s*/g, '• ')
      .replace(/:\s*%+\s*/g, ': ')
      .replace(/\s+%+\s+/g, ' ')
      .replace(/^%+\s*/gm, '') // Remove % at start of lines
      .replace(/\s*%+$/gm, '') // Remove % at end of lines
      .replace(/\s%\s/g, ' ') // Remove isolated % between spaces

      // Preserve valid percentages (number followed by %)
      .replace(/(\d+(?:\.\d+)?)\s*%/g, '$1%')

      // Clean up extra whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove excessive line breaks
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
      .trim();
  };

  // Main Title
  addText('CÁLCULO DE FATIGA EN MÁQUINAS', 18, true, [70, 130, 180]);
  addSeparator();

  // Study Information
  addText(`Estudio: ${data.studyName || 'Sin nombre'}`, 12, true);
  addText(`Fecha: ${data.timestamp || new Date().toLocaleString()}`, 10);
  addSeparator();

  // Case Information
  const getCaseTitle = (caseId: string) => {
    switch (caseId) {
      case 'case1': return 'Caso 1: Ambos suplementos fuera del ciclo';
      case 'case2': return 'Caso 2: Fatiga parcialmente dentro del ciclo';
      case 'case3': return 'Caso 3: Solo necesidades personales fuera del ciclo';
      case 'case4': return 'Caso 4: Ambos suplementos dentro del ciclo';
      default: return 'Caso no determinado';
    }
  };

  addSectionTitle('CASO APLICADO', [220, 53, 69]);
  addText(getCaseTitle(data.calculationCase), 11, true);
  addValueRow('Tiempo de inactividad', formatNumberAsString(data.inactivityTime, { decimals: 2 }), 'segundos');
  addSeparator();

  // Parameters Section
  addSectionTitle('PARÁMETROS DE ENTRADA', [139, 92, 246]);
  addValueRow('Tiempo máquina parada', formatNumberAsString(data.machineStopTime, { decimals: 2 }), 'seg');
  addValueRow('Tiempo máquina en marcha', formatNumberAsString(data.machineRunTime, { decimals: 2 }), 'seg');
  addValueRow('Tiempo de máquina', formatNumberAsString(data.machineTime, { decimals: 2 }), 'seg');
  addValueRow('Necesidades personales', data.personalNeedsPercentage.toString(), '%');
  addValueRow('Fatiga', data.fatiguePercentage.toString(), '%');
  addSeparator();

  // Results Summary
  addSectionTitle('RESULTADOS DEL CÁLCULO', [16, 185, 129]);
  addValueRow('Tiempo base', formatNumberAsString(data.results.baseTime, { decimals: 2 }), 'seg');
  addValueRow('Suplemento por necesidades personales', formatNumberAsString(data.results.personalNeedsSupplement, { decimals: 2 }), 'seg');
  addValueRow('Suplemento por fatiga', formatNumberAsString(data.results.fatigueSupplement, { decimals: 2 }), 'seg');

  if (data.results.remainingFatigue !== undefined) {
    addValueRow('Fatiga restante', formatNumberAsString(data.results.remainingFatigue, { decimals: 2 }), 'seg');
  }

  // Final Result - Highlighted
  checkNewPage(30);
  yPosition += 10;

  // Draw special box for final result
  doc.setFillColor(70, 130, 180);
  doc.rect(margin, yPosition, pageWidth - 2 * margin, 25, 'F');

  // Add border
  doc.setDrawColor(50, 50, 50);
  doc.setLineWidth(1);
  doc.rect(margin, yPosition, pageWidth - 2 * margin, 25);

  // Add final result text
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(14);
  doc.text('CICLO NORMAL TOTAL:', margin + 10, yPosition + 12);

  doc.setFontSize(16);
  const finalValue = `${formatNumberAsString(data.results.totalCycleTime, { decimals: 2 })} segundos`;
  const finalValueWidth = doc.getTextWidth(finalValue);
  doc.text(finalValue, pageWidth - margin - finalValueWidth - 10, yPosition + 18);

  yPosition += 35;
  addSeparator();

  // Detailed Calculations Section
  addSectionTitle('CÁLCULOS DETALLADOS', [55, 65, 81]);

  // Base Time Calculation
  addText('Cálculo del Tiempo Base:', 12, true, [70, 130, 180]);
  addText(cleanTextForPDF(data.results.explanation.baseTimeCalculation), 10);
  addSeparator();

  // Personal Needs Calculation
  addText('Cálculo de Necesidades Personales:', 12, true, [139, 92, 246]);
  addText(cleanTextForPDF(data.results.explanation.personalNeedsCalculation), 10);
  addSeparator();

  // Fatigue Calculation
  addText('Cálculo de Fatiga:', 12, true, [245, 158, 11]);
  addText(cleanTextForPDF(data.results.explanation.fatigueCalculation), 10);
  addSeparator();

  // Total Time Calculation
  addText('Cálculo del Tiempo Total:', 12, true, [16, 185, 129]);
  addText(cleanTextForPDF(data.results.explanation.totalTimeCalculation), 10);

  if (data.results.explanation.remainingFatigueCalculation) {
    addSeparator();
    addText('Cálculo de Fatiga Restante:', 12, true, [220, 53, 69]);
    addText(cleanTextForPDF(data.results.explanation.remainingFatigueCalculation), 10);
  }

  // Add fatigue detailed explanation if available
  if (data.fatigueExplanationDetails) {
    addSeparator();
    addSectionTitle('DETALLES DEL CÁLCULO DE FATIGA PONDERADA', [245, 158, 11]);

    // Clean the fatigue details using the same function
    const cleanedDetails = cleanTextForPDF(data.fatigueExplanationDetails)
      .replace(/<hr>/g, '\n' + '─'.repeat(60) + '\n'); // Add separators for <hr>

    // Split into sections for better formatting
    const sections = cleanedDetails.split(/(?=\d+\.\s+[A-Z])/);

    sections.forEach((section, index) => {
      if (section.trim()) {
        if (index > 0) addSeparator();
        addText(section.trim(), 9);
      }
    });
  }

  // Save the PDF
  const fileName = `calculo_fatiga_${data.studyName?.replace(/[^a-zA-Z0-9]/g, '_') || 'estudio'}_${new Date().toISOString().split('T')[0]}.pdf`;
  doc.save(fileName);
};

export const exportToExcel = async (data: ExportData) => {
  // Create workbook
  const workbook = new Workbook();
  workbook.creator = 'Cronometras';
  workbook.lastModifiedBy = 'Cronometras';
  workbook.created = new Date();
  workbook.modified = new Date();

  // Summary Sheet
  const summarySheet = workbook.addWorksheet('Resumen');

  // Add title
  const titleRow = summarySheet.addRow(['CÁLCULO DE FATIGA EN MÁQUINAS']);
  titleRow.getCell(1).font = { bold: true, size: 16 };
  titleRow.getCell(1).alignment = { horizontal: 'center' };
  summarySheet.mergeCells('A1:B1');

  summarySheet.addRow([]);

  // Study Information
  summarySheet.addRow(['Información del Estudio']).getCell(1).font = { bold: true, size: 14 };
  summarySheet.addRow(['Nombre del estudio', data.studyName || 'Sin nombre']);
  summarySheet.addRow(['Fecha de cálculo', data.timestamp || new Date().toLocaleString()]);
  summarySheet.addRow([]);

  // Case Applied
  summarySheet.addRow(['Caso Aplicado']).getCell(1).font = { bold: true, size: 14 };
  summarySheet.addRow(['Tipo de caso', getCaseTitle(data.calculationCase)]);
  
  // Agregar tiempo de inactividad como número con formato
  const inactivityRow = summarySheet.addRow(['Tiempo de inactividad (seg)', data.inactivityTime]);
  const inactivityCell = inactivityRow.getCell(2);
  const inactivityFormat = formatNumberForExcel(data.inactivityTime, { decimals: 2 });
  inactivityCell.numFmt = inactivityFormat.format;
  
  summarySheet.addRow([]);

  // Input Parameters
  summarySheet.addRow(['Parámetros de Entrada']).getCell(1).font = { bold: true, size: 14 };
  
  // Agregar parámetros como números con formato
  const machineStopRow = summarySheet.addRow(['Tiempo máquina parada (seg)', data.machineStopTime]);
  const machineStopCell = machineStopRow.getCell(2);
  const machineStopFormat = formatNumberForExcel(data.machineStopTime, { decimals: 2 });
  machineStopCell.numFmt = machineStopFormat.format;
  
  const machineRunRow = summarySheet.addRow(['Tiempo máquina en marcha (seg)', data.machineRunTime]);
  const machineRunCell = machineRunRow.getCell(2);
  const machineRunFormat = formatNumberForExcel(data.machineRunTime, { decimals: 2 });
  machineRunCell.numFmt = machineRunFormat.format;
  
  const machineTimeRow = summarySheet.addRow(['Tiempo de máquina (seg)', data.machineTime]);
  const machineTimeCell = machineTimeRow.getCell(2);
  const machineTimeFormat = formatNumberForExcel(data.machineTime, { decimals: 2 });
  machineTimeCell.numFmt = machineTimeFormat.format;
  
  summarySheet.addRow(['Necesidades personales (%)', data.personalNeedsPercentage]);
  summarySheet.addRow(['Fatiga (%)', data.fatiguePercentage]);
  summarySheet.addRow([]);

  // Results
  summarySheet.addRow(['Resultados']).getCell(1).font = { bold: true, size: 14 };
  
  // Agregar resultados como números con formato
  const baseTimeRow = summarySheet.addRow(['Tiempo base (seg)', data.results.baseTime]);
  const baseTimeCell = baseTimeRow.getCell(2);
  const baseTimeFormat = formatNumberForExcel(data.results.baseTime, { decimals: 2 });
  baseTimeCell.numFmt = baseTimeFormat.format;
  
  const personalNeedsRow = summarySheet.addRow(['Suplemento NP (seg)', data.results.personalNeedsSupplement]);
  const personalNeedsCell = personalNeedsRow.getCell(2);
  const personalNeedsFormat = formatNumberForExcel(data.results.personalNeedsSupplement, { decimals: 2 });
  personalNeedsCell.numFmt = personalNeedsFormat.format;
  
  const fatigueRow = summarySheet.addRow(['Suplemento fatiga (seg)', data.results.fatigueSupplement]);
  const fatigueCell = fatigueRow.getCell(2);
  const fatigueFormat = formatNumberForExcel(data.results.fatigueSupplement, { decimals: 2 });
  fatigueCell.numFmt = fatigueFormat.format;

  if (data.results.remainingFatigue !== undefined) {
    const remainingFatigueRow = summarySheet.addRow(['Fatiga restante (seg)', data.results.remainingFatigue]);
    const remainingFatigueCell = remainingFatigueRow.getCell(2);
    const remainingFatigueFormat = formatNumberForExcel(data.results.remainingFatigue, { decimals: 2 });
    remainingFatigueCell.numFmt = remainingFatigueFormat.format;
  }

  summarySheet.addRow([]);
  const totalRow = summarySheet.addRow(['CICLO NORMAL TOTAL (seg)', data.results.totalCycleTime]);
  totalRow.getCell(1).font = { bold: true };
  totalRow.getCell(2).font = { bold: true };
  const totalCell = totalRow.getCell(2);
  const totalFormat = formatNumberForExcel(data.results.totalCycleTime, { decimals: 2 });
  totalCell.numFmt = totalFormat.format;

  // Set column widths
  summarySheet.getColumn(1).width = 30;
  summarySheet.getColumn(2).width = 20;

  // Detailed Calculations Sheet
  const detailsSheet = workbook.addWorksheet('Detalles');

  detailsSheet.addRow(['CÁLCULOS DETALLADOS']).getCell(1).font = { bold: true, size: 16 };
  detailsSheet.addRow([]);

  // Add each explanation section
  const explanationSections = [
    { title: 'Distribución de Suplementos', content: data.results.explanation.supplementsExplanation },
    { title: 'Cálculo del Tiempo Base', content: data.results.explanation.baseTimeCalculation },
    { title: 'Cálculo de Necesidades Personales', content: data.results.explanation.personalNeedsCalculation },
    { title: 'Cálculo de Fatiga', content: data.results.explanation.fatigueCalculation },
    { title: 'Cálculo del Tiempo Total', content: data.results.explanation.totalTimeCalculation }
  ];

  if (data.results.explanation.remainingFatigueCalculation) {
    explanationSections.push({
      title: 'Cálculo de Fatiga Restante',
      content: data.results.explanation.remainingFatigueCalculation
    });
  }

  explanationSections.forEach(section => {
    detailsSheet.addRow([section.title]).getCell(1).font = { bold: true, size: 12 };
    const contentLines = section.content.split('\n');
    contentLines.forEach(line => {
      if (line.trim()) {
        detailsSheet.addRow([line.trim()]);
      }
    });
    detailsSheet.addRow([]);
  });

  detailsSheet.getColumn(1).width = 100;

  // Parameters Comparison Sheet
  const comparisonSheet = workbook.addWorksheet('Comparación');

  comparisonSheet.addRow(['COMPARACIÓN DE PARÁMETROS Y RESULTADOS']).getCell(1).font = { bold: true, size: 16 };
  comparisonSheet.mergeCells('A1:D1');
  comparisonSheet.addRow([]);

  // Header row
  const headerRow = comparisonSheet.addRow(['Parámetro', 'Valor', 'Unidad', 'Descripción']);
  headerRow.eachCell(cell => {
    cell.font = { bold: true };
    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };
  });

  // Data rows
  const comparisonData = [
    ['Tiempo máquina parada', formatNumberForExcel(data.machineStopTime, { decimals: 2 }), 'segundos', 'Tiempo total de elementos de máquina parada'],
    ['Tiempo máquina en marcha', formatNumberForExcel(data.machineRunTime, { decimals: 2 }), 'segundos', 'Tiempo total de elementos de máquina en marcha'],
    ['Tiempo de máquina', formatNumberForExcel(data.machineTime, { decimals: 2 }), 'segundos', 'Tiempo total de elementos de tiempo de máquina'],
    ['Tiempo de inactividad', formatNumberForExcel(data.inactivityTime, { decimals: 2 }), 'segundos', 'Diferencia entre tiempo de máquina y tiempo en marcha'],
    ['Necesidades personales', data.personalNeedsPercentage.toString(), '%', 'Porcentaje aplicado para necesidades personales'],
    ['Fatiga', data.fatiguePercentage.toString(), '%', 'Porcentaje calculado de fatiga ponderada'],
    ['', '', '', ''],
    ['Resultados Calculados', '', '', ''],
    ['Tiempo base', formatNumberForExcel(data.results.baseTime, { decimals: 2 }), 'segundos', 'Tiempo máquina parada + máximo(tiempo máquina, tiempo en marcha)'],
    ['Suplemento NP', formatNumberForExcel(data.results.personalNeedsSupplement, { decimals: 2 }), 'segundos', 'Tiempo base × porcentaje de necesidades personales'],
    ['Suplemento fatiga', formatNumberForExcel(data.results.fatigueSupplement, { decimals: 2 }), 'segundos', 'Tiempo base × porcentaje de fatiga']
  ];

  if (data.results.remainingFatigue !== undefined) {
    comparisonData.push(['Fatiga restante', formatNumberForExcel(data.results.remainingFatigue, { decimals: 2 }), 'segundos', 'Fatiga no cubierta por tiempo de inactividad']);
  }

  comparisonData.push(['Ciclo normal total', formatNumberForExcel(data.results.totalCycleTime, { decimals: 2 }), 'segundos', 'Resultado final del cálculo']);

  comparisonData.forEach(row => {
    const addedRow = comparisonSheet.addRow(row);
    if (row[0] === 'Resultados Calculados') {
      addedRow.getCell(1).font = { bold: true };
    }
    if (row[0] === 'Ciclo normal total') {
      addedRow.eachCell(cell => {
        cell.font = { bold: true };
      });
    }
  });

  // Set column widths
  comparisonSheet.getColumn(1).width = 25;
  comparisonSheet.getColumn(2).width = 15;
  comparisonSheet.getColumn(3).width = 12;
  comparisonSheet.getColumn(4).width = 50;

  // Fatigue Details Sheet (if available)
  if (data.fatigueExplanationDetails) {
    const fatigueSheet = workbook.addWorksheet('Fatiga Ponderada');

    fatigueSheet.addRow(['DETALLES DEL CÁLCULO DE FATIGA PONDERADA']).getCell(1).font = { bold: true, size: 16 };
    fatigueSheet.mergeCells('A1:A1');
    fatigueSheet.addRow([]);

    // Clean up the fatigue details for Excel (same cleaning as PDF)
    let cleanedDetails = data.fatigueExplanationDetails
      // Remove HTML elements
      .replace(/<hr>/g, '')
      .replace(/<\/?\w+>/g, '') // Remove any other HTML tags
      .replace(/▸/g, '• ')

      // Handle percentage symbols that are used as decorative elements
      .replace(/%{10,}/g, '────────────────────────────────────') // Replace long sequences of % with lines
      .replace(/%{4,9}/g, '────────────') // Replace medium sequences with shorter lines
      .replace(/%%+/g, '') // Remove double or more %

      // Clean up special characters
      .replace(/¸/g, '') // Remove cedilla characters
      .replace(/[^\x00-\x7F]/g, function(char) {
        // Replace non-ASCII characters with safe alternatives
        const charCode = char.charCodeAt(0);
        if (charCode === 8226) return '• '; // Bullet point
        if (charCode === 8211 || charCode === 8212) return '-'; // En/Em dash
        if (charCode === 8220 || charCode === 8221) return '"'; // Smart quotes
        if (charCode === 8216 || charCode === 8217) return "'"; // Smart apostrophes
        return ''; // Remove other problematic characters
      })

      // Clean up malformed bullet points and colons
      .replace(/•\s*%+\s*/g, '• ')
      .replace(/:\s*%+\s*/g, ': ')
      .replace(/\s+%+\s+/g, ' ')
      .replace(/^%+\s*/gm, '') // Remove % at start of lines
      .replace(/\s*%+$/gm, '') // Remove % at end of lines

      // Preserve valid percentages (number followed by %)
      .replace(/(\d+(?:\.\d+)?)\s*%/g, '$1%')

      // Clean up extra whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove excessive line breaks
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
      .trim();

    // Split the cleaned text into lines and add them
    const fatigueLines = cleanedDetails.split('\n');
    fatigueLines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine) {
        const row = fatigueSheet.addRow([trimmedLine]);

        // Make section headers bold
        if (trimmedLine.match(/^\d+\.\s+[A-Z]/) || // Numbered sections
            trimmedLine.includes('CÁLCULO') ||
            trimmedLine.includes('DATOS DE ENTRADA') ||
            trimmedLine.includes('NORMALIZACIÓN') ||
            trimmedLine.includes('INFORMACIÓN TEÓRICA') ||
            trimmedLine.includes('ELEMENTOS ANALIZADOS')) {
          row.getCell(1).font = { bold: true, size: 12 };
        }

        // Make subsection headers semi-bold
        if (trimmedLine.startsWith('Elemento "') ||
            trimmedLine.includes('Factor de Frecuencia') ||
            trimmedLine.includes('Tiempo Total')) {
          row.getCell(1).font = { bold: true, size: 11 };
        }
      } else {
        fatigueSheet.addRow(['']);
      }
    });

    fatigueSheet.getColumn(1).width = 120;
  }

  // Save the Excel file
  const fileName = `calculo_fatiga_${data.studyName?.replace(/[^a-zA-Z0-9]/g, '_') || 'estudio'}_${new Date().toISOString().split('T')[0]}.xlsx`;
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.click();
  window.URL.revokeObjectURL(url);
};

// Helper function for case titles
const getCaseTitle = (caseId: string) => {
  switch (caseId) {
    case 'case1': return 'Caso 1: Ambos suplementos fuera del ciclo';
    case 'case2': return 'Caso 2: Fatiga parcialmente dentro del ciclo';
    case 'case3': return 'Caso 3: Solo necesidades personales fuera del ciclo';
    case 'case4': return 'Caso 4: Ambos suplementos dentro del ciclo';
    default: return 'Caso no determinado';
  }
};
