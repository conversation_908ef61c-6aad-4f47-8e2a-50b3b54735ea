-- Migración para agregar gestión de empresas/departamentos
-- Archivo: 20250130_add_companies_management.sql

-- Agregar campos para gestión de empresas/departamentos en perfiles de usuario
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS companies_list JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS default_company TEXT;

-- Agregar comentarios explicativos
COMMENT ON COLUMN public.profiles.companies_list IS 'Lista de empresas/departamentos del usuario para selección rápida en estudios';
COMMENT ON COLUMN public.profiles.default_company IS 'Empresa/departamento por defecto para nuevos estudios';

-- <PERSON><PERSON>r índice para mejorar el rendimiento de consultas en companies_list
CREATE INDEX IF NOT EXISTS idx_profiles_default_company ON public.profiles(default_company);

-- Notificación de migración completada
DO $$
BEGIN
    RAISE NOTICE '✅ Migración completada: Gestión de empresas/departamentos agregada a profiles';
    RAISE NOTICE '📝 Nuevos campos: companies_list (JSONB), default_company (TEXT)';
    RAISE NOTICE '🔍 Índice creado: idx_profiles_default_company';
END $$; 