import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';

const supabaseUrl = import.meta.env.VITE_PUBLIC_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase configuration');
}

// Función para normalizar URLs
const normalizeUrl = (url: string) => {
  // Remover barra final si existe
  const baseUrl = url.endsWith('/') ? url.slice(0, -1) : url;
  // Asegurarse de que la URL de desarrollo use HTTPS
  if (import.meta.env.DEV && baseUrl.startsWith('http://')) {
    return baseUrl.replace('http://', 'https://');
  }
  return baseUrl;
};

// Determinar la URL base basada en el entorno
const site_url = normalizeUrl(
  import.meta.env.DEV 
    ? import.meta.env.VITE_DEV_URL
    : import.meta.env.VITE_APP_URL
);

// Extraer el dominio de la URL para las cookies
const getCookieDomain = () => {
  if (import.meta.env.DEV) {
    return 'localhost';
  }
  
  try {
    const url = new URL(import.meta.env.VITE_APP_URL);
    // Agregar punto al inicio para incluir subdominios
    return `.${url.hostname}`;
  } catch (error) {
    console.warn('Error parsing VITE_APP_URL for cookie domain:', error);
    // Fallback a localhost si hay error
    return 'localhost';
  }
};

const cookieDomain = getCookieDomain();

// Crear una única instancia del cliente Supabase
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: window.localStorage,
    storageKey: 'sb-auth',
    redirectTo: `${site_url}/auth/v1/callback`,
    cookieOptions: {
      name: 'sb-auth-token',
      lifetime: 60 * 60 * 24 * 7, // 1 semana
      domain: cookieDomain,
      path: '/',
      sameSite: 'Lax',
      secure: true
    }
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-web',
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  }
});