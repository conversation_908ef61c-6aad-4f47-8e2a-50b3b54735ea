import { Database } from './supabase';

export type Organization = {
  id: string;
  name: string;
  description?: string;
  invite_code: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  userRole?: string; // Rol del usuario actual en la organización
};

export type OrganizationMember = {
  organization_id: string;
  user_id: string;
  user_email: string;
  role: 'owner' | 'admin' | 'member';
  created_at: string;
};

export type OrganizationJoinRequest = {
  id: string;
  organization_id: string;
  user_id: string;
  user_email: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  processed_at?: string;
  processed_by?: string;
};

// Actualizar el tipo Database para incluir las nuevas tablas
declare module './supabase' {
  interface Database {
    public: {
      Tables: {
        // ... otras tablas existentes ...
        organizations: {
          Row: Organization;
          Insert: Omit<Organization, 'id' | 'created_at' | 'updated_at' | 'invite_code'>;
          Update: Partial<Omit<Organization, 'id' | 'created_at' | 'updated_at' | 'invite_code'>>;
        };
        organization_members: {
          Row: OrganizationMember;
          Insert: Omit<OrganizationMember, 'created_at'>;
          Update: Partial<Omit<OrganizationMember, 'created_at'>>;
        };
        organization_join_requests: {
          Row: OrganizationJoinRequest;
          Insert: Omit<OrganizationJoinRequest, 'id' | 'created_at' | 'processed_at' | 'processed_by'>;
          Update: Partial<Omit<OrganizationJoinRequest, 'id' | 'created_at'>>;
        };
      };
    };
  }
}
