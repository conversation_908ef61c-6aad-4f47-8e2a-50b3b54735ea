import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Folder, Palette } from 'lucide-react';
import { Button } from './ui/button';
import { Folder as FolderType, FolderTreeNode } from '../types/index';

interface FolderModalProps {
  isOpen: boolean;
  folder?: FolderType; // undefined for create, defined for edit
  parentFolder?: FolderTreeNode;
  availableFolders: FolderType[];
  onClose: () => void;
  onSave: (folderData: Partial<FolderType>) => Promise<void>;
}

const FOLDER_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green  
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
];

const FOLDER_ICONS = [
  'folder',
  'folder-archive',
  'folder-check',
  'folder-clock',
  'folder-edit',
  'folder-heart',
  'folder-key',
  'folder-lock',
  'folder-star',
  'folder-tree'
];

export const FolderModal: React.FC<FolderModalProps> = ({
  isOpen,
  folder,
  parentFolder,
  availableFolders,
  onClose,
  onSave
}) => {
  const { t } = useTranslation(['common', 'study']);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: 'folder',
    parent_folder_id: parentFolder?.id || ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form when modal opens or folder changes
  useEffect(() => {
    if (isOpen) {
      if (folder) {
        // Edit mode
        setFormData({
          name: folder.name,
          description: folder.description || '',
          color: folder.color,
          icon: folder.icon,
          parent_folder_id: folder.parent_folder_id || ''
        });
      } else {
        // Create mode
        setFormData({
          name: '',
          description: '',
          color: '#3B82F6',
          icon: 'folder',
          parent_folder_id: parentFolder?.id || ''
        });
      }
      setErrors({});
    }
  }, [isOpen, folder, parentFolder]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('common:nameRequired');
    } else if (formData.name.trim().length < 2) {
      newErrors.name = t('common:nameTooShort');
    } else if (formData.name.trim().length > 50) {
      newErrors.name = t('common:nameTooLong');
    }

    // Check for duplicate names in the same parent folder
    // Handle root level correctly: both null/undefined and empty string represent root
    const formParentId = formData.parent_folder_id || null;
    const siblings = availableFolders.filter(f => {
      const folderParentId = f.parent_folder_id || null;
      return folderParentId === formParentId && f.id !== folder?.id;
    });
    
    if (siblings.some(f => f.name.toLowerCase() === formData.name.trim().toLowerCase())) {
      newErrors.name = t('common:nameAlreadyExists');
    }

    if (formData.description && formData.description.length > 200) {
      newErrors.description = t('common:descriptionTooLong');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log('📁 FolderModal: Submitting form', { formData, folder });
    
    if (!validateForm()) {
      console.log('📁 FolderModal: Validation failed', errors);
      return;
    }

    setIsLoading(true);
    try {
      const folderData: Partial<FolderType> = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        parent_folder_id: formData.parent_folder_id || undefined,
        color: formData.color,
        icon: formData.icon,
      };

      // Ensure parent_folder_id can be null
      if (!formData.parent_folder_id) {
        (folderData as any).parent_folder_id = null;
      }

      console.log('📁 FolderModal: Calling onSave with data:', folderData);
      await onSave(folderData);
      console.log('📁 FolderModal: Save successful, closing modal');
      onClose();
    } catch (error) {
      console.error('📁 FolderModal: Error saving folder:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Get folder hierarchy for parent selection
  const buildFolderOptions = (folders: FolderType[], excludeId?: string): Array<{id: string, name: string, path: string}> => {
    const options: Array<{id: string, name: string, path: string}> = [];
    
    const buildPath = (folderId: string): string => {
      const folderChain: string[] = [];
      let currentId: string | undefined = folderId;
      
      while (currentId) {
        const currentFolder = folders.find(f => f.id === currentId);
        if (!currentFolder) break;
        
        folderChain.unshift(currentFolder.name);
        currentId = currentFolder.parent_folder_id;
      }
      
      return folderChain.join(' / ');
    };

    folders
      .filter(f => f.id !== excludeId) // Exclude the folder being edited
      .forEach(folder => {
        // Don't allow selecting descendants when editing
        if (folder && excludeId) {
          let current: FolderType | undefined = folder;
          let isDescendant = false;
          while (current?.parent_folder_id) {
            if (current.parent_folder_id === excludeId) {
              isDescendant = true;
              break;
            }
            current = folders.find(f => f.id === current?.parent_folder_id);
          }
          if (isDescendant) return;
        }

        options.push({
          id: folder.id,
          name: folder.name,
          path: buildPath(folder.id)
        });
      });

    return options.sort((a, b) => a.path.localeCompare(b.path));
  };

  const folderOptions = buildFolderOptions(availableFolders, folder?.id);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[95vh] sm:max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-lg font-semibold text-gray-900">
            {folder ? t('common:editFolder') : t('common:createFolder')}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form - Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-4">
          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common:name')} *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={t('common:folderNamePlaceholder')}
              maxLength={50}
            />
            {errors.name && (
              <p className="text-red-500 text-xs mt-1">{errors.name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common:description')}
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={t('common:folderDescriptionPlaceholder')}
              rows={3}
              maxLength={200}
            />
            {errors.description && (
              <p className="text-red-500 text-xs mt-1">{errors.description}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              {formData.description.length}/200
            </p>
          </div>

          {/* Parent Folder */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common:parentFolder')}
            </label>
            <select
              value={formData.parent_folder_id}
              onChange={(e) => handleInputChange('parent_folder_id', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">{t('common:rootLevel')}</option>
              {folderOptions.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.path}
                </option>
              ))}
            </select>
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('common:color')}
            </label>
            <div className="flex flex-wrap gap-2">
              {FOLDER_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => handleInputChange('color', color)}
                  className={`w-8 h-8 rounded-full border-2 ${
                    formData.color === color ? 'border-gray-400' : 'border-gray-200'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* Preview */}
          <div className="border border-gray-200 rounded-md p-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('common:preview')}
            </label>
            <div className="flex items-center gap-2">
              <Folder 
                className="h-5 w-5" 
                style={{ color: formData.color }} 
              />
              <span className="text-sm font-medium">
                {formData.name || t('common:newFolder')}
              </span>
            </div>
          </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-4 sm:p-6 border-t border-gray-200 flex-shrink-0">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading || !formData.name.trim()}
          >
            {isLoading ? t('common:saving') : (
              folder ? t('common:save') : t('common:create')
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}; 