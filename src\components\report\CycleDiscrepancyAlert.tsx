import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AlertTriangle, Info } from 'lucide-react';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import { TimeUnit } from '../../types/index';
import { useStudyStore } from '../../store/studyStore';

interface CycleDiscrepancyAlertProps {
  cycleDiscrepancy: {
    hasDiscrepancy: boolean;
    difference: number;
    selectedCycleSource: 'report' | 'supplements';
    supplementsCycleTime: number;
    reportCycleTime: number;
  };
  timeUnit: TimeUnit;
  onCycleSourceChange: (source: 'report' | 'supplements') => void;
}

export const CycleDiscrepancyAlert: React.FC<CycleDiscrepancyAlertProps> = ({
  cycleDiscrepancy,
  timeUnit,
  onCycleSourceChange
}) => {
  const { t } = useTranslation(['report']);
  const [selectedSource, setSelectedSource] = useState<'report' | 'supplements'>(
    cycleDiscrepancy.selectedCycleSource
  );

  if (!cycleDiscrepancy.hasDiscrepancy) {
    return null;
  }

  const handleSourceChange = (source: 'report' | 'supplements') => {
    setSelectedSource(source);
    onCycleSourceChange(source);
  };

  const formatValue = (value: number) => value.toFixed(2);

  // Los valores ya vienen convertidos a la unidad del estudio desde reportcalculations.ts
  // No necesitamos hacer conversión adicional
  const supplementsValue = cycleDiscrepancy.supplementsCycleTime;
  const reportValue = cycleDiscrepancy.reportCycleTime;
  const difference = cycleDiscrepancy.difference;

  console.log('📊 VALORES EN ALERT (sin conversión doble):', {
    supplementsValue,
    reportValue,
    difference,
    timeUnit,
    note: 'Valores ya están en la unidad del estudio'
  });

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <div className="flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-sm font-medium text-yellow-800">
              {t('cycleDiscrepancy.title')}
            </h3>
            <Tippy content={
              <div className="p-2 max-w-[300px] text-sm">
                {t('cycleDiscrepancy.tooltip')}
              </div>
            }>
              <div>
                <Info className="h-4 w-4 text-yellow-600 cursor-help" />
              </div>
            </Tippy>
          </div>
          
          <p className="text-sm text-yellow-700 mb-3">
            {t('cycleDiscrepancy.message')}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
            <div className="bg-white rounded p-3 border">
              <div className="text-xs font-medium text-gray-500 mb-1">
                {t('cycleDiscrepancy.reportValue')}
              </div>
              <div className="text-sm font-semibold text-gray-900">
                {formatValue(cycleDiscrepancy.reportCycleTime)} {t(`units.${timeUnit}`)}
              </div>
            </div>
            
            <div className="bg-white rounded p-3 border">
              <div className="text-xs font-medium text-gray-500 mb-1">
                {t('cycleDiscrepancy.supplementsValue')}
              </div>
              <div className="text-sm font-semibold text-gray-900">
                {formatValue(supplementsValue)} {t(`units.${timeUnit}`)}
              </div>
            </div>
            
            <div className="bg-white rounded p-3 border">
              <div className="text-xs font-medium text-gray-500 mb-1">
                {t('cycleDiscrepancy.difference')}
              </div>
              <div className="text-sm font-semibold text-red-600">
                {formatValue(difference)} {t(`units.${timeUnit}`)}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="text-sm font-medium text-yellow-800">
              {t('cycleDiscrepancy.selectSource')}:
            </div>
            
            <div className="space-y-2">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="cycleSource"
                  value="report"
                  checked={selectedSource === 'report'}
                  onChange={() => handleSourceChange('report')}
                  className="text-yellow-600 focus:ring-yellow-500"
                />
                <span className="text-sm text-gray-700">
                  {t('cycleDiscrepancy.useReportCycle')} ({formatValue(cycleDiscrepancy.reportCycleTime)} {t(`units.${timeUnit}`)})
                </span>
              </label>
              
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="cycleSource"
                  value="supplements"
                  checked={selectedSource === 'supplements'}
                  onChange={() => handleSourceChange('supplements')}
                  className="text-yellow-600 focus:ring-yellow-500"
                />
                <span className="text-sm text-gray-700">
                  {t('cycleDiscrepancy.useSupplementsCycle')} ({formatValue(supplementsValue)} {t(`units.${timeUnit}`)})
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
