import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { X, RotateCcw, InfoIcon } from 'lucide-react';
import { useSupplementTablesStore } from '../../store/supplementTablesStore';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../components/ui/tooltip"
import { Dialog } from "../../components/ui/dialog"

interface FactorA5ModalProps {
  onClose: () => void;
  onSelect: (value: number, selectedIndexes: number[]) => void;
  onReset: () => void;
  currentValue?: number;
  currentSelections?: number[];
}

export const FactorA5Modal: React.FC<FactorA5ModalProps> = ({
  onClose,
  onSelect,
  onReset,
  currentValue,
  currentSelections = []
}) => {
  const { t } = useTranslation(['talFactors', 'supplements', 'common']);
  const tables = useSupplementTablesStore(state => state.tables);
  const modalRef = useRef<HTMLDivElement>(null);
  const [selectedItems, setSelectedItems] = useState<number[]>(Array.isArray(currentSelections) ? currentSelections : []);

  const table = tables.A5;
  if (!table || !('puntos' in table)) return null;

  const options = table.puntos.map((points, index) => ({
    description: t(`A5.description.${index}`, { ns: 'talFactors' }),
    points: points
  }));

  const totalPoints = selectedItems.reduce((sum, index) => {
    const points = options[index]?.points;
    return sum + (typeof points === 'number' ? points : 0);
  }, 0);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleItemClick = (index: number) => {
    setSelectedItems(prev => {
      if (!Array.isArray(prev)) return [index];
      return prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index];
    });
  };

  const handleApply = () => {
    onSelect(totalPoints, selectedItems);
    onClose();
  };

  return (
    <Dialog open onOpenChange={() => onClose()}>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div ref={modalRef} className="bg-white rounded-lg w-full max-w-md">
          <div className="p-4 bg-red-500 text-white flex items-center justify-between rounded-t-lg">
            <div className="flex items-center gap-2">
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild onClick={(e) => e.preventDefault()}>
                    <button 
                      className="p-1 hover:bg-red-600 rounded-full transition-colors"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                    >
                      <InfoIcon className="h-5 w-5 text-white" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent 
                    side="top"
                    align="center"
                    className="max-w-sm bg-white text-gray-900 p-3 rounded shadow-lg border border-gray-200"
                    sideOffset={5}
                  >
                    <p className="text-sm whitespace-pre-line leading-relaxed">
                      {t('A5.tooltip', { ns: 'talFactors' })}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <div className="flex items-center gap-1 truncate">
                <h3 className="tracking-tight text-lg font-semibold text-white truncate">
                  {t('A5.title', { ns: 'talFactors' })}
                </h3>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={onReset}
                className="p-2 hover:bg-red-600 rounded-full"
                title={t('reset', { ns: 'supplements' })}
              >
                <RotateCcw className="w-5 h-5" />
              </button>
              <button
                onClick={onClose}
                className="p-2 hover:bg-red-600 rounded-full"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="p-4 max-h-[70vh] overflow-y-auto">
            {options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleItemClick(index)}
                className={`w-full text-left p-3 rounded-lg mb-2 transition-colors ${
                  selectedItems.includes(index)
                    ? 'bg-red-100 hover:bg-red-200'
                    : 'hover:bg-gray-100'
                }`}
              >
                <div className="flex justify-between items-center">
                  <span className="flex-1">{option.description}</span>
                  <span className="text-red-500 font-semibold ml-4">
                    {option.points} {t('points', { ns: 'supplements' })}
                  </span>
                </div>
              </button>
            ))}
          </div>

          <div className="p-4 border-t">
            <div className="flex justify-between items-center">
              <div>
                <div className="text-sm text-gray-600">{t('totalPoints', { ns: 'supplements' })}</div>
                <div className="text-lg font-bold">{totalPoints}</div>
              </div>
              <button
                onClick={handleApply}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                {t('apply', { ns: 'common' })}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
}