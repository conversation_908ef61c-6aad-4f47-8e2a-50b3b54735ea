# Documentación: Interfaz de Suplementos

## Navegación Principal

### Acceso a la Pantalla
1. Seleccionar un estudio activo
2. Navegar a la pantalla de "Suplementos"
3. Elegir tipo de tabla (OIT o TAL)
4. Seleccionar elemento específico
5. Acceder a la pestaña "Cálculo de fatiga"

### Permisos
- **Propietario del estudio**: Acceso completo de edición
- **Usuario compartido**: Solo visualización (mensaje de alerta amarillo)

## Pestañas del Sistema

### 🔵 Pestaña Resumen

**Propósito**: Vista general de los resultados calculados

**Información mostrada**:
- **Tiempo Base**: Tiempo sin suplementos aplicados
- **Suplemento por Necesidades Personales**: Valor calculado y aplicado
- **Suplemento por Fatiga**: Valor calculado y aplicado  
- **Ciclo Normal Total**: Resultado final

**Características**:
- Valores en tiempo real
- Tarjetas con colores diferenciados
- Tooltips explicativos
- Resultado destacado en azul

**Caso aplicado**: Se muestra el icono y descripción del caso detectado automáticamente

### 📊 Pestaña Detalles

**Propósito**: Explicación paso a paso de todos los cálculos

**Secciones principales**:

1. **Distribución de Suplementos**
   - Cálculo detallado por elemento
   - Separación de NP y fatiga
   - Resumen por tipos de elementos
   - Tiempo de espera calculado

2. **Cálculo del Tiempo Base**
   - Fórmula aplicada: MP + MAX(MM, TM)
   - Valores de cada componente
   - Ajustes por caso específico

3. **Necesidades Personales**
   - Aplicación del 5% fijo
   - Total calculado vs aplicado
   - Estado de absorción

4. **Cálculo de Fatiga**
   - Porcentaje restante después de NP
   - Total calculado vs aplicado
   - Estado de absorción

5. **Tiempo Total del Ciclo**
   - Resultado final
   - Explicación de ajustes

6. **Cálculo Completo de Fatiga Ponderada** (expandible)
   - Proceso paso a paso
   - Datos de entrada por elemento
   - Normalización de tiempos
   - Cálculo de pesos
   - Suplemento ponderado
   - Aplicación sobre elementos activos

**Formato**: Texto monoespacio con resaltado de colores para mejor legibilidad

### ⚙️ Pestaña Parámetros

**Propósito**: Mostrar todos los parámetros y valores calculados

**Secciones**:

1. **Parámetros de Tiempo**
   - Tiempo Máquina Parada
   - Tiempo Máquina en Marcha
   - Tiempo de Máquina
   - Tiempo de Inactividad
   
2. **Parámetros de Suplementos**
   - Necesidades Personales (siempre 5%)
   - Fatiga (porcentaje calculado)

3. **Valores Calculados**
   - Tiempo Base (con fórmula)
   - Suplemento NP en segundos
   - Suplemento Fatiga en segundos (valor exacto de resumen)

**Características**:
- Tarjetas organizadas por categoría
- Fórmulas explicativas
- Tooltips informativos
- Referencias cruzadas entre pestañas

## Elementos de la Interfaz

### Encabezado del Caso
- **Icono**: Color según el caso (🔴🟡🔵🟢)
- **Título**: Descripción del caso aplicado
- **Subtítulo**: Explicación breve del comportamiento
- **Métricas**: Tiempo de inactividad, porcentajes, ciclo total

### Botones de Acción
- **🖨️ Imprimir**: Genera versión imprimible
- **📄 PDF**: Exporta reporte en PDF
- **📊 Excel**: Exporta datos en formato Excel

### Indicadores Visuales
- **Colores de fondo**: Diferenciación por tipo de información
- **Iconos**: Identificación rápida de secciones
- **Tipografía**: Monoespaciada para números y fórmulas

## Casos de Uso

### Verificación de Cálculos
1. Revisar **Pestaña Resumen** para valores finales
2. Consultar **Pestaña Detalles** para entender el proceso
3. Validar **Pestaña Parámetros** para datos de entrada

### Análisis de Eficiencia
1. Identificar el caso aplicado
2. Revisar tiempo de espera
3. Analizar absorción de suplementos
4. Comparar con otros estudios

### Troubleshooting
1. Verificar que todos los elementos tengan datos
2. Confirmar configuración de actividad
3. Revisar frecuencias de elementos
4. Validar consistencia entre pestañas

## Mensajes del Sistema

### Notificaciones de Guardado
- **Éxito**: "Datos guardados correctamente" (verde)
- **Error**: "Error al guardar los datos" (rojo)
- **Advertencia**: "Cálculo inválido - no se guardará" (amarillo)

### Estados de Carga
- **Calculando**: "Guardando datos del cálculo..."
- **Sin datos**: "No hay resultados de cálculo disponibles"
- **Sin detalles**: "No hay detalles de cálculo disponibles"

## Responsive Design

### Escritorio
- Layout de 3 columnas para tarjetas
- Pestañas horizontales
- Botones de acción en fila

### Tablet
- Layout de 2 columnas
- Pestañas horizontales mantenidas
- Botones ajustados

### Móvil
- Layout de 1 columna
- Pestañas stack/scroll
- Botones de ancho completo

## Accesibilidad

### Navegación por Teclado
- Tab para navegar entre pestañas
- Enter para activar botones
- Escape para cerrar modales

### Lectores de Pantalla
- Labels descriptivos en elementos
- ARIA roles apropiados
- Texto alternativo para iconos

### Contraste
- Ratios de contraste WCAG AA
- Colores diferenciados para daltónicos
- Texto legible en todos los fondos

## Validaciones en Tiempo Real

### Datos de Entrada
- Verificación de elementos con registros
- Validación de actividades positivas
- Control de frecuencias válidas

### Cálculos
- Tiempos no negativos
- Porcentajes en rangos válidos
- Consistencia matemática

### Resultados
- Ciclo total positivo
- Suplementos no negativos
- Casos aplicados correctamente

## Integraciones

### Sistema de Estudios
- Carga automática de elementos
- Sincronización con datos de tiempo
- Guardado automático de resultados

### Sistema de Reportes
- Datos disponibles para reportes
- Exportación en múltiples formatos
- Histórico de cálculos

### Sistema de Permisos
- Respeto a permisos de usuario
- Modo solo lectura para invitados
- Validación de propietario 