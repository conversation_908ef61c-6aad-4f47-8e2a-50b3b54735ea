import { NextApiRequest, NextApiResponse } from 'next';
import { buffer } from 'micro';
import Stripe from 'stripe';
import { creditService } from '../../services/creditService';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  let event: Stripe.Event;

  try {
    const buf = await buffer(req);
    const sig = req.headers['stripe-signature']!;

    event = stripe.webhooks.constructEvent(
      buf.toString(),
      sig,
      webhookSecret
    );
  } catch (err) {
    console.error('Error verifying webhook signature:', err);
    return res.status(400).json({
      error: `Webhook signature verification failed: ${err.message}`,
    });
  }

  try {
    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        if (session.payment_status === 'paid') {
          const { userId, credits } = session.metadata!;
          
          // Actualizar los créditos del usuario
          const result = await creditService.purchaseCredits({
            userId,
            amount: parseInt(credits),
            paymentId: session.payment_intent as string,
          });

          if (!result.success) {
            throw new Error('Failed to update user credits');
          }
        }
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.error('Payment failed:', paymentIntent.id);
        // Aquí podrías implementar lógica adicional para manejar pagos fallidos
        break;
      }
    }

    res.json({ received: true });
  } catch (err) {
    console.error('Error processing webhook:', err);
    res.status(500).json({
      error: 'Error processing webhook',
      details: err.message,
    });
  }
}
