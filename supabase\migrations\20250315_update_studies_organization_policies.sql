-- Update studies table policies to improve organization-based access control

-- First, drop existing policies
DROP POLICY IF EXISTS "Users can delete their own studies" ON studies;
DROP POLICY IF EXISTS "Users can view their own studies" ON studies;
DROP POLICY IF EXISTS "Users can update their own studies" ON studies;

-- <PERSON>reate updated policies
-- Allow users to delete studies they own OR studies in organizations where they are admin/owner
CREATE POLICY "Users can delete their own studies" 
  ON studies 
  FOR DELETE 
  TO public 
  USING (
    (user_id = auth.uid()) OR 
    (organization_id IN (
      SELECT organization_members.organization_id 
      FROM organization_members 
      WHERE organization_members.user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    ))
  );

-- Allow users to view studies they own OR studies in organizations they are members of
CREATE POLICY "Users can view their own studies" 
  ON studies 
  FOR SELECT 
  TO public 
  USING (
    (user_id = auth.uid()) OR 
    (organization_id IN (
      SELECT organization_members.organization_id 
      FROM organization_members 
      WHERE organization_members.user_id = auth.uid()
    ))
  );

-- Allow users to update studies they own OR studies in organizations where they are admin/owner
CREATE POLICY "Users can update their own studies" 
  ON studies 
  FOR UPDATE 
  TO public 
  USING (
    (user_id = auth.uid()) OR 
    (organization_id IN (
      SELECT organization_members.organization_id 
      FROM organization_members 
      WHERE organization_members.user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    ))
  );