import React from 'react';
import { useTranslation } from 'react-i18next';
import { Study } from '../types';

interface StudyFiltersProps {
  selectedStudy: Study | null;
  filters: {
    company: boolean;
    date: boolean;
  };
  onFilterChange: (filterName: string, value: boolean) => void;
}

export const StudyFilters: React.FC<StudyFiltersProps> = ({
  selectedStudy,
  filters,
  onFilterChange,
}) => {
  const { t } = useTranslation(['filters', 'common']);

  return (
    <div className="flex flex-wrap gap-4 mb-6 p-4 bg-white rounded-lg shadow">
      <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={filters.company}
          onChange={(e) => onFilterChange('company', e.target.checked)}
          disabled={!selectedStudy}
          className="rounded text-purple-600 focus:ring-purple-500"
        />
        <span className="text-sm font-medium text-gray-700">{t('filters:company')}</span>
      </label>

      <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={filters.date}
          onChange={(e) => onFilterChange('date', e.target.checked)}
          className="rounded text-purple-600 focus:ring-purple-500"
        />
        <span className="text-sm font-medium text-gray-700">{t('filters:date')}</span>
      </label>
    </div>
  );
};