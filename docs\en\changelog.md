---

# Changelog

## [42.1.37] - 2025-03-28
### Improvements
- **Element Reordering System**: Implemented a new navigation system to reorder elements in both the method screen and continuous chronometer screen, using buttons instead of drag-and-drop to improve compatibility and performance.
- **Enhanced Synchronization**: Optimized synchronization between the method screen and continuous chronometer screen when reordering elements, maintaining data consistency.
- **Password Change**: Added a new section for changing password in the profile page, enhancing security options for users.
### Fixed
- Removed warnings related to the drag-and-drop library in the development console.
- Improved accessibility of navigation buttons by adding appropriate aria labels.

## [42.1.36] - 2025-03-25
### Fixes
- A minimum of 10 takes is now required for elements with times less than 30 seconds before applying the statistical calculation of remaining takes.
- Elements with 0% supplements are now considered as elements without assigned supplements, affecting the visibility of the report button.
- Improved element detail table design in the report:
  - Description column now adjusts in height while maintaining its width
  - Content centered in all columns except description
  - Adjusted column widths for better visualization

## [42.1.35] - 2025-03-20
### Improvements
- **Enhanced Wake Lock System**:
  - Added an automatic retry system (up to 3 attempts).
  - Battery state verification.
  - Improved visibility event handling.
  - Automatic wake lock recovery when released by the system.
  - Enhanced resource cleanup.
  - Fixed screen turning off issue on mobile devices.

## [42.1.34] - 2025-03-15
### Improvements
- **Shared Study Warning**: Implemented a visual warning when a user attempts to edit a study they don't own. This warning is displayed in the study form, method page, supplements page, machine page, and frequency page, informing users that changes won't be saved if they're not the study owner.

## [42.1.33] - 2025-03-10
### Improvements
- **Shared Study Warning**: Implemented a visual warning when a user attempts to edit a study they don't own. This warning is displayed in the study form, method page, and supplements page, informing users that changes won't be saved if they're not the study owner.

## [42.1.32] - 2025-03-05
### Fixed
- **Supplement Preservation**: Fixed an issue that caused supplements of selected elements not to be properly preserved when creating a new study. Now percentage values and other properties of supplements are maintained intact when creating a study from existing elements.

## [42.1.31] - 2025-03-01
### Fixed
- **Type Errors**: Fixed type errors in the `StudyForm` component related to user profile data handling and new study initialization, improving application stability.
- **Data Structure**: Updated the initialization of new studies to ensure the `elements` field is correctly created as an empty array, maintaining consistency with the expected data structure.

## [42.1.30] - 2025-02-25
### Fixed
- **Study Data References**: Fixed references to activity scale and study name in multiple components (FrequencyPage, MachinePage, CronoSeguidoPage, ReportPage, MethodPage) to use the correct structure `selectedStudy.required_info` instead of `selectedStudy.info.required`, improving consistency and application stability.

## [42.1.29] - 2025-02-20
### Improvements
- **Study Persistence**: Implemented functionality to maintain the selected study when the page is refreshed, using the URL as a source of information. This enhances the user experience by preventing the loss of current work context.

## [42.1.28] - 2025-02-15
### Fixed
- Fixed an issue when creating a new study; it is now automatically selected and displays its name in the header.
- Fixed the base time calculation in fatigue supplements to use the correct activity scale (60-80) instead of a fixed value of 100, improving the accuracy of fatigue calculations.

## [42.1.27] - 2025-02-10
### Improvements
- **Enhanced Translations**: Added translations for the notification that informs users when a study is automatically shared with their organization.
- **Code Corrections**: Fixed typographical errors in translation references to improve application stability.
### Fixed
- **Fatigue Calculation**: Corrected the remaining fatigue calculation in CASE 2 (fatigue partially inside) to properly apply the formula: Total Fatigue - (Inactivity - 30).

## [42.1.26] - 2025-02-05
### Improvements
- **Organization Member Removal**: When a member is removed from an organization, their studies are automatically unshared, and they no longer have access to other organization studies.
- **Enhanced Privacy Controls**: Improved privacy controls to ensure studies are only visible to current organization members.
- **Security Enhancements**: Added additional security measures to protect study data when organization membership changes.
- **Permission Notifications**: Implemented a notification system that alerts users when they attempt to modify studies they don't own, informing them that their changes won't be saved.

## [42.1.25] - 2025-01-31
### Fixed
- **Report Translations**: Fixed internationalization issues in report components by ensuring all translation calls use explicit namespaces.
- **PDF and Excel Export**: Corrected translation references in PDF and Excel export utilities to properly use namespaces.
- **Spanish Translations**: Updated the Spanish translation file structure to match its English counterpart, ensuring consistent keys across languages.

## [42.1.24] - 2025-01-27
### Fixed
- **Menu Translations**: Fixed an issue that prevented the correct display of translations for "Continuous Chronometer" and "Frequency Times" in the navigation menu.

## [42.1.23] - 2025-01-23
### Improvements
- **Auto-share Studies**: New studies you create will be automatically shared with your organization if you are a member of one, simplifying the collaboration process.

## [42.1.22] - 2025-01-19
### Improvements
- **Share Studies with Organizations**: You can now share your studies with your organization directly from the profile page.
- **Improved Interface**: Updated the interface to make selecting studies to share with your organization more intuitive.
- **Notifications**: You'll now receive notifications when sharing studies with your organization, keeping you informed about the status of your actions.

## [42.1.21] - 2025-01-15
### Improvements
- **Share Studies with Organizations**: You can now share your studies with your organization directly from the profile page.

## [42.1.20] - 2025-01-11
### Fixed
- **Request Access Restriction**: Improved the security of your organization by restricting access to pending join requests to only administrators and organization owners.
- **Security Enhancement**: Added an extra layer of security to ensure that only authorized users can view and manage join requests.

## [42.1.19] - 2025-01-07
### Fixed
- **Duplicate Key Error**: Fixed an issue that caused errors when approving requests from previously expelled users.
- **Previous Requests Handling**: Improved the way we handle previous requests to avoid conflicts and ensure a smoother experience.

## [42.1.18] - 2025-01-03
### Added
- **Automatic Join Request Updates**: Implemented a feature to automatically refresh join requests on the profile page, ensuring you see the latest updates without needing to manually refresh.
- **Manual Refresh Button**: You can now manually refresh pending organization join requests with a single click.
- **User Experience Improvements**: Added visual indicators and success/error messages to make it clearer when requests are being updated.
### Fixed
- **Notification Issue**: Fixed an issue where administrators were not receiving notifications for pending join requests to their organizations.
- **Request Cache**: Improved caching logic to allow forced refresh of pending requests, ignoring the configured cache time.

## [42.1.17] - 2024-12-29
### Added
- **RPC Functions Integration**: Integrated the `isOrganizationMember` function to verify if a user is a member of an organization.
- **Improved Name Retrieval**: Updated the `getOrganizationName` function to use the new `isOrganizationMember` function and simplify the code.
### Fixed
- **Syntax Error**: Fixed a syntax error in the `fetchOrganizationNames` function that prevented the application from compiling.

## [42.1.16] - 2024-12-25
### Added
- **Known Organization Names**: Implemented a mechanism to use known organization names when the user is a member of the organization but cannot directly access the name.
- **Improved Cache**: Enhanced the caching system to check the local cache first before making database queries.
### Fixed
- **Organization Name Retrieval**: Simplified the process of obtaining organization names, removing unnecessary queries to tables that do not contain the required information.

## [42.1.15] - 2024-12-21
### Added
- **Enhanced Debugging**: Added code to print the complete structure of join requests, helping to identify available fields for obtaining the organization name.
- **New Functions**: Implemented the `fetchSentRequests` function to retrieve requests sent by the current user.
### Fixed
- **Organization Name Retrieval**: Improved the system to obtain organization names by attempting to query multiple tables (organizations, organization_join_requests, organization_invites).

## [42.1.14] - 2024-12-17
### Fixed
- **Organization Name Retrieval**: Improved the system for retrieving organization names using multiple alternative methods when the main query fails.
- **Non-existent RPC Function**: Removed the call to a non-existent RPC function (`get_organization_name`) and replaced it with direct queries to the tables.

## [42.1.13] - 2024-12-13
### Fixed
- **SQL Query Error**: Fixed an error when trying to access a non-existent column (`organization_name`) in the `organization_join_requests` table.
- **Organization Name Retrieval**: Improved the system for retrieving organization names using RPC functions when possible.

## [42.1.12] - 2024-12-09
### Fixed
- **Syntax Error**: Fixed a syntax error in the type definition of the fetchOrganizationNames function in organizationStore.ts that prevented the application from compiling correctly.

## [42.1.11] - 2024-12-05
### Fixed
- **Organization Names**: Fixed an issue where incorrect organization names stored in the local cache were displayed instead of retrieving the real name from the database.
- **Name Retrieval**: Improved the function to get organization names to always prioritize direct database queries.

## [42.1.10] - 2024-12-01
### Fixed
- **Pending Request Visibility**: Fixed an issue where pending requests were not appearing for organization owners due to the caching system.
- **Request Updates**: Added a manual refresh button to force reload of pending requests.
- **Owner Detection**: Improved logic to correctly detect organization owners.

## [42.1.9] - 2024-11-27
### Fixed
- **Member Count Update**: Fixed an issue where the member count was not updating instantly when approving join requests.
- **Database Updates**: Simplified the process of updating requests in the database to prevent issues when approving or rejecting requests.

## [42.1.8] - 2024-11-23
### Fixed
- **Join Request Permissions**: Fixed an issue where users who are not organization owners could see pending join requests.

## [42.1.7] - 2024-11-19
### Improvements
- **Interface Simplification**: Integrated organization member functionality directly into the profile page, eliminating the need for a separate organization page.
- **Enhanced User Experience**: Added an organization selector in the profile page to manage members of multiple organizations from a single location.

## [42.1.6] - 2024-11-15
### Fixed
- Improved organization member management to update the interface immediately when expelling members.
- Fixed duplicate request issues when approving or rejecting organization join requests.
- Implemented logic to correctly handle cases of re-approving previously expelled members.

## [42.1.5] - 2024-11-11
### Fixed
- Fixed an infinite recursion issue in security policies for the "organization_members" relation.
- Optimized the security policy for the "studies" table to prevent recursion when querying organization members.
- Implemented helper functions to improve performance and avoid recursion in security policies.
- Added indexes to optimize organization-related queries.

## [42.1.4] - 2024-11-07
### Security
- Sanitized sensitive data before logging to the console to prevent log injection attacks.
- Improved handling of potentially malicious data in debug logging.

## [42.1.3] - 2024-11-03
### Added
- Implemented exclusive Google registration for new users.
- Added translations for Google registration in both languages.
### Fixed
- Improved error handling in the authentication process.
- Fixed an issue with error message display in the login form.
- Optimized authentication flow to prevent page reloads that were hiding error messages.

## [42.1.2] - 2024-10-30
### Fixed
- Fixed an issue with error messages in the login form not being displayed.
- Improved translations for authentication error messages.
- Reorganized translation structure to maintain consistency between languages.

## [42.1.1] - 2024-10-26
### Fixed
- Removed duplicate keys in translation files (en/common.ts and es/common.ts).
- Reorganized translation structure to improve consistency.

## [42.1.0] - 2024-10-22
### Added
- Restored the credits information card on the profile page.
### Fixed
- Fixed the "unknown organization" issue when querying organizations.
- Improved error handling in the `getOrganizationName` function to query the database directly.
- Implemented an alternative solution to query organizations without relying on the `exec_sql` function.
- Fixed the data structure for `members` and `joinRequests` in the organization store.
- Updated the `OrganizationPage` component to use the new data structure.
- Updated the `OrganizationMembers` component to use the new data structure.
- Updated the `PendingJoinRequests` component to use the new data structure.
### Improved
- Implemented a local cache system for organization names.
- Enhanced the `getOrganizationName` function to try multiple methods for obtaining names.
- Added detailed logging to facilitate debugging.

## [42.0.27] - 2024-10-18
### Added
- Added functionality to manage organization members.
- Implemented an organization page to view and remove members.
- Improved the user interface for organization management.

## [42.0.26] - 2024-10-14
### Fixed
- Fixed an error when switching to English where an object was being rendered directly in the CreditsCard component.
- Corrected the structure of translation keys for credits.buy in both English and Spanish.

## [42.0.25] - 2024-10-10
### Fixed
- Disabled i18next debug mode to reduce console errors.
- Fixed translation references in the PendingJoinRequests component.

## [42.0.24] - 2024-10-06
### Fixed
- Added missing translations for header, subscription, and join requests.
- Fixed namespace issues in the PendingJoinRequests component.
- Added show/hide translations at the root level.

## [42.0.23] - 2024-10-02
### Fixed
- Migrated translations from JSON to TypeScript files for better type safety.
- Added missing translations for header and subscription sections.
- Improved i18next configuration with proper namespace structure.
- Enhanced error handling in organization name fetching.
- Added TypeScript interface for JoinRequest type.
- Improved async error handling in useEffect hooks.

## [42.0.22] - 2024-09-28
### Improved
- We have reorganized the order of sections in the profile page for a better user experience.
- We have added translations in Spanish and English for the new password change functionality.

## [42.0.21] - 2024-09-24
### Fixed
- Improved i18n integration in the MySentJoinRequests component by specifying the namespace.
- Enhanced error handling in organization name fetching.
- Added locale detection for date formatting based on the current language.

## [42.0.20] - 2024-09-20
### Fixed
- Fixed an error when retrieving organization names by using maybeSingle instead of single.
- Added missing translations for join request texts in both English and Spanish.
- Improved error handling when organization data is not available.

## [42.0.19] - 2024-09-16
### Fixed
- Fixed UI component imports in the MySentJoinRequests component.
- Replaced custom UI components with native HTML and Tailwind CSS classes.
- Improved error handling and loading states with a simplified UI.

## [42.0.18] - 2024-09-12
### Fixed
- Fixed UI component imports in the MySentJoinRequests component.
- Replaced Ant Design components with the application's own UI components.
- Improved layout and styling of the sent join requests display.

## [42.0.17] - 2024-09-08
### Fixed
- Fixed issues with security policies when viewing sent join requests.
- Improved organization name retrieval to work with existing RLS policies.
- Added a caching mechanism for organization names to reduce database queries.
- Enhanced error handling to prevent application crashes when permissions are insufficient.

## [42.0.16] - 2024-09-04
### Added
- Added a new feature: Users can now see their sent join requests on the profile page.
- Added a new component to display sent join requests with their current status.
- Enhanced the organization store to fetch and display sent join requests with organization names.
- Added translations for the new join request features.

## [42.0.15] - 2024-08-31
### Improved
- Enhanced logging for the join request approval process to better diagnose membership verification issues.
- Added detailed logging of user IDs and membership status for improved debugging.
- Restructured member insertion code for better clarity and traceability.

## [42.0.14] - 2024-08-27
### Fixed
- Fixed an error when approving join requests for users who are already members of the organization.
- Added a verification step to check if a user is already a member before attempting to add them.
- Improved error handling to provide more descriptive error messages.

## [42.0.13] - 2024-08-23
### Fixed
- Updated join request approval/rejection functions to use direct UPDATE operations after implementing the necessary security policy.
- Simplified code by removing the DELETE + INSERT workaround.
- Improved logging for better debugging and monitoring.

## [42.0.12] - 2024-08-19
### Fixed
- Resolved a critical issue with organization join request approval/rejection by implementing a DELETE + INSERT strategy to bypass Row Level Security constraints.
- Enhanced logging for database operations to improve debugging capabilities.
- Maintained the same functionality while working within the database security constraints.

## [42.0.11] - 2024-08-15
### Fixed
- Completely rewrote the join request approval/rejection logic to use direct database updates.
- Added detailed logging for better debugging of database operations.
- Implemented verification steps to confirm successful updates.
- Created SQL functions for future database-level processing of join requests.

## [42.0.10] - 2024-08-11
### Fixed
- Fixed a critical error in join request processing where update operations were failing.
- Improved validation for Supabase response data in update and insert operations.
- Enhanced error handling for database operations.

## [42.0.9] - 2024-08-07
### Fixed
- Fixed a critical issue where rejected organization join requests were not being properly updated in the database.
- Enhanced join request processing with additional metadata (processed_at and processed_by).
- Improved error handling and logging for request approval/rejection operations.

## [42.0.8] - 2024-08-03
### Fixed
- Fixed an issue where rejected organization join requests would still appear after a page refresh.
- Improved state management for join requests to immediately remove approved or rejected requests from the UI.

## [42.0.7] - 2024-07-30
### Added
- Implemented a notification system for pending organization join requests.
- Added the PendingJoinRequests component to display join requests on the profile page.
- Updated the JoinRequestsList component to properly handle the approval and rejection of requests.

## [42.0.6] - 2024-07-26
### Fixed
- Implemented missing functionality for organization join requests.
- Added the sendJoinRequest function to organizationStore to handle invitation codes.
- Updated the JoinOrganization component to use the new function.

## [42.0.5] - 2024-07-22
### Added
- Restored the credits information card on the profile page.
- Added functionality to display available and used credits for the current month.
- Created a new CreditsCard component for better code organization.
### Changed
- Updated ProfilePage to include the credits information section.
- Improved integration with the existing credits service.

## [42.0.4] - 2024-07-18
### Added
- Added a Privacy Policy page with translations in English and Spanish.
- Added a Cookie Policy link to the login form footer.
- Added the Name field to the data collection section in the Privacy Policy.
### Changed
- Updated the email <NAME_EMAIL> throughout the application.
- Fixed English translations not showing up in the Privacy Policy page.

## [42.0.3] - 2024-07-14
### Changed
- Fix: Updated the FAQ route to use the FAQ component directly.

## [42.0.2] - 2024-07-10
### Changed
- Updated App.tsx.

## [42.0.1] - 2024-07-06
### Changed
- Fix: Updated scripts to ES modules and improved markdown rendering.

## [42.0.0] - 2024-07-02
### Added
- Integrated GitHub Actions for automatic version updates.
- Improved the version management system.
### Fixed
- Fixed the translation system in SupplementsMachine.
- Removed redundant spans in explanation texts.
- Restored and organized complete explanation texts in translations.

## [41.0.0] - 2024-06-25
### Added
- Improved the machine-based supplement system.
- Added a new interface for supplement management.
- Made improvements to the user experience.
### Changed
- Refactored the calculation system.
- Optimized overall performance.

## [40.0.3] - 2024-06-18
### Fixed
- Made corrections to the translation system.
- Made general stability improvements.
- Optimized performance.

## [4.2.0] - 2024-06-10
### New Features
- **Study Access Restrictions**: When a member is removed from an organization, their studies are no longer visible to the organization, and the removed member cannot see studies from other members.
- **Orphaned Studies Cleanup**: New functionality to clean up studies from removed members that still maintain references to the organization.

## [1.0.0] - 2024-06-01
### Added
- Added a multi-language translation system.
- Added calculation of supplements per machine.
- Added weighted fatigue calculation.
- Added detailed explanations of calculations.
- Added a modern and responsive interface.

## [Unreleased]
### Added
- Improved visualization of base time calculation in the machine supplements section.
- Visual clarification of base time calculation as the sum of machine stop time plus the maximum value between machine time and machine running time.
- New translations for UI elements related to base time calculation.
### Fixed
- Correction in the base time calculation to ensure that the maximum value between machine time and machine running time is used.
- Consistency in the weighted fatigue supplement calculation, using the same base time logic across all functions.

## [Next version]

## [42.0.22] - 2024-09-28
### Improved
- We have reorganized the order of sections in the profile page for a better user experience.
- We have added translations in Spanish and English for the new password change functionality.

---