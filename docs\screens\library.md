# Pantalla de Biblioteca

## Descripción
Gestión de elementos reutilizables entre estudios.

## Componentes
- `LibraryElementList`: Lista de elementos
- `LibraryElementForm`: Formulario de creación/edición
- `DeleteConfirmModal`: Confirmación de eliminación
- `ShareToggle`: Control de compartición

## Funcionalidades

### 1. Gestión de Elementos
- Creación de elementos reutilizables
- Edición de elementos existentes
- Eliminación de elementos
- Compartición de elementos

### 2. Propiedades
- Descripción
- Tipo de elemento
- Configuración de repeticiones
- Estadísticas históricas
- Estado de compartición

### 3. Integración
- Reutilización en estudios
- Sincronización de datos
- Actualización de estadísticas
- Gestión de permisos

### 4. Compartición
- Control individual por elemento
- Visibilidad para otros usuarios
- Restricciones de edición
- Estadísticas de uso

## Estados
```typescript
{
  elements: LibraryElement[];
  showForm: boolean;
  editingElement: LibraryElement | null;
  showDeleteModal: boolean;
  elementToDelete: string | null;
}
```

## Flujo de Trabajo
1. Visualización de biblioteca
   - Elementos propios
   - Elementos compartidos
2. Gestión de elementos:
   - Crear nuevo elemento
   - Editar existente
   - Eliminar elemento
   - Compartir/dejar de compartir
3. Uso en estudios:
   - Selección de elementos
   - Aplicación en método
   - Actualización de estadísticas