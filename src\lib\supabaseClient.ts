import { createClient } from '@supabase/supabase-js';

// Usar las variables de entorno de Vite
const supabaseUrl = import.meta.env.VITE_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase configuration');
}

// Re-export the supabase client from the main file
export { supabase } from './supabase';
