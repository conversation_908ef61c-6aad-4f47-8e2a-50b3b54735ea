/*
=======================================================================
MIGRACIÓN COMPLETA - PROYECTO STADLER CRONOMETRAS
=======================================================================
Este archivo contiene todas las migraciones necesarias para replicar
la estructura completa de base de datos en el nuevo proyecto Stadler.

INSTRUCCIONES:
1. Ve al dashboard de Supabase del proyecto Stadler
2. Ve a SQL Editor
3. Copia y pega todo este contenido
4. Ejecuta el script completo

¡IMPORTANTE! Ejecutar SOLO en el proyecto nuevo (Stadler)
=======================================================================
*/

-- =====================================================
-- EXTENSIONES Y CONFIGURACIÓN INICIAL
-- =====================================================

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Configurar storage para uploads públicos
INSERT INTO storage.buckets (id, name, public) 
VALUES ('public', 'public', true) 
ON CONFLICT (id) DO UPDATE SET public = true;

-- Configurar bucket para logos de empresa
INSERT INTO storage.buckets (id, name, public) 
VALUES ('logos', 'logos', true) 
ON CONFLICT (id) DO UPDATE SET public = true;

-- =====================================================
-- POLÍTICAS DE STORAGE
-- =====================================================

-- Eliminar todas las políticas existentes en storage.objects
DROP POLICY IF EXISTS "Public bucket read access" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload to public" ON storage.objects;
DROP POLICY IF EXISTS "Public read access for logos" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload own logos" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own logos" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow public viewing" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete own files" ON storage.objects;
DROP POLICY IF EXISTS "Permitir lectura pública de logos" ON storage.objects;
DROP POLICY IF EXISTS "Permitir subida de logos a usuarios autenticados" ON storage.objects;
DROP POLICY IF EXISTS "Permitir eliminación de logos propios" ON storage.objects;
DROP POLICY IF EXISTS "User can delete their own objects" ON storage.objects;
DROP POLICY IF EXISTS "Public read access for all buckets" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload to any bucket" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to view logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload their own logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to update their own logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete their own logos" ON storage.objects;

-- Políticas corregidas y funcionales para storage.objects
-- Lectura pública para todos los buckets
DROP POLICY IF EXISTS "Enable public read access" ON storage.objects;
CREATE POLICY "Enable public read access" 
ON storage.objects FOR SELECT 
TO public 
USING (true);

-- Subida para usuarios autenticados sin restricciones
DROP POLICY IF EXISTS "Enable authenticated uploads" ON storage.objects;
CREATE POLICY "Enable authenticated uploads" 
ON storage.objects FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- Actualización solo para archivos propios basado en owner
DROP POLICY IF EXISTS "Enable users to update own files" ON storage.objects;
CREATE POLICY "Enable users to update own files" 
ON storage.objects FOR UPDATE 
TO authenticated 
USING (owner = auth.uid());

-- Eliminación solo para archivos propios basado en owner
DROP POLICY IF EXISTS "Enable users to delete own files" ON storage.objects;
CREATE POLICY "Enable users to delete own files" 
ON storage.objects FOR DELETE 
TO authenticated 
USING (owner = auth.uid());

-- =====================================================
-- CREACIÓN DE TABLAS PRINCIPALES
-- =====================================================

-- Tabla de límites de estudio por usuario (estructura completa con suscripciones)
CREATE TABLE IF NOT EXISTS public.user_study_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    user_email TEXT NOT NULL,
    total_credits INTEGER NOT NULL DEFAULT 10,
    used_credits INTEGER NOT NULL DEFAULT 0,
    password_hash TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT credits_check CHECK (used_credits <= total_credits)
);

-- Actualizar estructura de user_study_limits para suscripciones
-- Primero, eliminar constraints antiguos de forma segura
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'credits_check' 
               AND table_name = 'user_study_limits') THEN
        ALTER TABLE public.user_study_limits DROP CONSTRAINT credits_check;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'monthly_credits_check' 
               AND table_name = 'user_study_limits') THEN
        ALTER TABLE public.user_study_limits DROP CONSTRAINT monthly_credits_check;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'extra_credits_check' 
               AND table_name = 'user_study_limits') THEN
        ALTER TABLE public.user_study_limits DROP CONSTRAINT extra_credits_check;
    END IF;
END $$;

-- Eliminar columnas antiguas si existen
ALTER TABLE public.user_study_limits DROP COLUMN IF EXISTS total_credits;
ALTER TABLE public.user_study_limits DROP COLUMN IF EXISTS used_credits;
ALTER TABLE public.user_study_limits DROP COLUMN IF EXISTS password_hash;

-- Agregar nuevas columnas para el sistema de suscripciones
ALTER TABLE public.user_study_limits 
ADD COLUMN IF NOT EXISTS monthly_credits INTEGER NOT NULL DEFAULT 1,
ADD COLUMN IF NOT EXISTS extra_credits INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS used_monthly_credits INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS used_extra_credits INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS reset_date DATE DEFAULT date_trunc('month', current_date)::date,
ADD COLUMN IF NOT EXISTS subscription_plan TEXT,
ADD COLUMN IF NOT EXISTS subscription_start_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS subscription_end_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- =====================================================
-- CORRECCIÓN DE DATOS EXISTENTES ANTES DE CONSTRAINTS
-- =====================================================

-- Corregir datos inconsistentes antes de agregar constraints
UPDATE public.user_study_limits 
SET 
    used_monthly_credits = LEAST(used_monthly_credits, COALESCE(monthly_credits, 1)),
    monthly_credits = GREATEST(COALESCE(monthly_credits, 1), used_monthly_credits)
WHERE used_monthly_credits > COALESCE(monthly_credits, 1);

-- Asegurar que todos los valores requeridos están presentes
UPDATE public.user_study_limits 
SET 
    monthly_credits = COALESCE(monthly_credits, 1),
    extra_credits = COALESCE(extra_credits, 0),
    used_monthly_credits = COALESCE(used_monthly_credits, 0),
    used_extra_credits = COALESCE(used_extra_credits, 0),
    reset_date = COALESCE(reset_date, date_trunc('month', current_date)::date)
WHERE monthly_credits IS NULL 
   OR extra_credits IS NULL 
   OR used_monthly_credits IS NULL 
   OR used_extra_credits IS NULL 
   OR reset_date IS NULL;

-- Corregir datos inconsistentes específicos
UPDATE public.user_study_limits 
SET used_monthly_credits = monthly_credits
WHERE used_monthly_credits > monthly_credits;

UPDATE public.user_study_limits 
SET used_extra_credits = extra_credits
WHERE used_extra_credits > extra_credits;

-- Agregar constraint UNIQUE en user_id si no existe (de forma segura)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'user_study_limits_user_id_key' 
                   AND table_name = 'user_study_limits') THEN
        ALTER TABLE public.user_study_limits 
        ADD CONSTRAINT user_study_limits_user_id_key UNIQUE (user_id);
    END IF;
END $$;

-- Agregar nuevos constraints de forma segura con verificación y manejo de errores
DO $$
DECLARE
    bad_rows INTEGER;
    row_record RECORD;
BEGIN
    -- Verificar datos antes de agregar constraints
    SELECT COUNT(*) INTO bad_rows
    FROM public.user_study_limits
    WHERE used_monthly_credits > monthly_credits;
    
    IF bad_rows > 0 THEN
        RAISE NOTICE 'Advertencia: % filas con used_monthly_credits > monthly_credits serán corregidas', bad_rows;
        UPDATE public.user_study_limits 
        SET used_monthly_credits = monthly_credits
        WHERE used_monthly_credits > monthly_credits;
    END IF;
    
    -- Verificar extra_credits
    SELECT COUNT(*) INTO bad_rows
    FROM public.user_study_limits
    WHERE used_extra_credits > extra_credits;
    
    IF bad_rows > 0 THEN
        RAISE NOTICE 'Advertencia: % filas con used_extra_credits > extra_credits serán corregidas', bad_rows;
        UPDATE public.user_study_limits 
        SET used_extra_credits = extra_credits
        WHERE used_extra_credits > extra_credits;
    END IF;
    
    -- Agregar constraint de monthly_credits
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'monthly_credits_check' 
                   AND table_name = 'user_study_limits') THEN
        ALTER TABLE public.user_study_limits 
        ADD CONSTRAINT monthly_credits_check CHECK (used_monthly_credits <= monthly_credits);
        RAISE NOTICE '✅ Constraint monthly_credits_check agregado exitosamente';
    END IF;
    
    -- Agregar constraint de extra_credits
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'extra_credits_check' 
                   AND table_name = 'user_study_limits') THEN
        ALTER TABLE public.user_study_limits 
        ADD CONSTRAINT extra_credits_check CHECK (used_extra_credits <= extra_credits);
        RAISE NOTICE '✅ Constraint extra_credits_check agregado exitosamente';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Error al agregar constraints: %', SQLERRM;
        -- Mostrar datos problemáticos para debugging
        RAISE NOTICE 'Mostrando primeras 5 filas problemáticas:';
        FOR row_record IN 
            SELECT user_id, monthly_credits, used_monthly_credits, extra_credits, used_extra_credits
            FROM public.user_study_limits
            WHERE used_monthly_credits > monthly_credits 
               OR used_extra_credits > extra_credits
            LIMIT 5
        LOOP
            RAISE NOTICE 'user_id: %, monthly: %/%, extra: %/%', 
                row_record.user_id, row_record.used_monthly_credits, row_record.monthly_credits, 
                row_record.used_extra_credits, row_record.extra_credits;
        END LOOP;
        RAISE;
END $$;

-- Tabla de elementos de biblioteca
CREATE TABLE IF NOT EXISTS public.library_elements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_shared BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Tabla de estudios
CREATE TABLE IF NOT EXISTS public.studies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    required_info JSONB NOT NULL DEFAULT '{}'::jsonb,
    optional_info JSONB DEFAULT '{}'::jsonb,
    elements JSONB DEFAULT '[]'::jsonb,
    time_records JSONB DEFAULT '{}'::jsonb,
    supplements JSONB DEFAULT '{}'::jsonb,
    crono_seguido_records JSONB DEFAULT '[]'::jsonb,
    organization_id UUID,
    is_shared BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT required_fields_check CHECK (
        required_info::jsonb ? 'name' AND
        required_info::jsonb ? 'company' AND
        required_info::jsonb ? 'date' AND
        required_info::jsonb ? 'activity_scale'
    )
);

-- Tabla de dispositivos de usuario
CREATE TABLE IF NOT EXISTS public.user_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_name TEXT NOT NULL,
    device_id TEXT NOT NULL UNIQUE,
    last_used TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Tabla de logs de webhooks
CREATE TABLE IF NOT EXISTS public.webhook_logs (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    event_type TEXT NOT NULL,
    data JSONB,
    error TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- =====================================================
-- TABLAS DE ORGANIZACIONES
-- =====================================================

-- Tabla de organizaciones
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    invite_code UUID DEFAULT uuid_generate_v4() NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Tabla de solicitudes de unión a organizaciones
CREATE TABLE IF NOT EXISTS public.organization_join_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    user_email TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMPTZ,
    processed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    CONSTRAINT unique_pending_request UNIQUE(organization_id, user_id, status)
);

-- Tabla de miembros de organizaciones
CREATE TABLE IF NOT EXISTS public.organization_members (
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    user_email TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    PRIMARY KEY (organization_id, user_id)
);

-- Agregar la referencia de organización a studies (si no existe)
ALTER TABLE public.studies 
ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id) ON DELETE SET NULL;

-- =====================================================
-- SISTEMA DE CARPETAS JERÁRQUICAS
-- =====================================================

-- Migration: Add folder system for hierarchical organization
-- This enables organizing studies in folders and subfolders

-- Create folders table with hierarchical structure
CREATE TABLE IF NOT EXISTS public.folders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    parent_folder_id UUID REFERENCES public.folders(id) ON DELETE CASCADE,
    color TEXT DEFAULT '#3B82F6', -- Color for visual organization
    icon TEXT DEFAULT 'folder', -- Icon identifier
    is_shared BOOLEAN NOT NULL DEFAULT true, -- Por defecto compartidas en organizaciones
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Ensure folder names are unique within the same parent and user/organization
    CONSTRAINT unique_folder_name_per_parent_user UNIQUE (user_id, parent_folder_id, name),
    CONSTRAINT unique_folder_name_per_parent_org UNIQUE (organization_id, parent_folder_id, name),
    -- Prevent circular references
    CONSTRAINT no_self_reference CHECK (id != parent_folder_id),
    -- Ensure either user_id or organization_id is set for shared folders
    CONSTRAINT folder_ownership_check CHECK (
        (user_id IS NOT NULL AND (organization_id IS NULL OR is_shared = false)) OR
        (organization_id IS NOT NULL AND is_shared = true)
    )
);

-- Add folder_id to studies table
ALTER TABLE public.studies 
ADD COLUMN IF NOT EXISTS folder_id UUID REFERENCES public.folders(id) ON DELETE SET NULL;

-- =====================================================
-- TABLAS ADICIONALES
-- =====================================================

-- Tabla de perfiles de usuario (estructura completa)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    logo_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Agregar todas las columnas necesarias para la aplicación
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS default_time_unit TEXT DEFAULT 'dmh',
ADD COLUMN IF NOT EXISTS default_language TEXT DEFAULT 'es',
ADD COLUMN IF NOT EXISTS default_contingency NUMERIC(5,2) DEFAULT 5.00,
ADD COLUMN IF NOT EXISTS minutes_per_shift INTEGER DEFAULT 480,
ADD COLUMN IF NOT EXISTS company_name TEXT,
ADD COLUMN IF NOT EXISTS default_normal_activity NUMERIC(5,2) DEFAULT 100.00,
ADD COLUMN IF NOT EXISTS default_optimal_activity NUMERIC(5,2) DEFAULT 133.00,
ADD COLUMN IF NOT EXISTS show_shared_studies BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS companies_list JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS default_company TEXT;

-- Agregar campo points_per_hour para equivalencia de puntos por hora
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS points_per_hour INTEGER NOT NULL DEFAULT 100;

-- Comentario explicativo
COMMENT ON COLUMN public.profiles.points_per_hour IS 'Equivalencia de puntos por hora para informes y cálculos personalizados. 100 = 1 hora son 100 puntos, configurable por usuario.';

-- =====================================================
-- CREAR ÍNDICES
-- =====================================================

-- Índices para user_study_limits
CREATE INDEX IF NOT EXISTS idx_user_study_limits_user_id ON public.user_study_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_user_study_limits_reset_date ON public.user_study_limits(reset_date);
CREATE INDEX IF NOT EXISTS idx_user_study_limits_stripe_customer ON public.user_study_limits(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_user_study_limits_subscription ON public.user_study_limits(stripe_subscription_id);

-- Índices para library_elements
CREATE INDEX IF NOT EXISTS idx_library_elements_user_id ON public.library_elements(user_id);

-- Índices para studies
CREATE INDEX IF NOT EXISTS idx_studies_user_id ON public.studies(user_id);
CREATE INDEX IF NOT EXISTS idx_studies_organization_id ON public.studies(organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_studies_is_shared ON public.studies(is_shared) WHERE is_shared = true;

-- Índices para user_devices
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);

-- Índices para organizaciones
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON public.organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_org_id ON public.organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_join_requests_org_id ON public.organization_join_requests(organization_id) WHERE status = 'pending';

-- Índices para carpetas
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON public.folders(user_id);
CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON public.folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_folders_organization_id ON public.folders(organization_id);
CREATE INDEX IF NOT EXISTS idx_studies_folder_id ON public.studies(folder_id);

-- Índices para tablas adicionales
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);

-- =====================================================
-- HABILITAR ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE public.user_study_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.library_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.studies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webhook_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_join_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.folders ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- FUNCIONES SQL (DEBEN CREARSE ANTES QUE LAS POLÍTICAS)
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Funciones auxiliares para organizaciones (necesarias para las políticas RLS)
CREATE OR REPLACE FUNCTION is_organization_member(org_id UUID, member_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.organization_members
        WHERE organization_id = org_id
        AND user_id = member_id
    );
END;
$$;

CREATE OR REPLACE FUNCTION get_user_organizations(p_user_id UUID)
RETURNS SETOF UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT organization_id
    FROM public.organization_members
    WHERE user_id = p_user_id;
END;
$$;

CREATE OR REPLACE FUNCTION is_organization_creator(org_id UUID, creator_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.organizations
        WHERE id = org_id
        AND created_by = creator_id
    );
END;
$$;

-- =====================================================
-- POLÍTICAS RLS - USER_STUDY_LIMITS
-- =====================================================

-- Limpiar todas las políticas existentes de user_study_limits
DROP POLICY IF EXISTS "Users can view their own study limits" ON public.user_study_limits;
DROP POLICY IF EXISTS "Admin full access" ON public.user_study_limits;
DROP POLICY IF EXISTS "Users can insert own study limits" ON public.user_study_limits;
DROP POLICY IF EXISTS "Users can update own study limits" ON public.user_study_limits;
DROP POLICY IF EXISTS "Additional user select policy" ON public.user_study_limits;
DROP POLICY IF EXISTS "Additional user update policy" ON public.user_study_limits;
DROP POLICY IF EXISTS "Additional user update policy 2" ON public.user_study_limits;
DROP POLICY IF EXISTS "Public read access" ON public.user_study_limits;

CREATE POLICY "Users can view their own study limits"
    ON public.user_study_limits
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admin full access"
    ON public.user_study_limits
    AS PERMISSIVE
    FOR ALL
    TO authenticated
    USING (
        auth.email() = '<EMAIL>' OR 
        auth.role() = 'service_role'
    )
    WITH CHECK (
        auth.email() = '<EMAIL>' OR 
        auth.role() = 'service_role'
    );

-- Políticas adicionales para user_study_limits (para coincidir con el original)
CREATE POLICY "Users can insert own study limits"
    ON public.user_study_limits
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own study limits"
    ON public.user_study_limits
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Additional user select policy"
    ON public.user_study_limits
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Additional user update policy"
    ON public.user_study_limits
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Additional user update policy 2"
    ON public.user_study_limits
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Public read access"
    ON public.user_study_limits
    FOR ALL
    TO public
    USING (true);

-- =====================================================
-- POLÍTICAS RLS - LIBRARY_ELEMENTS
-- =====================================================

DROP POLICY IF EXISTS "Users can view their own and shared library elements" ON public.library_elements;
CREATE POLICY "Users can view their own and shared library elements"
    ON public.library_elements
    FOR SELECT
    TO authenticated
    USING ((user_id = auth.uid()) OR (is_shared = true));

DROP POLICY IF EXISTS "Users can insert their own library elements" ON public.library_elements;
CREATE POLICY "Users can insert their own library elements"
    ON public.library_elements
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can update their own library elements" ON public.library_elements;
CREATE POLICY "Users can update their own library elements"
    ON public.library_elements
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can delete their own library elements" ON public.library_elements;
CREATE POLICY "Users can delete their own library elements"
    ON public.library_elements
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS RLS - STUDIES
-- =====================================================

-- Limpiar políticas existentes
DROP POLICY IF EXISTS "Users can view their own studies" ON public.studies;
DROP POLICY IF EXISTS "Users can view shared organization studies" ON public.studies;
DROP POLICY IF EXISTS "Users can view shared organization studies v2" ON public.studies;
DROP POLICY IF EXISTS "Users can view shared organization studies v3" ON public.studies;
DROP POLICY IF EXISTS "Users can insert their own studies" ON public.studies;
DROP POLICY IF EXISTS "Users can insert studies" ON public.studies;
DROP POLICY IF EXISTS "Users can update their own studies" ON public.studies;
DROP POLICY IF EXISTS "Users can delete their own studies" ON public.studies;

-- Políticas principales para studies (6 políticas como en el original)
CREATE POLICY "Users can view their own studies"
    ON public.studies
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can view shared organization studies v3"
    ON public.studies
    FOR SELECT
    TO authenticated
    USING (
        is_shared = true
        AND organization_id IS NOT NULL
        AND is_organization_member(organization_id, auth.uid())
    );

CREATE POLICY "Users can insert their own studies"
    ON public.studies
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can insert studies"
    ON public.studies
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own studies"
    ON public.studies
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own studies"
    ON public.studies
    FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- =====================================================
-- POLÍTICAS RLS - USER_DEVICES
-- =====================================================

DROP POLICY IF EXISTS "Users can view their own devices" ON public.user_devices;
CREATE POLICY "Users can view their own devices"
    ON public.user_devices
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can register their own devices" ON public.user_devices;
CREATE POLICY "Users can register their own devices"
    ON public.user_devices
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own devices" ON public.user_devices;
CREATE POLICY "Users can update their own devices"
    ON public.user_devices
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own devices" ON public.user_devices;
CREATE POLICY "Users can delete their own devices"
    ON public.user_devices
    FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- =====================================================
-- POLÍTICAS RLS - WEBHOOK_LOGS
-- =====================================================

-- Limpiar todas las políticas existentes de webhook_logs
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.webhook_logs;
DROP POLICY IF EXISTS "Enable insert for service role" ON public.webhook_logs;
DROP POLICY IF EXISTS "Enable read access for public" ON public.webhook_logs;
DROP POLICY IF EXISTS "Enable insert for public" ON public.webhook_logs;

-- Políticas para webhook_logs (corregidas para coincidir con el original)
CREATE POLICY "Enable read access for public" 
    ON public.webhook_logs
    FOR SELECT 
    TO public
    USING (true);

CREATE POLICY "Enable insert for public" 
    ON public.webhook_logs
    FOR INSERT 
    TO public
    WITH CHECK (true);

-- =====================================================
-- POLÍTICAS RLS - ORGANIZATIONS
-- =====================================================

-- Limpiar políticas existentes de organizations
DROP POLICY IF EXISTS "Users can create organizations" ON public.organizations;
DROP POLICY IF EXISTS "Organization members can view their organizations" ON public.organizations;
DROP POLICY IF EXISTS "Users can view their organizations v2" ON public.organizations;
DROP POLICY IF EXISTS "Users can view organizations they created" ON public.organizations;
DROP POLICY IF EXISTS "Additional organization select policy" ON public.organizations;

CREATE POLICY "Users can create organizations"
    ON public.organizations 
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can view their organizations v2"
    ON public.organizations 
    FOR SELECT
    TO authenticated
    USING (
        created_by = auth.uid()
        OR id IN (SELECT get_user_organizations(auth.uid()))
    );

-- Política SELECT adicional para organizations (que existe en el original)
CREATE POLICY "Additional organization select policy"
    ON public.organizations
    FOR SELECT
    TO authenticated
    USING (
        created_by = auth.uid()
        OR id IN (SELECT get_user_organizations(auth.uid()))
    );

-- =====================================================
-- POLÍTICAS RLS - ORGANIZATION_MEMBERS
-- =====================================================

-- Limpiar políticas existentes de organization_members
DROP POLICY IF EXISTS "Organization admins can manage members" ON public.organization_members;
DROP POLICY IF EXISTS "Users can view members of their organizations" ON public.organization_members;
DROP POLICY IF EXISTS "Users can view members of their organizations v2" ON public.organization_members;
DROP POLICY IF EXISTS "Users can manage their own memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Organization creators can manage members" ON public.organization_members;

CREATE POLICY "Users can view members of their organizations v2"
    ON public.organization_members 
    FOR SELECT
    TO authenticated
    USING (
        user_id = auth.uid()
        OR organization_id IN (SELECT get_user_organizations(auth.uid()))
    );

CREATE POLICY "Users can manage their own memberships"
    ON public.organization_members 
    FOR ALL
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Organization creators can manage members"
    ON public.organization_members 
    FOR ALL
    TO authenticated
    USING (is_organization_creator(organization_id, auth.uid()));

-- =====================================================
-- POLÍTICAS RLS - ORGANIZATION_JOIN_REQUESTS
-- =====================================================

-- Limpiar todas las políticas existentes de organization_join_requests
DROP POLICY IF EXISTS "Users can create join requests" ON public.organization_join_requests;
DROP POLICY IF EXISTS "Users can view their own join requests" ON public.organization_join_requests;
DROP POLICY IF EXISTS "Public can update join requests" ON public.organization_join_requests;

CREATE POLICY "Users can create join requests"
    ON public.organization_join_requests 
    FOR INSERT
    TO authenticated
    WITH CHECK (
        auth.uid() = user_id 
        AND status = 'pending'
        AND NOT EXISTS (
            SELECT 1 FROM public.organization_members
            WHERE organization_id = organization_join_requests.organization_id
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can view their own join requests"
    ON public.organization_join_requests 
    FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id
        OR EXISTS (
            SELECT 1 FROM public.organization_members
            WHERE organization_id = organization_join_requests.organization_id
            AND user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    );

-- Política UPDATE para public (que existe en el original)
CREATE POLICY "Public can update join requests"
    ON public.organization_join_requests
    FOR UPDATE
    TO public
    USING (true)
    WITH CHECK (true);

-- =====================================================
-- POLÍTICAS RLS - PROFILES
-- =====================================================

-- Limpiar todas las políticas existentes de profiles
DROP POLICY IF EXISTS "Enable all for authenticated users" ON public.profiles;
DROP POLICY IF EXISTS "Enable all for service role" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Public read access for profiles" ON public.profiles;

-- Políticas para profiles (basado en el original)
CREATE POLICY "Enable all for authenticated users"
    ON public.profiles
    FOR ALL 
    TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable all for service role"
    ON public.profiles
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Políticas adicionales para profiles (para coincidir con el original)
CREATE POLICY "Users can view own profile"
    ON public.profiles
    FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
    ON public.profiles
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile"
    ON public.profiles
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Política adicional para public (parece que existe en el original)
CREATE POLICY "Public read access for profiles"
    ON public.profiles
    FOR ALL
    TO public
    USING (true)
    WITH CHECK (true);



-- =====================================================
-- POLÍTICAS RLS - FOLDERS
-- =====================================================

-- Limpiar políticas existentes de folders
DROP POLICY IF EXISTS "Users can view their own folders and shared ones" ON public.folders;
DROP POLICY IF EXISTS "Users can create their own folders" ON public.folders;
DROP POLICY IF EXISTS "Users can update their own folders" ON public.folders;
DROP POLICY IF EXISTS "Users can delete their own folders" ON public.folders;
DROP POLICY IF EXISTS "Organization members can create shared folders" ON public.folders;
DROP POLICY IF EXISTS "Organization members can update shared folders" ON public.folders;
DROP POLICY IF EXISTS "Organization owners can delete shared folders" ON public.folders;

-- Política principal: Ver carpetas propias y compartidas de organizaciones
CREATE POLICY "Users can view their own folders and shared ones"
    ON public.folders
    FOR SELECT
    TO authenticated
    USING (
        -- Carpetas propias del usuario
        user_id = auth.uid() 
        OR (
            -- Carpetas compartidas de organizaciones donde es miembro
            is_shared = true 
            AND organization_id IS NOT NULL 
            AND EXISTS (
                SELECT 1 FROM public.organization_members 
                WHERE organization_id = folders.organization_id 
                AND user_id = auth.uid()
            )
        )
    );

-- Crear carpetas (simplificado gracias al trigger automático)
CREATE POLICY "Users can create their own folders"
    ON public.folders
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

-- Crear carpetas organizacionales compartidas
CREATE POLICY "Organization members can create shared folders"
    ON public.folders
    FOR INSERT
    TO authenticated
    WITH CHECK (
        is_shared = true
        AND organization_id IS NOT NULL
        AND user_id = auth.uid()
        AND EXISTS (
            SELECT 1 FROM public.organization_members 
            WHERE organization_id = folders.organization_id 
            AND user_id = auth.uid()
            AND role IN ('owner', 'admin', 'member')
        )
    );

-- Actualizar carpetas propias y organizacionales
CREATE POLICY "Users can update their own folders"
    ON public.folders
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid() 
        AND (
            -- Carpetas personales
            (organization_id IS NULL AND is_shared = false)
            OR
            -- Carpetas organizacionales (creador puede editar)
            (organization_id IS NOT NULL AND is_shared = true)
        )
    )
    WITH CHECK (
        user_id = auth.uid() 
        AND (
            -- Carpetas personales
            (organization_id IS NULL AND is_shared = false)
            OR
            -- Carpetas organizacionales (creador puede editar)
            (organization_id IS NOT NULL AND is_shared = true)
        )
    );

-- Actualizar carpetas organizacionales (solo admins y owners)
CREATE POLICY "Organization members can update shared folders"
    ON public.folders
    FOR UPDATE
    TO authenticated
    USING (
        is_shared = true
        AND organization_id IS NOT NULL
        AND EXISTS (
            SELECT 1 FROM public.organization_members 
            WHERE organization_id = folders.organization_id 
            AND user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    )
    WITH CHECK (
        is_shared = true
        AND organization_id IS NOT NULL
        AND EXISTS (
            SELECT 1 FROM public.organization_members 
            WHERE organization_id = folders.organization_id 
            AND user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    );

-- Eliminar carpetas propias y organizacionales
CREATE POLICY "Users can delete their own folders"
    ON public.folders
    FOR DELETE
    TO authenticated
    USING (
        user_id = auth.uid() 
        AND (
            -- Carpetas personales
            (organization_id IS NULL AND is_shared = false)
            OR
            -- Carpetas organizacionales (creador puede eliminar)
            (organization_id IS NOT NULL AND is_shared = true)
        )
    );

-- Eliminar carpetas organizacionales (solo owners)
CREATE POLICY "Organization owners can delete shared folders"
    ON public.folders
    FOR DELETE
    TO authenticated
    USING (
        is_shared = true
        AND organization_id IS NOT NULL
        AND EXISTS (
            SELECT 1 FROM public.organization_members 
            WHERE organization_id = folders.organization_id 
            AND user_id = auth.uid()
            AND role = 'owner'
        )
    );

-- =====================================================
-- FUNCIONES ADICIONALES
-- =====================================================

-- Función para procesar solicitudes de unión
CREATE OR REPLACE FUNCTION process_join_request(
    request_id UUID,
    new_status TEXT,
    admin_user_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_organization_id UUID;
    v_user_id UUID;
    v_user_email TEXT;
BEGIN
    -- Verificar que el admin tiene permisos
    IF NOT EXISTS (
        SELECT 1 
        FROM public.organization_join_requests r
        JOIN public.organization_members m ON m.organization_id = r.organization_id
        WHERE r.id = request_id
        AND m.user_id = admin_user_id
        AND m.role IN ('owner', 'admin')
    ) THEN
        RAISE EXCEPTION 'Unauthorized';
    END IF;

    -- Obtener detalles de la solicitud
    SELECT organization_id, user_id, user_email
    INTO v_organization_id, v_user_id, v_user_email
    FROM public.organization_join_requests
    WHERE id = request_id;

    -- Actualizar estado de la solicitud
    UPDATE public.organization_join_requests
    SET status = new_status,
        processed_at = NOW(),
        processed_by = admin_user_id
    WHERE id = request_id;

    -- Si se aprueba, agregar como miembro
    IF new_status = 'approved' THEN
        INSERT INTO public.organization_members (
            organization_id,
            user_id,
            user_email,
            role
        )
        VALUES (
            v_organization_id,
            v_user_id,
            v_user_email,
            'member'
        )
        ON CONFLICT (organization_id, user_id) DO NOTHING;
    END IF;
END;
$$;

-- Función para unirse a organización por código
CREATE OR REPLACE FUNCTION join_organization_by_code(
    p_invite_code UUID,
    p_user_id UUID,
    p_user_email TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_organization_id UUID;
BEGIN
    -- Buscar la organización por código de invitación
    SELECT id INTO v_organization_id
    FROM public.organizations
    WHERE invite_code = p_invite_code;

    IF v_organization_id IS NULL THEN
        RAISE EXCEPTION 'Invalid invite code';
    END IF;

    -- Verificar que el usuario no sea ya miembro
    IF EXISTS (
        SELECT 1 FROM public.organization_members
        WHERE organization_id = v_organization_id
        AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'User is already a member';
    END IF;

    -- Agregar como miembro directamente
    INSERT INTO public.organization_members (
        organization_id,
        user_id,
        user_email,
        role
    )
    VALUES (
        v_organization_id,
        p_user_id,
        p_user_email,
        'member'
    );
END;
$$;

-- Función RPC para limpiar estudios huérfanos
CREATE OR REPLACE FUNCTION rpc_clean_orphaned_studies(org_id UUID)
RETURNS INT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    cleaned_count INT := 0;
    orphaned_user_ids UUID[];
BEGIN
    -- Verificar que el usuario actual es propietario de la organización
    IF NOT EXISTS (
        SELECT 1 FROM public.organization_members
        WHERE organization_id = org_id
        AND user_id = auth.uid()
        AND role = 'owner'
    ) THEN
        RAISE EXCEPTION 'Solo los propietarios de la organización pueden limpiar estudios huérfanos';
    END IF;
    
    -- Encontrar usuarios que tienen estudios compartidos con la organización pero ya no son miembros
    SELECT ARRAY_AGG(DISTINCT s.user_id)
    INTO orphaned_user_ids
    FROM public.studies s
    WHERE s.organization_id = org_id
    AND s.is_shared = true
    AND NOT EXISTS (
        SELECT 1 FROM public.organization_members om
        WHERE om.organization_id = org_id
        AND om.user_id = s.user_id
    );
    
    -- Si hay usuarios con estudios huérfanos, actualizar esos estudios
    IF orphaned_user_ids IS NOT NULL AND array_length(orphaned_user_ids, 1) > 0 THEN
        WITH updated_studies AS (
            UPDATE public.studies
            SET organization_id = NULL, 
                is_shared = false
            WHERE organization_id = org_id
            AND user_id = ANY(orphaned_user_ids)
            RETURNING *
        )
        SELECT COUNT(*) INTO cleaned_count FROM updated_studies;
    END IF;
    
    RETURN cleaned_count;
END;
$$;

-- Función para limpiar estudios cuando un miembro es eliminado
CREATE OR REPLACE FUNCTION clean_member_studies_on_removal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Actualizar los estudios del miembro eliminado
    UPDATE public.studies
    SET organization_id = NULL,
        is_shared = false
    WHERE user_id = OLD.user_id
    AND organization_id = OLD.organization_id;
    
    RETURN OLD;
END;
$$;

-- Función para limpiar solicitudes cuando un miembro es eliminado
CREATE OR REPLACE FUNCTION clean_join_requests_on_member_removal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Eliminar todas las solicitudes de unión pendientes del usuario para esta organización
    DELETE FROM public.organization_join_requests
    WHERE user_id = OLD.user_id
    AND organization_id = OLD.organization_id;
    
    RETURN OLD;
END;
$$;

-- =====================================================
-- FUNCIONES PARA SINCRONIZACIÓN DE CARPETAS Y ESTUDIOS
-- =====================================================

-- Función para auto-asignar organización y compartir carpetas
CREATE OR REPLACE FUNCTION auto_assign_organization_to_folder()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_org_id UUID;
BEGIN
    -- Si no se especificó organization_id, buscar la organización del usuario
    IF NEW.organization_id IS NULL THEN
        SELECT organization_id INTO user_org_id
        FROM public.organization_members 
        WHERE user_id = NEW.user_id 
        LIMIT 1;
        
        -- Si el usuario pertenece a una organización, asignarla y compartir
        IF user_org_id IS NOT NULL THEN
            NEW.organization_id := user_org_id;
            NEW.is_shared := true;
        ELSE
            -- Si no pertenece a organización, carpeta personal privada
            NEW.organization_id := NULL;
            NEW.is_shared := false;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Función para auto-compartir estudios cuando se mueven a carpetas compartidas
CREATE OR REPLACE FUNCTION auto_share_study_in_shared_folder()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    folder_org_id UUID;
    folder_is_shared BOOLEAN;
BEGIN
    -- Si se asigna o cambia folder_id
    IF NEW.folder_id IS NOT NULL AND (OLD.folder_id IS NULL OR OLD.folder_id != NEW.folder_id) THEN
        -- Obtener información de la carpeta
        SELECT organization_id, is_shared 
        INTO folder_org_id, folder_is_shared
        FROM public.folders 
        WHERE id = NEW.folder_id;
        
        -- Si la carpeta es compartida en una organización
        IF folder_is_shared = true AND folder_org_id IS NOT NULL THEN
            -- Auto-compartir el estudio con la organización
            NEW.organization_id := folder_org_id;
            NEW.is_shared := true;
        END IF;
    END IF;
    
    -- Si se quita de carpeta (folder_id se pone NULL)
    IF NEW.folder_id IS NULL AND OLD.folder_id IS NOT NULL THEN
        -- Obtener información de la carpeta anterior
        SELECT organization_id, is_shared 
        INTO folder_org_id, folder_is_shared
        FROM public.folders 
        WHERE id = OLD.folder_id;
        
        -- Si estaba en carpeta compartida, dejar de compartir
        IF folder_is_shared = true AND folder_org_id IS NOT NULL THEN
            NEW.organization_id := NULL;
            NEW.is_shared := false;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Función para mover estudio a carpeta compartida con verificación de permisos
CREATE OR REPLACE FUNCTION move_study_to_shared_folder(
    p_study_id UUID,
    p_folder_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    study_user_id UUID;
    folder_org_id UUID;
    folder_is_shared BOOLEAN;
    folder_user_id UUID;
    result JSON;
BEGIN
    -- Verificar que el usuario puede mover el estudio
    SELECT user_id INTO study_user_id
    FROM public.studies
    WHERE id = p_study_id;
    
    IF study_user_id != auth.uid() THEN
        RAISE EXCEPTION 'Solo puedes mover tus propios estudios';
    END IF;
    
    -- Obtener información de la carpeta destino
    SELECT organization_id, is_shared, user_id
    INTO folder_org_id, folder_is_shared, folder_user_id
    FROM public.folders
    WHERE id = p_folder_id;
    
    -- Verificar permisos sobre la carpeta
    IF folder_is_shared = true THEN
        -- Carpeta compartida: debe ser miembro de la organización
        IF NOT EXISTS (
            SELECT 1 FROM public.organization_members
            WHERE organization_id = folder_org_id
            AND user_id = auth.uid()
        ) THEN
            RAISE EXCEPTION 'No tienes permisos para usar esta carpeta organizacional';
        END IF;
    ELSE
        -- Carpeta personal: debe ser del usuario
        IF folder_user_id != auth.uid() THEN
            RAISE EXCEPTION 'No puedes mover estudios a carpetas personales de otros usuarios';
        END IF;
    END IF;
    
    -- Mover el estudio
    UPDATE public.studies
    SET 
        folder_id = p_folder_id,
        organization_id = CASE 
            WHEN folder_is_shared = true THEN folder_org_id 
            ELSE NULL 
        END,
        is_shared = folder_is_shared
    WHERE id = p_study_id;
    
    -- Retornar resultado
    SELECT json_build_object(
        'success', true,
        'study_id', p_study_id,
        'folder_id', p_folder_id,
        'is_shared', folder_is_shared,
        'organization_id', folder_org_id
    ) INTO result;
    
    RETURN result;
END;
$$;

-- =====================================================
-- FUNCIONES PARA USER_STUDY_LIMITS
-- =====================================================

-- Función para actualizar créditos de usuario
CREATE OR REPLACE FUNCTION update_user_credits(
  p_user_id UUID,
  p_user_email TEXT,
  p_extra_credits INTEGER
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result JSON;
BEGIN
  -- Actualizar o insertar el registro
  INSERT INTO public.user_study_limits (
    user_id,
    user_email,
    monthly_credits,
    extra_credits,
    used_monthly_credits,
    used_extra_credits,
    reset_date,
    created_at,
    updated_at
  )
  VALUES (
    p_user_id,
    p_user_email,
    1, -- créditos mensuales por defecto
    p_extra_credits,
    0,
    0,
    date_trunc('month', current_date)::date,
    current_timestamp,
    current_timestamp
  )
  ON CONFLICT (user_id) DO UPDATE
  SET
    extra_credits = p_extra_credits,
    updated_at = current_timestamp
  RETURNING json_build_object(
    'user_id', user_id,
    'extra_credits', extra_credits,
    'monthly_credits', monthly_credits,
    'used_extra_credits', used_extra_credits,
    'used_monthly_credits', used_monthly_credits,
    'reset_date', reset_date
  ) INTO v_result;

  RETURN v_result;
END;
$$;

-- Función para reiniciar créditos mensuales
CREATE OR REPLACE FUNCTION reset_monthly_credits()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Reiniciar los créditos usados si estamos en un mes posterior al último reinicio
  UPDATE public.user_study_limits
  SET 
    used_monthly_credits = 0,
    reset_date = date_trunc('month', current_date)::date,
    updated_at = current_timestamp
  WHERE date_trunc('month', current_date) > date_trunc('month', reset_date);
END;
$$;

-- Función trigger para verificar reinicio automático de créditos
CREATE OR REPLACE FUNCTION check_reset_credits()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Reiniciar los créditos usados si estamos en un mes posterior al último reinicio
  IF date_trunc('month', current_date) > date_trunc('month', NEW.reset_date) THEN
    NEW.used_monthly_credits := 0;
    NEW.reset_date := date_trunc('month', current_date)::date;
    NEW.updated_at := current_timestamp;
  END IF;
  RETURN NEW;
END;
$$;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Triggers para updated_at
DROP TRIGGER IF EXISTS update_user_study_limits_updated_at ON public.user_study_limits;
CREATE TRIGGER update_user_study_limits_updated_at
    BEFORE UPDATE ON public.user_study_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para reinicio automático de créditos
DROP TRIGGER IF EXISTS reset_credits_trigger ON public.user_study_limits;
CREATE TRIGGER reset_credits_trigger
    BEFORE UPDATE ON public.user_study_limits
    FOR EACH ROW
    EXECUTE FUNCTION check_reset_credits();

DROP TRIGGER IF EXISTS update_library_elements_updated_at ON public.library_elements;
CREATE TRIGGER update_library_elements_updated_at
    BEFORE UPDATE ON public.library_elements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_studies_updated_at ON public.studies;
CREATE TRIGGER update_studies_updated_at
    BEFORE UPDATE ON public.studies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_devices_updated_at ON public.user_devices;
CREATE TRIGGER update_user_devices_updated_at
    BEFORE UPDATE ON public.user_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_organizations_updated_at ON public.organizations;
CREATE TRIGGER update_organizations_updated_at
    BEFORE UPDATE ON public.organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Triggers adicionales para limpieza automática
DROP TRIGGER IF EXISTS clean_studies_on_member_removal ON public.organization_members;
CREATE TRIGGER clean_studies_on_member_removal
    AFTER DELETE ON public.organization_members
    FOR EACH ROW
    EXECUTE FUNCTION clean_member_studies_on_removal();

DROP TRIGGER IF EXISTS clean_join_requests_on_member_removal ON public.organization_members;
CREATE TRIGGER clean_join_requests_on_member_removal
    AFTER DELETE ON public.organization_members
    FOR EACH ROW
    EXECUTE FUNCTION clean_join_requests_on_member_removal();

-- Triggers para carpetas
DROP TRIGGER IF EXISTS update_folders_updated_at ON public.folders;
CREATE TRIGGER update_folders_updated_at
    BEFORE UPDATE ON public.folders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para auto-asignación de organización a carpetas
DROP TRIGGER IF EXISTS auto_assign_org_trigger ON public.folders;
CREATE TRIGGER auto_assign_org_trigger
    BEFORE INSERT ON public.folders
    FOR EACH ROW
    EXECUTE FUNCTION auto_assign_organization_to_folder();

-- Triggers para auto-compartir estudios en carpetas compartidas
DROP TRIGGER IF EXISTS auto_share_study_trigger ON public.studies;
CREATE TRIGGER auto_share_study_trigger
    BEFORE UPDATE ON public.studies
    FOR EACH ROW
    EXECUTE FUNCTION auto_share_study_in_shared_folder();

DROP TRIGGER IF EXISTS auto_share_study_insert_trigger ON public.studies;
CREATE TRIGGER auto_share_study_insert_trigger
    BEFORE INSERT ON public.studies
    FOR EACH ROW
    EXECUTE FUNCTION auto_share_study_in_shared_folder();

-- Triggers para tablas adicionales
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- PERMISOS
-- =====================================================

-- Permisos para webhook_logs
GRANT ALL ON public.webhook_logs TO service_role;
GRANT SELECT ON public.webhook_logs TO authenticated;

-- Permisos para profiles
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- =====================================================
-- ACTUALIZACIÓN DE DATOS EXISTENTES
-- =====================================================

-- Actualizar el constraint de folders para ser más flexible
ALTER TABLE public.folders DROP CONSTRAINT IF EXISTS folder_ownership_check;
ALTER TABLE public.folders 
ADD CONSTRAINT folder_ownership_check CHECK (
    -- Carpetas personales: user_id requerido, organization_id opcional
    (user_id IS NOT NULL) AND
    -- Si tiene organization_id, debe ser compartida
    (organization_id IS NULL OR is_shared = true)
);

-- Actualizar carpetas existentes para que sigan el nuevo patrón automático
UPDATE public.folders 
SET 
    organization_id = (
        SELECT organization_id 
        FROM public.organization_members 
        WHERE user_id = folders.user_id 
        LIMIT 1
    ),
    is_shared = CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.organization_members 
            WHERE user_id = folders.user_id
        ) THEN true 
        ELSE false 
    END
WHERE organization_id IS NULL;

-- Sincronizar estudios existentes en carpetas compartidas
UPDATE public.studies 
SET 
    organization_id = f.organization_id,
    is_shared = f.is_shared
FROM public.folders f
WHERE studies.folder_id = f.id 
AND f.is_shared = true 
AND f.organization_id IS NOT NULL
AND (studies.organization_id IS NULL OR studies.organization_id != f.organization_id);

-- =====================================================
-- COMENTARIOS DOCUMENTACIÓN
-- =====================================================

COMMENT ON TABLE public.user_study_limits IS 'Stores user subscription and credit information with Stripe integration';
COMMENT ON TABLE public.library_elements IS 'Stores reusable elements for time studies';
COMMENT ON TABLE public.studies IS 'Stores time study data with JSON structure';
COMMENT ON TABLE public.user_devices IS 'Stores registered user devices';
COMMENT ON TABLE public.organizations IS 'Organizations for sharing studies and collaboration';
COMMENT ON TABLE public.organization_members IS 'Members of organizations with roles';
COMMENT ON TABLE public.organization_join_requests IS 'Requests to join organizations';
COMMENT ON TABLE public.profiles IS 'User profiles with email and logo information';
COMMENT ON TABLE public.folders IS 'Hierarchical folder structure for organizing studies, supports both personal and organizational folders';

-- Comentarios en columnas específicas
COMMENT ON COLUMN public.studies.elements IS 'Array of study elements with timing data';
COMMENT ON COLUMN public.studies.time_records IS 'Object mapping element IDs to time records';
COMMENT ON COLUMN public.studies.supplements IS 'Supplementary study data';
COMMENT ON COLUMN public.studies.crono_seguido_records IS 'Array of continuous chronometer records';
COMMENT ON COLUMN public.studies.folder_id IS 'Reference to the folder containing this study';
COMMENT ON COLUMN public.folders.organization_id IS 'Organization ID for shared folders, NULL for personal folders';
COMMENT ON COLUMN public.folders.is_shared IS 'Whether this folder is shared with organization members';
COMMENT ON COLUMN public.folders.parent_folder_id IS 'Parent folder for hierarchical structure';

-- Comentarios en funciones de sincronización
COMMENT ON FUNCTION auto_assign_organization_to_folder() IS 'Automatically assigns organization and sharing status to folders based on user membership';
COMMENT ON FUNCTION auto_share_study_in_shared_folder() IS 'Automatically shares studies when moved to shared organizational folders';
COMMENT ON FUNCTION move_study_to_shared_folder(UUID, UUID) IS 'Moves a study to a folder with proper permission checks and automatic sharing';

-- =====================================================
-- VERIFICACIÓN FINAL
-- =====================================================

DO $$
DECLARE
    user_limits_count INTEGER;
    folders_count INTEGER;
    studies_count INTEGER;
    organizations_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_limits_count FROM public.user_study_limits;
    SELECT COUNT(*) INTO folders_count FROM public.folders;
    SELECT COUNT(*) INTO studies_count FROM public.studies;
    SELECT COUNT(*) INTO organizations_count FROM public.organizations;
    
    RAISE NOTICE '=== ESTADÍSTICAS DE MIGRACIÓN ===';
    RAISE NOTICE '👥 Límites de usuario: %', user_limits_count;
    RAISE NOTICE '📁 Carpetas: %', folders_count;
    RAISE NOTICE '📊 Estudios: %', studies_count;
    RAISE NOTICE '🏢 Organizaciones: %', organizations_count;
    RAISE NOTICE '✅ Sistema de carpetas listo para usar';
    RAISE NOTICE '✅ Políticas RLS configuradas';
    RAISE NOTICE '✅ Triggers y funciones instalados';
    RAISE NOTICE '=== MIGRACIÓN COMPLETADA ===';
END $$;

/*
=======================================================================
✅ MIGRACIÓN COMPLETADA EXITOSAMENTE
=======================================================================

🔧 CORRECCIONES INCLUIDAS DESDE stadler-migration-simple.sql y stadler-migration-fixed.sql:
✅ Eliminación segura de constraints con IF EXISTS
✅ Corrección de datos inconsistentes antes de agregar constraints  
✅ Manejo de valores NULL en user_study_limits
✅ Verificación robusta de datos antes de constraints
✅ Manejo de errores y debugging mejorado
✅ Sistema de carpetas jerárquicas completo
✅ Políticas RLS corregidas y funcionales

PRÓXIMOS PASOS:
1. ✅ Estructura de base de datos replicada
2. 🔄 Configurar Edge Functions manualmente
3. 🔄 Configurar variables de entorno
4. 🔄 Configurar webhooks de Stripe
5. 🔄 Actualizar URLs de autenticación

Para desplegar Edge Functions, usa:
supabase functions deploy stripe-webhook
supabase functions deploy stripe-payment
supabase functions deploy stripe-payment-v2
supabase functions deploy stripe-cancel

=======================================================================
*/ 
-- Migración para agregar carpeta por defecto al perfil de usuario
-- Archivo: 20250130_add_default_folder_preference.sql

-- Agregar campo default_folder_id a la tabla profiles
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS default_folder_id UUID REFERENCES public.folders(id) ON DELETE SET NULL;

-- Crear índice para mejorar el rendimiento de consultas
CREATE INDEX IF NOT EXISTS idx_profiles_default_folder ON public.profiles(default_folder_id);

-- Comentario explicativo
COMMENT ON COLUMN public.profiles.default_folder_id IS 'Carpeta que se carga por defecto al abrir la aplicación. NULL significa raíz';
COMMENT ON COLUMN public.profiles.companies_list IS 'Lista de empresas/departamentos del usuario para selección rápida en estudios';
COMMENT ON COLUMN public.profiles.default_company IS 'Empresa/departamento por defecto para nuevos estudios';
-- Migración para agregar gestión de empresas/departamentos
-- Archivo: 20250130_add_companies_management.sql

-- Agregar campos para gestión de empresas/departamentos en perfiles de usuario
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS companies_list JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS default_company TEXT;

-- Agregar comentarios explicativos
COMMENT ON COLUMN public.profiles.companies_list IS 'Lista de empresas/departamentos del usuario para selección rápida en estudios';
COMMENT ON COLUMN public.profiles.default_company IS 'Empresa/departamento por defecto para nuevos estudios';

-- Crear índice para mejorar el rendimiento de consultas en companies_list
CREATE INDEX IF NOT EXISTS idx_profiles_default_company ON public.profiles(default_company);

-- Notificación de migración completada
DO $$
BEGIN
    RAISE NOTICE '✅ Migración completada: Gestión de empresas/departamentos agregada a profiles';
    RAISE NOTICE '📝 Nuevos campos: companies_list (JSONB), default_company (TEXT)';
    RAISE NOTICE '🔍 Índice creado: idx_profiles_default_company';
END $$;