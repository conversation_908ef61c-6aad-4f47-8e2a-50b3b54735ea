import { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userEmail, userId } = req.body;

  if (!userEmail || !userId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  // Configurar el transporte de correo
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER || '<EMAIL>',
      pass: process.env.EMAIL_PASSWORD, // Asegúrate de configurar esta variable de entorno
    },
  });

  // Configurar el email
  const mailOptions = {
    from: process.env.EMAIL_USER || '<EMAIL>',
    to: '<EMAIL>',
    subject: `Cancelar suscripción - ${userEmail}`,
    text: `
      Solicitud de cancelación de suscripción
      
      Email del usuario: ${userEmail}
      ID del usuario: ${userId}
      
      Por favor, procesa esta solicitud de cancelación manualmente.
    `,
    html: `
      <h2>Solicitud de cancelación de suscripción</h2>
      <p><strong>Email del usuario:</strong> ${userEmail}</p>
      <p><strong>ID del usuario:</strong> ${userId}</p>
      <p>Por favor, procesa esta solicitud de cancelación manualmente.</p>
    `,
  };

  try {
    // Enviar el email
    await transporter.sendMail(mailOptions);
    return res.status(200).json({ message: 'Solicitud de cancelación enviada correctamente' });
  } catch (error) {
    console.error('Error al enviar el email:', error);
    return res.status(500).json({ error: 'Error al enviar el email' });
  }
}
