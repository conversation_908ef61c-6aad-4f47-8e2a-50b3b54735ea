let audioContext: AudioContext | null = null;
let currentSound: OscillatorNode | null = null;

// Initialize AudioContext
export const initializeAudioContext = (): AudioContext | null => {
  try {
    if (!audioContext) {
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      console.log('AudioContext initialized successfully.');
    }
    return audioContext;
  } catch (error) {
    console.error('Failed to initialize AudioContext:', error);
    return null;
  }
};

// Stop any currently playing sound
const stopCurrentSound = () => {
  if (currentSound) {
    currentSound.stop();
    currentSound.disconnect();
    currentSound = null;
  }
};

// Play a sound with a specific frequency
export const playSound = async (soundName: 'start' | 'lap' | 'stop') => {
  try {
    const context = audioContext || initializeAudioContext();
    if (!context) {
      console.warn('AudioContext not initialized.');
      return;
    }

    // Stop any currently playing sound
    stopCurrentSound();

    // Resume context if suspended
    if (context.state === 'suspended') {
      console.log('Resuming AudioContext...');
      await context.resume();
    }

    const oscillator = context.createOscillator();
    const gainNode = context.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(context.destination);

    // Configure frequency based on sound type
    let frequency = 440; // Default (A4 note)
    switch (soundName) {
      case 'lap':
        frequency = 880; // A5 note
        break;
      case 'start':
        frequency = 660; // E5 note
        break;
      case 'stop':
        frequency = 440; // A4 note
        break;
    }

    oscillator.frequency.setValueAtTime(frequency, context.currentTime);
    console.log(`Playing sound: ${soundName} at frequency ${frequency}Hz`);

    // Short beep
    gainNode.gain.setValueAtTime(0, context.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.2, context.currentTime + 0.01); // Volume ramp up
    gainNode.gain.linearRampToValueAtTime(0, context.currentTime + 0.1); // Fade out

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + 0.1); // Duration 0.1 seconds
    currentSound = oscillator;

    // Ensure sound is fully stopped after playing
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        stopCurrentSound();
        resolve();
      }, 150); // Wait slightly longer than the sound duration
    });
  } catch (error) {
    console.error('Failed to play sound:', error);
  }
};

// Play a sound and vibrate the device
export const playSoundAndVibrate = async (soundName: 'start' | 'lap' | 'stop', vibratePattern: number | number[]) => {
  await playSound(soundName);
  if (navigator.vibrate) {
    navigator.vibrate(vibratePattern);
    console.log(`Vibration triggered with pattern: ${vibratePattern}`);
  } else {
    console.warn('Vibration API not supported.');
  }
};