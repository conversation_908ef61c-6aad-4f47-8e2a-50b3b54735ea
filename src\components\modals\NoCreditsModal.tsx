import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useCreditStore } from '../../store/creditStore';
import { formatTimeToReset } from '../../utils/dateUtils';

interface NoCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigateToProfile: () => void;
}

export const NoCreditsModal: React.FC<NoCreditsModalProps> = ({ isOpen, onClose, onNavigateToProfile }) => {
  const { t } = useTranslation(['study', 'common']);
  const { studyLimits, fetchUserLimits } = useCreditStore();

  useEffect(() => {
    if (isOpen) {
      fetchUserLimits();
    }
  }, [isOpen, fetchUserLimits]);

  const timeToReset = formatTimeToReset(studyLimits?.reset_date || null);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {t('no_credits.title', { ns: 'study' })}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 text-xl font-bold"
          >
            ×
          </button>
        </div>
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {t('no_credits.message', { ns: 'study' })}
          </p>
          
          {studyLimits && (
            <div className="bg-gray-50 p-4 rounded-md mb-4">
              <p className="text-sm text-gray-600 mb-2">
                {t('credits_info.monthly', { 
                  used: studyLimits.used_monthly_credits,
                  total: studyLimits.monthly_credits,
                  ns: 'study'
                })}
              </p>
              {studyLimits.extra_credits > 0 && (
                <p className="text-sm text-gray-600 mb-2">
                  {t('credits_info.extra', { 
                    used: studyLimits.used_extra_credits,
                    total: studyLimits.extra_credits,
                    ns: 'study'
                  })}
                </p>
              )}
              {timeToReset && (
                <p className="text-sm text-gray-600">
                  {t('credits_info.reset_date', { 
                    time: timeToReset,
                    ns: 'study'
                  })}
                </p>
              )}
            </div>
          )}

          <div className="mt-6 flex justify-end space-x-4">
            <button
              onClick={onNavigateToProfile}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              {t('buy_credits', { ns: 'study' })}
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              {t('close', { ns: 'common' })}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};