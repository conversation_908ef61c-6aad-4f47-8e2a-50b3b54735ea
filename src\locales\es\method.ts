export default {
  title: '<PERSON><PERSON><PERSON><PERSON>',
  noStudySelected: 'No hay estudio seleccionado',
  addElement: 'Agregar Elemento',
  element: 'Elemento',
  new: 'Nuevo elemento',
  edit: 'Editar elemento',
  create: 'Crear elemento',
  update: 'Actualizar elemento',
  delete: 'Eliminar elemento',
  description: 'Descripción',
  descriptionPlaceholder: 'Ingrese la descripción del elemento',
  type: 'En función de su relación hombre-máquina',
  types: {
    'machine-stopped': 'Máquina Parada',
    'machine-running': 'Máquina en Marcha',
    'machine-time': 'Tiempo de Máquina',
    'machine': 'Máquina',
    'repetitive': 'Repetitivo',
    'frequency': 'Frecuencial',
    'repetitive-type': 'Repetitivo',
    'frequency-type': 'Frecuencial',
    'machine-type': '<PERSON>á<PERSON>a'
  },
  typesLong: {
    'machine-stopped': 'Máquina Parada',
    'machine-running': '<PERSON><PERSON><PERSON><PERSON> en Marcha',
    'machine-time': 'Tiempo de Máquina',
    'machine': 'M<PERSON><PERSON>a',
    'repetitive': 'Repetitivo',
    'frequency': 'Frecuencial'
  },
  repetitionType: 'En función de su regularidad',
  repetitions: 'Repeticiones',
  cycles: 'Ciclos',
  frequency: {
    title: 'Frecuencia',
    repetitions: 'Repeticiones',
    cycles: 'Ciclos',
    each: 'cada'
  },
  observations: 'Observaciones',
  observationsPlaceholder: 'Ingrese las observaciones del elemento',
  saveSuccess: 'Método guardado correctamente',
  saveError: 'Error al guardar el método',
  deleteSuccess: 'Elemento eliminado correctamente',
  deleteError: 'Error al eliminar el elemento',
  confirmDelete: '¿Está seguro de que desea eliminar este elemento?',
  noElements: 'No hay elementos disponibles',
  loadError: 'Error al cargar los elementos',
  name: 'Elementos de máquina, {{number}}',
  save: 'Guardar',
  cancel: 'Cancelar',
  dragAndDrop: 'Arrastrar y soltar para reordenar',
  deleteElementTitle: 'Eliminar Elemento',
  deleteElementConfirmation: '¿Está seguro de que desea eliminar este elemento?',
  addToLibrary: 'Agregar a la biblioteca',
  addToLibrarySuccess: 'Elemento agregado a la biblioteca correctamente',
  addToLibraryError: 'Error al agregar el elemento a la biblioteca',
  libraryTitle: 'Biblioteca de elementos',
  speech: {
    listening: 'Escuchando...',
    pressToSpeak: 'Presionar para hablar',
    noSpeech: 'No se detectó voz',
    noMicrophone: 'No se detectó micrófono',
    notAllowed: 'Acceso al micrófono denegado',
    error: 'Error al reconocer la voz'
  },
  selectFromLibrary: 'Seleccionar desde la biblioteca',
  selectElements: 'Seleccionar elementos'
 

  
};
