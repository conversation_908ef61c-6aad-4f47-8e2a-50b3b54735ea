import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { LibraryElement } from '../types';

interface LibraryState {
  elements: LibraryElement[];
  isLoading: boolean;
  error: string | null;
  fetchElements: () => Promise<void>;
  createElement: (element: Partial<LibraryElement>) => Promise<void>;
  updateElement: (id: string, element: Partial<LibraryElement>) => Promise<void>;
  deleteElement: (id: string) => Promise<void>;
  toggleShare: (element: LibraryElement) => Promise<void>;
}

export const useLibraryStore = create<LibraryState>((set, get) => ({
  elements: [],
  isLoading: false,
  error: null,

  fetchElements: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      // Get all organization memberships for the user
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        throw membershipError;
      }

      let query = supabase
        .from('library_elements')
        .select('*');

      if (memberships && memberships.length > 0) {
        // Find organizations where user is admin/owner
        const adminOrgIds = memberships
          .filter(m => m.role === 'admin' || m.role === 'owner')
          .map(m => m.organization_id);

        if (adminOrgIds.length > 0) {
          // Get all members of organizations where user is admin/owner
          const { data: orgMembers, error: membersError } = await supabase
            .from('organization_members')
            .select('user_id')
            .in('organization_id', adminOrgIds);

          if (membersError) {
            console.error('Error fetching org members:', membersError);
            throw membersError;
          }

          const memberIds = orgMembers?.map(m => m.user_id) || [];
          // Include elements from user and org members where user is admin/owner
          query = query.in('user_id', [user.id, ...memberIds]);
        } else {
          // If user is only a regular member, only get their own elements
          query = query.eq('user_id', user.id);
        }
      } else {
        // If not in any organization, only get user's own elements
        query = query.eq('user_id', user.id);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;

      set({ elements: data, isLoading: false });
    } catch (error) {
      console.error('Error fetching library elements:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  createElement: async (element: Partial<LibraryElement>) => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('library_elements')
        .insert({
          user_id: user.id,
          description: element.description,
          type: element.type,
          frequency_repetitions: element.frequency_repetitions,
          frequency_cycles: element.frequency_cycles,
          repetition_type: element.repetition_type,
          is_shared: element.is_shared
        })
        .select()
        .single();

      if (error) throw error;

      set(state => ({
        elements: [data, ...state.elements],
        isLoading: false
      }));
    } catch (error) {
      console.error('Error creating library element:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  updateElement: async (id: string, element: Partial<LibraryElement>) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('library_elements')
        .update({
          description: element.description,
          type: element.type,
          frequency_repetitions: element.frequency_repetitions,
          frequency_cycles: element.frequency_cycles,
          repetition_type: element.repetition_type,
          is_shared: element.is_shared
        })
        .eq('id', id);

      if (error) throw error;

      set(state => ({
        elements: state.elements.map(e =>
          e.id === id ? { ...e, ...element } : e
        ),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error updating library element:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  deleteElement: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('library_elements')
        .delete()
        .eq('id', id);

      if (error) throw error;

      set(state => ({
        elements: state.elements.filter(e => e.id !== id),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error deleting library element:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  toggleShare: async (element: LibraryElement) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('library_elements')
        .update({ is_shared: !element.is_shared })
        .eq('id', element.id);

      if (error) throw error;

      set(state => ({
        elements: state.elements.map(e =>
          e.id === element.id ? { ...e, is_shared: !e.is_shared } : e
        ),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error toggling share status:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  }
}));