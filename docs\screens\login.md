# Pantalla de Inicio de Sesión

## Descripción
Pantalla inicial que gestiona la autenticación de usuarios y el control de licencias por dispositivo.

## Componentes
- `LoginForm`: Formulario principal de inicio de sesión
- `Header`: Encabezado con logo de la aplicación

## Funcionalidades

### 1. Autenticación
- Validación de credenciales contra Supabase Auth
- Gestión de errores de autenticación
- Redirección automática tras inicio de sesión exitoso

### 2. Control de Licencias
- Verificación de dispositivo registrado
- Registro automático del primer dispositivo
- Bloqueo de acceso desde dispositivos no autorizados

## Estados
```typescript
{
  email: string;
  password: string;
  isLoading: boolean;
  error: string | null;
}
```

## Flujo de Trabajo
1. Usuario ingresa credenciales
2. Sistema valida credenciales con Supabase
3. Si las credenciales son válidas:
   - Verifica si existe dispositivo registrado
   - Si no existe, registra el dispositivo actual
   - Si existe, verifica que sea el dispositivo actual
4. Redirecciona a la pantalla principal o muestra error