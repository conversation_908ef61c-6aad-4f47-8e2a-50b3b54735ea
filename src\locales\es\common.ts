export default {
  appName: 'Cronometras',
  pwa: {
    installPrompt: '¡Instala la aplicación para un mejor rendimiento!',
    install: 'Instalar',
    hideBanner: 'Ocultar notificación'
  },
  search: {
    title: "Buscar estudios",
    placeholder: "Buscar",
    button: "Buscar",
    search: "Buscar estudios",
    clear: "Limpiar búsqueda",
    modal: {
      title: "Buscar estudios",
      subtitle: "Encontrar estudios por nombre, empresa, proceso u operador",
      searchPlaceholder: "Buscar por palabra clave",
      filters: "Filtros",
      clearFilters: "Limpiar filtros",
      noResults: "No se encontraron resultados",
      loadingResults: "Cargando resultados..."
    }
  },
  decreaseActivity: 'Disminuir actividad',
  increaseActivity: 'Aumentar actividad',
  addComment: 'Añadir comentario',
  save: 'Guardar',
  create: 'Crear',
  cancel: 'Cancelar',
  select: 'Seleccionar',
  delete: 'Eliminar',
  edit: 'Editar',
  back: 'Volver',
  backToHome: 'Volver al inicio',
  saving: 'Guardando...',
  loading: 'Cargando...',
  retry: 'Reintentar',
  refresh: 'Actualizar',
  refreshing: 'Actualizando...',
  deleteConfirmation: '¿Está seguro de que desea eliminar este elemento?',
  confirmDelete: 'Confirmar Eliminación',
  each: 'cada',
  apply: 'Aplicar',
  reset: 'Restablecer',
  close: 'Cerrar',
  add: 'Añadir',
  filter: 'Filtrar',
  error: 'Error',
  since: 'Desde',
  you: 'Tú',
  notifications: 'Notificaciones',
  confirm: 'Confirmar',
  next: 'Siguiente',
  previous: 'Anterior',
  changelog: {
    title: 'Documentación',
    tabs: {
      changelog: 'Registro de cambios',
      security: 'Seguridad',
      faq: 'Preguntas frecuentes'
    }
  },
  version: 'Versión',
  changeTypes: {
    added: 'Añadido',
    changed: 'Cambiado',
    fixed: 'Corregido',
    updated: 'Actualizado',
    removed: 'Eliminado'
  },
  subscription: {
    active: 'Activa',
    cancel: 'Cancelar',
    inactive: 'Inactiva',
    request: 'Solicitar suscripción',
  },
  units: {
    seconds: 'Segundos',
    minutes: 'Minutos',
    hours: 'horas',
    mmm: 'MMM (Minutos Medidos y Modificados)',
    cmm: 'CMM (Centésimas de Minuto)',
    tmu: 'TMU (Time Measurement Unit)',
    dmh: 'DMH (Diezmilésimas de Hora)',
    currency: '$',
  },

  tal: {
    A1: {
      description: {
        Reducido: 'Actividad nula, el operario parece medio dormido y sin interés en el trabajo',
        Mediano: 'Actividad normal, el operario parece lento pero no pierde tiempo adrede',
        Intenso: 'Actividad normal, el operario tiene buena concentración'
      }
    },
    A2: {
      description: 'Actividad intensiva, el operario tiene muy buena concentración'
    },
    A3: {
      description: 'Actividad muy intensiva, el operario tiene velocidad y destreza'
    },
    A4: {
      description: 'Actividad excepcional, el operario muestra velocidad y precisión extraordinarias'
    },
    A5: {
      description: 'Actividad excepcional sostenida, el operario mantiene un ritmo muy alto'
    },
    B1: {
      description: 'Esfuerzo nulo, el operario no muestra esfuerzo físico'
    },
    B2: {
      description: 'Esfuerzo bajo, el operario muestra poco esfuerzo físico'
    },
    B3: {
      description: 'Esfuerzo normal, el operario muestra un esfuerzo físico moderado'
    },
    B4: {
      description: 'Esfuerzo alto, el operario muestra un esfuerzo físico considerable'
    },
    C1: {
      description: 'Condiciones ideales, ambiente perfecto sin molestias'
    },
    C2: {
      description: 'Condiciones buenas, ambiente agradable con pocas molestias'
    },
    C3: {
      description: 'Condiciones normales, ambiente típico de trabajo'
    },
    C4: {
      description: 'Condiciones regulares, ambiente con algunas molestias'
    },
    C5: {
      description: 'Condiciones malas, ambiente con molestias frecuentes'
    },
    C6: {
      description: 'Condiciones pésimas, ambiente muy desfavorable'
    }
  },
  termsAndConditions: 'Términos y Condiciones',
  accept: 'Aceptar',
  decline: 'Rechazar',
  privacyPolicy: 'Política de Privacidad',
  continueWithGoogle: 'Continuar con Google',
  signIn: 'Iniciar sesión',
  signUp: 'Registrarse',
  email: 'Correo electrónico',
  password: 'Contraseña',
  dontHaveAccount: '¿No tienes una cuenta?',
  alreadyHaveAccount: '¿Ya tienes una cuenta?',
  noStudySelected: 'No hay ningún estudio seleccionado',
  errors: {
    savingRecord: 'Error al guardar el registro',
    updatingRecord: 'Error al actualizar el registro',
    deletingRecord: 'Error al eliminar el registro',
    deletingAllRecords: 'Error al eliminar todos los registros',
    loadingStudy: 'Error al cargar el estudio',
    savingStudy: 'Error al guardar el estudio',
    deletingStudy: 'Error al eliminar el estudio',
    noSession: 'No hay sesión activa',
    createOrganization: 'Error al crear la organización',
    joinOrganization: 'Ha ocurrido un error al unirse a la organización',
    fetchMembers: 'Error al obtener los miembros',
    fetchRequests: 'Error al obtener las solicitudes',
    processRequest: 'Error al procesar la solicitud',
    refreshFailed: 'Error al actualizar',
    generic: 'Ha ocurrido un error',
    unauthorized: 'No tienes permisos para realizar esta acción',
    notFound: 'No se ha encontrado el recurso solicitado',
    serverError: 'Error del servidor',
    networkError: 'Error de red',
    validationError: 'Error de validación',
  },
  auth: {
    login: {
      forgotPassword: '¿Olvidaste tu contraseña?',
    },
    resetPassword: {
      title: 'Restablecer contraseña',
      description: 'Ingresa tu correo electrónico y te enviaremos un enlace para restablecer tu contraseña.',
      submit: 'Enviar enlace',
      success: 'Se ha enviado un enlace a tu correo electrónico.',
      error: 'Ha ocurrido un error. Por favor, inténtalo de nuevo.',
      backToLogin: 'Volver al inicio de sesión'
    },
    updatePassword: {
      title: 'Actualizar contraseña',
      description: 'Ingresa tu nueva contraseña.',
      submit: 'Actualizar contraseña',
      success: 'Tu contraseña ha sido actualizada correctamente.',
      error: 'Ha ocurrido un error. Por favor, inténtalo de nuevo.',
      passwordMismatch: 'Las contraseñas no coinciden.'
    }
  },
  selected: 'Seleccionado',
  name: 'Nombre',
  description: 'Descripción',
  date: 'Fecha',
  time: 'Tiempo',
  status: 'Estado',
  type: 'Tipo',
  category: 'Categoría',
  notes: 'Notas',
  comments: 'Comentarios',
  actions: 'Acciones',
  details: 'Detalles',
  summary: 'Resumen',
  total: 'Total',
  average: 'Promedio',
  minimum: 'Mínimo',
  maximum: 'Máximo',
  count: 'Cantidad',
  all: 'Todos',
  clearAll: 'Limpiar todo',
  untitledStudy: 'Estudio sin título',
  clearFilters: 'Limpiar filtros',
  creating: 'Creando...',
  nameRequired: 'El nombre es requerido',
  none: 'Ninguno',
  other: 'Otro',
  custom: 'Personalizado',
  position: 'Posición',
  averageActivity: 'Actividad promedio',
  mainMachineTime: 'Tiempo de máquina principal',
  noMachineTimeElements: 'No hay elementos de tiempo de máquina',
  concurrentMachineTime: 'Tiempo concurrente',
  convertToMachineStopped: 'Convertir a máquina parada',
  convertToRunningMachine: 'Convertir a máquina en marcha',
  convertToRunningMachineDescription: 'Este elemento se convertirá en un elemento de máquina en marcha repetitivo y se eliminará de la lista de tiempos de máquina. ¿Estás seguro?',
  authSignIn: 'Iniciar sesión',
  authSignOut: 'Cerrar sesión',
  authSignUp: 'Registrarse',
  authEmail: 'Correo electrónico',
  authPassword: 'Contraseña',
  authNewPassword: 'Nueva contraseña',
  authConfirmPassword: 'Confirmar contraseña',
  authForgotPassword: 'Olvidé mi contraseña',
  authResetPassword: 'Restablecer contraseña',
  authChangePassword: 'Cambiar contraseña',
  authProfile: 'Perfil',
  authSettings: 'Configuración',
  errorsRequired: 'Campo requerido',
  errorsInvalid: 'Campo inválido',
  errorsMinLength: 'Mínimo {{min}} caracteres',
  errorsMaxLength: 'Máximo {{max}} caracteres',
  errorsEmail: 'Correo electrónico inválido',
  errorsPassword: 'Contraseña inválida',
  errorsPasswordMatch: 'Las contraseñas no coinciden',
  errorsGeneric: 'Ha ocurrido un error',
  no_creditsTitle: "Sin Créditos Disponibles",
  no_creditsDescription: "No tienes créditos disponibles para realizar esta acción.",
  no_creditsAction: "Comprar Créditos",
  profile: {
    basicInfo: 'Información básica',
    email: 'Correo electrónico',
    companyName: 'Nombre de la empresa',
    companyLogo: 'Logo de la empresa',
  },
  preferences: {
    title: 'Preferencias',
    defaultTimeUnit: 'Unidad de tiempo predeterminada',
    defaultLanguage: 'Idioma predeterminado',
    defaultContingency: 'Porcentaje de contingencias predeterminado',
    minutesPerShift: 'Minutos por turno',
    updateSuccess: 'Preferencias actualizadas',
    updateSuccessMessage: 'Tus preferencias han sido actualizadas correctamente',
    updateError: 'Error al actualizar',
    updateErrorMessage: 'Ha ocurrido un error al actualizar tus preferencias'
  },
  Organizaciones: 'Organizaciones',
  'Mis organizaciones': 'Mis organizaciones',
  'Crear organización': 'Crear organización',
  'Unirse a organización': 'Unirse a organización',
  'No perteneces a ninguna organización': 'No perteneces a ninguna organización',
  'Invitar a organización': 'Invitar a organización',
  'Crear nueva organización': 'Crear nueva organización',
  'Organización creada': 'Organización creada correctamente',
  'Error al crear organización': 'Error al crear la organización',
  'Invitación enviada': 'Invitación enviada correctamente',
  'Error al enviar invitación': 'Error al enviar la invitación',
  'Invitación aceptada': 'Te has unido a la organización correctamente',
  'Error al aceptar invitación': 'Error al aceptar la invitación',
  'Salir de organización': 'Salir de organización',
  'Confirmar salida': '¿Estás seguro de que deseas salir de esta organización?',
  'Has salido de la organización': 'Has salido de la organización correctamente',
  'Error al salir de organización': 'Error al salir de la organización',
  'Propietario': 'Propietario',
  'Administrador': 'Administrador',
  'Miembro': 'Miembro',
  'Te han eliminado de la organización': 'Has sido eliminado de la organización',
  'Usuario eliminado': 'Usuario eliminado de la organización correctamente',
  'Error al eliminar usuario': 'Error al eliminar el usuario de la organización',
  'No puedes eliminar al propietario': 'No puedes eliminar al propietario de la organización',
  'No puedes eliminar a otro administrador': 'No puedes eliminar a otro administrador',
  organizations: {
    title: 'Organizaciones',
    myOrganizations: 'Mis organizaciones',
    createAndManage: 'Crea y gestiona tus organizaciones',
    teamMembers: 'Miembros del Equipo',
    manageMembers: 'Administra los miembros de tu organización',
    shareStudiesDescription: 'Comparte tus estudios con los miembros de esta organización',
    create: 'Crear organización',
    join: 'Unirse a organización',
    manage: 'Gestionar',
    noOrganizations: 'No perteneces a ninguna organización',
    name: 'Nombre de la organización',
    description: 'Descripción (opcional)',
    createButton: 'Crear',
    inviteCode: 'Código de invitación',
    copy: 'Copiar',
    viewMembers: 'Ver miembros',
    loading: 'Cargando tus organizaciones...',
    inviteCodeCopied: 'Código copiado',
    inviteCodeCopiedDescription: 'El código de invitación ha sido copiado al portapapeles'
  },
  createOrganization: {
    name: 'Nombre de la organización',
    description: 'Descripción',
    namePlaceholder: 'Introduce el nombre de la organización',
    descriptionPlaceholder: 'Describe brevemente la organización',
    creating: 'Creando...',
    created: 'Organización creada',
    createdDescription: 'La organización se ha creado correctamente. Tu código de invitación es: {{code}}',
  },
  joinOrganization: {
    description: 'Introduce el código de invitación que te han proporcionado para unirte a una organización.',
    inviteCodePlaceholder: 'Introduce el código de invitación',
    sending: 'Enviando solicitud...',
    send: 'Enviar solicitud',
    requestSent: 'Solicitud enviada',
    requestSentDescription: 'Tu solicitud para unirte a la organización ha sido enviada. El administrador deberá aprobarla.',
  },
  members: {
    title: 'Miembros',
    role: 'Rol',
    email: 'Email',
    joinDate: 'Fecha de unión',
    actions: 'Acciones',
    remove: 'Eliminar',
    promote: 'Promover a admin',
    demote: 'Quitar admin',
    owner: 'Propietario',
    admin: 'Administrador',
    member: 'Miembro',
    noMembers: 'No hay miembros en esta organización',
  },
  joinRequests: {
    title: 'Solicitudes de unión',
    pendingTitle: 'Solicitudes pendientes',
    approveRequest: 'Aprobar',
    rejectRequest: 'Rechazar',
    approved: 'Solicitud aprobada',
    approvedDescription: 'La solicitud ha sido aprobada correctamente',
    rejected: 'Solicitud rechazada',
    rejectedDescription: 'La solicitud ha sido rechazada correctamente',
    noRequests: 'No hay solicitudes pendientes',
    requestApproved: 'Solicitud aprobada correctamente',
    requestRejected: 'Solicitud rechazada correctamente',
    errorProcessing: 'Error al procesar la solicitud',
    errorLoading: 'Error al cargar las solicitudes',
    errorLoadingNames: 'Error al cargar los nombres de las organizaciones',
    statusPending: 'Pendiente',
    statusApproved: 'Aprobada',
    statusRejected: 'Rechazada',
    mySentRequests: 'Mis solicitudes enviadas',
    noSentRequests: 'No has enviado ninguna solicitud de unión',
    loadingSentRequests: 'Cargando tus solicitudes enviadas...',
    loadingOrganizationNames: 'Cargando nombres de organizaciones...',
    yourRequest: 'Tu solicitud',
    joinInvitation: '¿Quieres unirte a una organización existente? Pide el código de invitación a un administrador y envía tu solicitud.',
    unknownOrganization: 'Organización desconocida',
    sentAt: 'Enviada {{time}}',
    processedAt: 'Procesada {{time}}',
    sent: 'Enviada:',
    processed: 'Procesada:',
    requestedOn: 'Solicitado el',
    refresh: 'Actualizar',
    refreshSuccess: 'Solicitudes actualizadas',
    refreshing: 'Actualizando...',
    loading: 'Cargando...',
    refreshed: 'Solicitudes actualizadas',
    refreshedDescription: 'Las solicitudes se han actualizado correctamente',
  },
  organizationMembers: {
    title: 'Miembros de la organización',
    email: 'Correo electrónico',
    role: 'Rol',
    actions: 'Acciones',
    remove: 'Eliminar miembro',
    promote: 'Promover a administrador',
    demote: 'Degradar a miembro',
    owner: 'Propietario',
    admin: 'Administrador',
    member: 'Miembro',
    noMembers: 'No hay miembros en esta organización',
    selectedOrganization: 'Organización seleccionada',
    shareStudiesHint: 'Gestiona tus organizaciones, miembros y comparte estudios con tu equipo',
    removeConfirm: '¿Estás seguro de que quieres expulsar a este miembro?',
    removeSuccess: 'Miembro expulsado correctamente',
    removeError: 'Error al expulsar al miembro',
    promoteSuccess: 'Miembro promovido correctamente',
    demoteSuccess: 'Miembro degradado correctamente',
    cannotRemoveSelf: 'No puedes expulsarte a ti mismo',
    cannotRemoveOwner: 'No puedes expulsar al propietario de la organización',
    studiesUnsharedOnRemoval: 'Cuando un miembro es expulsado, sus estudios dejarán de compartirse con la organización',
    accessRevokedOnRemoval: 'El miembro expulsado ya no tendrá acceso a los estudios de la organización'
  },
  header: {
    profile: 'Perfil',
    logout: 'Cerrar sesión',
  },
  organization: {
    title: 'Organización',
    name: 'Organización',
    description: 'Descripción',
    create: 'Crear organización',
    shareStudies: 'Compartir estudios con la organización',
    noStudiesFound: 'No se encontraron estudios para compartir',
    studyShared: 'Estudio compartido con la organización',
    studyUnshared: 'Estudio dejado de compartir con la organización',
    allStudiesShared: 'Todos los estudios compartidos con la organización',
    allStudiesUnshared: 'Todos los estudios dejados de compartir con la organización',
    studyAutoShared: 'El estudio ha sido compartido automáticamente con tu organización',
    join: 'Unirse a organización',
    leave: 'Abandonar organización',
    delete: 'Eliminar organización',
    edit: 'Editar organización',
    save: 'Guardar cambios',
    cancel: 'Cancelar',
    members: 'Miembros',
    viewMembers: 'Ver miembros',
    noOrganizations: 'No perteneces a ninguna organización',
    createFirst: 'Crea tu primera organización',
    joinFirst: 'Únete a una organización',
    inviteCode: 'Código de invitación',
    createdAt: 'Creada el',
    details: 'Detalles',
    inviteCodeCopied: 'Código de invitación copiado al portapapeles',
    joinSuccess: 'Te has unido a la organización correctamente',
    createSuccess: 'Organización creada correctamente',
    editSuccess: 'Organización actualizada correctamente',
    deleteSuccess: 'Organización eliminada correctamente',
    leaveSuccess: 'Has abandonado la organización correctamente',
    deleteConfirm: '¿Estás seguro de que quieres eliminar esta organización?',
    leaveConfirm: '¿Estás seguro de que quieres abandonar esta organización?',
    deleteWarning: 'Esta acción no se puede deshacer',
    leaveWarning: 'Necesitarás un nuevo código de invitación para volver a unirte',
    invalidInviteCode: 'Código de invitación inválido',
    alreadyMember: 'Ya eres miembro de esta organización',
    notFound: 'Organización no encontrada',
    error: 'Ha ocurrido un error',
    errorCreate: 'Error al crear la organización',
    errorJoin: 'Error al unirse a la organización',
    errorLeave: 'Error al abandonar la organización',
    errorDelete: 'Error al eliminar la organización',
    errorEdit: 'Error al editar la organización',
    errorFetch: 'Error al obtener las organizaciones',
    errorFetchMembers: 'Error al obtener los miembros de la organización',
    role: 'Rol',
    actions: 'Acciones',
    remove: 'Eliminar',
    removeConfirm: '¿Estás seguro de que quieres eliminar a este miembro?',
    removeMemberSuccess: 'Miembro eliminado correctamente',
    removeMemberError: 'Error al eliminar miembro',
    memberRemovalWarning: 'Cuando un miembro es eliminado, sus estudios dejarán de compartirse con la organización',
    memberLossOfAccess: 'El miembro eliminado ya no tendrá acceso a los estudios de la organización',
    memberRemoved: 'Miembro eliminado correctamente',
    studiesNoLongerShared: 'Los estudios del miembro expulsado ya no se comparten con la organización',
    errorRemovingMember: 'Error al eliminar miembro',
    cleanOrphanedStudies: 'Limpiar Estudios Huérfanos',
    cleanOrphanedStudiesDescription: 'Eliminar acceso de la organización a estudios pertenecientes a miembros expulsados',
    cleanOrphanedStudiesNote: 'Nota: Los estudios se limpian automáticamente cuando un miembro es expulsado de la organización. Este botón es para limpieza manual si es necesario.',
    cleaningOrphanedStudies: 'Limpiando estudios huérfanos...',
    noOrphanedStudies: 'No se encontraron estudios huérfanos',
    errorCleaningStudies: 'Error al limpiar estudios huérfanos',
    studiesCleanedSuccess: 'Estudios huérfanos limpiados correctamente',
  },
  common: {
    selectAll: 'Seleccionar todos',
    deselectAll: 'Deseleccionar todos',
    ownedByYou: 'Propio',
  },
  show: 'Mostrar',
  hide: 'Ocultar',
  notFound: 'No encontrado',
  inviteCode: 'Código de invitación',
  createdAt: 'Creado el',
  copy: 'Copiar',
  copied: 'Copiado',

  useElement: 'Usar elemento',
  deselect: 'Deseleccionar',
  'exportFormat.title': 'Formato de decimales para Excel',
  'exportFormat.description': 'Elige cómo quieres que se muestren los números decimales en el archivo Excel:',
  'exportFormat.tip': 'Recomendación',
  'exportFormat.tipDescription': 'En España se usa la coma (,) como separador decimal. En países anglosajones se usa el punto (.).',
  'exportFormat.auto': 'Automático (según idioma)',
  'exportFormat.comma': 'Coma como separador',
  'exportFormat.dot': 'Punto como separador',
  'exportFormat.export': 'Exportar Excel',
  'exportFormat.currentLanguage': 'Idioma actual',
  'exportFormat.filename': 'Archivo',
  'exportFormat.cancel': 'Cancelar',
  'exportFormat.close': 'Cerrar',
  'exportFormat.autoDescription': 'Usa coma para español, punto para inglés',
  'exportFormat.commaDescription': 'Formato europeo (España, Francia, etc.)',
  'exportFormat.dotDescription': 'Formato anglosajón (EE.UU., Reino Unido, etc.)',
  'exportFormat.example': 'Ejemplo',
  'exportFormat.recommended': 'Recomendado',
  'exportFormat.formatOptions': 'Opciones de Formato',
  'exportFormat.detectedLanguage': 'Idioma detectado',
  'exportFormat.detectedSeparator': 'Separador decimal',
  'formatAuto': 'Automático según idioma',
  'formatComma': 'Formato español',
  'formatDot': 'Formato internacional',

  // Folder system translations
  folders: 'Carpetas',
  createFolder: 'Crear Carpeta',
  editFolder: 'Editar Carpeta',
  deleteFolder: 'Eliminar Carpeta',
  confirmDeleteFolder: '¿Estás seguro de que quieres eliminar esta carpeta?',
  createSubfolder: 'Crear Subcarpeta',
  subfolder: 'Subcarpeta',
  parentFolder: 'Carpeta Padre',
  rootLevel: 'Nivel Raíz',
  allStudies: 'Todos los Estudios',
  folderNamePlaceholder: 'Nombre de la carpeta...',
  folderDescriptionPlaceholder: 'Descripción opcional...',
  nameRequired: 'El nombre es obligatorio',
  nameTooShort: 'El nombre debe tener al menos 2 caracteres',
  nameTooLong: 'El nombre no puede tener más de 50 caracteres',
  nameAlreadyExists: 'Ya existe una carpeta con este nombre',
  descriptionTooLong: 'La descripción no puede tener más de 200 caracteres',
  moveToFolder: 'Mover a carpeta',
  noFolder: 'Sin carpeta',
  noFoldersAvailable: 'No hay carpetas disponibles',
  moving: 'Moviendo...',
  newFolder: 'Nueva Carpeta',
  preview: 'Vista Previa',
  color: 'Color',
  exportReports: 'Exportar Informes',
  export: 'Exportar',
  studies: 'estudios',
  unorganized: 'Sin organizar',
  consolidate: 'Consolidar',
  consolidateStudies: 'Consolidar Estudios',
  includeSubfolders: 'Incluir subcarpetas',
  consolidateSubfolders: 'Consolidar también los estudios de las subcarpetas'
} as const;
