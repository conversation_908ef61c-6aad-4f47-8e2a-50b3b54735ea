-- Eliminar todas las políticas existentes que pueden causar recursión
DROP POLICY IF EXISTS "Users can view shared organization studies" ON studies;
DROP POLICY IF EXISTS "Users can view shared organization studies v2" ON studies;
DROP POLICY IF EXISTS "Organization members can view their organizations" ON organizations;
DROP POLICY IF EXISTS "Users can view their organizations v2" ON organizations;
DROP POLICY IF EXISTS "Users can view members of their organizations" ON organization_members;
DROP POLICY IF EXISTS "Users can view members of their organizations v2" ON organization_members;
DROP POLICY IF EXISTS "Users can view organizations they created" ON organizations;
DROP POLICY IF EXISTS "Organization creators can manage members" ON organization_members;
DROP POLICY IF EXISTS "Users can view organization memberships" ON organization_members;
DROP POLICY IF EXISTS "Users can manage their own memberships" ON organization_members;
DROP POLICY IF EXISTS "Users can create organizations" ON organizations;

-- Eliminar funciones existentes si ya existen
DROP FUNCTION IF EXISTS is_organization_member(uuid, uuid);
DROP FUNCTION IF EXISTS get_user_organizations(uuid);
DROP FUNCTION IF EXISTS is_organization_creator(uuid, uuid);

-- Crear funciones de ayuda que no generen recursión
CREATE OR REPLACE FUNCTION is_organization_member(org_id uuid, member_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = org_id
        AND user_id = member_id
    );
END;
$$;

-- Crear función para obtener organizaciones de un usuario de forma segura
CREATE OR REPLACE FUNCTION get_user_organizations(p_user_id uuid)
RETURNS SETOF uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT organization_id
    FROM organization_members
    WHERE user_id = p_user_id;
END;
$$;

-- Crear función para verificar si un usuario creó una organización
CREATE OR REPLACE FUNCTION is_organization_creator(org_id uuid, creator_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organizations
        WHERE id = org_id
        AND created_by = creator_id
    );
END;
$$;

-- Crear nueva política para studies que use la función de ayuda
CREATE POLICY "Users can view shared organization studies v2"
    ON studies FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id
        OR (
            is_shared = true
            AND organization_id IS NOT NULL
            AND is_organization_member(organization_id, auth.uid())
        )
    );

-- Crear políticas para organizations
CREATE POLICY "Users can view their organizations v2"
    ON organizations FOR SELECT
    TO authenticated
    USING (
        created_by = auth.uid()
        OR id IN (SELECT get_user_organizations(auth.uid()))
    );

CREATE POLICY "Users can create organizations"
    ON organizations FOR INSERT
    TO authenticated
    WITH CHECK (created_by = auth.uid());

-- Crear políticas para organization_members
CREATE POLICY "Users can view members of their organizations v2"
    ON organization_members FOR SELECT
    TO authenticated
    USING (
        user_id = auth.uid()
        OR organization_id IN (SELECT get_user_organizations(auth.uid()))
    );

CREATE POLICY "Users can manage their own memberships"
    ON organization_members FOR ALL
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Organization creators can manage members"
    ON organization_members FOR ALL
    TO authenticated
    USING (is_organization_creator(organization_id, auth.uid()));

-- Crear índices para mejorar rendimiento
CREATE INDEX IF NOT EXISTS idx_studies_user_id ON studies(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_organizations_created_by ON organizations(created_by);
