import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Mic, Search, Calculator, X } from 'lucide-react';
// import { ElementInstance } from '../types';
import { supabase } from '../lib/supabase';
import { Switch } from '@headlessui/react';
import { toast } from 'react-hot-toast';
import { getFlowchartSymbols, FlowchartSymbolIcon, FlowchartSymbol } from './FlowchartSymbols';

interface MethodFormProps {
  onSubmit: (element: any, addToLibrary: boolean) => Promise<void>;
  onClose: () => void;
  initialData?: any;
}

export const MethodForm: React.FC<MethodFormProps> = ({
  onSubmit,
  onClose,
  initialData
}) => {
  const { t, i18n } = useTranslation(['method', 'common', 'library']);
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeechSupported, setIsSpeechSupported] = useState(false);
  const [speechError, setSpeechError] = useState<string | null>(null);
  const recognition = useRef<any | null>(null);
  const silenceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [currentTranscript, setCurrentTranscript] = useState('');
  
  // Estados para el modal de selección de elementos
  const [showElementSelectorModal, setShowElementSelectorModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<{element: any, studyName: string, studyId: string}[]>([]);
  const [selectedElements, setSelectedElements] = useState<{element: any, studyName: string, studyId: string}[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [averagedElement, setAveragedElement] = useState<any | null>(null);

  // Estados para sumar tiempos a un elemento de la biblioteca
  const [addToLibraryElement, setAddToLibraryElement] = useState(false);
  const [selectedLibraryElementId, setSelectedLibraryElementId] = useState('');
  const [librarySearch, setLibrarySearch] = useState('');
  const [isLibraryDropdownOpen, setIsLibraryDropdownOpen] = useState(false);
  const [libraryElements, setLibraryElements] = useState<{element: any, studyName: string, studyId: string}[]>([]);

  const [formData, setFormData] = useState({
    description: initialData?.description || '',
    type: initialData?.type || 'machine-stopped',
    frequency_repetitions: initialData?.frequency_repetitions ? parseFloat(initialData.frequency_repetitions.toString()) : 1,
    frequency_cycles: initialData?.frequency_cycles ? parseFloat(initialData.frequency_cycles.toString()) : 1,
    repetition_type: initialData?.repetition_type || 'repetitive',
    flowchartSymbol: initialData?.flowchartSymbol || undefined,
    addToLibrary: false
  });

  useEffect(() => {
    const checkSpeechSupport = () => {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      setIsSpeechSupported(!!SpeechRecognition);
    };
    checkSpeechSupport();

    return () => {
      if (recognition.current) {
        recognition.current.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        description: initialData.description || '',
        type: initialData.type || '',
        frequency_repetitions: initialData.frequency_repetitions ? parseFloat(initialData.frequency_repetitions.toString()) : 1,
        frequency_cycles: initialData.frequency_cycles ? parseFloat(initialData.frequency_cycles.toString()) : 1,
        repetition_type: initialData.repetition_type || 'repetitive',
        flowchartSymbol: initialData.flowchartSymbol || undefined
      }));
      
      // Si es tipo máquina, forzar el tipo de repetición a machine-type
      if (initialData.type === 'machine-time') {
        setFormData(prev => ({
          ...prev,
          repetition_type: 'machine-type'
        }));
      }
    }
  }, [initialData]);

  const resetSilenceTimer = () => {
    if (silenceTimer.current) {
      clearTimeout(silenceTimer.current);
    }
    silenceTimer.current = setTimeout(() => {
      if (isRecording) {
        stopRecording();
      }
    }, 2000); // Detener después de 2 segundos de silencio
  };

  const initSpeechRecognition = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition && !recognition.current) {
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = i18n.language === 'es' ? 'es-ES' : 'en-US';

      recognition.current.onresult = (event: any) => {
        setSpeechError(null);
        const finalTranscript = event.results[0][0].transcript;
        
        setFormData(prev => ({
          ...prev,
          description: (prev.description + ' ' + finalTranscript).trim()
        }));
        setCurrentTranscript('');
        stopRecording();
      };

      recognition.current.onerror = (event: any) => {
        setIsRecording(false);
        switch (event.error) {
          case 'no-speech':
            setSpeechError(t('speech.noSpeech', { ns: 'method' }));
            break;
          case 'audio-capture':
            setSpeechError(t('speech.noMicrophone', { ns: 'method' }));
            break;
          case 'not-allowed':
            setSpeechError(t('speech.notAllowed', { ns: 'method' }));
            break;
          default:
            setSpeechError(t('speech.error', { ns: 'method' }));
        }
      };

      recognition.current.onend = () => {
        if (isRecording) {
          recognition.current.start();
        } else {
          setCurrentTranscript('');
        }
      };
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const startRecording = () => {
    setSpeechError(null);
    setCurrentTranscript('');
    initSpeechRecognition();
    if (recognition.current) {
      recognition.current.lang = i18n.language === 'es' ? 'es-ES' : 'en-US';
      try {
        recognition.current.start();
        setIsRecording(true);
        resetSilenceTimer();
      } catch (error) {
        setSpeechError(t('speech.error', { ns: 'method' }));
      }
    }
  };

  const stopRecording = () => {
    if (recognition.current) {
      recognition.current.stop();
      setIsRecording(false);
      if (silenceTimer.current) {
        clearTimeout(silenceTimer.current);
      }
    }
  };

  // Función para buscar elementos SOLO de estudios compartidos
  const searchElements = async (query: string) => {
    setIsSearching(true);
    setSearchError(null);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user');
      }

      console.log('🔍 MethodForm: Iniciando búsqueda de elementos...');

      // Get all organization memberships for the user (mismo patrón que elementSearchStore)
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        // No fallar si hay error con memberships, continuar solo con estudios del usuario
      }

      console.log('👥 MethodForm: Memberships encontradas:', memberships?.length || 0);

      // Construir consulta que incluya estudios del usuario Y de organizaciones
      let supabaseQuery = supabase
        .from('studies')
        .select('id, required_info, optional_info, elements, time_records, supplements, user_id, organization_id');

      if (memberships && memberships.length > 0) {
        // Get all organization IDs where the user is a member
        const orgIds = memberships
          .filter(m => m && typeof m === 'object' && 'organization_id' in m)
          .map(m => (m as any).organization_id);
        console.log('🏢 MethodForm: IDs de organizaciones:', orgIds);
        
        // Include studies that either belong to the user OR are in organizations where the user is a member
        supabaseQuery = supabaseQuery.or(`user_id.eq.${user.id},organization_id.in.(${orgIds.join(',')})`);
      } else {
        // If not in any organization, only get user's own studies
        supabaseQuery = supabaseQuery.eq('user_id', user.id);
      }

      const { data: studies, error } = await supabaseQuery.order('created_at', { ascending: false });

      if (error) {
        console.error('❌ MethodForm: Error obteniendo estudios:', error);
        throw error;
      }

      console.log(`📊 MethodForm: Total estudios encontrados: ${studies?.length || 0}`);

      if (!studies || studies.length === 0) {
        console.log('⚠️ MethodForm: No se encontraron estudios');
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      // Filtrar estudios para mostrar solo los que tienen visibleEnBiblioteca=true (mismo patrón que elementSearchStore)
      const visibleStudies = (studies as any[]).filter(study => 
        study && typeof study === 'object' && 
        study.optional_info && 
        typeof study.optional_info === 'object' && 
        study.optional_info.visibleEnBiblioteca === true
      );

      console.log(`📚 MethodForm: Estudios visibles en biblioteca: ${visibleStudies.length}`);

      // Transformar elementos de todos los estudios visibles
      const allElements = visibleStudies.flatMap(study => {
        const studyName = study.required_info?.name || 'Estudio sin nombre';
        const elements = study.elements || [];
        
        console.log(`📝 MethodForm: Procesando estudio "${studyName}" con ${elements.length} elementos`);
        
        return elements.map((element: any) => {
          // Buscar registros de tiempo para este elemento
          const timeRecords = study.time_records && study.time_records[element.id] 
            ? study.time_records[element.id].map((record: any) => ({
                ...record,
                time: parseFloat(record.time) || 0
              }))
            : [];

          // Buscar suplementos para este elemento
          const elementSupplements = study.supplements && study.supplements[element.id]
            ? [{
                id: crypto.randomUUID(),
                name: 'Suplemento',
                description: 'Suplemento recuperado',
                percentage: parseFloat(study.supplements[element.id].percentage) || 0,
                is_forced: study.supplements[element.id].is_forced || false,
                points: study.supplements[element.id].points || {},
                factor_selections: study.supplements[element.id].factor_selections || {}
              }]
            : [];

          // Crear objeto con todos los datos
          const fullElement = {
            ...element,
            time: parseFloat(element.time) || 0,
            timeRecords,
            supplements: elementSupplements
          };

          return {
            element: fullElement,
            studyName,
            studyId: study.id
          };
        });
      });

      console.log(`🔍 MethodForm: Total elementos disponibles: ${allElements.length}`);

      // Filtrar por búsqueda si hay query
      const filteredElements = query
        ? allElements.filter(({ element, studyName }) =>
            (element.description?.toLowerCase() || '').includes(query.toLowerCase()) ||
            (studyName?.toLowerCase() || '').includes(query.toLowerCase())
          )
        : allElements;

      console.log(`✅ MethodForm: Elementos después del filtro: ${filteredElements.length}`);

      setSearchResults(filteredElements);
    } catch (error) {
      console.error('❌ MethodForm: Error durante búsqueda:', error);
      setSearchError(t('errorSearchingElements', { ns: 'library' }));
    } finally {
      setIsSearching(false);
    }
  };

  // Función para alternar la selección de un elemento
  const toggleElementSelection = (element: any, studyName: string, studyId: string) => {
    setSelectedElements(prev => {
      const isSelected = prev.some(
        sel => sel.element.id === element.id && sel.studyId === studyId
      );
      
      if (isSelected) {
        return prev.filter(
          sel => !(sel.element.id === element.id && sel.studyId === studyId)
        );
      } else {
        return [...prev, { element, studyName, studyId }];
      }
    });
  };

  // Función para calcular el promedio de los elementos seleccionados
  const handleCreateAverage = () => {
    if (selectedElements.length === 0) return;

    // Calcular el tiempo promedio usando los registros de tiempo
    let totalTime = 0;
    let validElements = 0;
    
    selectedElements.forEach(sel => {
      // Verificar si el elemento tiene registros de tiempo
      if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
        // Calcular el promedio de los tiempos de este elemento
        const elementTime = sel.element.timeRecords.reduce(
          (sum: number, record: any) => sum + (parseFloat(record.time) || 0), 
          0
        ) / sel.element.timeRecords.length;
        
        totalTime += elementTime;
        validElements++;
      } else if (sel.element.time) {
        // Si no hay registros pero hay un tiempo base, usar ese
        totalTime += parseFloat(sel.element.time) || 0;
        validElements++;
      }
    });
    
    const averageTime = validElements > 0 ? totalTime / validElements : 0;

    // Calcular el promedio de suplementos
    let totalSupplements = 0;
    let elementsWithSupplements = 0;
    
    selectedElements.forEach(sel => {
      if (sel.element.supplements && sel.element.supplements.length > 0) {
        totalSupplements += parseFloat(sel.element.supplements[0].percentage) || 0;
        elementsWithSupplements++;
      }
    });
    
    const averageSupplements = elementsWithSupplements > 0 ? totalSupplements / elementsWithSupplements : 0;

    // Crear descripción con los elementos de origen
    const descriptions = selectedElements.map(sel => 
      `${t('elementFrom', { ns: 'library' })}: ${sel.element.name || 'Elemento'}: ${sel.element.description || ''}`
    );
    const combinedDescription = descriptions.join('\n');

    // Crear elemento promediado con el tiempo correcto
    const newAveragedElement = {
      id: crypto.randomUUID(),
      name: t('averageOfElements', { ns: 'library' }),
      description: combinedDescription,
      type: formData.type || 'machine-stopped',
      time: averageTime,
      position: 0,
      repetition_type: formData.repetition_type || 'repetitive',
      frequency_cycles: formData.frequency_cycles || 1,
      frequency_repetitions: formData.frequency_repetitions || 1,
      supplements: [{
        id: crypto.randomUUID(),
        name: t('averageSupplement', { ns: 'library' }),
        description: t('automaticallyGeneratedSupplement', { ns: 'library' }),
        percentage: averageSupplements,
        is_forced: true,
        points: {},
        factor_selections: {}
      }],
      timeRecords: [{
        id: crypto.randomUUID(),
        time: averageTime,
        activity: 100,
        elementId: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        comment: t('averagedTime', { ns: 'library' })
      }],
      sourceElements: selectedElements.map(sel => ({
        id: sel.element.id || '',
        name: sel.element.name || '',
        description: sel.element.description || '',
        time: parseFloat(sel.element.time) || 0
      }))
    };

    console.log('Cálculo del promedio:', {
      elementosTotales: selectedElements.length,
      elementosConRegistros: validElements,
      tiempoTotal: totalTime,
      tiempoPromedio: averageTime,
      registrosTiempo: selectedElements.map(sel => ({
        nombre: sel.element.name,
        tiempos: sel.element.timeRecords?.map((r: any) => parseFloat(r.time) || 0) || [],
        tiempoElemento: parseFloat(sel.element.time) || 0
      })),
      suplementosPromedio: averageSupplements
    });

    setAveragedElement(newAveragedElement);
  };

  // Función para usar directamente un elemento seleccionado
  const handleUseSelectedElement = (element: any) => {
    if (!element) return;
    
    // Obtener datos necesarios del elemento seleccionado
    const elementToSubmit: any = {
      // Propiedades básicas del elemento
      description: element.description || '',
      type: element.type || 'machine-stopped',
      repetition_type: element.repetition_type || 'repetitive',
      frequency_repetitions: element.frequency_repetitions || 1,
      frequency_cycles: element.frequency_cycles || 1,
      
      // Datos especiales que enviaremos al store
      _isFromLibrary: true,
      _originalId: element.id,
      _timeRecords: element.timeRecords || [],
      _supplements: element.supplements || []
    };
    
    try {
      // Llamar al método que enviará los datos al store
      onSubmit(elementToSubmit, false)
        .then(() => onClose())
        .catch(error => console.error('Error al guardar elemento:', error));
    } catch (error) {
      console.error('Error crítico:', error);
    }
  };

  // Función para usar el elemento promediado
  const handleUseAveragedElement = () => {
    if (!averagedElement) return;
    
    const elementToSubmit: any = {
      description: averagedElement.description || '',
      type: averagedElement.type || 'machine-stopped',
      repetition_type: averagedElement.repetition_type || 'repetitive',
      frequency_repetitions: averagedElement.frequency_repetitions || 1,
      frequency_cycles: averagedElement.frequency_cycles || 1,
      
      _isFromLibrary: true,
      _originalId: averagedElement.id,
      _timeRecords: averagedElement.timeRecords || [],
      _supplements: averagedElement.supplements || []
    };
    
    try {
      onSubmit(elementToSubmit, false)
        .then(() => onClose())
        .catch(error => console.error('Error al guardar elemento promediado:', error));
    } catch (error) {
      console.error('Error crítico:', error);
    }
  };

  // Cargar elementos cuando se abre el modal
  useEffect(() => {
    if (showElementSelectorModal) {
      searchElements('');
    }
  }, [showElementSelectorModal]);

  // Búsqueda con debounce
  useEffect(() => {
    if (!showElementSelectorModal) return;
    
    const delayDebounceFn = setTimeout(() => {
      searchElements(searchQuery);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery]);

  // Al activar el switch, hacer una búsqueda inicial vacía
  useEffect(() => {
    if (addToLibraryElement) {
      searchElements('');
    }
  }, [addToLibraryElement]);

  // Filtrar en tiempo real los elementos del dropdown según el texto de búsqueda
  useEffect(() => {
    if (addToLibraryElement) {
      const filtered = searchResults.filter(({ element, studyName }) =>
        (element.description?.toLowerCase() || '').includes(librarySearch.toLowerCase()) ||
        (studyName?.toLowerCase() || '').includes(librarySearch.toLowerCase())
      );
      setLibraryElements(filtered);
    }
  }, [addToLibraryElement, librarySearch, searchResults]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Asegurarse de que el tipo de repetición es correcto para máquinas
    const finalRepetitionType = formData.type === 'machine-time' ? 'machine-type' : formData.repetition_type;

    const elementData = {
      description: formData.description,
      type: formData.type,
      repetition_type: finalRepetitionType,
      frequency_repetitions: formData.frequency_repetitions,
      frequency_cycles: formData.frequency_cycles,
      flowchartSymbol: formData.flowchartSymbol,
      // Si hay tiempo y suplementos en los datos iniciales, conservarlos
      time: initialData?.time,
      supplements: initialData?.supplements, 
      timeRecords: initialData?.timeRecords
    };
    
    try {
      // Si el switch está activo y hay un elemento seleccionado de la biblioteca
      if (addToLibraryElement && selectedLibraryElementId) {
        // Aquí deberías implementar la lógica para sumar los tiempos
        // a los time_records del elemento de la biblioteca seleccionado
        // Por ejemplo, podrías hacer una llamada a Supabase o actualizar el store
        // Ejemplo de pseudo-código:
        // await addTimesToLibraryElement(selectedLibraryElementId, nuevosTiempos)
        // Puedes mostrar un toast o mensaje de éxito si lo deseas
      }
      await onSubmit(elementData, formData.addToLibrary);
      onClose();
    } catch (error) {
      // Error crítico
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type: inputTypeAttr } = e.target; // Renombrar 'type' de e.target para evitar colisión
    const checked = (e.target as HTMLInputElement).checked;

    if (name === 'type') { // Si el campo 'type' del formulario (formData.type) cambia
      const newFormDataType = value;
      if (newFormDataType === 'machine-time') {
        setFormData(prev => ({
          ...prev,
          type: newFormDataType, // Actualiza formData.type
          repetition_type: 'machine-type' // Forzar repetition_type a 'machine-type'
        }));
      } else { // Si el nuevo formData.type NO es 'machine-time' (e.g., 'machine-stopped', 'machine-running')
        setFormData(prev => ({
          ...prev,
          type: newFormDataType, // Actualiza formData.type
          // Si el repetition_type anterior era 'machine-type' (porque formData.type era 'machine-time'),
          // lo reseteamos a 'repetitive' como valor por defecto.
          // De lo contrario (si no era 'machine-type'), conservamos el repetition_type que ya tenía.
          repetition_type: prev.repetition_type === 'machine-type' ? 'repetitive' : prev.repetition_type
        }));
      }
    } else if (name === 'frequency_repetitions' || name === 'frequency_cycles') {
      // Manejar campos de frecuencia como números decimales
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? '' : parseFloat(value)
      }));
    } else {
      // Para otros campos (incluyendo cambios directos a 'repetition_type' si está habilitado)
      setFormData(prev => ({
        ...prev,
        [name]: inputTypeAttr === 'checkbox' ? checked : value
      }));
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div id="method-form-modal" className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-6">
            {initialData ? t('edit', { ns: 'method' }) : t('new', { ns: 'method' })}
          </h2>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('description', { ns: 'method' })}
              </label>
              <div className="relative">
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows={3}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 pr-12"
                  placeholder={t('descriptionPlaceholder', { ns: 'method' })}
                  aria-label={t('description', { ns: 'method' })}
                />
                {isSpeechSupported && (
                  <div className="absolute right-2 bottom-2">
                    <button
                      type="button"
                      onClick={toggleRecording}
                      className={`p-2 rounded-full ${
                        isRecording ? 'bg-red-500 text-white' : 'bg-purple-500 text-white'
                      }`}
                      title={isRecording ? t('speech.listening', { ns: 'method' }) : t('speech.pressToSpeak', { ns: 'method' })}
                    >
                      <Mic size={20} />
                    </button>
                  </div>
                )}
              </div>
              {speechError && (
                <p className="text-red-500 text-sm mt-1">{speechError}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="type-select">
                {t('type', { ns: 'method' })}
              </label>
              <select
                id="type-select"
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                aria-label={t('type', { ns: 'method' })}
              >
                <option value="machine-stopped">{t('typesLong.machine-stopped', { ns: 'method' })}</option>
                <option value="machine-running">{t('typesLong.machine-running', { ns: 'method' })}</option>
                <option value="machine-time">{t('typesLong.machine-time', { ns: 'method' })}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="repetition-type-select">
                {t('repetitionType', { ns: 'method' })}
              </label>
              <select
                id="repetition-type-select"
                name="repetition_type"
                value={formData.repetition_type}
                onChange={handleChange}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                disabled={formData.type === 'machine-time'}
                aria-label={t('repetitionType', { ns: 'method' })}
              >
                {formData.type === 'machine-time' ? (
                  <option value="machine-type">{t('types.machine-type', { ns: 'method' })}</option>
                ) : (
                  <>
                    <option value="repetitive">{t('types.repetitive-type', { ns: 'method' })}</option>
                    <option value="frequency-type">{t('types.frequency-type', { ns: 'method' })}</option>
                  </>
                )}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="frequency-repetitions">
                  {t('frequency.repetitions', { ns: 'method' })} 
                </label>
                <input
                  id="frequency-repetitions"
                  type="number"
                  name="frequency_repetitions"
                  value={formData.frequency_repetitions}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  aria-label={t('frequency.repetitions', { ns: 'method' })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="frequency-cycles">
                  {t('frequency.cycles', { ns: 'method' })}
                </label>
                <input
                  id="frequency-cycles"
                  type="number"
                  name="frequency_cycles"
                  value={formData.frequency_cycles}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  aria-label={t('frequency.cycles', { ns: 'method' })}
                />
              </div>
            </div>

            {/* Selector de símbolos de diagrama de flujo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('cronoSeguido:flowchartSymbol')}
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {getFlowchartSymbols((key: string, options?: any) => String(t(key, { ...options, ns: 'cronoSeguido' }))).map((symbol) => (
                  <button
                    key={symbol.id}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, flowchartSymbol: symbol.id }))}
                    className={`
                      p-3 rounded-lg border-2 transition-all duration-200 text-left
                      ${formData.flowchartSymbol === symbol.id 
                        ? 'border-green-500 bg-green-50 shadow-md' 
                        : 'border-gray-300 bg-white hover:border-green-300 hover:bg-green-25'
                      }
                    `}
                    title={symbol.description}
                  >
                    <div className="flex items-center space-x-2 mb-1">
                      <FlowchartSymbolIcon symbol={symbol.id} className="text-green-600" size={20} />
                      <span className="text-xs font-semibold text-green-800">{symbol.name}</span>
                    </div>
                    <p className="text-xs text-gray-600 leading-tight">{symbol.description}</p>
                  </button>
                ))}
                {/* Opción para quitar el símbolo */}
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, flowchartSymbol: undefined }))}
                  className={`
                    p-3 rounded-lg border-2 transition-all duration-200 text-left
                    ${!formData.flowchartSymbol 
                      ? 'border-gray-500 bg-gray-50 shadow-md' 
                      : 'border-gray-300 bg-white hover:border-gray-400 hover:bg-gray-25'
                    }
                  `}
                  title={t('cronoSeguido:noSymbol', { defaultValue: 'Sin símbolo' })}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-5 h-5 rounded border-2 border-dashed border-gray-400"></div>
                    <span className="text-xs font-semibold text-gray-700">
                      {t('cronoSeguido:noSymbol', { defaultValue: 'Sin símbolo' })}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 leading-tight">
                    {t('cronoSeguido:noSymbolDescription', { defaultValue: 'No asignar símbolo a este registro' })}
                  </p>
                </button>
              </div>
              {formData.flowchartSymbol && (
                <div className="mt-2 p-2 bg-green-50 rounded-md border border-green-200">
                  <div className="flex items-center space-x-2">
                    <FlowchartSymbolIcon symbol={formData.flowchartSymbol} className="text-green-600" size={16} />
                    <span className="text-sm text-green-800 font-medium">
                      {getFlowchartSymbols((key: string, options?: any) => String(t(key, { ...options, ns: 'cronoSeguido' }))).find(s => s.id === formData.flowchartSymbol)?.name}
                    </span>
                  </div>
                  <p className="text-xs text-green-700 mt-1">
                    {getFlowchartSymbols((key: string, options?: any) => String(t(key, { ...options, ns: 'cronoSeguido' }))).find(s => s.id === formData.flowchartSymbol)?.description}
                  </p>
                </div>
              )}
            </div>

            {/* Botón de biblioteca SOLO debe aparecer si NO estamos editando */}
            {!initialData && (
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => setShowElementSelectorModal(true)}
                  className="w-full bg-amber-100 text-amber-700 border border-amber-300 px-4 py-2 rounded-lg hover:bg-amber-200 transition-colors"
                >
                  {t('method:selectFromLibrary')}
                </button>
              </div>
            )}

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                {t('common:cancel', { ns: 'common' })}
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
              >
                {initialData ? t('common:save', { ns: 'common' }) : t('common:create', { ns: 'common' })}
              </button>
            </div>
          </form>

          {/* ===== FUNCIONALIDAD DE BIBLIOTECA (Completamente fuera del form) ===== */}
          {initialData && (
            <div className="mt-6 border-t pt-6 border-gray-200">
              <h3 className="text-lg font-semibold mb-4">{t('integration', { ns: 'library' })}</h3>
              
              {/* Switch para sumar tiempos a un elemento de la biblioteca */}
              <div className="flex items-center space-x-4 mb-4">
                <Switch
                  checked={addToLibraryElement}
                  onChange={setAddToLibraryElement}
                  className={`${addToLibraryElement ? 'bg-purple-600' : 'bg-gray-200'} relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none`}
                >
                  <span className="sr-only">{t('addTimesToElement', { ns: 'library' })}</span>
                  <span
                    className={`${addToLibraryElement ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                  />
                </Switch>
                <span className="text-sm text-gray-700">{t('addTimesToElementDescription', { ns: 'library' })}</span>
              </div>

              {/* Dropdown con búsqueda integrada para seleccionar elemento de la biblioteca */}
              {addToLibraryElement && (
                <div className="mt-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('selectLibraryElement', { ns: 'library' })}
                  </label>
                  <div className="relative">
                    {/* Campo que muestra la selección y abre/cierra el dropdown */}
                    <button
                      type="button"
                      className="w-full px-3 py-2 border rounded-md flex items-center justify-between bg-white"
                      onClick={() => setIsLibraryDropdownOpen((open) => !open)}
                      aria-haspopup="listbox"
                      aria-expanded={isLibraryDropdownOpen}
                    >
                      <span className="flex items-center">
                        {selectedLibraryElementId ? (
                          (() => {
                            const selected = libraryElements.find(({ element }) => element.id === selectedLibraryElementId);
                            if (!selected) return <span className="text-gray-400">{t('selectElement', { ns: 'library' })}</span>;
                            return (
                              <>
                                <span className="font-medium text-gray-800">{selected.element.description}</span>
                                <span className="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-purple-100 text-purple-700 border border-purple-200">{selected.studyName}</span>
                              </>
                            );
                          })()
                        ) : (
                          <span className="text-gray-400">{t('selectElement', { ns: 'library' })}</span>
                        )}
                      </span>
                      <svg className="w-4 h-4 ml-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    {/* Dropdown flotante */}
                    {isLibraryDropdownOpen && (
                      <div className="absolute z-20 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg">
                        {/* Input de búsqueda dentro del dropdown */}
                        <div className="p-2 border-b border-gray-100 relative">
                          <input
                            type="text"
                            autoFocus
                            value={librarySearch}
                            onChange={e => setLibrarySearch(e.target.value)}
                            placeholder={t('searchLibraryElement', { ns: 'library' })}
                            className="w-full px-2 py-1 border rounded-md pr-8"
                            aria-label={t('searchLibraryElement', { ns: 'library' })}
                          />
                          {/* Icono para limpiar el campo de texto */}
                          {librarySearch && (
                            <button
                              type="button"
                              className="absolute right-3 top-1.5 text-gray-400 hover:text-gray-600"
                              onClick={() => setLibrarySearch('')}
                              aria-label={t('clearSearch', { ns: 'library' })}
                            >
                              <X className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                        <ul className="max-h-60 overflow-y-auto">
                          {libraryElements.length === 0 ? (
                            <li className="px-4 py-2 text-gray-500">{t('noResults', { ns: 'library' })}</li>
                          ) : (
                            libraryElements.map(({ element, studyName, studyId }) => (
                              <li
                                key={`${element.id}-${studyId}`}
                                className={`flex items-center px-4 py-2 cursor-pointer hover:bg-purple-50 ${selectedLibraryElementId === element.id ? 'bg-purple-100' : ''}`}
                                onClick={() => {
                                  setSelectedLibraryElementId(element.id);
                                  setLibrarySearch('');
                                  setIsLibraryDropdownOpen(false);
                                }}
                              >
                                <span className="flex-1">{element.description}</span>
                                <span className="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-purple-100 text-purple-700 border border-purple-200">{studyName}</span>
                              </li>
                            ))
                          )}
                        </ul>
                      </div>
                    )}
                    {/* Botón para quitar selección */}
                    {selectedLibraryElementId && (
                      <button
                        type="button"
                        className="absolute right-2 top-2 text-gray-400 hover:text-gray-600"
                        onClick={() => {
                          setSelectedLibraryElementId('');
                          setLibrarySearch('');
                        }}
                        aria-label={t('removeSelection', { ns: 'library' })}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Botón para copiar tiempos a la biblioteca */}
              {initialData && addToLibraryElement && selectedLibraryElementId && (
                <div className="flex justify-end mb-4 mt-4">
                  <button
                    type="button"
                    className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600"
                    onClick={async () => {
                      const toastId = 'copy-times-toast'; // Usar un ID constante para los toasts
                      try {
                        // Mostrar toast de inicio de operación para feedback inmediato
                        toast.loading(t('copyingTimes', { ns: 'library' }), { id: toastId });
                        console.log('Iniciando copia de tiempos para el elemento:', selectedLibraryElementId);
                        console.log('initialData al inicio de onClick:', JSON.stringify(initialData, null, 2)); // LOG PARA INSPECCIONAR initialData
                        
                        // 1. Obtener los timeRecords del elemento actual
                        const currentRecords = initialData?.timeRecords || []; // Usar optional chaining por si initialData es null/undefined
                        if (!currentRecords.length) {
                          toast.error(t('noTimeRecords', { ns: 'library' }), { id: toastId });
                          console.error('No hay registros de tiempo disponibles en initialData.timeRecords');
                          return;
                        }
                        console.log(`Registros actuales a copiar: ${currentRecords.length}`);
                        
                        // 2. Buscar el elemento de la biblioteca seleccionado
                        const selectedLib = libraryElements.find(({ element }) => element.id === selectedLibraryElementId);
                        if (!selectedLib) {
                          toast.error(t('libraryElementNotFound', { ns: 'library' }), { id: toastId });
                          console.error('Elemento de biblioteca no encontrado en libraryElements:', selectedLibraryElementId);
                          return;
                        }
                        console.log('Elemento de biblioteca seleccionado:', selectedLib.studyId, selectedLib.element.description);
                        
                        // 3. Obtener el campo time_records del estudio de la biblioteca
                        const { data: studyData, error: fetchError } = await supabase
                          .from('studies')
                          .select('time_records')
                          .eq('id', selectedLib.studyId)
                          .single();
                          
                        if (fetchError || !studyData) {
                          console.error('Error al obtener datos del estudio de la biblioteca o estudio no encontrado:', fetchError || 'No study data');
                          toast.error(t('errorAccessingStudy', { ns: 'library' }), { id: toastId });
                          return;
                        }
                        
                        // Acceso más seguro a time_records
                        let timeRecordsFromDB: Record<string, any[]> = {};
                        if (studyData && typeof studyData === 'object' && 'time_records' in studyData && studyData.time_records !== null && typeof studyData.time_records === 'object') {
                          // Se asume que si time_records existe y es un objeto, es compatible con Record<string, any[]>
                          // Para una mayor seguridad, se podría validar la estructura interna si fuera necesario.
                          timeRecordsFromDB = studyData.time_records as Record<string, any[]>;
                        } else {
                          console.warn('La propiedad time_records no está disponible en studyData, es null, o no es un objeto.');
                        }
                        console.log('Registros de tiempo obtenidos de la biblioteca:', timeRecordsFromDB);
                        
                        const libTimeRecords = Array.isArray(timeRecordsFromDB[selectedLibraryElementId]) 
                          ? timeRecordsFromDB[selectedLibraryElementId] 
                          : [];
                        console.log(`Registros existentes en biblioteca para el elemento: ${libTimeRecords.length}`);
                        
                        // 4. Filtrar los que no estén ya en la biblioteca (por id)
                        const libIds = new Set(libTimeRecords.map((r: any) => r.id)); 
                        const nuevos = currentRecords.filter((r: any) => !libIds.has(r.id)); 
                        
                        console.log(`Nuevos registros a copiar: ${nuevos.length}`);
                        
                        if (!nuevos.length) {
                          console.log('INTENTANDO MOSTRAR TOAST: No hay nuevos tiempos para copiar.');
                          // Actualizar el toast de carga existente con un mensaje informativo
                          toast(t('noNewTimes', { ns: 'library' }), { 
                            id: toastId,
                            icon: 'ℹ️' // Ícono de información
                          }); 
                          console.log('TOAST "No hay nuevos tiempos" ACTUALIZADO Y DEBERÍA HABERSE MOSTRADO.');
                          onClose();
                          return;
                        }
                        
                        // 5. Añadir los nuevos registros al array y actualizar el campo time_records
                        const nuevosTimeRecords = [...libTimeRecords, ...nuevos];
                        const newTimeRecordsObj = {
                          ...timeRecordsFromDB, // Corregido: Usar timeRecordsFromDB en lugar de timeRecordsObj
                          [selectedLibraryElementId]: nuevosTimeRecords
                        };
                        
                        const safeTimeRecords = JSON.parse(JSON.stringify(newTimeRecordsObj));
                        console.log('Intentando actualizar time_records en Supabase con:', safeTimeRecords);
                        
                        let updateError;
                        try {
                          console.log('Intentando actualización con .update() directo');
                          const updateResult = await supabase
                            .from('studies')
                            .update({
                              time_records: safeTimeRecords as any // Usar 'as any' temporalmente
                            })
                            .eq('id', selectedLib.studyId);
                          updateError = updateResult.error;
                          if(updateError){
                            console.error('Update directo falló:', updateError);
                          } else {
                            console.log('Update directo tuvo éxito.');
                          }
                        } catch (err: any) { 
                          console.error('Excepción durante el intento de actualización (RPC o directo):', err);
                          updateError = { message: err.message || String(err) };
                        }
                        
                        if (updateError) {
                          console.error('Error final al actualizar time_records:', updateError);
                          toast.error(`Error al copiar los tiempos: ${updateError.message}`, { id: toastId });
                          return;
                        }
                        
                        console.log('Tiempos copiados exitosamente a la biblioteca.');
                        toast.success(`${nuevos.length} registro(s) de tiempo copiado(s) con éxito.`, { id: toastId });
                        onClose();
                        
                      } catch (err: any) { 
                        console.error('Error inesperado en la función onClick:', err);
                        toast.error(`Error inesperado: ${err.message || 'Ocurrió un problema.'}`, { id: toastId });
                      }
                    }}
                  >
                    {t('copyTimesToLibrary', { ns: 'library' })}
                  </button>
                </div>
              )}
            </div>
          )}
          {/* ===== FIN FUNCIONALIDAD DE BIBLIOTECA ===== */}
        </div>
      </div>

      {/* Modal de selección de elementos */}
      {showElementSelectorModal && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowElementSelectorModal(false);
            }
          }}
        >
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">{t('method:selectElements')}</h2>
                <button 
                  onClick={() => setShowElementSelectorModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label={t('close', { ns: 'common' })}
                >
                  <X size={24} />
                </button>
              </div>
              
              {/* Campo de búsqueda */}
              <div className="mb-6">
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder={t('searchPlaceholder', { ns: 'library' })}
                    className="w-full px-4 py-2 pl-10 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-amber-500"
                    aria-label={t('searchPlaceholder', { ns: 'library' })}
                  />
                  <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                      aria-label={t('clearSearch', { ns: 'library' })}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>

              {/* Panel de elementos seleccionados */}
              {selectedElements.length > 0 && (
                <div className="mb-6 p-4 bg-white rounded-lg shadow">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">
                      {t('selectedElements', { count: selectedElements.length, ns: 'library' })}
                    </h3>
                    
                    {selectedElements.length === 1 ? (
                      <button
                        onClick={() => handleUseSelectedElement(selectedElements[0].element)}
                        className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center"
                      >
                        {t('common:useElement')}
                      </button>
                    ) : (
                      <button
                        onClick={handleCreateAverage}
                        className="bg-amber-500 text-white px-4 py-2 rounded hover:bg-amber-600 flex items-center"
                      >
                        <Calculator className="w-5 h-5 mr-2" />
                        {t('calculateAverage', { ns: 'library' })}
                      </button>
                    )}
                  </div>
                  <div className="space-y-2">
                    {selectedElements.map(({ element, studyName, studyId }) => (
                      <div
                        key={`${element.id}-${studyId}`}
                        className="flex items-center justify-between bg-white p-3 rounded-lg shadow"
                      >
                        <div>
                          <p className="font-medium">{element.name}</p>
                          <p className="text-sm text-gray-600">
                            {t('studyName', { ns: 'library' })}: {studyName}
                          </p>
                        </div>
                        <button
                          onClick={() => toggleElementSelection(element, studyName, studyId)}
                          className="px-3 py-1 rounded bg-amber-100 text-amber-600 hover:bg-amber-200"
                        >
                          {t('common:deselect')}
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Elemento promediado */}
              {averagedElement && (
                <div className="mb-6 p-4 bg-white rounded-lg shadow">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">{t('averagedElement', { ns: 'library' })}</h3>
                    <button
                      onClick={handleUseAveragedElement}
                      className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                    >
                      {t('common:useElement')}
                    </button>
                  </div>

                  <div className="space-y-2">
                    <p><strong>{t('name', { ns: 'common' })}:</strong> {averagedElement.name}</p>
                    <p><strong>{t('description', { ns: 'common' })}:</strong> {averagedElement.description}</p>
                    <p><strong>{t('baseTime', { ns: 'library' })}:</strong> {(averagedElement.time || 0).toFixed(2)}</p>
                    <p><strong>{t('totalTakes', { ns: 'library' })}:</strong> {(averagedElement.sourceElements || []).length}</p>
                    {averagedElement.supplements && averagedElement.supplements.length > 0 && (
                      <div>
                        <strong>{t('supplements', { ns: 'library' })}:</strong>
                        <ul className="ml-4">
                          {averagedElement.supplements.map((supplement: any, index: number) => (
                            <li key={index}>
                              {supplement.percentage.toFixed(2)}%
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Resultados de búsqueda */}
              {!isSearching && searchResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  {t('noResults', { ns: 'library' })}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {searchResults.map(({ element, studyName, studyId }) => {
                    const isSelected = selectedElements.some(
                      sel => sel.element.id === element.id && sel.studyId === studyId
                    );
                    return (
                      <div
                        key={`${element.id}-${studyId}`}
                        className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold">{element.name}</h3>
                          <button
                            onClick={() => toggleElementSelection(element, studyName, studyId)}
                            className={`px-3 py-1 rounded ${
                              isSelected
                                ? 'bg-amber-100 text-amber-600 hover:bg-amber-200'
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                          >
                            {isSelected
                              ? t('selected', { ns: 'common' })
                              : t('select', { ns: 'common' })}
                          </button>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{element.description}</p>
                        <p className="text-sm text-gray-500">{studyName}</p>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Estado de carga y error */}
              {isSearching && (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-amber-500"></div>
                </div>
              )}

              {searchError && (
                <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-6">
                  {searchError}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};