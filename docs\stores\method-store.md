# Method Store

## Descripción
Gestiona los elementos del método de trabajo y sus operaciones.

## Estado
```typescript
interface MethodState {
  elements: ElementInstance[];
  isLoading: boolean;
  error: string | null;
  createElement: (studyId: string, element: Partial<ElementInstance>) => Promise<void>;
  updateElement: (elementId: string, element: Partial<ElementInstance>) => Promise<void>;
  deleteElement: (elementId: string) => Promise<void>;
  reorderElements: (elements: ElementInstance[]) => Promise<void>;
}
```

## Funcionalidades

### 1. Gestión de Elementos
- Creación de elementos
- Actualización de elementos
- Eliminación de elementos
- Reordenamiento

### 2. Validaciones
- Tipos de elementos válidos
- Configuraciones de repetición
- Integridad de datos

### 3. Sincronización
- Actualización en tiempo real
- Persistencia en Supabase
- Manejo de conflictos

## Uso
```typescript
const { 
  elements, 
  createElement, 
  reorderElements 
} = useMethodStore();

// Crear elemento
await createElement(studyId, {
  description: "Nuevo Elemento",
  type: "machine-stopped",
  repetition_type: "repetitive"
});

// Reordenar elementos
await reorderElements(newOrder);
```