import { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';

const stripe = new Stripe(import.meta.env.VITE_STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Crear producto mensual
    const monthlyProduct = await stripe.products.create({
      name: 'Suscripción Mensual Cronometras',
      description: 'Acceso ilimitado a Cronometras - Plan Mensual',
    });

    const monthlyPrice = await stripe.prices.create({
      product: monthlyProduct.id,
      unit_amount: 999, // 9.99€
      currency: 'eur',
      recurring: {
        interval: 'month',
      },
    });

    // Crear producto anual
    const annualProduct = await stripe.products.create({
      name: 'Suscripción Anual Cronometras',
      description: 'Acceso ilimitado a Cronometras - Plan Anual',
    });

    const annualPrice = await stripe.prices.create({
      product: annualProduct.id,
      unit_amount: 9999, // 99.99€
      currency: 'eur',
      recurring: {
        interval: 'year',
      },
    });

    // Guardar los IDs en la base de datos o en un archivo de configuración
    const priceIds = {
      monthly: monthlyPrice.id,
      annual: annualPrice.id,
    };

    return res.status(200).json(priceIds);
  } catch (error) {
    console.error('Error creating products:', error);
    return res.status(500).json({ error: 'Error creating products' });
  }
}
