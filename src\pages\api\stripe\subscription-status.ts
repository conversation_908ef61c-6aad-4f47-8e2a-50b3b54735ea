import { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { subscriptionId } = req.query;

    if (!subscriptionId || typeof subscriptionId !== 'string') {
      return res.status(400).json({ error: 'Missing subscription ID' });
    }

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    return res.status(200).json({
      status: subscription.status,
      currentPeriodEnd: subscription.current_period_end,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
    });
  } catch (error) {
    console.error('Error retrieving subscription status:', error);
    return res.status(500).json({ error: 'Error retrieving subscription status' });
  }
}
