import { useState, useCallback } from 'react';
import { speechRecognition } from '../utils/speechRecognition';

export const useSpeechRecognition = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');

  const startListening = useCallback(() => {
    setIsListening(true);
    setTranscript('');
    
    speechRecognition.start(
      (text) => setTranscript(text),
      () => setIsListening(false)
    );
  }, []);

  const stopListening = useCallback(() => {
    speechRecognition.stop();
    setIsListening(false);
  }, []);

  const resetTranscript = useCallback(() => {
    setTranscript('');
  }, []);

  const updateLanguage = useCallback((language: string) => {
    speechRecognition.updateLanguage(language);
  }, []);

  return {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
    updateLanguage,
    isSupported: speechRecognition.isSupported()
  };
};