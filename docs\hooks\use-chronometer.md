# useChronometer Hook

## Descripción
Hook personalizado para gestionar la lógica del cronómetro.

## Funcionalidades
```typescript
interface UseChronometerReturn {
  time: number;
  isRunning: boolean;
  isPaused: boolean;
  start: () => void;
  pause: () => void;
  stop: () => void;
  reset: () => void;
}

function useChronometer(options?: {
  onTick?: (time: number) => void;
  precision?: number;
}): UseChronometerReturn;
```

### Características
- Control preciso del tiempo
- Estados del cronómetro
- Callbacks personalizables
- Precisión configurable
- Limpieza automática

### Uso
```typescript
const {
  time,
  isRunning,
  start,
  pause,
  stop
} = useChronometer({
  onTick: (time) => console.log(time),
  precision: 10 // ms
});
```