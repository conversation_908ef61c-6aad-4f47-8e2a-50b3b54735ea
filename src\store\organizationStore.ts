import { create } from 'zustand';
import { supabase } from '../lib/supabaseClient';
import { Organization, OrganizationMember, OrganizationJoinRequest } from '../types/organization';
import { toast } from '../components/ui/use-toast';
import i18next from 'i18next';

// Definir el tipo del estado
type OrganizationState = {
  organizations: Organization[];
  currentOrganization: Organization | null;
  members: Record<string, OrganizationMember[]>;
  joinRequests: Record<string, OrganizationJoinRequest[]>;
  mySentJoinRequests: OrganizationJoinRequest[];
  isLoading: boolean;
  error: string | null;
  membersFetchTimestamp: Record<string, number>;
  requestsFetchTimestamp: Record<string, number>;
  myRequestsFetchTimestamp: number;
  organizationNamesCache: Record<string, string>;
  
  // Acciones principales
  fetchOrganizations: () => Promise<void>;
  createOrganization: (name: string, description?: string) => Promise<Organization>;
  fetchMembers: (organizationId: string) => Promise<void>;
  fetchJoinRequests: (organizationId: string, forceRefresh: boolean) => Promise<void>;
  fetchMySentJoinRequests: () => Promise<void>;
  
  // Gestión de miembros
  promoteMember: (organizationId: string, userId: string) => Promise<void>;
  demoteMember: (organizationId: string, userId: string) => Promise<void>;
  removeMember: (organizationId: string, userId: string) => Promise<void>;
  
  // Gestión de solicitudes
  sendJoinRequest: (inviteCode: string) => Promise<void>;
  approveJoinRequest: (requestId: string) => Promise<void>;
  rejectJoinRequest: (requestId: string) => Promise<void>;
  
  // Gestión de la organización actual
  setCurrentOrganization: (organization: Organization | null) => void;
  
  // Manejo de errores
  resetError: () => void;
  
  // Obtener nombres de organizaciones
  getOrganizationName: (organizationId: string) => Promise<string>;
  
  // Función para verificar acceso a organizaciones
  checkOrganizationExists: () => Promise<boolean>;
  
  // Función auxiliar para obtener nombres de organizaciones para solicitudes
  fetchOrganizationNames: (requests: OrganizationJoinRequest[]) => Promise<void>;
  
  // Función para limpiar los estudios de miembros expulsados
  cleanOrphanedStudies: (organizationId: string) => Promise<{ cleaned: number }>;
};

// Crear el store
export const useOrganizationStore = create<OrganizationState>((set, get) => ({
  organizations: [],
  currentOrganization: null,
  members: {},
  joinRequests: {},
  mySentJoinRequests: [],
  isLoading: false,
  error: null,
  membersFetchTimestamp: {},
  requestsFetchTimestamp: {},
  myRequestsFetchTimestamp: 0,
  organizationNamesCache: {},
  
  fetchOrganizations: async () => {
    set({ isLoading: true, error: null });
    try {
      // Obtener el usuario actual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No user authenticated');
      
      // Primero obtenemos las membresías del usuario
      console.log('Fetching organizations for user:', user.id);
      
      try {
        // Usar la función RPC para evitar recursión
        const { data: organizationIds, error: rpcError } = await supabase.rpc('get_user_organizations', {
          p_user_id: user.id
        });
        
        if (rpcError) {
          console.log('Error usando RPC get_user_organizations, intentando método alternativo:', rpcError);
          // Intentar el método original
          const { data: memberships, error: membershipError } = await supabase
            .from('organization_members')
            .select('organization_id,role')
            .eq('user_id', user.id);
          
          if (membershipError) {
            // Verificar si es un error de recursión infinita
            if (membershipError.code === '42P17' && membershipError.message.includes('infinite recursion')) {
              console.warn('Detectada recursión infinita en políticas de seguridad. Usando enfoque alternativo.');
              // Usar un enfoque alternativo para obtener las organizaciones
              set({ organizations: [], isLoading: false });
              return;
            }
            
            console.error('Error fetching organization memberships:', membershipError);
            throw membershipError;
          }
          
          if (memberships && memberships.length > 0) {
            console.log('User is member of organizations:', memberships);
            
            // Obtener detalles de cada organización
            const organizationIds = memberships.map(m => m.organization_id);
            const { data: organizations, error: orgError } = await supabase
              .from('organizations')
              .select('*')
              .in('id', organizationIds);
            
            if (orgError) {
              console.error('Error fetching organizations:', orgError);
              throw orgError;
            }
            
            // Asignar roles a las organizaciones
            const orgsWithRoles = organizations?.map(org => {
              const membership = memberships.find(m => m.organization_id === org.id);
              return {
                ...org,
                userRole: membership?.role || 'member'
              };
            }) || [];
            
            set({ organizations: orgsWithRoles, isLoading: false });
          } else {
            console.log('Organizations fetched:', memberships);
            set({ organizations: [], isLoading: false });
          }
        } else {
          // Si la función RPC devuelve IDs, obtener los detalles de las organizaciones
          const { data: organizations, error: orgError } = await supabase
            .from('organizations')
            .select('*')
            .in('id', organizationIds);
          
          if (orgError) {
            console.error('Error fetching organizations:', orgError);
            throw orgError;
          }
          
          set({ organizations: organizations || [], isLoading: false });
        }
      } catch (error) {
        // Si hay un error al obtener las membresías, intentamos un enfoque alternativo
        console.error('Error fetching organizations:', error);
        
        // Establecer un estado vacío para evitar bloquear la interfaz de usuario
        set({ organizations: [], isLoading: false, error: (error as Error).message });
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  createOrganization: async (name: string, description?: string) => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No user authenticated');

      // Crear la organización
      const { data: org, error: orgError } = await supabase
        .from('organizations')
        .insert([
          {
            name,
            description,
            created_by: user.id
          }
        ])
        .select()
        .single();

      if (orgError) throw orgError;
      if (!org) throw new Error('Error creating organization');

      // Añadir al creador como owner
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert([
          {
            organization_id: org.id,
            user_id: user.id,
            user_email: user.email,
            role: 'owner'
          }
        ]);

      if (memberError) throw memberError;

      // Actualizar el estado
      const { organizations } = get();
      set({ 
        organizations: [...organizations, org],
        currentOrganization: org,
        isLoading: false 
      });

      return org;
    } catch (error) {
      console.error('Error creating organization:', error);
      set({ 
        error: (error as Error).message, 
        isLoading: false 
      });
      throw error;
    }
  },

  fetchMembers: async (organizationId: string) => {
    // Verificar si ya se han cargado los miembros recientemente (en los últimos 30 segundos)
    const now = Date.now();
    const lastFetch = get().membersFetchTimestamp[organizationId] || 0;
    const CACHE_TIME = 30000; // 30 segundos
    
    if (now - lastFetch < CACHE_TIME) {
      console.log('Using cached members for organization:', organizationId);
      return;
    }
    
    set({ isLoading: true, error: null });
    try {
      console.log('Fetching members for organization:', organizationId);
      
      const { data: members, error } = await supabase
        .from('organization_members')
        .select('organization_id, user_id, user_email, role, created_at')
        .eq('organization_id', organizationId);

      if (error) throw error;
      
      console.log('Members fetched:', members);
      set({ 
        members: { ...get().members, [organizationId]: members || [] }, 
        isLoading: false,
        membersFetchTimestamp: {
          ...get().membersFetchTimestamp,
          [organizationId]: now
        }
      });
    } catch (error) {
      console.error('Error fetching members:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  fetchJoinRequests: async (organizationId: string, forceRefresh: boolean = false) => {
    // Verificar si ya se han cargado las solicitudes recientemente (en los últimos 30 segundos)
    const now = Date.now();
    const lastFetch = get().requestsFetchTimestamp[organizationId] || 0;
    const CACHE_TIME = 30000; // 30 segundos
    
    if (!forceRefresh && now - lastFetch < CACHE_TIME) {
      console.log('Using cached join requests for organization:', organizationId);
      return;
    }
    
    set({ isLoading: true, error: null });
    try {
      const { data: requests, error } = await supabase
        .from('organization_join_requests')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('status', 'pending');

      if (error) throw error;
      
      set({ 
        joinRequests: { ...get().joinRequests, [organizationId]: requests || [] }, 
        isLoading: false,
        requestsFetchTimestamp: {
          ...get().requestsFetchTimestamp,
          [organizationId]: now
        }
      });
    } catch (error) {
      console.error('Error fetching join requests:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  fetchMySentJoinRequests: async () => {
    set({ isLoading: true, error: null });
    try {
      // Obtener el usuario actual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No user authenticated');

      // Llamar a la función RPC para unirse a la organización
      const { data: requests, error } = await supabase
        .from('organization_join_requests')
        .select('*')
        .eq('user_id', user.id);

      if (error) {
        // Verificar si es un error de recursión infinita
        if (error.code === '42P17' && error.message.includes('infinite recursion')) {
          console.warn('Detectada recursión infinita en políticas de seguridad al obtener solicitudes. Usando enfoque alternativo.');
          // Usar un enfoque alternativo o devolver un array vacío
          set({ mySentJoinRequests: [], isLoading: false });
          return;
        }
        
        console.error('Error al obtener solicitudes enviadas:', error);
        throw error;
      }

      console.log('Solicitudes enviadas obtenidas:', requests);
      set({ mySentJoinRequests: requests || [], isLoading: false, myRequestsFetchTimestamp: Date.now() });

      // Si hay solicitudes, intentamos obtener los nombres de las organizaciones
      if (requests && requests.length > 0) {
        await get().fetchOrganizationNames(requests);
      }
    } catch (error) {
      console.error('Error fetching my sent join requests:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  promoteMember: async (organizationId: string, userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('organization_members')
        .update({ role: 'admin' })
        .eq('organization_id', organizationId)
        .eq('user_id', userId);

      if (error) throw error;

      // Actualizar la lista de miembros
      await get().fetchMembers(organizationId);
      set({ isLoading: false });
    } catch (error) {
      console.error('Error promoting member:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  demoteMember: async (organizationId: string, userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('organization_members')
        .update({ role: 'member' })
        .eq('organization_id', organizationId)
        .eq('user_id', userId);

      if (error) throw error;

      // Actualizar la lista de miembros
      await get().fetchMembers(organizationId);
      set({ isLoading: false });
    } catch (error) {
      console.error('Error demoting member:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  removeMember: async (organizationId: string, userId: string) => {
    set({ isLoading: true, error: null });
    try {
      // 1. Eliminar al miembro de la organización
      const { error } = await supabase
        .from('organization_members')
        .delete()
        .eq('organization_id', organizationId)
        .eq('user_id', userId);

      if (error) throw error;

      // 2. Actualizar los estudios de este usuario para que ya no estén compartidos con la organización
      const { error: studyUpdateError } = await supabase
        .from('studies')
        .update({
          organization_id: null,
          is_shared: false
        })
        .eq('user_id', userId)
        .eq('organization_id', organizationId);

      if (studyUpdateError) {
        console.error('Error al actualizar estudios del miembro expulsado:', studyUpdateError);
        // No lanzamos el error para que el proceso continúe
      }

      // 3. Limpiar cualquier estudio huérfano que pueda haber quedado
      try {
        // Llamar a la función RPC para limpiar estudios huérfanos
        const { data: cleanedCount, error: cleanError } = await supabase
          .rpc('rpc_clean_orphaned_studies', { org_id: organizationId });
        
        if (cleanError) {
          console.error('Error al limpiar estudios huérfanos durante la expulsión:', cleanError);
          // No lanzamos el error para que el proceso continúe
        } else if (cleanedCount > 0) {
          console.log(`Se limpiaron ${cleanedCount} estudios huérfanos adicionales`);
        }
      } catch (cleaningError) {
        console.error('Error en la limpieza de estudios huérfanos:', cleaningError);
        // No lanzamos el error para que el proceso continúe
      }

      // 4. Forzar actualización de la lista de estudios para reflejar los cambios
      try {
        const studyStore = await import('../store/studyStore');
        if (studyStore && studyStore.useStudyStore) {
          const { fetchStudies } = studyStore.useStudyStore.getState();
          if (typeof fetchStudies === 'function') {
            await fetchStudies();
          }
        }
      } catch (refreshError) {
        console.error('Error al actualizar la lista de estudios:', refreshError);
      }

      // 5. Actualizar la lista de miembros en el estado local inmediatamente
      // para evitar tener que esperar a una nueva consulta
      const currentMembers = get().members[organizationId] || [];
      const updatedMembers = currentMembers.filter(member => member.user_id !== userId);
      
      set({ 
        members: { 
          ...get().members, 
          [organizationId]: updatedMembers 
        },
        isLoading: false 
      });
      
      // 6. Además, actualizar desde la base de datos para asegurar consistencia
      await get().fetchMembers(organizationId);

      // 7. Mostrar notificaciones de éxito
      toast({
        description: t('organization.memberRemoved'),
      });

      toast({
        description: t('organization.studiesNoLongerShared'),
      });

      return true;
    } catch (error) {
      console.error('Error al eliminar miembro:', error);
      set({ error: (error as Error).message, isLoading: false });
      
      toast({
        variant: 'destructive',
        description: t('organization.errorRemovingMember'),
      });
      
      return false;
    }
  },

  sendJoinRequest: async (inviteCode: string) => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No user authenticated');

      // Llamar a la función RPC para unirse a la organización
      const { error } = await supabase.rpc('join_organization_by_code', {
        p_invite_code: inviteCode,
        p_user_id: user.id,
        p_user_email: user.email
      });

      if (error) throw error;

      set({ isLoading: false });
    } catch (error) {
      console.error('Error sending join request:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  approveJoinRequest: async (requestId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Obtener la información de la solicitud
      const { data: request, error: requestError } = await supabase
        .from('organization_join_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError) {
        console.error('Error al obtener la solicitud:', requestError);
        throw requestError;
      }
      
      if (!request) {
        console.error('Solicitud no encontrada');
        throw new Error('Solicitud no encontrada');
      }

      // Obtener el usuario actual para registrar quién procesó la solicitud
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('Usuario no autenticado');
        throw new Error('Usuario no autenticado');
      }

      console.log('Usuario que procesa la solicitud:', user.id);
      console.log('Usuario que envió la solicitud:', request.user_id);

      // Verificar si el usuario ya es miembro de la organización
      const { data: existingMember, error: memberCheckError } = await supabase
        .from('organization_members')
        .select('*')
        .eq('organization_id', request.organization_id)
        .eq('user_id', request.user_id)
        .single();

      if (memberCheckError && memberCheckError.code !== 'PGRST116') {
        // PGRST116 significa que no se encontró ningún registro, lo cual es lo que esperamos
        console.error('Error al verificar si el usuario ya es miembro:', memberCheckError);
        throw memberCheckError;
      }

      console.log('¿El usuario ya es miembro?', existingMember ? 'Sí' : 'No');
      if (existingMember) {
        console.log('Detalles del miembro existente:', existingMember);
      }

      // Solo añadir el miembro si no existe ya
      if (!existingMember) {
        console.log('El usuario no es miembro, añadiendo...');
        
        // Crear objeto de miembro para mayor claridad
        const newMember = {
          organization_id: request.organization_id,
          user_id: request.user_id,
          user_email: request.user_email,
          role: 'member'
        };
        
        console.log('Datos del nuevo miembro a insertar:', newMember);
        
        const { error: memberError } = await supabase
          .from('organization_members')
          .insert([newMember]);

        if (memberError) {
          console.error('Error al añadir miembro:', memberError);
          throw memberError;
        }

        console.log('Miembro añadido correctamente');
      } else {
        console.log('El usuario ya es miembro de la organización');
      }

      // Verificar si existen solicitudes anteriores con el mismo estado para evitar duplicados
      console.log('Verificando solicitudes anteriores para evitar duplicados...');
      const { data: existingRequests, error: existingRequestsError } = await supabase
        .from('organization_join_requests')
        .select('id')
        .eq('organization_id', request.organization_id)
        .eq('user_id', request.user_id)
        .eq('status', 'approved')
        .neq('id', requestId); // Excluir la solicitud actual

      if (existingRequestsError) {
        console.error('Error al verificar solicitudes anteriores:', existingRequestsError);
        // Continuamos a pesar del error para intentar completar la operación
      } else if (existingRequests && existingRequests.length > 0) {
        console.log('Se encontraron solicitudes anteriores aprobadas:', existingRequests);
        
        // Eliminar solicitudes anteriores para evitar conflictos
        const { error: deleteError } = await supabase
          .from('organization_join_requests')
          .delete()
          .in('id', existingRequests.map(req => req.id));
          
        if (deleteError) {
          console.error('Error al eliminar solicitudes anteriores:', deleteError);
          // Continuamos a pesar del error para intentar completar la operación
        } else {
          console.log('Solicitudes anteriores eliminadas correctamente');
        }
      }

      // Ahora que tenemos la política UPDATE, podemos actualizar directamente
      const { data: updateData, error: updateError } = await supabase
        .from('organization_join_requests')
        .update({
          status: 'approved',
          processed_at: new Date().toISOString(),
          processed_by: user.id
        })
        .eq('id', requestId)
        .select();

      if (updateError) {
        console.error('Error al actualizar la solicitud:', updateError);
        throw updateError;
      }

      console.log('Solicitud aprobada correctamente:', updateData);

      // Actualizar las listas
      await get().fetchJoinRequests(request.organization_id);
      await get().fetchMembers(request.organization_id);
      
      // Actualizar el estado local para eliminar inmediatamente la solicitud aprobada
      set(state => ({
        joinRequests: { ...state.joinRequests, [request.organization_id]: state.joinRequests[request.organization_id].filter(req => req.id !== requestId) },
        isLoading: false
      }));
    } catch (error) {
      console.error('Error approving request:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  rejectJoinRequest: async (requestId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Obtener la información de la solicitud
      const { data: request, error: requestError } = await supabase
        .from('organization_join_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (requestError) {
        console.error('Error al obtener la solicitud:', requestError);
        throw requestError;
      }
      
      if (!request) {
        console.error('Solicitud no encontrada');
        throw new Error('Solicitud no encontrada');
      }

      // Obtener el usuario actual para registrar quién procesó la solicitud
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('Usuario no autenticado');
        throw new Error('Usuario no autenticado');
      }

      console.log('Intentando actualizar solicitud con ID:', requestId);

      // Verificar si existen solicitudes anteriores con el mismo estado para evitar duplicados
      console.log('Verificando solicitudes anteriores para evitar duplicados...');
      const { data: existingRequests, error: existingRequestsError } = await supabase
        .from('organization_join_requests')
        .select('id')
        .eq('organization_id', request.organization_id)
        .eq('user_id', request.user_id)
        .eq('status', 'rejected')
        .neq('id', requestId); // Excluir la solicitud actual

      if (existingRequestsError) {
        console.error('Error al verificar solicitudes anteriores:', existingRequestsError);
        // Continuamos a pesar del error para intentar completar la operación
      } else if (existingRequests && existingRequests.length > 0) {
        console.log('Se encontraron solicitudes anteriores rechazadas:', existingRequests);
        
        // Eliminar solicitudes anteriores para evitar conflictos
        const { error: deleteError } = await supabase
          .from('organization_join_requests')
          .delete()
          .in('id', existingRequests.map(req => req.id));
          
        if (deleteError) {
          console.error('Error al eliminar solicitudes anteriores:', deleteError);
          // Continuamos a pesar del error para intentar completar la operación
        } else {
          console.log('Solicitudes anteriores eliminadas correctamente');
        }
      }

      // Ahora que tenemos la política UPDATE, podemos actualizar directamente
      const { data: updateData, error: updateError } = await supabase
        .from('organization_join_requests')
        .update({
          status: 'rejected',
          processed_at: new Date().toISOString(),
          processed_by: user.id
        })
        .eq('id', requestId)
        .select();

      if (updateError) {
        console.error('Error al actualizar la solicitud:', updateError);
        throw updateError;
      }

      console.log('Solicitud rechazada correctamente:', updateData);

      // Actualizar las listas
      await get().fetchJoinRequests(request.organization_id);
      
      // Actualizar el estado local para eliminar inmediatamente la solicitud rechazada
      set(state => ({
        joinRequests: { ...state.joinRequests, [request.organization_id]: state.joinRequests[request.organization_id].filter(req => req.id !== requestId) },
        isLoading: false
      }));
    } catch (error) {
      console.error('Error rejecting request:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  setCurrentOrganization: (organization) => {
    set({ currentOrganization: organization });
  },

  resetError: () => {
    set({ error: null });
  },

  getOrganizationName: async (organizationId: string): Promise<string> => {
    try {
      // Primero intentamos buscar en las organizaciones ya cargadas
      const cachedOrg = get().organizations.find(org => org.id === organizationId);
      if (cachedOrg) {
        return cachedOrg.name;
      }

      // Verificar si ya tenemos el nombre en el caché local
      const localCache = get().organizationNamesCache || {};
      if (localCache[organizationId]) {
        return localCache[organizationId];
      }
      
      // Consulta directa a la tabla de organizaciones
      const { data: organization, error } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', organizationId)
        .single();
      
      if (organization && organization.name) {
        // Guardar en caché local
        const updatedCache = { ...get().organizationNamesCache };
        updatedCache[organizationId] = organization.name;
        set({ organizationNamesCache: updatedCache });
        return organization.name;
      }
      
      // Si no encontramos la organización, intentamos obtener el nombre a través de la función RPC
      try {
        const { data: orgName, error: rpcError } = await supabase.rpc('get_organization_name', {
          p_organization_id: organizationId
        });
        
        if (!rpcError && orgName) {
          const updatedCache = { ...get().organizationNamesCache };
          updatedCache[organizationId] = orgName;
          set({ organizationNamesCache: updatedCache });
          return orgName;
        }
      } catch (rpcErr) {
        console.error('Error al obtener nombre mediante RPC:', rpcErr);
      }
      
      // Si no podemos obtener el nombre, usamos un nombre temporal
      const tempName = `Organización ${organizationId}`;
      const updatedCache = { ...get().organizationNamesCache };
      updatedCache[organizationId] = tempName;
      set({ organizationNamesCache: updatedCache });
      return tempName;
      
    } catch (error) {
      console.error('Error obteniendo el nombre de la organización:', error);
      return 'Error al obtener nombre';
    }
  },

  // Función para verificar si podemos acceder a las organizaciones
  checkOrganizationExists: async () => {
    console.log('Verificando si podemos acceder a las organizaciones...');
    try {
      // Usar un enfoque directo para evitar problemas de recursión
      const { data, error } = await supabase.rpc('is_organization_creator', {
        org_id: '00000000-0000-0000-0000-000000000000', // ID ficticio
        creator_id: (await supabase.auth.getUser()).data.user?.id || '00000000-0000-0000-0000-000000000000'
      });
      
      if (error) {
        // Si hay error en la función RPC, intentamos el enfoque original
        try {
          const { data: orgData, error: orgError } = await supabase
            .from('organizations')
            .select('id')
            .limit(1);
          
          if (orgError) {
            // Verificar si es un error de recursión infinita
            if (orgError.code === '42P17' && orgError.message.includes('infinite recursion')) {
              console.warn('Detectada recursión infinita en políticas de seguridad al verificar organizaciones. Asumiendo que existen.');
              // Asumimos que las organizaciones existen para no bloquear la funcionalidad
              return true;
            }
            
            console.error('Error al verificar acceso a organizaciones:', orgError);
            return false;
          }
          
          return true;
        } catch (e) {
          console.error('Error en verificación de organizaciones:', e);
          return false;
        }
      }
      
      return true;
    } catch (e) {
      console.error('Error general en verificación de organizaciones:', e);
      return false;
    }
  },
  
  // Función auxiliar para obtener nombres de organizaciones para solicitudes
  fetchOrganizationNames: async (requests: OrganizationJoinRequest[]) => {
    try {
      const organizationIds = requests.map(req => req.organization_id);
      const uniqueIds = [...new Set(organizationIds)];
      
      // Crear un mapa para almacenar los nombres de las organizaciones
      const namesMap: Record<string, string> = {};
      
      // Primero intentar obtener los nombres de la caché
      const cachedNames = get().organizationNamesCache;
      uniqueIds.forEach(id => {
        if (cachedNames[id]) {
          namesMap[id] = cachedNames[id];
        }
      });
      
      // Obtener los IDs que no están en caché
      const uncachedIds = uniqueIds.filter(id => !namesMap[id]);
      
      if (uncachedIds.length > 0) {
        // Intentar obtener los nombres directamente de la tabla de organizaciones
        const { data: organizations, error } = await supabase
          .from('organizations')
          .select('id, name')
          .in('id', uncachedIds);
        
        if (!error && organizations && organizations.length > 0) {
          organizations.forEach(org => {
            namesMap[org.id] = org.name;
          });
        }
        
        // Para los IDs restantes, intentar obtener a través de RPC
        const remainingIds = uncachedIds.filter(id => !namesMap[id]);
        
        for (const id of remainingIds) {
          try {
            const { data: orgName, error: rpcError } = await supabase.rpc('get_organization_name', {
              p_organization_id: id
            });
            
            if (!rpcError && orgName) {
              namesMap[id] = orgName;
            } else {
              namesMap[id] = `Organización ${id}`;
            }
          } catch (error) {
            namesMap[id] = `Organización ${id}`;
          }
        }
      }
      
      // Actualizar la caché de nombres
      set({ organizationNamesCache: { ...get().organizationNamesCache, ...namesMap } });
    } catch (error) {
      console.error('Error al obtener nombres de organizaciones:', error);
    }
  },
  
  // Función para limpiar los estudios de miembros expulsados
  cleanOrphanedStudies: async (organizationId: string) => {
    set({ isLoading: true, error: null });
    try {
      console.log('Iniciando limpieza de estudios huérfanos para la organización:', organizationId);
      
      // Llamar a la función RPC para limpiar estudios huérfanos
      const { data: cleanedCount, error } = await supabase
        .rpc('rpc_clean_orphaned_studies', { org_id: organizationId });
      
      if (error) {
        console.error('Error al limpiar estudios huérfanos:', error);
        throw error;
      }
      
      console.log('Estudios huérfanos limpiados:', cleanedCount);
      
      // Forzar actualización de la lista de estudios
      try {
        const studyStore = await import('../store/studyStore');
        if (studyStore && studyStore.useStudyStore) {
          const { fetchStudies } = studyStore.useStudyStore.getState();
          if (typeof fetchStudies === 'function') {
            await fetchStudies();
          }
        }
      } catch (refreshError) {
        console.error('Error al actualizar la lista de estudios:', refreshError);
      }
      
      set({ isLoading: false });
      
      // Mostrar notificación de éxito
      if (cleanedCount > 0) {
        toast({
          description: `${cleanedCount} estudios han sido desvinculados de la organización`,
        });
      } else {
        toast({
          description: t('organization.noOrphanedStudies'),
        });
      }
      
      return { cleaned: cleanedCount };
    } catch (error) {
      console.error('Error en cleanOrphanedStudies:', error);
      set({ error: (error as Error).message, isLoading: false });
      
      // Mostrar notificación de error
      toast({
        variant: 'destructive',
        description: t('organization.errorCleaningStudies'),
      });
      
      return { cleaned: 0 };
    }
  },
}));
