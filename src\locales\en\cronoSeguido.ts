export default {
  title: 'Continuous chronometer',
  cronoseguido: 'Continuous',
  pageDescription: 'Sequential time measurement',
  start: 'Start',
  stop: 'Stop',
  reset: 'Reset',
  lap: 'Lap',
  save: 'Save',
  records: 'Records',
  addToMethod: 'Bulk add',
  addSingleRecordToMethod: 'Add record to method',
  bulkAddToMethod: 'Add to method',
  selectElement: 'Select element',
  deleteAll: 'Delete all',
  confirmDeleteAll: 'Are you sure you want to delete all records?',
  confirmDeleteAllDescription: 'This action cannot be undone.',
  confirmBulkAdd: 'You are adding ALL the records in bulk, take into account that they will be added as NEW elements of type REPEATABLE with a FREQUENCY of 1/1.', 
  noDescription: 'No description',
  nextElement: 'Next element',
  previousElement: 'Previous element',
  currentElement: 'Current element',
  noElements: 'No elements available',
  noStudySelected: 'No study selected',
  editRecord: 'Edit record',
  deleteRecord: 'Delete record',
  addRecord: 'Add record',
  description: 'Description',
  time: 'Time',
  activity: 'Activity',
  addedToMethod: 'Added to method',
  notAddedToMethod: 'Not added to method',
  timestamp: 'Timestamp',
  voiceInput: 'Voice input',
  pressToSpeak: 'Press to speak',
  listening: 'Listening...',
  noSpeech: 'No speech detected',
  speechError: 'Speech recognition error',
  noVoiceDetected: 'No voice detected',
  stats: 'Statistics',
  totalTime: 'Total time',
  totalTakes: 'Total takes',
  saveToMethod: 'Save in Bulk ',
  elementType: 'Element type',
  newElement: 'New element',
  existingElement: 'Existing element',
  deleteAllTitle: 'Delete all records',
  deleteAllConfirmation: 'Are you sure you want to delete all records? This action cannot be undone.',
  addDescription: 'Add description',
  addVoiceDescription: 'Add voice description',
  flowchartSymbol: 'Flowchart symbol',
  selectSymbol: 'Select symbol',
  flowchartSymbols: {
    operation: {
      name: 'OPERATION',
      description: 'Indicates the main phases of the process. Adds, modifies, assembly, etc.'
    },
    inspection: {
      name: 'INSPECTION',
      description: 'Verifies quality or quantity. Generally does not add value.'
    },
    transport: {
      name: 'TRANSPORT',
      description: 'Indicates movement of materials. Transfer from one place to another.'
    },
    delay: {
      name: 'DELAY',
      description: 'Indicates delay between two operations or momentary abandonment.'
    },
    storage: {
      name: 'STORAGE',
      description: 'Indicates deposit of an object under surveillance in a warehouse'
    },
    combined: {
      name: 'COMBINED',
      description: 'Indicates several simultaneous activities'
    }
  },
  noSymbol: 'No symbol',
  noSymbolDescription: 'Do not assign symbol to this record',
  clearDescription: 'Clear description',
  voiceInputTip: 'Use the microphone button to add more text to the description',
  confirmDeleteRecord: 'Are you sure you want to delete this record? This action cannot be undone.',
  deleteRecordHint: 'Click: confirm | Double click: delete directly',
  comment: 'Comment',
  commentPlaceholder: 'Add comment about this record...',
  addVoiceComment: 'Add voice comment',
  clearComment: 'Clear comment',
  commentWillExport: 'This comment will appear in the Excel report and when transferring time to method'
} as const;
