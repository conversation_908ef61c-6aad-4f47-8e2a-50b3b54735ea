import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Folder, 
  FolderPlus, 
  ChevronRight, 
  ChevronDown, 
  MoreHorizontal, 
  Move, 
  Edit, 
  Trash2, 
  Download,
  FileText,
  Home,
  X,
  Combine
} from 'lucide-react';
import { Button } from './ui/button';
import { FolderTreeNode, StudyWithFolder, Study } from '../types/index';
import { cn } from '../lib/utils';

interface FolderNavigatorProps {
  folders: FolderTreeNode[];
  currentFolder: FolderTreeNode | null;
  studies: StudyWithFolder[];
  selectedStudy: Study | null;
  onFolderSelect: (folder: FolderTreeNode | null) => void;
  onStudySelect: (study: StudyWithFolder) => void;
  onCreateFolder: (parentId?: string) => void;
  onEditFolder: (folder: FolderTreeNode) => void;
  onDeleteFolder: (folder: FolderTreeNode) => void;
  onMoveItem: (itemId: string, itemType: 'study' | 'folder', targetFolderId?: string) => void;
  onBulkExport: (folderId?: string) => void;
  className?: string;
}

export const FolderNavigator: React.FC<FolderNavigatorProps> = ({
  folders,
  currentFolder,
  studies,
  selectedStudy,
  onFolderSelect,
  onStudySelect,
  onCreateFolder,
  onEditFolder,
  onDeleteFolder,
  onMoveItem,
  onBulkExport,
  className
}) => {
  const { t } = useTranslation(['common', 'study']);
  
  console.log('📁 FolderNavigator: Rendering with folders:', folders);
  console.log('📁 FolderNavigator: Current folder:', currentFolder);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [activeMenuFolder, setActiveMenuFolder] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  const parentMap = useMemo(() => {
    const map = new Map<string, string>();
    const buildMap = (nodes: FolderTreeNode[]) => {
      for (const node of nodes) {
        if (node.children) {
          node.children.forEach(child => {
            map.set(child.id, node.id);
          });
          buildMap(node.children);
        }
      }
    };
    buildMap(folders);
    return map;
  }, [folders]);

  const getAncestorIds = useCallback((folderId: string): string[] => {
    const ancestors: string[] = [];
    let currentId: string | undefined = folderId;
    while (currentId) {
        const parentId = parentMap.get(currentId);
        if (parentId) {
            ancestors.unshift(parentId);
        }
        currentId = parentId;
    }
    return ancestors;
  }, [parentMap]);

  // Mobile detection
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newExpanded = new Set(prev);

      if (newExpanded.has(folderId)) {
        // Folder is open, so close it and all its descendants.
        const descendants = new Set<string>();
        const findNode = (nodes: FolderTreeNode[], id: string): FolderTreeNode | null => {
          for (const node of nodes) {
            if (node.id === id) return node;
            if (node.children) {
              const found = findNode(node.children, id);
              if (found) return found;
            }
          }
          return null;
        };
        const collectDescendants = (nodes: FolderTreeNode[]) => {
          for (const n of nodes) {
            descendants.add(n.id);
            if (n.children) collectDescendants(n.children);
          }
        };
        const folderNode = findNode(folders, folderId);
        if (folderNode?.children) {
          collectDescendants(folderNode.children);
        }
        
        newExpanded.delete(folderId);
        descendants.forEach(id => newExpanded.delete(id));
        return newExpanded;
      } else {
        // Folder is closed. Open it and its ancestors, collapsing other branches.
        const ancestorIds = getAncestorIds(folderId);
        return new Set([...ancestorIds, folderId]);
      }
    });
  };

  const FolderItem: React.FC<{ 
    folder: FolderTreeNode; 
    level: number; 
  }> = ({ folder, level }) => {
    const isExpanded = expandedFolders.has(folder.id);
    const isActive = currentFolder?.id === folder.id;
    const hasChildren = folder.children && folder.children.length > 0;
    const showMenu = activeMenuFolder === folder.id;
    
    // Get studies for this folder and check if there's anything to expand
    const studiesInFolder = studies.filter(study => study.folder_id === folder.id);
    const hasContentToExpand = hasChildren || studiesInFolder.length > 0;
    
    // Calcular dinámicamente el conteo de estudios en esta carpeta
    const folderStudyCount = studiesInFolder.length;

    return (
      <div className="relative">
        <div 
          className={cn(
            "flex items-center gap-2 p-2 hover:bg-gray-50 cursor-pointer rounded-md group",
            isActive && "bg-blue-50 border-l-4 border-blue-500",
            level > 0 && "ml-4"
          )}
          style={{ paddingLeft: `${12 + level * 16}px` }}
        >
          {/* Expand/Collapse Button */}
          {hasContentToExpand ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                toggleFolder(folder.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <div className="w-6" />
          )}

          {/* Folder Icon */}
          <div 
            className="flex items-center gap-2 flex-1"
            onClick={() => onFolderSelect(folder)}
          >
            <Folder 
              className="h-5 w-5" 
              style={{ color: folder.color }} 
            />
            <span className="text-sm font-medium">{folder.name}</span>
            
            {/* Study Count Badge */}
            {folderStudyCount > 0 && (
              <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                {folderStudyCount}
              </span>
            )}
          </div>

          {/* Actions Menu */}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
              setActiveMenuFolder(showMenu ? null : folder.id);
            }}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>

          {/* Dropdown Menu */}
          {showMenu && (
            <div className="absolute right-0 top-8 z-10 bg-white border border-gray-200 rounded-md shadow-lg py-1 min-w-[180px]">
              <button
                className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-gray-50"
                onClick={() => {
                  onCreateFolder(folder.id);
                  setActiveMenuFolder(null);
                }}
              >
                <FolderPlus className="h-4 w-4" />
                {t('common:createSubfolder')}
              </button>
              
              <button
                className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-gray-50"
                onClick={() => {
                  onEditFolder(folder);
                  setActiveMenuFolder(null);
                }}
              >
                <Edit className="h-4 w-4" />
                {t('common:edit')}
              </button>

              <button
                className="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-gray-50"
                onClick={() => {
                  onBulkExport(folder.id);
                  setActiveMenuFolder(null);
                }}
              >
                <Combine className="h-4 w-4" />
                {t('common:consolidateStudies', { defaultValue: 'Consolidate Studies' })}
              </button>

              <hr className="my-1" />

              <button
                className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                onClick={() => {
                  onDeleteFolder(folder);
                  setActiveMenuFolder(null);
                }}
              >
                <Trash2 className="h-4 w-4" />
                {t('common:delete')}
              </button>
            </div>
          )}
        </div>

        {/* Child Folders & Studies */}
        {isExpanded && (
          <div>
            {hasChildren && folder.children.map((child) => (
              <FolderItem 
                key={child.id} 
                folder={child} 
                level={level + 1} 
              />
            ))}
            {studiesInFolder.map(study => {
              const isStudySelected = selectedStudy?.id === study.id;
              return (
                <div
                  key={study.id}
                  onClick={() => onStudySelect(study)}
                  className={cn(
                    "flex items-center gap-2 p-2 text-sm hover:bg-gray-50 cursor-pointer rounded-md group",
                    isStudySelected && "bg-blue-50 font-semibold text-blue-700",
                  )}
                  style={{ paddingLeft: `${12 + (level + 1) * 16}px` }}
                >
                  <FileText className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <span className="truncate">{study.required_info?.name || t('study:unnamed')}</span>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => setActiveMenuFolder(null);
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Get studies in current folder
  const currentFolderStudies = studies.filter(study => 
    study.folder_id === currentFolder?.id
  );

  const unorganizedStudies = studies.filter(study => 
    !study.folder_id && !currentFolder
  );

  const displayStudies = currentFolder ? currentFolderStudies : unorganizedStudies;

  return (
    <div className={cn("bg-white border-r border-gray-200 h-full flex flex-col", className)}>
      {/* Mobile Header - Shows close button */}
      {isMobile && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 text-lg">
              {t('common:folders')}
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()} // Or implement close function
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Desktop Header */}
      {!isMobile && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">
              {t('common:folders')}
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCreateFolder()}
              className="h-8 w-8 p-0"
              title={t('common:createFolder')}
            >
              <FolderPlus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className={`flex-1 overflow-y-auto ${isMobile ? 'p-4' : 'p-2'}`}>
        {/* Home/Root Level */}
        <div 
          className={cn(
            "flex items-center gap-3 cursor-pointer rounded-lg transition-colors",
            isMobile ? "p-4 hover:bg-gray-100" : "p-2 hover:bg-gray-50",
            !currentFolder && "bg-blue-50 border-l-4 border-blue-500"
          )}
          onClick={() => onFolderSelect(null)}
        >
          <Home className={`text-gray-500 ${isMobile ? 'h-6 w-6' : 'h-5 w-5'}`} />
          <span className={`font-medium ${isMobile ? 'text-base' : 'text-sm'}`}>
            {t('common:allStudies')}
          </span>
          {studies.length > 0 && (
            <span className={`bg-gray-200 text-gray-700 px-2 py-1 rounded-full ml-auto ${isMobile ? 'text-sm' : 'text-xs'}`}>
              {studies.length}
            </span>
          )}
        </div>

        {/* Folder Tree */}
        <div className={isMobile ? "mt-4 space-y-2" : "mt-2"}>
          {folders.map((folder) => (
            <FolderItem 
              key={folder.id} 
              folder={folder} 
              level={0} 
            />
          ))}
        </div>

        {/* Mobile Create Folder Button */}
        {isMobile && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <Button
              onClick={() => onCreateFolder()}
              className="w-full py-3 text-sm font-medium bg-blue-600 hover:bg-blue-700"
            >
              <FolderPlus className="h-4 w-4 mr-2" />
              {t('common:createFolder')}
            </Button>
          </div>
        )}
      </div>

      {/* Current Folder Actions */}
      {currentFolder && (
        <div className="border-t border-gray-200 p-4">
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-900">
              {currentFolder.name}
            </div>
            <div className="text-xs text-gray-500">
              {displayStudies.length} {t('common:studies')}
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkExport(currentFolder.id)}
                disabled={displayStudies.length === 0}
              >
                <Combine className="h-4 w-4 mr-1" />
                {t('common:consolidate', { defaultValue: 'Consolidate' })}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCreateFolder(currentFolder.id)}
              >
                <FolderPlus className="h-4 w-4 mr-1" />
                {t('common:subfolder')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 