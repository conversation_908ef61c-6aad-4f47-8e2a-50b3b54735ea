-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own study limits" ON user_study_limits;
DROP POLICY IF EXISTS "Users can update their own study limits" ON user_study_limits;
DROP POLICY IF EXISTS "Users can insert their own study limits" ON user_study_limits;
DROP POLICY IF EXISTS "Admin full access" ON user_study_limits;

-- Remove duplicate entries, keeping the most recently updated row for each user
DELETE FROM user_study_limits a
USING (
    SELECT user_id, MAX(updated_at) as max_updated_at
    FROM user_study_limits
    GROUP BY user_id
    HAVING COUNT(*) > 1
) b
WHERE a.user_id = b.user_id
AND a.updated_at < b.max_updated_at;

-- Add unique constraint on user_id
ALTER TABLE user_study_limits DROP CONSTRAINT IF EXISTS user_study_limits_user_id_key;
ALTER TABLE user_study_limits ADD CONSTRAINT user_study_limits_user_id_key UNIQUE (user_id);

-- Recreate policies with proper constraints
CREATE POLICY "Users can view their own study limits"
    ON user_study_limits FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own study limits"
    ON user_study_limits FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can insert their own study limits"
    ON user_study_limits FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admin full access"
    ON user_study_limits
    AS PERMISSIVE
    FOR ALL
    TO authenticated
    USING (
        auth.email() = '<EMAIL>' OR 
        auth.role() = 'service_role'
    )
    WITH CHECK (
        auth.email() = '<EMAIL>' OR 
        auth.role() = 'service_role'
    );