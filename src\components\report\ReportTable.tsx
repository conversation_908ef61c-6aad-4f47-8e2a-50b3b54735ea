import React from 'react';
import { useTranslation } from 'react-i18next';
import { ElementStats, TimeUnit } from '../../types';

interface ReportTableProps {
  elements: ElementStats[];
  timeUnit: TimeUnit;
}

export const ReportTable: React.FC<ReportTableProps> = ({
  elements,
  timeUnit
}) => {
  const { t } = useTranslation(['report', 'method']);

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                {t('table.description', { ns: 'report' })}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                {t('table.type', { ns: 'report' })}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                {t('table.frequency', { ns: 'report' })}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                {`${t('table.observedTime', { ns: 'report' })} (${t(`units.${timeUnit}`, { ns: 'report' })})`}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                {t('table.activity', { ns: 'report' })}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                {`${t('table.supplements', { ns: 'report' })} (%)`}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                {`${t('table.finalTime', { ns: 'report' })} (${t(`units.${timeUnit}`, { ns: 'report' })})`}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {elements?.map((element) => (
              <tr 
                key={element.id}
                className={element.concurrent_machine_time ? "bg-red-100 border-l-4 border-red-500" : ""}
              >
                <td className="px-6 py-4 text-sm text-gray-900 w-2/5">
                  <div className="break-words">
                    {element.description}
                    {element.concurrent_machine_time && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-red-200 text-red-800">
                        {t('concurrent', { ns: 'machine' })}
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-4 py-4 text-center text-sm text-gray-500 w-24">
                  {t(`machineTypes.${element.type}`, { ns: 'report' })}
                </td>
                <td className="px-4 py-4 text-center text-sm text-gray-500 w-24">
                  {element.frequency}
                </td>
                <td className="px-4 py-4 text-center text-sm text-gray-500 w-32">
                  {element.observedTime.toFixed(3)}
                </td>
                <td className="px-4 py-4 text-center text-sm text-gray-500 w-24">
                  {element.averageActivity.toFixed(0)}
                </td>
                <td className="px-4 py-4 text-center text-sm text-gray-500 w-24">
                  {element.supplements.toFixed(1)}
                </td>
                <td className={`px-4 py-4 text-center text-sm w-32 ${element.concurrent_machine_time ? "text-red-500 font-medium italic" : "text-gray-500"}`}>
                  {element.finalTime.toFixed(3)}
                  {element.concurrent_machine_time && (
                    <span className="ml-1 text-xs text-red-600">
                      ({t('excluded', { ns: 'report', defaultValue: 'excluded' })})
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};