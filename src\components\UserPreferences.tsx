import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useUserPreferencesStore } from '../store/userPreferencesStore';
import { useAuthStore } from '../store/authStore';
import { TimeUnit } from '../types';

export const UserPreferences: React.FC = () => {
  const { t, i18n } = useTranslation();
  const user = useAuthStore(state => state.user);
  const { preferences, isLoading, error, fetchPreferences, updatePreferences } = useUserPreferencesStore();

  useEffect(() => {
    if (user?.id) {
      fetchPreferences(user.id);
    }
  }, [user?.id]);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!preferences) return null;

  const handleTimeUnitChange = async (unit: TimeUnit) => {
    await updatePreferences({ default_time_unit: unit });
  };

  const handleLanguageChange = async (language: string) => {
    await updatePreferences({ default_language: language });
    i18n.changeLanguage(language);
  };

  const handleContingencyChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value);
    if (!isNaN(value)) {
      await updatePreferences({ default_contingency: value });
    }
  };

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow">
      <h2 className="text-xl font-semibold">{t('preferences.title')}</h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            {t('preferences.timeUnit')}
          </label>
          <select
            value={preferences.default_time_unit}
            onChange={(e) => handleTimeUnitChange(e.target.value as TimeUnit)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="seconds">{t('units.seconds')}</option>
            <option value="minutes">{t('units.minutes')}</option>
            <option value="mmm">{t('units.mmm')}</option>
            <option value="cmm">{t('units.cmm')}</option>
            <option value="tmu">{t('units.tmu')}</option>
            <option value="dmh">{t('units.dmh')}</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            {t('preferences.language')}
          </label>
          <select
            value={preferences.default_language}
            onChange={(e) => handleLanguageChange(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="es">Español</option>
            <option value="en">English</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            {t('preferences.contingency')}
          </label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <input
              type="number"
              value={preferences.default_contingency}
              onChange={handleContingencyChange}
              min="0"
              step="0.1"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
