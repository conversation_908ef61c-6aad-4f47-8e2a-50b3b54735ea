# Library Element List Component

## Descripción
Lista de elementos de biblioteca con funciones de gestión.

## Props
```typescript
interface LibraryElementListProps {
  elements: LibraryElement[];
  onEditElement: (element: LibraryElement) => void;
  onDeleteElement: (elementId: string) => void;
  onToggleShare: (element: LibraryElement) => void;
}
```

## Características
- Lista paginada de elementos
- Acciones por elemento:
  - Edición
  - Eliminación
  - Compartición
- Estadísticas por elemento
- Filtros y búsqueda
- Ordenación configurable

## Uso
```tsx
<LibraryElementList
  elements={libraryElements}
  onEditElement={handleEdit}
  onDeleteElement={handleDelete}
  onToggleShare={handleShare}
/>
```