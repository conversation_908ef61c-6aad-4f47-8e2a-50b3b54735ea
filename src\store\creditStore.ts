import { create } from 'zustand';
import { supabase } from '../lib/supabaseClient';
import { UserStudyLimits, StudyCredit } from '../types';

interface CreditStore {
  credits: number;
  studyLimits: UserStudyLimits | null;
  isLoading: boolean;
  error: string | null;

  fetchCredits: () => Promise<number>;
  fetchUserLimits: () => Promise<UserStudyLimits | null>;
  checkCredits: () => Promise<StudyCredit>;
  consumeCredit: () => Promise<boolean>;
  resetError: () => void;
}

export const useCreditStore = create<CreditStore>((set, get) => ({
  credits: 0,
  studyLimits: null,
  isLoading: false,
  error: null,

  fetchCredits: async () => {
    const limits = await get().fetchUserLimits();
    if (!limits) return 0;

    // Si tiene suscripción activa, devolver Infinity
    if (limits.subscription_plan && limits.subscription_end_date) {
      const endDate = new Date(limits.subscription_end_date);
      if (endDate > new Date()) {
        set({ credits: Infinity });
        return Infinity;
      }
    }

    // Calcular créditos mensuales disponibles
    const availableMonthlyCredits = Math.max(0, limits.monthly_credits - limits.used_monthly_credits);
    
    // Calcular créditos extra disponibles
    const availableExtraCredits = Math.max(0, limits.extra_credits - limits.used_extra_credits);
    
    // Total de créditos disponibles
    const totalAvailableCredits = availableMonthlyCredits + availableExtraCredits;
    
    set({ credits: totalAvailableCredits });
    return totalAvailableCredits;
  },

  fetchUserLimits: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('user_study_limits')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;

      // Si tiene suscripción activa, verificar la fecha
      if (data.subscription_plan && data.subscription_end_date) {
        const endDate = new Date(data.subscription_end_date);
        if (endDate > new Date()) {
          set({ 
            studyLimits: data, 
            credits: Infinity,
            isLoading: false 
          });
          return data;
        }
      }

      // Actualizar los créditos al mismo tiempo que los límites
      const availableMonthlyCredits = Math.max(0, data.monthly_credits - data.used_monthly_credits);
      const availableExtraCredits = Math.max(0, data.extra_credits - data.used_extra_credits);
      const totalAvailableCredits = availableMonthlyCredits + availableExtraCredits;

      set({ 
        studyLimits: data, 
        credits: totalAvailableCredits,
        isLoading: false 
      });
      
      return data;
    } catch (error) {
      console.error('Error fetching user limits:', error);
      set({ error: (error as Error).message, isLoading: false });
      return null;
    }
  },

  checkCredits: async () => {
    const limits = await get().fetchUserLimits();
    if (!limits) {
      console.log('❌ No limits found');
      return { hasAvailableCredits: false, message: 'Error fetching credits' };
    }

    console.log('📊 Credit limits:', {
      monthly: {
        total: limits.monthly_credits,
        used: limits.used_monthly_credits,
        available: limits.monthly_credits - limits.used_monthly_credits
      },
      extra: {
        total: limits.extra_credits,
        used: limits.used_extra_credits,
        available: limits.extra_credits - limits.used_extra_credits
      }
    });

    // Si tiene suscripción activa y está vigente
    if (limits.subscription_plan && limits.subscription_end_date) {
      const endDate = new Date(limits.subscription_end_date);
      if (endDate > new Date()) {
        console.log('✨ Active subscription found');
        return { 
          hasAvailableCredits: true,
          remainingCredits: Infinity,
          monthlyCredits: Infinity,
          extraCredits: 0,
          usedMonthlyCredits: limits.used_monthly_credits,
          usedExtraCredits: limits.used_extra_credits,
          subscriptionEndDate: limits.subscription_end_date,
          resetDate: limits.reset_date
        };
      }
    }

    // Calcular créditos disponibles
    const availableMonthlyCredits = Math.max(0, limits.monthly_credits - limits.used_monthly_credits);
    const availableExtraCredits = Math.max(0, limits.extra_credits - limits.used_extra_credits);
    const totalAvailableCredits = availableMonthlyCredits + availableExtraCredits;

    // Un usuario tiene créditos disponibles si:
    // 1. Tiene créditos mensuales disponibles, O
    // 2. Tiene créditos extra disponibles
    const hasAvailableCredits = availableMonthlyCredits > 0 || availableExtraCredits > 0;

    console.log('💳 Credits check result:', {
      hasAvailableCredits,
      monthly: availableMonthlyCredits,
      extra: availableExtraCredits,
      total: totalAvailableCredits
    });

    return {
      hasAvailableCredits,
      remainingCredits: totalAvailableCredits,
      monthlyCredits: availableMonthlyCredits,
      extraCredits: availableExtraCredits,
      usedMonthlyCredits: limits.used_monthly_credits,
      usedExtraCredits: limits.used_extra_credits,
      subscriptionEndDate: limits.subscription_end_date,
      resetDate: limits.reset_date
    };
  },

  consumeCredit: async () => {
    const limits = await get().fetchUserLimits();
    if (!limits) return false;

    // Si tiene suscripción activa, no consumir créditos
    if (limits.subscription_plan && limits.subscription_end_date) {
      const endDate = new Date(limits.subscription_end_date);
      if (endDate > new Date()) {
        return true;
      }
    }

    // Primero intentar consumir créditos mensuales
    if (limits.monthly_credits > limits.used_monthly_credits) {
      const { error } = await supabase
        .from('user_study_limits')
        .update({ 
          used_monthly_credits: limits.used_monthly_credits + 1,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', limits.user_id);

      if (!error) {
        await get().fetchUserLimits();
        return true;
      }
    }

    // Si no hay créditos mensuales, intentar consumir créditos extra
    if (limits.extra_credits > limits.used_extra_credits) {
      const { error } = await supabase
        .from('user_study_limits')
        .update({ 
          used_extra_credits: limits.used_extra_credits + 1,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', limits.user_id);

      if (!error) {
        await get().fetchUserLimits();
        return true;
      }
    }

    return false;
  },

  resetError: () => set({ error: null })
}));
