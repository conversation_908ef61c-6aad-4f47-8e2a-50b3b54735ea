# useInstallPrompt Hook

## Descripción
Hook para gestionar la instalación de la PWA.

## Funcionalidades
- Detección de capacidad de instalación
- Gestión del prompt de instalación
- Estado de instalación actual
- Manejo de eventos del ciclo de vida

## Retorno
```typescript
{
  isInstallable: boolean;
  isInstalled: boolean;
  promptToInstall: () => Promise<void>;
}
```

## Estados
- `isInstallable`: Indica si la app puede ser instalada
- `isInstalled`: Indica si la app ya está instalada
- `deferredPrompt`: Almacena el evento de instalación

## Uso
```typescript
const { isInstallable, isInstalled, promptToInstall } = useInstallPrompt();

if (isInstallable && !isInstalled) {
  return (
    <button onClick={promptToInstall}>
      Instalar aplicación
    </button>
  );
}
```