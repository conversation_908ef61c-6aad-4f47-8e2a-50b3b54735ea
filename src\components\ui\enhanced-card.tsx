import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '../../lib/utils';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'minimal' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  interactive?: boolean;
  status?: 'default' | 'success' | 'warning' | 'error' | 'info';
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  hover = false,
  interactive = false,
  status = 'default',
  ...props
}) => {
  const baseClasses = 'rounded-lg sm:rounded-xl overflow-hidden transition-all duration-300 ease-out mobile-animate-fade-in';
  
  const variantClasses = {
    default: 'bg-white border border-gray-200 shadow-sm mobile-hover-lift',
    elevated: 'bg-white shadow-lg border border-gray-100 mobile-hover-lift',
    outlined: 'bg-white border-2 border-gray-300 shadow-none mobile-hover-lift',
    minimal: 'bg-gray-50 border-none shadow-none',
    gradient: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-md mobile-hover-glow'
  };

  const sizeClasses = {
    sm: 'mobile-card-compact',
    md: 'mobile-padding',
    lg: 'mobile-padding-lg'
  };

  const hoverClasses = hover ? 'mobile-hover-lift mobile-active' : '';
  const interactiveClasses = interactive ? 'cursor-pointer mobile-ripple mobile-touch-target' : '';
  
  const statusClasses = {
    default: '',
    success: 'border-l-4 border-l-green-500',
    warning: 'border-l-4 border-l-yellow-500',
    error: 'border-l-4 border-l-red-500',
    info: 'border-l-4 border-l-blue-500'
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        hoverClasses,
        interactiveClasses,
        statusClasses[status],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// Componente Card específico para organizaciones
export const OrganizationCard: React.FC<{
  organization: {
    id: string;
    name: string;
    description?: string;
    invite_code: string;
    memberCount?: number;
  };
  onManage: () => void;
  onCopyCode: (code: string) => void;
  className?: string;
}> = ({ organization, onManage, onCopyCode, className }) => {
  const { t } = useTranslation(['common']);
  return (
    <Card 
      variant="gradient" 
      hover 
      interactive
      className={cn("group", className)}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center mobile-gap mb-2 sm:mb-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-white mobile-text-lg font-bold">
                  {organization.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="mobile-text-lg font-bold text-gray-900 truncate group-hover:text-blue-700 transition-colors">
                  {organization.name}
                </h3>
                {organization.memberCount && (
                  <p className="mobile-text-xs text-gray-500 flex items-center mt-1">
                    <span className="mr-1 flex-shrink-0">👥</span>
                    <span className="truncate">{organization.memberCount} miembros</span>
                  </p>
                )}
              </div>
            </div>
            {organization.description && (
              <p className="mobile-text-xs text-gray-600 line-clamp-2 leading-relaxed">
                {organization.description}
              </p>
            )}
          </div>
        </div>

        {/* Invite Code Section */}
        <div className="bg-white/70 rounded-lg p-3 border border-gray-200/50">
          <p className="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">
            {t('common:inviteCode', { defaultValue: 'Invitation Code' })}
          </p>
          <div className="mobile-flex-col mobile-gap">
            <code className="mobile-text-xs font-mono bg-gray-100 px-2 py-2 sm:py-1 rounded text-gray-800 flex-1 break-all">
              {organization.invite_code}
            </code>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCopyCode(organization.invite_code);
              }}
              className="mobile-button-secondary mobile-touch-target whitespace-nowrap"
            >
              📋 {t('common:copy', { defaultValue: 'Copy' })}
            </button>
          </div>
        </div>


      </div>
    </Card>
  );
};

// Componente Card para miembros
export const MemberCard: React.FC<{
  member: {
    user_id: string;
    user_email: string;
    role: string;
    created_at?: string;
  };
  onRemove?: () => void;
  onPromote?: () => void;
  onDemote?: () => void;
  canManage?: boolean;
  isCurrentUser?: boolean;
  className?: string;
}> = ({ member, onRemove, onPromote, onDemote, canManage, isCurrentUser, className }) => {
  const { t } = useTranslation(['common']);
  const getRoleBadge = (role: string) => {
    const badges = {
      owner: 'bg-purple-100 text-purple-800 border-purple-200',
      admin: 'bg-blue-100 text-blue-800 border-blue-200',
      member: 'bg-green-100 text-green-800 border-green-200'
    };
    
    const icons = {
      owner: '👑',
      admin: '⚡',
      member: '👤'
    };

    return (
      <span className={cn(
        'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold border',
        badges[role as keyof typeof badges] || badges.member
      )}>
        <span className="mr-1">{icons[role as keyof typeof icons] || icons.member}</span>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    );
  };

  return (
    <Card variant="outlined" size="sm" className={cn("group", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Avatar */}
          <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-bold">
              {member.user_email.charAt(0).toUpperCase()}
            </span>
          </div>
          
          {/* Info */}
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-900 truncate">
                {member.user_email}
              </p>
              {isCurrentUser && (
                <span className="text-xs text-blue-600 font-medium">({t('common:you', { defaultValue: 'You' })})</span>
              )}
            </div>
            <div className="flex items-center space-x-2 mt-1">
              {getRoleBadge(member.role)}
              {member.created_at && (
                <span className="text-xs text-gray-500">
                  {t('common:since', { defaultValue: 'Since' })} {new Date(member.created_at).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        {canManage && !isCurrentUser && member.role !== 'owner' && (
          <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {member.role === 'member' && onPromote && (
              <button
                onClick={onPromote}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Promover a admin"
              >
                ⬆️
              </button>
            )}
            {member.role === 'admin' && onDemote && (
              <button
                onClick={onDemote}
                className="p-2 text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                title="Degradar a miembro"
              >
                ⬇️
              </button>
            )}
            {onRemove && (
              <button
                onClick={onRemove}
                className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Eliminar miembro"
              >
                🗑️
              </button>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

// Componente Card para solicitudes
export const RequestCard: React.FC<{
  request: {
    id: string;
    user_email: string;
    created_at: string;
    status?: string;
    organization_name?: string;
    processed_at?: string;
  };
  type: 'incoming' | 'outgoing';
  onApprove?: () => void;
  onReject?: () => void;
  className?: string;
}> = ({ request, type, onApprove, onReject, className }) => {
  const { t } = useTranslation(['common']);

  const getStatusBadge = (status?: string) => {
    if (!status) return null;
    
    const badges = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      approved: 'bg-green-100 text-green-800 border-green-200',
      rejected: 'bg-red-100 text-red-800 border-red-200'
    };
    
    const icons = {
      pending: '⏳',
      approved: '✅',
      rejected: '❌'
    };

    return (
      <span className={cn(
        'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold border',
        badges[status as keyof typeof badges] || badges.pending
      )}>
        <span className="mr-1">{icons[status as keyof typeof icons] || icons.pending}</span>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <Card 
      variant="default" 
      size="sm" 
      status={type === 'incoming' ? 'info' : 'default'}
      className={cn("group", className)}
    >
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3 flex-1">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">
                {request.user_email.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-gray-900 truncate">
                {request.user_email}
              </p>
              {request.organization_name && (
                <p className="text-xs text-gray-600 truncate">
                  {request.organization_name}
                </p>
              )}
            </div>
          </div>
          {request.status && getStatusBadge(request.status)}
        </div>

        {/* Timestamps */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>
            📅 {t('common:joinRequests.sent', { defaultValue: 'Sent:' })} {new Date(request.created_at).toLocaleString()}
          </p>
          {request.processed_at && (
            <p>
              ⚡ {t('common:joinRequests.processed', { defaultValue: 'Processed:' })} {new Date(request.processed_at).toLocaleString()}
            </p>
          )}
        </div>

        {/* Actions for incoming requests */}
        {type === 'incoming' && onApprove && onReject && (
          <div className="flex space-x-2 pt-2">
            <button
              onClick={onApprove}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors"
            >
              ✅ Aprobar
            </button>
            <button
              onClick={onReject}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors"
            >
              ❌ Rechazar
            </button>
          </div>
        )}
      </div>
    </Card>
  );
}; 