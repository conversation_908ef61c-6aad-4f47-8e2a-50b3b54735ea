-- Crear el bucket de logos
INSERT INTO storage.buckets (id, name, public) VALUES ('logos', 'logos', true) ON CONFLICT DO NOTHING;

-- Crear tabla profiles si no existe (para usuarios que actualicen su perfil)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    logo_url TEXT,
    company_name TEXT,
    user_name TEXT,
    email TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Habilitar RLS en la tabla profiles si no está habilitado
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Políticas de storage para el bucket logos
-- Permitir que usuarios autenticados vean objetos públicos
CREATE POLICY "Allow authenticated users to view logos"
ON storage.objects
FOR SELECT
TO authenticated
USING (bucket_id = 'logos');

-- Permitir que usuarios autenticados suban archivos con su user_id como prefijo
CREATE POLICY "Allow authenticated users to upload their own logos"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'logos' AND
    name ~ ('^' || auth.uid()::text || '-.*')
);

-- Permitir que usuarios actualicen sus propios archivos
CREATE POLICY "Allow users to update their own logos"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
    bucket_id = 'logos' AND
    name ~ ('^' || auth.uid()::text || '-.*')
);

-- Permitir que usuarios eliminen sus propios archivos
CREATE POLICY "Allow users to delete their own logos"
ON storage.objects
FOR DELETE
TO authenticated
USING (
    bucket_id = 'logos' AND
    name ~ ('^' || auth.uid()::text || '-.*')
);

-- Crear trigger para updated_at en profiles si no existe
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Comentario
COMMENT ON TABLE public.profiles IS 'Stores user profile information including logo URLs'; 