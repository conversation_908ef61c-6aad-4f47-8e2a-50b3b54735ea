import { ChartVisibilitySettings } from '../types/profile';

export const DEFAULT_CHART_VISIBILITY: ChartVisibilitySettings = {
  machineTypes: true,
  saturation: true,
  elements: true,
  supplements: true,
  cycleDistribution: true,
  flowchartSymbols: true,
  operationPercentages: true,
  operationCount: true,
};

export const CHART_LABELS = {
  machineTypes: 'Distribución por Tipo de Máquina',
  saturation: 'Saturación/Rendimiento Normal',
  elements: 'Elementos del Proceso',
  supplements: 'Suplementos por Elemento',
  cycleDistribution: 'Distribución del Tiempo de Ciclo',
  flowchartSymbols: 'Distribución por Tipo de Operación',
  operationPercentages: 'Porcentajes por Tipo de Operación',
  operationCount: 'Cantidad de Operaciones por Tipo',
};
