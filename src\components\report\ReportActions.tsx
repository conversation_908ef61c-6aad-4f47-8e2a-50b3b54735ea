import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FileText, FileSpreadsheet, Loader2 } from 'lucide-react';
import type { Study } from '../../types/study';
import type { ElementStats, ReportStats, TimeUnit } from '../../types/index';
import { generatePDF } from '../../utils/reportPDF';
import { generateExcel } from '../../utils/reportExcel';
import { captureAllCharts } from '../../utils/chartExport';

interface ReportActionsProps {
  study: Study;
  elementStats: ElementStats[];
  reportStats: ReportStats | null;
  timeUnit: TimeUnit;
  shiftMinutes: number;
  contingency: number;
  pointsPerHour?: number;
}

export const ReportActions: React.FC<ReportActionsProps> = ({
  study,
  elementStats,
  reportStats,
  timeUnit,
  shiftMinutes,
  contingency,
  pointsPerHour = 100
}) => {
  const { t } = useTranslation(['report']);
  const [isExportingPDF, setIsExportingPDF] = useState(false);
  const [isExportingExcel, setIsExportingExcel] = useState(false);

  const handleExportPDF = async () => {
    if (!reportStats) return;
    
    setIsExportingPDF(true);
    try {
      // Capturar gráficas
      const chartData = await captureAllCharts(reportStats, elementStats, t);
      
      // Generar PDF con gráficas
      await generatePDF(study, elementStats, reportStats, timeUnit, shiftMinutes, contingency, t, undefined, chartData, pointsPerHour);
    } catch (error) {
      console.error('Error exportando PDF:', error);
    } finally {
      setIsExportingPDF(false);
    }
  };

  const handleExportExcel = async () => {
    if (!reportStats) return;
    
    setIsExportingExcel(true);
    try {
      // Capturar gráficas
      const chartData = await captureAllCharts(reportStats, elementStats, t);
      
      // Generar Excel con gráficas
      await generateExcel(study, elementStats, reportStats, timeUnit, shiftMinutes, contingency, t, chartData, pointsPerHour);
    } catch (error) {
      console.error('Error exportando Excel:', error);
    } finally {
      setIsExportingExcel(false);
    }
  };

  return (
    <div className="flex justify-end space-x-4">
      <button
        onClick={handleExportPDF}
        disabled={isExportingPDF}
        className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isExportingPDF ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <FileText className="w-5 h-5" />
        )}
        <span>{t('actions.exportPDF')}</span>
      </button>
      <button
        onClick={handleExportExcel}
        disabled={isExportingExcel}
        className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isExportingExcel ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <FileSpreadsheet className="w-5 h-5" />
        )}
        <span>{t('actions.exportExcel')}</span>
      </button>
    </div>
  );
};