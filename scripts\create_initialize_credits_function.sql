-- Función para inicializar los créditos de usuario de manera atómica
CREATE OR REPLACE FUNCTION initialize_user_credits(
  p_user_id UUID,
  p_user_email TEXT,
  p_monthly_credits INTEGER,
  p_reset_date TIMESTAMP WITH TIME ZONE
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_exists BOOLEAN;
BEGIN
  -- Verificar si ya existe un registro para este usuario
  SELECT EXISTS (
    SELECT 1 
    FROM user_study_limits 
    WHERE user_id = p_user_id
  ) INTO v_exists;

  -- Si no existe, crear nuevo registro
  IF NOT v_exists THEN
    INSERT INTO user_study_limits (
      user_id,
      user_email,
      monthly_credits,
      extra_credits,
      used_monthly_credits,
      used_extra_credits,
      reset_date,
      created_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_user_email,
      p_monthly_credits,
      0,
      0,
      0,
      p_reset_date,
      NOW(),
      NOW()
    );
  END IF;
END;
$$;
