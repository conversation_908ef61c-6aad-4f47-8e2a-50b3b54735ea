import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock, Share2, Trash2, Edit } from 'lucide-react';
import { LibraryElement } from '../../types';

interface LibraryElementListProps {
  elements: LibraryElement[];
  onEditElement: (element: LibraryElement) => void;
  onDeleteElement: (elementId: string) => void;
  onToggleShare: (element: LibraryElement) => void;
}

export const LibraryElementList: React.FC<LibraryElementListProps> = ({
  elements,
  onEditElement,
  onDeleteElement,
  onToggleShare
}) => {
  const { t } = useTranslation();

  const formatFrequency = (element: LibraryElement) => {
    return `${element.frequency_repetitions} ${t('method.repetitions')} ${t('common.each')} ${element.frequency_cycles} ${t('method.cycles')}`;
  };

  return (
    <div className="space-y-4">
      {elements.map((element) => (
        <div
          key={element.id}
          className="bg-white rounded-lg shadow-md p-4"
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-medium">
                  {element.description}
                </h3>
                {element.is_shared && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    {t('library.shared')}
                  </span>
                )}
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <p>{formatFrequency(element)}</p>
                <p>{t(`method.types.${element.type}`)} - {t(`method.types.${element.repetition_type}`)}</p>
              </div>
              {element.time_records.length > 0 && (
                <div className="mt-2 flex items-center text-sm text-gray-600">
                  <Clock className="w-4 h-4 mr-1" />
                  <span>{element.time_records.length} {t('chronometer.takes')}</span>
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onToggleShare(element)}
                className={`p-2 rounded-lg ${
                  element.is_shared 
                    ? 'text-green-600 hover:bg-green-50' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
                title={element.is_shared ? t('library.unshare') : t('library.share')}
              >
                <Share2 className="w-5 h-5" />
              </button>
              <button
                onClick={() => onEditElement(element)}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                title={t('common.edit')}
              >
                <Edit className="w-5 h-5" />
              </button>
              <button
                onClick={() => onDeleteElement(element.id)}
                className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                title={t('common.delete')}
              >
                <Trash2 className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};