import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserProfile } from '../types/profile';
import { TimeUnit } from '../types/index';
import { supabase } from '../lib/supabase';
import { useToast } from './ui/use-toast';
import { useFolderStore } from '../store/folderStore';

interface ProfilePreferencesProps {
  profile: UserProfile;
  onUpdate: (updatedProfile: UserProfile) => void;
}

export const ProfilePreferences: React.FC<ProfilePreferencesProps> = ({
  profile,
  onUpdate,
}) => {
  const { t, i18n } = useTranslation('profile');
  const { toast } = useToast();
  const { folders, fetchFolders } = useFolderStore();
  const [newCompany, setNewCompany] = useState('');

  // Cargar carpetas al montar el componente
  useEffect(() => {
    fetchFolders();
  }, [fetchFolders]);

  const handleUpdate = async (updates: Partial<UserProfile>) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        } as any)
        .eq('id', profile.id);

      if (error) throw error;

      // Si se actualizó el idioma, cambiarlo en la aplicación
      if (updates.default_language) {
        i18n.changeLanguage(updates.default_language);
      }

      onUpdate({ ...profile, ...updates });
      
      toast({
        title: t('preferences.updateSuccess'),
        description: t('preferences.updateSuccessMessage'),
      });
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast({
        title: t('preferences.updateError'),
        description: t('preferences.updateErrorMessage'),
        variant: 'destructive',
      });
    }
  };

  const handleAddCompany = () => {
    if (newCompany.trim() && !profile.companies_list?.includes(newCompany.trim())) {
      const updatedList = [...(profile.companies_list || []), newCompany.trim()];
      handleUpdate({ companies_list: updatedList });
      setNewCompany('');
    }
  };

  const handleRemoveCompany = (companyToRemove: string) => {
    const updatedList = profile.companies_list?.filter(company => company !== companyToRemove) || [];
    const updates: Partial<UserProfile> = { companies_list: updatedList };
    
    // Si se está eliminando la empresa por defecto, también resetearla
    if (profile.default_company === companyToRemove) {
      updates.default_company = null;
    }
    
    handleUpdate(updates);
  };

  return (
    <div className="space-y-8">
      {/* ===== CONFIGURACIÓN DE TIEMPO Y UNIDADES ===== */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
          <span className="mr-2">⏱️</span>
          {t('preferences.timeAndUnitsSection', { defaultValue: 'Configuración de Tiempo y Unidades' })}
        </h3>
        
        <div className="space-y-4">
          {/* Unidad de tiempo por defecto */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.defaultTimeUnit')}
            </label>
            <select
              value={profile.default_time_unit}
              onChange={(e) => handleUpdate({ default_time_unit: e.target.value as TimeUnit })}
              className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              title={t('preferences.defaultTimeUnit')}
            >
              <option value="seconds">{t('units.seconds')}</option>
              <option value="minutes">{t('units.minutes')}</option>
              <option value="mmm">{t('units.mmm')}</option>
              <option value="cmm">{t('units.cmm')}</option>
              <option value="tmu">{t('units.tmu')}</option>
              <option value="dmh">{t('units.dmh')}</option>
            </select>
          </div>

          {/* Minutos por turno */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.minutesPerShift')}
            </label>
            <input
              type="number"
              value={profile.minutes_per_shift}
              onChange={(e) => handleUpdate({ minutes_per_shift: parseInt(e.target.value) })}
              min="1"
              step="1"
              className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              title={t('preferences.minutesPerShift')}
              placeholder={t('preferences.minutesPerShift')}
            />
          </div>

          {/* Porcentaje de contingencias por defecto */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.defaultContingency')}
            </label>
            <div className="relative">
              <input
                type="number"
                value={profile.default_contingency}
                onChange={(e) => handleUpdate({ default_contingency: parseFloat(e.target.value) })}
                min="0"
                step="0.1"
                className="w-full rounded-md border border-gray-300 p-2 pr-8 focus:outline-none focus:ring-2 focus:ring-purple-500"
                title={t('preferences.defaultContingency')}
                placeholder={t('preferences.defaultContingency')}
              />
              <span className="absolute right-3 top-2 text-gray-500">%</span>
            </div>
          </div>
          {/* Puntos por hora */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.pointsPerHour')}
            </label>
            <div className="relative">
              <input
                type="number"
                value={profile.points_per_hour}
                onChange={(e) => handleUpdate({ points_per_hour: parseInt(e.target.value) })}
                min="1"
                step="1"
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder={t('preferences.pointsPerHourHelp')}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">{t('preferences.pointsPerHourHelp')}</p>
          </div>
        </div>
      </div>

      {/* ===== CONFIGURACIÓN DE IDIOMA ===== */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
          <span className="mr-2">🌍</span>
          {t('preferences.languageSection', { defaultValue: 'Configuración de Idioma' })}
        </h3>
        
        {/* Idioma por defecto */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('preferences.defaultLanguage')}
          </label>
          <select
            value={profile.default_language}
            onChange={(e) => handleUpdate({ default_language: e.target.value })}
            className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
            title={t('preferences.defaultLanguage')}
          >
            <option value="es">Español</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>

      {/* ===== CONFIGURACIÓN DE ESTUDIOS ===== */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center">
          <span className="mr-2">📊</span>
          {t('preferences.studiesSection', { defaultValue: 'Configuración de Estudios' })}
        </h3>
        
        <div className="space-y-4">
          {/* Escala de actividad por defecto */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.defaultActivityScale')}
            </label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-600 mb-1">{t('preferences.normalActivity')}</label>
                <select
                  value={profile.default_normal_activity ?? 100}
                  onChange={(e) => {
                    const normalActivity = parseInt(e.target.value);
                    const optimalActivity = normalActivity === 60 ? 80 : 133;
                    handleUpdate({
                      default_normal_activity: normalActivity,
                      default_optimal_activity: optimalActivity
                    });
                  }}
                  className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  title={t('preferences.normalActivity')}
                >
                  <option value={60}>60</option>
                  <option value={100}>100</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">{t('preferences.optimalActivity')}</label>
                <select
                  value={profile.default_optimal_activity ?? 133}
                  onChange={(e) => {
                    const optimalActivity = parseInt(e.target.value);
                    const normalActivity = optimalActivity === 80 ? 60 : 100;
                    handleUpdate({
                      default_optimal_activity: optimalActivity,
                      default_normal_activity: normalActivity
                    });
                  }}
                  className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  title={t('preferences.optimalActivity')}
                >
                  <option value={80}>80</option>
                  <option value={133}>133</option>
                </select>
              </div>
            </div>
          </div>

          {/* Mostrar estudios compartidos de la organización */}
          <div className="flex items-center">
            <input
              id="show_shared_studies"
              type="checkbox"
              className="mr-2 h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              checked={profile.show_shared_studies !== false}
              onChange={e => handleUpdate({ show_shared_studies: e.target.checked })}
            />
            <label htmlFor="show_shared_studies" className="text-sm text-gray-700">
              {t('preferences.showSharedStudies', { defaultValue: 'Mostrar estudios compartidos de mi organización' })}
            </label>
          </div>
        </div>
      </div>

      {/* ===== GESTIÓN DE EMPRESAS/DEPARTAMENTOS ===== */}
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center">
          <span className="mr-2">🏢</span>
          {t('preferences.companiesSection', { defaultValue: 'Gestión de Empresas/Departamentos' })}
        </h3>
        
        <div className="space-y-4">
          {/* Gestión de empresas/departamentos */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.companiesList', { defaultValue: 'Empresas / Departamentos' })}
            </label>
            
            {/* Lista de empresas existentes */}
            <div className="space-y-2 mb-4">
              {profile.companies_list && profile.companies_list.length > 0 ? (
                profile.companies_list.map((company, index) => (
                  <div key={index} className="flex items-center justify-between bg-white border border-gray-200 p-3 rounded-md shadow-sm">
                    <span className="text-sm font-medium">{company}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveCompany(company)}
                      className="text-red-600 hover:text-red-800 text-sm font-semibold px-2 py-1 rounded hover:bg-red-50"
                      title={t('preferences.removeCompany', { defaultValue: 'Eliminar empresa' })}
                    >
                      ✕
                    </button>
                  </div>
                ))
              ) : (
                <div className="bg-gray-50 border border-gray-200 p-3 rounded-md text-center">
                  <p className="text-gray-500 text-sm">
                    {t('preferences.noCompanies', { defaultValue: 'No hay empresas/departamentos guardados' })}
                  </p>
                </div>
              )}
            </div>

            {/* Agregar nueva empresa */}
            <div className="flex gap-2">
              <input
                type="text"
                value={newCompany}
                onChange={(e) => setNewCompany(e.target.value)}
                placeholder={t('preferences.addCompanyPlaceholder', { defaultValue: 'Agregar empresa/departamento...' })}
                className="flex-1 rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                onKeyPress={(e) => e.key === 'Enter' && handleAddCompany()}
              />
              <button
                type="button"
                onClick={handleAddCompany}
                disabled={!newCompany.trim()}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                title={t('preferences.addCompany', { defaultValue: 'Agregar empresa' })}
              >
                {t('preferences.add', { defaultValue: 'Agregar' })}
              </button>
            </div>
          </div>

          {/* Empresa por defecto */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('preferences.defaultCompany', { defaultValue: 'Empresa/Departamento por defecto' })}
            </label>
            <select
              value={profile.default_company || ''}
              onChange={(e) => handleUpdate({ default_company: e.target.value || null })}
              className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              title={t('preferences.defaultCompany', { defaultValue: 'Empresa/Departamento por defecto' })}
            >
              <option value="">{t('preferences.noDefaultCompany', { defaultValue: 'Ninguna (escribir manualmente)' })}</option>
              {profile.companies_list?.map((company, index) => (
                <option key={index} value={company}>
                  {company}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">
              {t('preferences.defaultCompanyHelp', { defaultValue: 'Esta empresa se seleccionará automáticamente en nuevos estudios' })}
            </p>
          </div>
        </div>
      </div>

      {/* ===== CONFIGURACIÓN DE CARPETAS ===== */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-yellow-900 mb-4 flex items-center">
          <span className="mr-2">📁</span>
          {t('preferences.foldersSection', { defaultValue: 'Configuración de Carpetas' })}
        </h3>
        
        {/* Carpeta por defecto al abrir la aplicación */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('preferences.defaultFolder', { defaultValue: 'Default folder when opening the app' })}
          </label>
          <select
            value={profile.default_folder_id || ''}
            onChange={(e) => handleUpdate({ default_folder_id: e.target.value || null })}
            className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            title={t('preferences.defaultFolder', { defaultValue: 'Default folder when opening the app' })}
          >
            <option value="">{t('preferences.rootFolder', { defaultValue: 'Raíz (todos los estudios)' })}</option>
            {folders.map((folder) => (
              <option key={folder.id} value={folder.id}>
                {folder.name}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">
            {t('preferences.defaultFolderHelp', { defaultValue: 'This folder will automatically open when starting the application' })}
          </p>
        </div>
      </div>
    </div>
  );
};
