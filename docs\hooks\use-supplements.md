# useSupplements Hook

## Descripción
Hook para cálculos y gestión de suplementos.

## Funcionalidades
- Conversión de puntos a porcentaje
- Validación de rangos
- Cálculos por tipo de factor
- Gestión de selecciones

## Retorno
```typescript
{
  calculatePercentage: (points: Record<string, number>) => number;
  validatePoints: (points: Record<string, number>) => boolean;
  getFactorPoints: (factor: string, selection: any) => number;
  getTotalPoints: (points: Record<string, number>) => number;
}
```

## Uso
```typescript
const { calculatePercentage, getTotalPoints } = useSupplements();

const points = { A1: 5, B2: 3 };
const totalPoints = getTotalPoints(points);
const percentage = calculatePercentage(points);
```