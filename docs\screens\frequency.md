# Pantalla de Tiempos Frecuenciales

## Descripción
Interfaz para la medición de elementos que ocurren con una frecuencia específica.

## Componentes
- `FrequencyElementList`: Lista de elementos frecuenciales
- `FrequencyTimer`: Cronómetro con control de frecuencia
- `FrequencyRecordList`: Lista especializada de registros
- `ElementStats`: Estadísticas con cálculos frecuenciales

## Funcionalidades

### 1. Control de Tiempo
- Iniciar/Pausar/Detener medición
- Registro de tiempos individuales
- Control de ciclos y repeticiones
- Gestión de frecuencias

### 2. Gestión de Frecuencias
- Configuración de ciclos
- Control de repeticiones
- Validación de patrones
- Alertas de desviación

### 3. Registros Especializados
- Agrupación por ciclos
- Estadísticas por frecuencia
- Comentarios contextuales
- Análisis de patrones

### 4. Cálculos Automáticos
- Tiempo por ciclo
- Frecuencia real vs teórica
- Desviaciones por ciclo
- Proyecciones temporales

## Estados
```typescript
{
  selectedElement: WorkElement | null;
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  activity: number;
  currentCycle: number;
  currentRepetition: number;
  records: TimeRecord[];
  editingRecord: TimeRecord | null;
  showCommentModal: boolean;
  showEditModal: boolean;
  showDeleteAllModal: boolean;
}
```

## Flujo de Trabajo
1. Selección de elemento frecuencial
2. Configuración de medición:
   - Definir ciclos
   - Establecer repeticiones
3. Proceso de medición:
   - Cronometrar elementos
   - Registrar frecuencias
   - Validar patrones
4. Análisis de resultados:
   - Revisar estadísticas
   - Validar frecuencias
   - Ajustar si necesario