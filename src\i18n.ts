import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import translationsEN from './locales/en';
import translationsES from './locales/es';

const resources = {
  en: {
    common: translationsEN.common,
    profile: translationsEN.profile,
    study: translationsEN.study,
    credits: translationsEN.credits,
    method: translationsEN.method,
    supplements: translationsEN.supplements,
    chronometer: translationsEN.chronometer,
    library: translationsEN.library,
    privacy: translationsEN.privacy,
    login: translationsEN.login,
    cronoSeguido: translationsEN.cronoSeguido,
    frequency: translationsEN.frequency,
    report: translationsEN.report,
    machine: translationsEN.machine,
    header: translationsEN.header,
    talFactors: translationsEN.talFactors,
    oitFactors: translationsEN.oitFactors,
    subscription: translationsEN.subscription,
    organizations: translationsEN.organizations,
    filters: translationsEN.filters
  },
  es: {
    common: translationsES.common,
    profile: translationsES.profile,
    study: translationsES.study,
    credits: translationsES.credits,
    method: translationsES.method,
    supplements: translationsES.supplements,
    chronometer: translationsES.chronometer,
    library: translationsES.library,
    privacy: translationsES.privacy,
    login: translationsES.login,
    cronoSeguido: translationsES.cronoSeguido,
    frequency: translationsES.frequency,
    report: translationsES.report,
    machine: translationsES.machine,
    header: translationsES.header,
    talFactors: translationsES.talFactors,
    oitFactors: translationsES.oitFactors,
    subscription: translationsES.subscription,
    organizations: translationsES.organizations,
    filters: translationsES.filters
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'profile', 'study', 'credits', 'method', 'supplements', 'chronometer', 'library', 'privacy', 'login', 'cronoSeguido', 'frequency', 'report', 'machine', 'header', 'talFactors', 'oitFactors', 'subscription', 'organizations'],
    detection: {
      order: ['querystring', 'localStorage', 'navigator', 'htmlTag'],
      lookupQuerystring: 'lng',
      lookupLocalStorage: 'i18nextLng',
      lookupFromPathIndex: 0,
      lookupFromSubdomainIndex: 0,
      caches: ['localStorage'],
      excludeCacheFor: ['cimode'],
      htmlTag: document.documentElement,
      checkWhitelist: true
    },
    interpolation: {
      escapeValue: false,
    },
    load: 'languageOnly',
    returnObjects: true,
    debug: false,
    react: {
      useSuspense: true
    }
  });

const userLanguage = navigator.language.split('-')[0];
console.log('Browser language:', navigator.language);
console.log('Detected language:', userLanguage);
if (['es', 'en'].includes(userLanguage)) {
  console.log('Setting language to:', userLanguage);
  i18n.changeLanguage(userLanguage);
} else {
  console.log('Using default language: es');
  i18n.changeLanguage('es');
}

export default i18n;
