import React, { useState, useMemo, useEffect } from 'react';
import { Accordion, AccordionSummary, AccordionDetails, Typography, Box, TextField, InputAdornment, IconButton } from '@mui/material';
import { ChevronDown, Search, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

// Importar todos los archivos markdown
import es_faq from '../../docs/es/faq.md?raw';
import en_faq from '../../docs/en/faq.md?raw';

interface FAQItem {
  question: string;
  answer: string[];
}

function parseFAQMarkdown(markdown: string): FAQItem[] {
  const items: FAQItem[] = [];
  const sections = markdown.split('<details>').slice(1); // Ignorar el primer split que es el título
  
  sections.forEach(section => {
    const questionMatch = section.match(/<summary>(.*?)<\/summary>/);
    if (questionMatch) {
      const question = questionMatch[1].trim();
      const answerText = section
        .split('</summary>')[1]
        .split('</details>')[0]
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.includes('<details>'));
      
      if (question && answerText.length > 0) {
        items.push({
          question,
          answer: answerText
        });
      }
    }
  });

  return items;
}

// Mapa de contenido por idioma
const contentMap: { [key: string]: string } = {
  es: es_faq,
  en: en_faq,
};

export function FAQ() {
  const { i18n, t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedPanel, setExpandedPanel] = useState<number | false>(false);
  const [faqs, setFaqs] = useState<FAQItem[]>([]);

  useEffect(() => {
    const content = contentMap[i18n.language] || contentMap.en;
    const parsedFaqs = parseFAQMarkdown(content);
    setFaqs(parsedFaqs);
  }, [i18n.language]);

  const filteredFaqs = useMemo(() => {
    if (!searchQuery) return faqs;
    const query = searchQuery.toLowerCase();
    return faqs.filter(faq => 
      faq.question.toLowerCase().includes(query) || 
      faq.answer.some(line => line.toLowerCase().includes(query))
    );
  }, [searchQuery, faqs]);

  const handleChange = (panel: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedPanel(isExpanded ? panel : false);
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Box sx={{ mb: 4 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder={t('search.placeholder')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search size={20} />
              </InputAdornment>
            ),
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <IconButton onClick={() => setSearchQuery('')} edge="end" size="small">
                  <X size={20} />
                </IconButton>
              </InputAdornment>
            )
          }}
        />
      </Box>

      {filteredFaqs.map((faq, index) => (
        <Accordion 
          key={index} 
          expanded={expandedPanel === index}
          onChange={handleChange(index)}
          sx={{ 
            mb: 1,
            '&:before': { display: 'none' },
            boxShadow: 'none',
            border: '1px solid',
            borderColor: 'divider',
            '&:not(:last-child)': {
              borderBottom: 0,
            },
            '&:first-of-type': {
              borderTopLeftRadius: 1,
              borderTopRightRadius: 1,
            },
            '&:last-of-type': {
              borderBottomLeftRadius: 1,
              borderBottomRightRadius: 1,
              borderBottom: '1px solid',
              borderColor: 'divider',
            },
          }}
        >
          <AccordionSummary 
            expandIcon={<ChevronDown />}
            sx={{
              '&:hover': {
                bgcolor: 'action.hover',
              },
              '&.Mui-expanded': {
                '& .MuiTypography-root': {
                  fontWeight: 700
                }
              }
            }}
          >
            <Typography sx={{ fontWeight: 500 }}>{faq.question}</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {faq.answer.map((line, i) => (
              <Typography 
                key={i} 
                paragraph={i < faq.answer.length - 1}
                sx={{ 
                  color: 'text.secondary',
                  '&:last-child': {
                    mb: 0
                  }
                }}
              >
                {line}
              </Typography>
            ))}
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
}
