import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Mic, Trash2 } from 'lucide-react';
import { VoiceInputModal } from './VoiceInputModal';
import { getFlowchartSymbols, FlowchartSymbolIcon, FlowchartSymbol } from './FlowchartSymbols';

interface EditTimeModalProps {
  onClose: () => void;
  onSave: (data: { time: number; activity: number; description: string; comment?: string; flowchartSymbol?: FlowchartSymbol }) => void;
  initialData: {
    time: number;
    activity: number;
    description: string;
    comment?: string;
    flowchartSymbol?: FlowchartSymbol;
  };
}

export const EditTimeModal: React.FC<EditTimeModalProps> = ({
  onClose,
  onSave,
  initialData
}) => {
  const { t } = useTranslation(['chronometer', 'common', 'cronoSeguido']);
  const [time, setTime] = useState(initialData.time);
  const [activity, setActivity] = useState(initialData.activity);
  const [description, setDescription] = useState(initialData.description);
  const [comment, setComment] = useState(initialData.comment || '');
  const [flowchartSymbol, setFlowchartSymbol] = useState<FlowchartSymbol | undefined>(initialData.flowchartSymbol);
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [voiceInputTarget, setVoiceInputTarget] = useState<'description' | 'comment'>('description');
  const modalRef = useRef<HTMLDivElement>(null);

  const flowchartSymbols = getFlowchartSymbols((key: string, options?: any) => String(t(key, { ...options, ns: 'cronoSeguido' })));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ time, activity, description, comment, flowchartSymbol });
  };

  const handleVoiceTranscript = (text: string) => {
    if (voiceInputTarget === 'description') {
      // Añadir el nuevo texto a la descripción existente
      setDescription(prev => {
        // Si la descripción anterior está vacía, usar solo el nuevo texto
        if (!prev.trim()) return text;
        // Si no, añadir el nuevo texto como un nuevo párrafo
        return `${prev}\n\n${text}`;
      });
    } else {
      // Añadir el nuevo texto al comentario existente
      setComment(prev => {
        // Si el comentario anterior está vacío, usar solo el nuevo texto
        if (!prev.trim()) return text;
        // Si no, añadir el nuevo texto como un nuevo párrafo
        return `${prev}\n\n${text}`;
      });
    }
  };

  const handleClearDescription = () => {
    setDescription('');
  };

  const handleClearComment = () => {
    setComment('');
  };

  const openVoiceInput = (target: 'description' | 'comment') => {
    setVoiceInputTarget(target);
    setShowVoiceInput(true);
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-40">
        <div ref={modalRef} className="bg-white rounded-lg w-full max-w-md tablet:max-w-2xl laptop:max-w-4xl max-h-[90vh] flex flex-col">
          {/* Header fijo */}
          <div className="flex justify-between items-center p-6 pb-4 border-b border-gray-200">
            <h3 className="text-lg font-bold">{t('chronometer:editRecord')}</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Contenido con scroll */}
          <div className="flex-1 overflow-y-auto p-6 pt-4">
            <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                {t('chronometer:time')}
              </label>
              <input
                type="number"
                step="0.01"
                value={time}
                onChange={(e) => setTime(parseFloat(e.target.value))}
                className="w-full rounded-lg border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">
                {t('chronometer:activity')}
              </label>
              <input
                type="number"
                value={activity}
                onChange={(e) => setActivity(parseInt(e.target.value))}
                className="w-full rounded-lg border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-purple-700">
                {t('chronometer:description')}
              </label>
              <div className="flex flex-col space-y-2">
                <div className="flex items-start space-x-2">
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full rounded-lg border-2 border-purple-300 focus:border-purple-500 focus:ring-purple-500 bg-purple-50"
                    rows={3}
                    placeholder={t('chronometer:descriptionPlaceholder', { defaultValue: 'Element or activity description...' })}
                  />
                  <div className="flex flex-col space-y-2">
                    <button
                      type="button"
                      onClick={() => openVoiceInput('description')}
                      className="p-2 rounded-full bg-purple-500 text-white hover:bg-purple-600"
                      title={t('chronometer:addVoiceDescription')}
                    >
                      <Mic className="w-5 h-5" />
                    </button>
                    <button
                      type="button"
                      onClick={handleClearDescription}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
                      title={t('chronometer:clearDescription')}
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                {description && (
                  <p className="text-xs text-purple-600">
                    {t('chronometer:voiceInputTip')}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-blue-700">
                {t('cronoSeguido:comment', { defaultValue: 'Comentario' })}
              </label>
              <div className="flex flex-col space-y-2">
                <div className="flex items-start space-x-2">
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="w-full rounded-lg border-2 border-blue-300 focus:border-blue-500 focus:ring-blue-500 bg-blue-50"
                    rows={2}
                    placeholder={t('cronoSeguido:commentPlaceholder', { defaultValue: 'Agregar comentario sobre este registro...' })}
                  />
                  <div className="flex flex-col space-y-2">
                    <button
                      type="button"
                      onClick={() => openVoiceInput('comment')}
                      className="p-2 rounded-full bg-blue-500 text-white hover:bg-blue-600"
                      title={t('cronoSeguido:addVoiceComment', { defaultValue: 'Agregar comentario por voz' })}
                    >
                      <Mic className="w-5 h-5" />
                    </button>
                    <button
                      type="button"
                      onClick={handleClearComment}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
                      title={t('cronoSeguido:clearComment', { defaultValue: 'Limpiar comentario' })}
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                {comment && (
                  <p className="text-xs text-blue-600">
                    {t('cronoSeguido:commentWillExport', { defaultValue: 'Este comentario aparecerá en el informe Excel y al pasar el tiempo al método' })}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-green-700">
                {t('cronoSeguido:flowchartSymbol')}
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {flowchartSymbols.map((symbol) => (
                  <button
                    key={symbol.id}
                    type="button"
                    onClick={() => setFlowchartSymbol(symbol.id)}
                    className={`
                      p-3 rounded-lg border-2 transition-all duration-200 text-left
                      ${flowchartSymbol === symbol.id 
                        ? 'border-green-500 bg-green-50 shadow-md' 
                        : 'border-gray-300 bg-white hover:border-green-300 hover:bg-green-25'
                      }
                    `}
                    title={symbol.description}
                  >
                    <div className="flex items-center space-x-2 mb-1">
                      <FlowchartSymbolIcon symbol={symbol.id} className="text-green-600" size={20} />
                      <span className="text-xs font-semibold text-green-800">{symbol.name}</span>
                    </div>
                    <p className="text-xs text-gray-600 leading-tight">{symbol.description}</p>
                  </button>
                ))}
                {/* Opción para quitar el símbolo */}
                <button
                  type="button"
                  onClick={() => setFlowchartSymbol(undefined)}
                  className={`
                    p-3 rounded-lg border-2 transition-all duration-200 text-left
                    ${!flowchartSymbol 
                      ? 'border-gray-500 bg-gray-50 shadow-md' 
                      : 'border-gray-300 bg-white hover:border-gray-400 hover:bg-gray-25'
                    }
                  `}
                  title={t('cronoSeguido:noSymbol', { defaultValue: 'Sin símbolo' })}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-5 h-5 rounded border-2 border-dashed border-gray-400"></div>
                    <span className="text-xs font-semibold text-gray-700">
                      {t('cronoSeguido:noSymbol', { defaultValue: 'Sin símbolo' })}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 leading-tight">
                    {t('cronoSeguido:noSymbolDescription', { defaultValue: 'No asignar símbolo a este registro' })}
                  </p>
                </button>
              </div>
              {flowchartSymbol && (
                <div className="mt-2 p-2 bg-green-50 rounded-md border border-green-200">
                  <div className="flex items-center space-x-2">
                    <FlowchartSymbolIcon symbol={flowchartSymbol} className="text-green-600" size={16} />
                    <span className="text-sm text-green-800 font-medium">
                      {flowchartSymbols.find(s => s.id === flowchartSymbol)?.name}
                    </span>
                  </div>
                  <p className="text-xs text-green-700 mt-1">
                    {flowchartSymbols.find(s => s.id === flowchartSymbol)?.description}
                  </p>
                </div>
              )}
            </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  {t('common:cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  {t('common:save')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <VoiceInputModal
        isOpen={showVoiceInput}
        onClose={() => setShowVoiceInput(false)}
        onTranscript={handleVoiceTranscript}
        title={voiceInputTarget === 'description' 
          ? t('chronometer:addVoiceDescription') 
          : t('cronoSeguido:addVoiceComment', { defaultValue: 'Agregar comentario por voz' })
        }
      />
    </>
  );
};