// Debug script para analizar la discrepancia actual reportada por el usuario

console.log('=== ANÁLISIS DE DISCREPANCIA ACTUAL ===');
console.log('');

// Datos que el usuario reporta en la página de informe:
const reportData = {
  reportValue: 53.32, // segundos
  supplementsValue: 56.30, // segundos
  difference: 2.98 // segundos
};

// Datos que el usuario reporta en la página de suplementos:
const supplementsPageData = {
  baseTime: 50.47, // seg
  personalNeeds: 2.52, // seg
  fatigue: 3.31, // seg
  totalCycle: 52.99 // segundos (resultado final)
};

console.log('DATOS REPORTADOS POR EL USUARIO:');
console.log('');

console.log('En la página de INFORME:');
console.log(`  ▸ Valor del informe: ${reportData.reportValue} segundos`);
console.log(`  ▸ Valor de suplementos: ${reportData.supplementsValue} segundos`);
console.log(`  ▸ Diferencia: ${reportData.difference} segundos`);
console.log('');

console.log('En la página de SUPLEMENTOS (cálculo de fatiga):');
console.log(`  ▸ Tiempo Base: ${supplementsPageData.baseTime} seg`);
console.log(`  ▸ Suplemento por Necesidades Personales: ${supplementsPageData.personalNeeds} seg`);
console.log(`  ▸ Suplemento por Fatiga: ${supplementsPageData.fatigue} seg`);
console.log(`  ▸ Ciclo Normal Total: ${supplementsPageData.totalCycle} segundos`);
console.log('');

console.log('=== VERIFICACIÓN DE CONSISTENCIA ===');
console.log('');

// Verificar si los cálculos en la página de suplementos son correctos
const calculatedTotal = supplementsPageData.baseTime + supplementsPageData.personalNeeds + supplementsPageData.fatigue;
console.log('Verificación cálculo página de suplementos:');
console.log(`  ▸ ${supplementsPageData.baseTime} + ${supplementsPageData.personalNeeds} + ${supplementsPageData.fatigue} = ${calculatedTotal.toFixed(2)}`);
console.log(`  ▸ Resultado mostrado: ${supplementsPageData.totalCycle}`);
console.log(`  ▸ ¿Coinciden? ${Math.abs(calculatedTotal - supplementsPageData.totalCycle) < 0.01 ? '✅ SÍ' : '❌ NO'}`);
console.log('');

console.log('=== PROBLEMA IDENTIFICADO ===');
console.log('');

console.log('INCONSISTENCIA ENTRE PÁGINAS:');
console.log(`  ▸ Página de suplementos muestra: ${supplementsPageData.totalCycle} segundos`);
console.log(`  ▸ Página de informe lee de suplementos: ${reportData.supplementsValue} segundos`);
console.log(`  ▸ Diferencia: ${Math.abs(supplementsPageData.totalCycle - reportData.supplementsValue).toFixed(2)} segundos`);
console.log('');

console.log('POSIBLES CAUSAS:');
console.log('1. 📊 El valor guardado en __machine_cycle_data__.totalCycleTime no coincide con lo mostrado');
console.log('2. 🔄 Hay un error en el proceso de guardado/lectura de los datos');
console.log('3. 🕐 Los datos no se actualizaron después del último cálculo');
console.log('4. 📋 La página de suplementos muestra datos de un cálculo diferente');
console.log('');

console.log('PASOS PARA INVESTIGAR:');
console.log('1. Revisar qué valor está realmente guardado en __machine_cycle_data__.totalCycleTime');
console.log('2. Verificar si el guardado se ejecuta correctamente después del cálculo');
console.log('3. Confirmar que ambas páginas leen del mismo lugar');
console.log('4. Verificar timestamps para confirmar que los datos están actualizados');
console.log('');

console.log('VALOR ESPERADO:');
console.log(`  ▸ El "Valor de suplementos" en el informe debería ser: ${supplementsPageData.totalCycle} segundos`);
console.log(`  ▸ No: ${reportData.supplementsValue} segundos`); 