// src/components/ChronometerControls.tsx
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Play, Pause, Square, ArrowUp, ArrowDown, Timer } from 'lucide-react';
import { playSoundAndVibrate, initializeAudioContext } from '../utils/sounds';

interface ChronometerControlsProps {
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  activity: number;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onLap: () => void;
  onActivityChange: (increment: boolean) => void;
}

export const ChronometerControls: React.FC<ChronometerControlsProps> = ({
  isRunning,
  isPaused,
  time,
  activity,
  onStart,
  onPause,
  onStop,
  onLap,
  onActivityChange,
}) => {
  const { t } = useTranslation(['chronometer', 'common']);

  useEffect(() => {
    // Inicializa AudioContext en la primera interacción del usuario
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener('click', handleFirstInteraction);
    };
    document.addEventListener('click', handleFirstInteraction);
    return () => {
      document.removeEventListener('click', handleFirstInteraction);
    };
  }, []);

  const handleStart = async () => {
    await playSoundAndVibrate('start', 200); // Sonido + vibración de 200ms
    onStart();
  };

  const handlePause = async () => {
    await playSoundAndVibrate('stop', 100); // Sonido + vibración de 100ms
    onPause();
  };

  const handleStop = async () => {
    await playSoundAndVibrate('stop', [100, 50, 100]); // Sonido + patrón de vibración
    onStop();
  };

  const handleLap = async () => {
    await playSoundAndVibrate('lap', 150); // Sonido + vibración de 150ms
    onLap();
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => onActivityChange(false)}
          className="p-2 rounded-full hover:bg-gray-100"
          title={t('decreaseActivity', { ns: 'chronometer' })}
        >
          <ArrowDown className="w-6 h-6" />
        </button>

        <div className="text-4xl font-mono font-bold">
          {(time / 1000).toFixed(2)}
        </div>

        <button
          onClick={() => onActivityChange(true)}
          className="p-2 rounded-full hover:bg-gray-100"
          title={t('increaseActivity', { ns: 'chronometer' })}
        >
          <ArrowUp className="w-6 h-6" />
        </button>
      </div>

      <div className="text-center mb-6">
        <span className="text-2xl font-bold">{activity}</span>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={handleStop}
          disabled={!isRunning && !isPaused}
          className="p-4 rounded-lg bg-red-500 text-white font-bold disabled:opacity-50"
        >
          <Square className="mx-auto" />
        </button>
        <button
          onClick={isRunning ? handlePause : handleStart}
          className={`p-4 rounded-lg text-white font-bold
            ${isRunning ? 'bg-yellow-500' : 'bg-green-500'}`}
        >
          {isRunning ? <Pause className="mx-auto" /> : <Play className="mx-auto" />}
        </button>
      </div>

      <button
        onClick={handleLap}
        disabled={!isRunning}
        className="w-full mt-4 p-4 rounded-lg bg-blue-500 text-white font-bold disabled:opacity-50 flex items-center justify-center space-x-2"
      >
        <Timer className="w-5 h-5" />
        <span>{t('nextElement', { ns: 'chronometer' })}</span>
      </button>
    </div>
  );
};
