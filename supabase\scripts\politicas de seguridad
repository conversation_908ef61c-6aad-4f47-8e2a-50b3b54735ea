-- Eliminar políticas existentes
DROP POLICY IF EXISTS "Users can read their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can delete their own profile" ON profiles;

DROP POLICY IF EXISTS "Enable read for authenticated admin users only" ON profiles;

DROP POLICY IF EXISTS "Users can view organizations they are members of" ON organizations;
DROP POLICY IF EXISTS "Users can view shared organization studies v2" ON studies;
DROP POLICY IF EXISTS "Users can view their organizations v2" ON organizations;
DROP POLICY IF EXISTS "Users can create organizations" ON organizations;
DROP POLICY IF EXISTS "Users can view members of their organizations v2" ON organization_members;

DROP POLICY IF EXISTS "Organization creators can manage members" ON organization_members;
DROP POLICY IF EXISTS "Users can update their own limits" ON user_study_limits;  -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "Users can read their own limits" ON user_study_limits;    -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "Users can insert their own studies" ON studies;
DROP POLICY IF EXISTS "Users can manage own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view their own transactions" ON transactions;
DROP POLICY IF EXISTS "System can insert transactions" ON transactions;
DROP POLICY IF EXISTS "Service role has full access" ON transactions;
DROP POLICY IF EXISTS "users_insert_own_limits" ON user_study_limits;  -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "admin_manage_all_limits" ON user_study_limits;    -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "users_manage_own_limits" ON user_study_limits;    -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "Users can delete their own studies" ON studies;
DROP POLICY IF EXISTS "Users can view their own studies" ON studies;
DROP POLICY IF EXISTS "Users can update their own studies" ON studies;
DROP POLICY IF EXISTS "Only admins can modify study limits" ON user_study_limits;  -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "Users can manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can create join requests" ON organization_join_requests;
DROP POLICY IF EXISTS "Users can view their own join requests" ON organization_join_requests;
DROP POLICY IF EXISTS "Admins can update join requests" ON organization_join_requests;
DROP POLICY IF EXISTS "Users can view their own study limits" ON user_study_limits;  -- Cambiado a user_study_limits
DROP POLICY IF EXISTS "Users can update their own study limits" ON user_study_limits;  -- Cambiado a user_study_limits

-- Crear políticas actuales
CREATE POLICY "Users can read their own profile" ON profiles FOR SELECT TO authenticated USING ((auth.uid()) = id);
CREATE POLICY "Users can insert their own profile" ON profiles FOR INSERT TO authenticated WITH CHECK ((auth.uid()) = id);
CREATE POLICY "Users can update their own profile" ON profiles FOR UPDATE TO authenticated USING ((auth.uid()) = id) WITH CHECK ((auth.uid()) = id);
CREATE POLICY "Users can delete their own profile" ON profiles FOR DELETE TO authenticated USING ((auth.uid()) = id);

CREATE POLICY "Enable read for authenticated admin users only" ON profiles FOR SELECT TO authenticated USING ((auth.email()) = '<EMAIL>'::text);

CREATE POLICY "Users can view organizations they are members of" ON organizations FOR SELECT TO authenticated USING (((created_by = (auth.uid())) OR (id IN (SELECT organization_members.organization_id FROM organization_members WHERE (organization_members.user_id = (auth.uid()))))));

CREATE POLICY "Users can view shared organization studies v2" ON studies FOR SELECT TO authenticated USING (((auth.uid()) = user_id) OR ((is_shared = true) AND (organization_id IS NOT NULL) AND is_organization_member(organization_id, (auth.uid()))));

CREATE POLICY "Users can view their organizations v2" ON organizations FOR SELECT TO authenticated USING (((created_by = (auth.uid())) OR (id IN (SELECT get_user_organizations((auth.uid())) AS get_user_organizations))));

CREATE POLICY "Users can create organizations" ON organizations FOR INSERT TO authenticated WITH CHECK ((created_by = (auth.uid())));

CREATE POLICY "Users can view members of their organizations v2" ON organization_members FOR SELECT TO authenticated USING (((user_id = (auth.uid())) OR (organization_id IN (SELECT get_user_organizations((auth.uid())) ))));

CREATE POLICY "Organization creators can manage members" ON organization_members FOR ALL TO authenticated USING (is_organization_creator(organization_id, (auth.uid())));

CREATE POLICY "Users can update their own limits" ON user_study_limits FOR UPDATE TO authenticated USING ((auth.uid()) = user_id) WITH CHECK ((auth.uid()) = user_id);
CREATE POLICY "Users can read their own limits" ON user_study_limits FOR SELECT TO authenticated USING ((auth.uid()) = user_id);

CREATE POLICY "Users can insert their own studies" ON studies FOR INSERT TO authenticated WITH CHECK ((auth.uid()) = user_id);

CREATE POLICY "Users can manage own profile" ON profiles FOR ALL TO authenticated USING ((auth.uid()) = id) WITH CHECK ((auth.uid()) = id);

CREATE POLICY "Users can view their own transactions" ON transactions FOR SELECT TO public USING ((auth.uid()) = user_id);
CREATE POLICY "System can insert transactions" ON transactions FOR INSERT TO public WITH CHECK (true);
CREATE POLICY "Service role has full access" ON transactions FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "users_insert_own_limits" ON user_study_limits FOR INSERT TO authenticated WITH CHECK (((auth.uid()) = user_id) OR ((auth.email()) = '<EMAIL>'::text));
CREATE POLICY "admin_manage_all_limits" ON user_study_limits FOR ALL TO authenticated USING ((auth.email()) = '<EMAIL>'::text) WITH CHECK ((auth.email()) = '<EMAIL>'::text);
CREATE POLICY "users_manage_own_limits" ON user_study_limits FOR ALL TO authenticated USING ((auth.uid()) = user_id) WITH CHECK ((auth.uid()) = user_id);

CREATE POLICY "Users can delete their own studies" ON studies FOR DELETE TO public USING ((user_id = (auth.uid())) OR (organization_id IN (SELECT organization_members.organization_id FROM organization_members WHERE organization_members.user_id = auth.uid() AND role IN ('owner', 'admin'))));
CREATE POLICY "Users can view their own studies" ON studies FOR SELECT TO public USING ((user_id = (auth.uid())) OR (organization_id IN (SELECT organization_members.organization_id FROM organization_members WHERE organization_members.user_id = auth.uid())));
CREATE POLICY "Users can update their own studies" ON studies FOR UPDATE TO public USING ((user_id = (auth.uid())) OR (organization_id IN (SELECT organization_members.organization_id FROM organization_members WHERE organization_members.user_id = auth.uid() AND role IN ('owner', 'admin'))));

CREATE POLICY "Only admins can modify study limits" ON user_study_limits FOR ALL TO public USING ((auth.role()) = 'service_role'::text);

CREATE POLICY "Users can manage their own profile" ON profiles FOR ALL TO public USING ((auth.uid()) = id);

CREATE POLICY "Users can create join requests" ON organization_join_requests FOR INSERT TO authenticated WITH CHECK (((auth.uid()) = user_id) AND (status = 'pending'::text) AND (NOT (EXISTS (SELECT 1 FROM organization_members WHERE ((organization_members.organization_id = organization_join_requests.organization_id) AND (organization_members.user_id = (auth.uid())))))));

CREATE POLICY "Users can view their own join requests" ON organization_join_requests FOR SELECT TO authenticated USING (((auth.uid()) = user_id) OR (EXISTS (SELECT 1 FROM organization_members WHERE ((organization_members.organization_id = organization_join_requests.organization_id) AND (organization_members.user_id = (auth.uid()) AND (organization_members.role = ANY (ARRAY['owner'::text, 'admin'::text])))))));

CREATE POLICY "Admins can update join requests" ON organization_join_requests FOR UPDATE TO public USING (((auth.uid()) = user_id) OR (EXISTS (SELECT 1 FROM organization_members WHERE ((organization_members.organization_id = organization_join_requests.organization_id) AND (organization_members.user_id = (auth.uid()) AND (organization_members.role = ANY (ARRAY['admin'::text, 'owner'::text])))))));

CREATE POLICY "Users can view their own study limits" ON user_study_limits FOR SELECT TO authenticated USING ((auth.uid()) = user_id);
CREATE POLICY "Users can update their own study limits" ON user_study_limits FOR UPDATE TO authenticated USING ((auth.uid()) = user_id) WITH CHECK ((auth.uid()) = user_id);