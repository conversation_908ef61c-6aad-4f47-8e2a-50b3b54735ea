import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ReportStats, TimeUnit } from '../../types/index';
import { InfoIcon, AlertTriangle, Calculator, Ruler } from 'lucide-react';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import { CycleDiscrepancyAlert } from './CycleDiscrepancyAlert';
import { SupplementsDataAlert } from './SupplementsDataAlert';
import { useStudyStore } from '../../store/studyStore';
import { useNavigate } from 'react-router-dom';

interface ReportResultsProps {
  stats: ReportStats;
  timeUnit: TimeUnit;
  shiftMinutes?: number;
  contingency?: number;
  onStatsUpdate?: (updatedStats: ReportStats) => void;
  pointsPerHour?: number;
}

interface ResultCardProps {
  title: string;
  value: number | string;
  unit?: string;
  description?: string;
  hasDiscrepancy?: boolean;
}

interface CustomUnitValues {
  enableSquareMeters: boolean;
  enableLinearMeters: boolean;
  enableCubicMeters: boolean;
  enableKilos: boolean;
  enablePerimeter: boolean;
  squareMeters: {
    length: number;
    width: number;
  };
  linearMeters: {
    length: number;
  };
  cubicMeters: {
    length: number;
    width: number;
    height: number;
  };
  kilos: {
    weight: number;
  };
  perimeter: {
    length: number;
    width: number;
  };
}

export const ReportResults = ({
  stats,
  timeUnit,
  shiftMinutes = 480,
  contingency = 0,
  pointsPerHour = 100,
  onStatsUpdate
}: ReportResultsProps) => {
  const { t } = useTranslation(['report']);
  const { selectedStudy, updateStudy } = useStudyStore();
  const navigate = useNavigate();
  const [selectedCycleSource, setSelectedCycleSource] = useState<'report' | 'supplements'>(
    stats.cycleDiscrepancy?.selectedCycleSource || 'report'
  );
  const [activeTab, setActiveTab] = useState<'cycle' | 'squareMeters' | 'linearMeters' | 'cubicMeters' | 'kilos' | 'perimeter'>('cycle');

  // Obtener configuración de unidades personalizadas
  const customUnits: CustomUnitValues | null = selectedStudy?.optional_info?.customUnits || null;

  // Función para calcular valores por unidad personalizada
  const calculateCustomUnitValues = (unitType: 'squareMeters' | 'linearMeters' | 'cubicMeters' | 'kilos' | 'perimeter') => {
    if (!customUnits) return null;

    const displayedNormalCycle = getDisplayedNormalCycle();
    let unitValue = 0;
    let unitLabel = '';

    switch (unitType) {
      case 'squareMeters':
        unitValue = customUnits.squareMeters.length * customUnits.squareMeters.width;
        unitLabel = 'm²';
        break;
      case 'linearMeters':
        unitValue = customUnits.linearMeters.length;
        unitLabel = 'm';
        break;
      case 'cubicMeters':
        unitValue = customUnits.cubicMeters.length * customUnits.cubicMeters.width * customUnits.cubicMeters.height;
        unitLabel = 'm³';
        break;
      case 'kilos':
        unitValue = customUnits.kilos.weight;
        unitLabel = 'kg';
        break;
      case 'perimeter':
        unitValue = 2 * (customUnits.perimeter.length + customUnits.perimeter.width);
        unitLabel = 'm (perímetro)';
        break;
    }

    if (unitValue <= 0) return null;

    // Calcular todos los valores divididos por la unidad personalizada
    return {
      unitValue,
      unitLabel,
      normalCycle: +(displayedNormalCycle / unitValue).toFixed(4),
      optimalCycle: +(stats.optimalCycle / unitValue).toFixed(4),
      normalProduction: stats.normalProduction * unitValue >= 1 ? Math.floor(stats.normalProduction * unitValue) : +(stats.normalProduction * unitValue).toFixed(3),
      optimalProduction: stats.optimalProduction * unitValue >= 1 ? Math.floor(stats.optimalProduction * unitValue) : +(stats.optimalProduction * unitValue).toFixed(3),
      normalProductionPerHour: +(stats.normalProductionPerHour * unitValue).toFixed(2),
      optimalProductionPerHour: +(stats.optimalProductionPerHour * unitValue).toFixed(2),
      valueHour: +(stats.valueHour / unitValue).toFixed(4),
      valueMinute: +(stats.valueMinute / unitValue).toFixed(4),
      valuePoint: +(stats.valuePoint / unitValue).toFixed(4)
    };
  };

  const handleCycleSourceChange = async (source: 'report' | 'supplements') => {
    setSelectedCycleSource(source);

    if (selectedStudy) {
      try {
        // Temporarily save cycle preference in supplements field
        const updatedSupplements = {
          ...(selectedStudy.supplements || {}),
          '__cycle_preference__': source
        };

        await updateStudy(selectedStudy.id, {
          supplements: updatedSupplements,
          updated_at: new Date().toISOString()
        });

        // Update stats with the selected cycle value and recalculate all dependent values
        if (onStatsUpdate && stats.cycleDiscrepancy) {
          // Recalculate all values that depend on the normal cycle
          // IMPORTANTE: Usar timeUnit (unidad actual) en lugar de stats.timeUnit (unidad original)

          // Use the same conversion constants as reportcalculations.ts
          const SHIFT_CONVERSIONS: Record<TimeUnit, number> = {
            seconds: 60,    // 60 seconds per minute
            minutes: 1,     // direct value in minutes
            hours: 1/60,
            mmm: 1000,      // 1000 milésimas per minute
            cmm: 100,       // 100 centésimas per minute
            tmu: 100000/60, // Valor exacto: 1666.6666666666667
            dmh: 10000/60   // Valor exacto: 166.66666666666666
          };

          // Conversion from seconds to study time unit
          const TIME_UNIT_CONVERSIONS: Record<TimeUnit, number> = {
            seconds: 1,
            minutes: 1/60,
            hours: 1/3600,
            mmm: 1000/60,         // Valor exacto: 16.666666666666668
            cmm: 100/60,          // Valor exacto: 1.6666666666666667
            tmu: 100000/3600,     // Valor exacto: 27.77777777777778
            dmh: 10000/3600       // Valor exacto: 2.7777777777777777
          };

          // Get the selected cycle value - both values are already in the study time unit
          let selectedCycleValue: number;
          if (source === 'supplements') {
            // Supplements value is already converted to study time unit in reportcalculations.ts
            selectedCycleValue = stats.cycleDiscrepancy.supplementsCycleTime;
            console.log('🔄 USANDO VALOR DE SUPLEMENTOS (ya convertido):', {
              supplementsCycleTime: stats.cycleDiscrepancy.supplementsCycleTime,
              currentTimeUnit: timeUnit,
              selectedCycleValue,
              note: 'No se requiere conversión adicional'
            });
          } else {
            // Report value is already in the correct unit
            selectedCycleValue = stats.cycleDiscrepancy.reportCycleTime;
          }

          // CORRECCIÓN: Los stats ya están en la unidad correcta del estudio, NO en segundos
          // No necesitamos convertir stats.normalCycle ni stats.optimalCycle
          
          // Calcular el ratio usando los valores originales directamente (ya están en la unidad correcta)
          const originalRatio = stats.optimalCycle / stats.normalCycle;
          const recalculatedOptimalCycle = selectedCycleValue * originalRatio;

          console.log('🔄 RECÁLCULO PROPORCIONAL DE CICLO ÓPTIMO (CORREGIDO):', {
            currentTimeUnit: timeUnit,
            originalValues: {
              normalCycle: stats.normalCycle,
              optimalCycle: stats.optimalCycle,
              note: 'Estos valores ya están en la unidad correcta del estudio'
            },
            selectedCycleValue,
            originalRatio,
            recalculatedOptimalCycle,
            formula: `${selectedCycleValue} × ${originalRatio.toFixed(4)} = ${recalculatedOptimalCycle.toFixed(3)}`,
            unitCheck: `Todos los valores están en ${timeUnit}`
          });

          // Production calculations
          const PRODUCTION_HOUR_DIVISORS: Record<TimeUnit, number> = {
            seconds: 3600,
            minutes: 60,
            hours: 1,
            mmm: 60000,
            cmm: 6000,
            tmu: 100000,
            dmh: 10000
          };

          const VALUE_HOUR_DIVISORS: Record<TimeUnit, number> = {
            seconds: 3600,
            minutes: 60,
            hours: 1,
            mmm: 60000,
            cmm: 6000,
            tmu: 100000,
            dmh: 10000
          };

          const VALUE_MINUTE_DIVISORS: Record<TimeUnit, number> = {
            seconds: 60,
            minutes: 1,
            hours: 1/60,
            mmm: 1000,
            cmm: 100,
            tmu: 100000/60, // Valor exacto: 1666.6666666666667
            dmh: 10000/60   // Valor exacto: 166.66666666666666
          };

          const VALUE_POINT_DIVISORS: Record<TimeUnit, number> = {
            seconds: 36,    // 3600/100 seconds per point
            minutes: 0.6,   // 60/100 minutes per point
            hours: 0.01,
            mmm: 600,       // 60000/100 milésimas per point
            cmm: 60,        // 6000/100 centésimas per point
            tmu: 1000,      // 100000/100 TMU per point
            dmh: 100        // 10000/100 DMH per point
          };

          // Convert shift minutes to the correct time unit
          const shiftTimeInUnit = shiftMinutes * SHIFT_CONVERSIONS[timeUnit];

          console.log('🔄 RECALCULANDO EN COMPONENTE:', {
            shiftMinutes,
            currentTimeUnit: timeUnit,
            shiftConversion: SHIFT_CONVERSIONS[timeUnit],
            shiftTimeInUnit,
            selectedCycleValue,
            recalculatedOptimalCycle,
            source,
            calculoEsperado: `${shiftTimeInUnit} / ${selectedCycleValue} = ${shiftTimeInUnit / selectedCycleValue}`
          });

          const recalculatedNormalProductionRaw = shiftTimeInUnit / selectedCycleValue;
          const recalculatedOptimalProductionRaw = shiftTimeInUnit / recalculatedOptimalCycle;
          
          // Only use Math.floor when production is >= 1, otherwise keep the decimal value
          const recalculatedNormalProduction = recalculatedNormalProductionRaw >= 1 ? Math.floor(recalculatedNormalProductionRaw) : +recalculatedNormalProductionRaw.toFixed(3);
          const recalculatedOptimalProduction = recalculatedOptimalProductionRaw >= 1 ? Math.floor(recalculatedOptimalProductionRaw) : +recalculatedOptimalProductionRaw.toFixed(3);
          const recalculatedNormalProductionPerHour = +(PRODUCTION_HOUR_DIVISORS[timeUnit] / selectedCycleValue).toFixed(2);
          const recalculatedOptimalProductionPerHour = +(PRODUCTION_HOUR_DIVISORS[timeUnit] / recalculatedOptimalCycle).toFixed(2);
          const recalculatedMaxActivity = (selectedCycleValue / recalculatedOptimalCycle) * 100;
          const recalculatedValueHour = +(selectedCycleValue / VALUE_HOUR_DIVISORS[timeUnit]).toFixed(4);
          const recalculatedValueMinute = +(selectedCycleValue / VALUE_MINUTE_DIVISORS[timeUnit]).toFixed(4);
          const recalculatedValuePoint = selectedCycleValue / VALUE_POINT_DIVISORS[timeUnit];

          // Recalculate normal saturation
          const recalculatedNormalSaturation = ((stats.machineStoppedTime + stats.machineRunningTime) / selectedCycleValue) * 100;

          // Recalculate optimal saturation using optimal machine times
          // The optimal times are reduced by the activity factor (133/100 = 1.33)
          const activityFactor = 133 / 100; // Default optimal activity factor
          const optimalMachineStoppedTime = stats.machineStoppedTime / activityFactor;
          const optimalMachineRunningTime = stats.machineRunningTime / activityFactor;
          const recalculatedOptimalSaturation = ((optimalMachineStoppedTime + optimalMachineRunningTime) / recalculatedOptimalCycle) * 100;

          console.log('🔄 RECALCULANDO SATURACIONES:', {
            normalSaturation: {
              machineStoppedTime: stats.machineStoppedTime,
              machineRunningTime: stats.machineRunningTime,
              totalMachineTime: stats.machineStoppedTime + stats.machineRunningTime,
              cycleTime: selectedCycleValue,
              saturation: recalculatedNormalSaturation
            },
            optimalSaturation: {
              optimalMachineStoppedTime,
              optimalMachineRunningTime,
              totalOptimalMachineTime: optimalMachineStoppedTime + optimalMachineRunningTime,
              optimalCycleTime: recalculatedOptimalCycle,
              saturation: recalculatedOptimalSaturation,
              activityFactor
            }
          });

          // Recalculate contingency time based on the selected cycle
          const baseCycleForContingency = source === 'supplements'
            ? (selectedStudy?.supplements?.['__machine_cycle_data__']?.baseTime || selectedCycleValue)
            : (selectedCycleValue - stats.contingencyTime); // Remove existing contingency to get base
          const recalculatedContingencyTime = (baseCycleForContingency * contingency) / 100;

          const updatedStats = {
            ...stats,
            normalCycle: selectedCycleValue,
            optimalCycle: recalculatedOptimalCycle,
            contingencyTime: recalculatedContingencyTime,
            normalProduction: recalculatedNormalProduction,
            optimalProduction: recalculatedOptimalProduction,
            normalProductionPerHour: recalculatedNormalProductionPerHour,
            optimalProductionPerHour: recalculatedOptimalProductionPerHour,
            maxActivity: recalculatedMaxActivity,
            normalSaturation: recalculatedNormalSaturation,
            optimalSaturation: recalculatedOptimalSaturation,
            valueHour: recalculatedValueHour,
            valueMinute: recalculatedValueMinute,
            valuePoint: recalculatedValuePoint,
            cycleDiscrepancy: {
              ...stats.cycleDiscrepancy,
              selectedCycleSource: source
            }
          };
          onStatsUpdate(updatedStats);
        }
      } catch (error) {
        console.error('Error saving cycle preference:', error);
      }
    }
  };

  const handleNavigateToSupplements = () => {
    if (selectedStudy) {
      // Navigate to supplements page for this study
      navigate(`/supplements/${selectedStudy.id}`);
    }
  };

  // Check for supplements data status
  const machineCycleData = selectedStudy?.supplements?.['__machine_cycle_data__'];
  const hasSupplementsData = machineCycleData && machineCycleData.totalCycleTime > 0;
  const hasIncompleteSupplementsData = machineCycleData && machineCycleData.timestamp && machineCycleData.totalCycleTime === 0;

  const ResultCard: React.FC<ResultCardProps> = ({ title, value, unit = '', description = '', hasDiscrepancy = false }) => {
    const getFormattedValue = (value: number | string, title: string) => {
      if (typeof value !== 'number') return value;

      // Usar 4 decimales para valueHour y valueMinute (incluyendo unidades personalizadas)
      if (title === t('descriptions.valueHour') || 
          title === t('descriptions.valueMinute') ||
          title.includes('Valor por Hora') || 
          title.includes('Valor por Minuto')) {
        return value.toFixed(4);
      }

      return value.toFixed(2);
    };

    return (
      <div className={`bg-white rounded-lg shadow p-4 ${hasDiscrepancy ? 'border-l-4 border-yellow-400' : ''}`}>
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          {hasDiscrepancy && (
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          )}
          <Tippy content={<div className="p-2 max-w-[300px] text-sm">{description}</div>}>
            <div>
              <InfoIcon className="h-4 w-4 text-gray-400 cursor-help" />
            </div>
          </Tippy>
        </div>
        <div className="mt-1">
          <span className="text-2xl font-semibold text-gray-900">
            {getFormattedValue(value, title)}
          </span>
          {unit && <span className="ml-1 text-gray-500">{unit}</span>}
        </div>
      </div>
    );
  };

  // Get the actual cycle value to display based on user selection
  const getDisplayedNormalCycle = () => {
    if (stats.cycleDiscrepancy?.hasDiscrepancy && selectedCycleSource === 'supplements') {
      // Los datos de cycleDiscrepancy.supplementsCycleTime ya están convertidos a la unidad del estudio
      // No necesitamos hacer conversión adicional
      return stats.cycleDiscrepancy.supplementsCycleTime;
    }
    return stats.normalCycle;
  };
  
  const displayedNormalCycle = getDisplayedNormalCycle();

  // Generar pestañas disponibles basadas en las unidades habilitadas
  const availableTabs = [
    { id: 'cycle', label: 'Por Ciclo', icon: Calculator }
  ];

  if (customUnits?.enableSquareMeters && customUnits.squareMeters.length > 0 && customUnits.squareMeters.width > 0) {
    availableTabs.push({ id: 'squareMeters', label: 'Por m²', icon: Ruler });
  }
  if (customUnits?.enableLinearMeters && customUnits.linearMeters.length > 0) {
    availableTabs.push({ id: 'linearMeters', label: 'Por m', icon: Ruler });
  }
  if (customUnits?.enableCubicMeters && customUnits.cubicMeters.length > 0 && customUnits.cubicMeters.width > 0 && customUnits.cubicMeters.height > 0) {
    availableTabs.push({ id: 'cubicMeters', label: 'Por m³', icon: Ruler });
  }
  if (customUnits?.enableKilos && customUnits.kilos.weight > 0) {
    availableTabs.push({ id: 'kilos', label: 'Por kg', icon: Ruler });
  }
  if (customUnits?.enablePerimeter && customUnits.perimeter.length > 0 && customUnits.perimeter.width > 0) {
    availableTabs.push({ id: 'perimeter', label: 'Por Perímetro', icon: Ruler });
  }

  // Función para renderizar tarjetas según la pestaña activa
  const renderResults = () => {
    if (activeTab === 'cycle') {
      // Mostrar resultados por ciclo (comportamiento original)
  return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <ResultCard
          title={t('descriptions.normalCycle')}
          value={displayedNormalCycle}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.normalCycle')}
          hasDiscrepancy={stats.cycleDiscrepancy?.hasDiscrepancy || false}
        />
        <ResultCard
          title={t('descriptions.optimalCycle')}
          value={stats.optimalCycle}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.optimalCycle')}
        />
        <ResultCard
          title={t('descriptions.contingencyTime')}
          value={stats.contingencyTime}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.contingencyTime')}
        />
        <ResultCard
          title={t('descriptions.totalK')}
          value={stats.totalK}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.totalK')}
        />
        <ResultCard
          title={t('descriptions.normalProduction')}
          value={stats.normalProduction}
          unit={t('units.cyclesPerShift')}
          description={t('calculations.normalProduction')}
        />
        <ResultCard
          title={t('descriptions.optimalProduction')}
          value={stats.optimalProduction}
          unit={t('units.cyclesPerShift')}
          description={t('calculations.optimalProduction')}
        />
        <ResultCard
          title={t('descriptions.normalProductionPerHour')}
          value={stats.normalProductionPerHour}
          unit={t('units.cyclesPerHour')}
          description={t('calculations.normalProductionPerHour')}
        />
        <ResultCard
          title={t('descriptions.optimalProductionPerHour')}
          value={stats.optimalProductionPerHour}
          unit={t('units.cyclesPerHour')}
          description={t('calculations.optimalProductionPerHour')}
        />
        <ResultCard
          title={t('descriptions.normalSaturation')}
          value={stats.normalSaturation}
          unit="%"
          description={t('calculations.normalSaturation')}
        />
        <ResultCard
          title={t('descriptions.optimalSaturation')}
          value={stats.optimalSaturation}
          unit="%"
          description={t('calculations.optimalSaturation')}
        />
        <ResultCard
          title={t('descriptions.maxActivity')}
          value={stats.maxActivity}
          unit="%"
          description={t('calculations.maxActivity')}
        />
        <ResultCard
          title={t('descriptions.machineStoppedTime')}
          value={stats.machineStoppedTime}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.machineStoppedTime', { defaultValue: 'Tiempo total de elementos tipo máquina parada' })}
        />
        <ResultCard
          title={t('descriptions.machineRunningTime')}
          value={stats.machineRunningTime}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.machineRunningTime', { defaultValue: 'Tiempo total de elementos tipo máquina en marcha' })}
        />
        <ResultCard
          title={t('descriptions.machineTime')}
          value={stats.machineTime}
          unit={t(`units.${timeUnit}`)}
          description={t('calculations.machineTime', { defaultValue: 'Tiempo total de elementos tipo tiempo de máquina' })}
        />
        <ResultCard
          title={t('descriptions.valueHour')}
          value={stats.valueHour}
          unit={t('units.hours')}
          description={t('calculations.valueHour')}
        />
        <ResultCard
          title={t('descriptions.valueMinute')}
          value={stats.valueMinute}
          unit={t('units.minutes')}
          description={t('calculations.valueMinute')}
        />
        <ResultCard
          title={t('descriptions.valuePoint')}
          value={stats.valuePoint}
          unit={t('units.points')}
          description={t('calculations.valuePoint')}
        />
      </div>
      );
    } else {
      // Mostrar resultados por unidad personalizada
      const customValues = calculateCustomUnitValues(activeTab as 'squareMeters' | 'linearMeters' | 'cubicMeters' | 'kilos' | 'perimeter');
      
      if (!customValues) {
        return (
          <div className="text-center text-gray-500 py-8">
            No hay datos suficientes para mostrar valores por esta unidad
          </div>
        );
      }

      const unitInfo = {
        squareMeters: { name: 'Metro Cuadrado', symbol: 'm²' },
        linearMeters: { name: 'Metro Lineal', symbol: 'm' },
        cubicMeters: { name: 'Metro Cúbico', symbol: 'm³' },
        kilos: { name: 'Kilogramo', symbol: 'kg' },
        perimeter: { name: 'Perímetro', symbol: 'm' }
      }[activeTab] || { name: 'Unidad', symbol: '' };

      return (
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="text-lg font-medium text-blue-900 mb-2">
              Valores por {unitInfo.name}
            </h4>
            <p className="text-sm text-blue-700">
              Total configurado: {customValues.unitValue.toFixed(3)} {customValues.unitLabel}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ResultCard
              title={`Tiempo Normal por ${unitInfo.symbol}`}
              value={customValues.normalCycle}
              unit={t(`units.${timeUnit}`)}
              description={`Tiempo necesario para procesar una unidad de ${customValues.unitLabel}`}
            />
            <ResultCard
              title={`Tiempo Óptimo por ${unitInfo.symbol}`}
              value={customValues.optimalCycle}
              unit={t(`units.${timeUnit}`)}
              description={`Tiempo óptimo para procesar una unidad de ${customValues.unitLabel}`}
            />
            <ResultCard
              title={`Producción Normal (${unitInfo.symbol}/turno)`}
              value={customValues.normalProduction}
              unit={customValues.unitLabel}
              description={`Cantidad de ${customValues.unitLabel} que se pueden producir en un turno con actividad normal`}
            />
            <ResultCard
              title={`Producción Óptima (${unitInfo.symbol}/turno)`}
              value={customValues.optimalProduction}
              unit={customValues.unitLabel}
              description={`Cantidad de ${customValues.unitLabel} que se pueden producir en un turno con actividad óptima`}
            />
            <ResultCard
              title={`Producción Normal (${unitInfo.symbol}/hora)`}
              value={customValues.normalProductionPerHour}
              unit={`${customValues.unitLabel}/h`}
              description={`Cantidad de ${customValues.unitLabel} que se pueden producir por hora con actividad normal`}
            />
            <ResultCard
              title={`Producción Óptima (${unitInfo.symbol}/hora)`}
              value={customValues.optimalProductionPerHour}
              unit={`${customValues.unitLabel}/h`}
              description={`Cantidad de ${customValues.unitLabel} que se pueden producir por hora con actividad óptima`}
            />
            <ResultCard
              title={`Valor por Hora (${unitInfo.symbol})`}
              value={customValues.valueHour}
              unit={`h/${customValues.unitLabel}`}
              description={`Tiempo en horas necesario para procesar una unidad de ${customValues.unitLabel}`}
            />
            <ResultCard
              title={`Valor por Minuto (${unitInfo.symbol})`}
              value={customValues.valueMinute}
              unit={`min/${customValues.unitLabel}`}
              description={`Tiempo en minutos necesario para procesar una unidad de ${customValues.unitLabel}`}
            />
            <ResultCard
              title={`Valor por Punto (${unitInfo.symbol})`}
              value={customValues.valuePoint}
              unit={`punto/${customValues.unitLabel}`}
              description={`Tiempo en puntos necesario para procesar una unidad de ${customValues.unitLabel}`}
            />
          </div>
        </div>
      );
    }
  };

  return (
    <div className="space-y-8">
      {/* Equivalencia de puntos por hora */}
      <div className="flex items-center gap-2 text-sm text-blue-700 font-semibold bg-blue-50 border border-blue-200 rounded px-4 py-2 mb-2">
        <span>Equivalencia:</span>
        <span>1 hora = {pointsPerHour} puntos</span>
      </div>
      {/* Supplements Data Alert */}
      <SupplementsDataAlert
        hasData={hasSupplementsData}
        hasIncompleteData={hasIncompleteSupplementsData}
        onNavigateToSupplements={handleNavigateToSupplements}
      />

      {/* Cycle Discrepancy Alert */}
      {stats.cycleDiscrepancy?.hasDiscrepancy && (
        <CycleDiscrepancyAlert
          cycleDiscrepancy={stats.cycleDiscrepancy}
          timeUnit={timeUnit}
          onCycleSourceChange={handleCycleSourceChange}
        />
      )}

      {/* Pestañas */}
      {availableTabs.length > 1 && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {availableTabs.map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
                >
                  <tab.icon className="h-4 w-4" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {renderResults()}
          </div>
        </div>
      )}

      {/* Si solo hay una pestaña (por ciclo), mostrar sin pestañas */}
      {availableTabs.length === 1 && renderResults()}
    </div>
  );
};