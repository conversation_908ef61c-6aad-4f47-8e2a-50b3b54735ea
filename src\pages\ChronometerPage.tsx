import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Play, Pause, Square, ArrowUp, ArrowDown, X, AlertCircle, MessageSquare, Trash2, ChevronLeft, ChevronRight, Timer } from 'lucide-react';
import { Header } from '../components/Header';
import { ElementStats } from '../components/ElementStats';
import { DeleteConfirmModal } from '../components/DeleteConfirmModal';
import { useStudyStore } from '../store/studyStore';
import { useTimeRecordStore } from '../store/timeRecordStore';
import { useTimeRecords } from '../hooks/useTimeRecords';
import { ElementInstance, TimeRecord } from '../types';
import { ChronometerControls } from '../components/ChronometerControls';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl';
import { VoiceInputModal } from '../components/VoiceInputModal';
import { useAuthStore } from '../store/authStore';

interface ChronometerPageProps {
  onBack: () => void;
}

export const ChronometerPage: React.FC<ChronometerPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['chronometer', 'common', 'study']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  useStudyFromUrl(); // Add this hook to handle study from URL
  const elements = selectedStudy?.elements || [];
  const { user } = useAuthStore();
  const [isSharedStudy, setIsSharedStudy] = useState(false);
  
  const repetitiveElements = useMemo(() => 
    elements.filter(e => e.repetition_type === 'repetitive'),
    [elements]
  );

  // Función para obtener el índice real del elemento en la lista completa
  const getElementIndex = (element: ElementInstance) => {
    return elements.findIndex(e => e.id === element.id) + 1;
  };

  const [activeElements, setActiveElements] = useState<Set<string>>(
    () => new Set(elements.filter(e => e.repetition_type === 'repetitive').map(e => e.id))
  );
  
  useEffect(() => {
    if (selectedStudy) {
      setActiveElements(new Set(repetitiveElements.map(e => e.id)));
    }
  }, [selectedStudy?.id]);

  const [currentElementIndex, setCurrentElementIndex] = useState(0);
  const [viewingElementIndex, setViewingElementIndex] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [time, setTime] = useState(0);
  const [activity, setActivity] = useState(selectedStudy?.required_info?.activity_scale?.normal || 100);
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const [comment, setComment] = useState('');
  const [editingRecord, setEditingRecord] = useState<TimeRecord | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { records, saveRecords, deleteRecord, deleteAllRecords, updateRecord } = useTimeRecordStore();

  const timerRef = useRef<number>();
  const startTimeRef = useRef<number>();

  const currentElement = repetitiveElements[currentElementIndex];
  const viewingElement = repetitiveElements[viewingElementIndex];

  const viewingElementStats = useTimeRecords(
    viewingElement ? (selectedStudy?.time_records[viewingElement.id] || []).map(record => ({
      ...record,
      description: viewingElement.description,
      addedToMethod: false,
      frequency_repetitions: viewingElement.frequency_repetitions || 1,
      frequency_cycles: viewingElement.frequency_cycles || 1
    })) : []
  );

  const handleElementToggle = (elementId: string) => {
    if (isRunning) return;

    setActiveElements(prev => {
      const next = new Set(prev);
      if (next.has(elementId)) {
        if (next.size === 1) return next;
        next.delete(elementId);
      } else {
        next.add(elementId);
      }
      return next;
    });

    if (currentElement?.id === elementId) {
      const nextIndex = getNextActiveElementIndex(currentElementIndex);
      if (nextIndex !== -1) {
        setCurrentElementIndex(nextIndex);
        setViewingElementIndex(nextIndex);
      }
    }
  };

  const getNextActiveElementIndex = (currentIndex: number) => {
    const activeIndexes = repetitiveElements
      .map((e, i) => ({ id: e.id, index: i }))
      .filter(e => activeElements.has(e.id))
      .map(e => e.index);

    if (activeIndexes.length === 0) return -1;

    const nextIndex = activeIndexes.find(i => i > currentIndex);
    return nextIndex !== undefined ? nextIndex : activeIndexes[0];
  };

  const handleStart = () => {
    if (!currentElement || activeElements.size === 0) return;

    startTimeRef.current = Date.now() - time;
    timerRef.current = window.setInterval(() => {
      setTime(Date.now() - startTimeRef.current!);
    }, 10);
    setIsRunning(true);
    setIsPaused(false);
  };

  const handlePause = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = undefined;
    }
    setIsRunning(false);
    setIsPaused(true);
  };

  const handleStop = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = undefined;
    }
    setTime(0);
    setIsRunning(false);
    setIsPaused(false);
  };

  const handleLap = async () => {
    if (!currentElement || !isRunning) return;

    try {
      const record: Omit<TimeRecord, 'id'> = {
        elementId: currentElement.id,
        time: time / 1000,
        original_time: time / 1000, // Guardamos el tiempo original
        activity,
        timestamp: new Date().toISOString(),
        description: currentElement.description,
        addedToMethod: false,
        frequency_repetitions: currentElement.frequency_repetitions || 1,
        frequency_cycles: currentElement.frequency_cycles || 1,
        comment: ''
      };

      await saveRecords([record]);
      setTime(0);
      startTimeRef.current = Date.now();

      if (activeElements.size > 1) {
        const nextIndex = getNextActiveElementIndex(currentElementIndex);
        if (nextIndex !== -1) {
          setCurrentElementIndex(nextIndex);
          setViewingElementIndex(nextIndex);
        }
      }
    } catch (err) {
      console.error('Error saving record:', err);
      setError(t('error.savingRecord', { ns: 'chronometer' }));
    }
  };

  const handleActivityChange = (increment: boolean) => {
    const activityScale = selectedStudy?.required_info?.activity_scale;
    if (!activityScale) return;

    const { normal, optimal } = activityScale;
    const step = 5;
    const min = Math.max(normal * 0.5);

    setActivity(prev => {
      const newActivity = increment ? prev + step : prev - step;
      return increment 
        ? Math.min(newActivity, optimal)
        : Math.max(newActivity, min);
    });
  };

  const handleUpdateRecord = async (record: TimeRecord) => {
    try {
      // Si se ha modificado la frecuencia, el tiempo ajustado se convierte en el nuevo tiempo original
      const updatedRecord = {
        ...record,
        original_time: record.time // El tiempo ajustado se convierte en el nuevo tiempo original
      };
      await updateRecord(record.id, updatedRecord);
      setEditingRecord(null);
      setShowEditModal(false);
    } catch (err) {
      console.error('Error updating record:', err);
      setError(t('error.updatingRecord', { ns: 'chronometer' }));
    }
  };

  const handleDeleteRecord = async (recordId: string) => {
    try {
      await deleteRecord(recordId);
    } catch (err) {
      console.error('Error deleting record:', err);
      setError(t('error.deletingRecord', { ns: 'chronometer' }));
    }
  };

  const handleDeleteAllRecords = async () => {
    if (viewingElement) {
      try {
        await deleteAllRecords(viewingElement.id);
        setShowDeleteAllModal(false);
      } catch (err) {
        console.error('Error deleting all records:', err);
        setError(t('error.deletingAllRecords', { ns: 'chronometer' }));
      }
    }
  };

  const navigateElement = (direction: 'prev' | 'next') => {
    if (repetitiveElements.length <= 1) return;

    setViewingElementIndex(prev => {
      if (direction === 'prev') {
        return prev === 0 ? repetitiveElements.length - 1 : prev - 1;
      } else {
        return prev === repetitiveElements.length - 1 ? 0 : prev + 1;
      }
    });
  };

  useEffect(() => {
    if (viewingElementIndex >= repetitiveElements.length) {
      setViewingElementIndex(0);
    }
  }, [repetitiveElements, viewingElementIndex]);

  useEffect(() => {
    if (isRunning) {
      setViewingElementIndex(currentElementIndex);
    }
  }, [currentElementIndex, isRunning]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (selectedStudy && user) {
      setIsSharedStudy(selectedStudy.user_id !== user.id);
    }
  }, [selectedStudy, user]);

  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="mt-4">{t('noStudySelected', { ns: 'chronometer' })}</div>
      </div>
    );
  }

  // Sort records in reverse chronological order
  const sortedRecords = viewingElement ? 
    [...(selectedStudy.time_records[viewingElement.id] || [])].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    ) : [];

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('repetitiveElements', { ns: 'chronometer' })}
        subtitle={selectedStudy?.required_info?.name || ''}
        showSearch={false}
        showActions={true}
        onBack={onBack}
      />

      <main className="container mx-auto px-4 py-6">
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {isSharedStudy && (
          <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{t('permission_error.message', { ns: 'study' })}</span>
            <button
              onClick={() => setIsSharedStudy(false)}
              className="ml-auto text-amber-500 hover:text-amber-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        <div className="flex overflow-x-auto gap-4 mb-8 pb-4 snap-x snap-mandatory">
          {repetitiveElements.map((element, index) => (
            <button
              key={element.id}
              onClick={() => handleElementToggle(element.id)}
              disabled={isRunning && index !== currentElementIndex}
              className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold snap-center transition-colors
                ${isRunning && index === currentElementIndex ? 'bg-blue-500 text-white' : 
                  activeElements.has(element.id) ? 'bg-green-500 text-white' : 'bg-gray-200'}
                ${isRunning ? 'cursor-not-allowed' : 'cursor-pointer hover:opacity-90'}`}
            >
              {String(getElementIndex(element)).padStart(2, '0')}
            </button>
          ))}
        </div>

        {viewingElement && (
          <div className="w-full">
            <ElementStats 
              {...viewingElementStats} 
              timeRecords={(selectedStudy.time_records[viewingElement.id] || []).map(record => ({
                ...record,
                description: viewingElement.description,
                addedToMethod: false,
                frequency_repetitions: viewingElement.frequency_repetitions || 1,
                frequency_cycles: viewingElement.frequency_cycles || 1
              }))}
            />

            <ChronometerControls
              isRunning={isRunning}
              isPaused={isPaused}
              time={time}
              activity={activity}
              onStart={handleStart}
              onPause={handlePause}
              onStop={handleStop}
              onLap={handleLap}
              onActivityChange={handleActivityChange}
            />

            <div className="bg-white rounded-lg shadow-lg p-6 mb-4 animate-slide-in">
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={() => navigateElement('prev')}
                  className="p-2 text-gray-600 hover:text-gray-800"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>
                
                <div className="flex items-center">
                  <h3 className="text-lg font-bold">
                    {`${getElementIndex(viewingElement)} - ${viewingElement.description}`}
                  </h3>
                  <button
                    onClick={() => setShowDeleteAllModal(true)}
                    className="ml-4 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>

                <button
                  onClick={() => navigateElement('next')}
                  className="p-2 text-gray-600 hover:text-gray-800"
                >
                  <ChevronRight className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-2">
                {sortedRecords.map((record, index) => (
                  <div
                    key={record.id}
                    onClick={() => {
                      setEditingRecord({
                        ...record,
                        description: viewingElement.description,
                        addedToMethod: false,
                        frequency_repetitions: viewingElement.frequency_repetitions || 1,
                        frequency_cycles: viewingElement.frequency_cycles || 1
                      });
                      setShowEditModal(true);
                    }}
                    className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer animate-slide-down"
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animationFillMode: 'backwards'
                    }}
                  >
                    <div className="flex items-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingRecord({
                            ...record,
                            description: viewingElement.description,
                            addedToMethod: false,
                            frequency_repetitions: viewingElement.frequency_repetitions || 1,
                            frequency_cycles: viewingElement.frequency_cycles || 1
                          });
                          setShowCommentModal(true);
                        }}
                        className="p-2 rounded-full bg-green-100 mr-2"
                        title={t('addComment', { ns: 'common' })}
                        aria-label={t('addComment', { ns: 'common' })}
                      >
                        <MessageSquare className="w-4 h-4 text-green-600" />
                      </button>
                      <div>
                        <div className="font-mono">
                          {typeof record.time === 'number' && !isNaN(record.time) 
                            ? record.time.toFixed(2) 
                            : '0.00'}s
                        </div>
                        <div className="text-sm text-gray-500">
                          {record.timestamp ? new Date(record.timestamp).toLocaleTimeString() : '-'}
                        </div>
                        {record.comment && (
                          <div className="flex items-start gap-1 mt-1">
                            <span className="text-blue-500 text-xs font-medium">💬</span>
                            <div className="text-sm text-blue-700 bg-blue-50 px-2 py-1 rounded-md border-l-2 border-blue-300">
                              {record.comment}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="font-bold mr-4">{record.activity}</span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteRecord(record.id);
                        }}
                        className="p-2 text-red-500 hover:text-red-700"
                        title={t('delete', { ns: 'common' })}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </main>

      {showCommentModal && editingRecord && (
        <VoiceInputModal
          isOpen={showCommentModal}
          onClose={() => setShowCommentModal(false)}
          onTranscript={(text) => {
            handleUpdateRecord({
              ...editingRecord,
              comment: text
            });
            setShowCommentModal(false);
          }}
          title={t('addComment', { ns: 'common' })}
        />
      )}

      {showEditModal && editingRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-bold mb-4">{t('editRecord', { ns: 'chronometer' })}</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('time', { ns: 'chronometer' })}
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editingRecord.time}
                  onChange={(e) => setEditingRecord({
                    ...editingRecord,
                    time: parseFloat(e.target.value)
                  })}
                  className="w-full p-2 border rounded"
                  title={t('time', { ns: 'chronometer' })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('activity', { ns: 'chronometer' })}
                </label>
                <input
                  type="number"
                  value={editingRecord.activity}
                  onChange={(e) => setEditingRecord({
                    ...editingRecord,
                    activity: parseInt(e.target.value)
                  })}
                  className="w-full p-2 border rounded"
                  title={t('activity', { ns: 'chronometer' })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('frequency', { ns: 'chronometer' })}
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    min="1"
                    value={editingRecord.frequency_repetitions || 1}
                    onChange={(e) => {
                      const repetitions = parseInt(e.target.value) || 1;
                      const cycles = editingRecord.frequency_cycles || 1;
                      // Calculamos el factor de ajuste basado en la cantidad esperada vs la real
                      const expectedPieces = viewingElement.frequency_cycles;
                      const actualPieces = cycles;
                      const adjustmentFactor = expectedPieces / actualPieces;
                      // Ajustamos el tiempo observado según la fórmula T'_o = T_o × (N_e/N_r)
                      // Usamos el tiempo original del registro para el cálculo
                      const originalTime = editingRecord.original_time || editingRecord.time;
                      const adjustedTime = originalTime * adjustmentFactor;
                      setEditingRecord({
                        ...editingRecord,
                        frequency_repetitions: repetitions,
                        time: adjustedTime,
                        original_time: originalTime // Guardamos el tiempo original
                      });
                    }}
                    className="w-1/2 p-2 border rounded"
                    title={t('repetitions', { ns: 'chronometer' })}
                  />
                  <span className="text-gray-500">/</span>
                  <input
                    type="number"
                    min="1"
                    value={editingRecord.frequency_cycles || 1}
                    onChange={(e) => {
                      const cycles = parseInt(e.target.value) || 1;
                      const repetitions = editingRecord.frequency_repetitions || 1;
                      // Calculamos el factor de ajuste basado en la cantidad esperada vs la real
                      const expectedPieces = viewingElement.frequency_cycles;
                      const actualPieces = cycles;
                      const adjustmentFactor = expectedPieces / actualPieces;
                      // Ajustamos el tiempo observado según la fórmula T'_o = T_o × (N_e/N_r)
                      // Usamos el tiempo original del registro para el cálculo
                      const originalTime = editingRecord.original_time || editingRecord.time;
                      const adjustedTime = originalTime * adjustmentFactor;
                      setEditingRecord({
                        ...editingRecord,
                        frequency_cycles: cycles,
                        time: adjustedTime,
                        original_time: originalTime // Guardamos el tiempo original
                      });
                    }}
                    className="w-1/2 p-2 border rounded"
                    title={t('cycles', { ns: 'chronometer' })}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 text-blue-700">
                  {t('comment', { ns: 'common', defaultValue: 'Comentario' })}
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-start space-x-2">
                    <textarea
                      value={editingRecord.comment || ''}
                      onChange={(e) => setEditingRecord({
                        ...editingRecord,
                        comment: e.target.value
                      })}
                      className="w-full rounded-lg border-2 border-blue-300 focus:border-blue-500 focus:ring-blue-500 bg-blue-50 p-2"
                      rows={2}
                      placeholder={t('addCommentPlaceholder', { ns: 'common', defaultValue: 'Agregar comentario sobre este registro...' })}
                    />
                    <button
                      type="button"
                      onClick={() => setEditingRecord({
                        ...editingRecord,
                        comment: ''
                      })}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
                      title={t('clearComment', { ns: 'common', defaultValue: 'Limpiar comentario' })}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  {editingRecord.comment && (
                    <p className="text-xs text-blue-600">
                      {t('commentWillExport', { ns: 'common', defaultValue: 'Este comentario aparecerá en el informe Excel' })}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
                title={t('cancel', { ns: 'common' })}
              >
                {t('cancel', { ns: 'common' })}
              </button>
              <button
                onClick={() => handleUpdateRecord(editingRecord)}
                className="px-4 py-2 bg-purple-600 text-white rounded"
                title={t('save', { ns: 'common' })}
              >
                {t('save', { ns: 'common' })}
              </button>
            </div>
          </div>
        </div>
      )}

      <DeleteConfirmModal
        isOpen={showDeleteAllModal}
        onClose={() => setShowDeleteAllModal(false)}
        onConfirm={handleDeleteAllRecords}
        title={t('deleteAllRecords', { ns: 'chronometer' })}
        message={t('deleteAllRecordsConfirmation', { ns: 'chronometer' })}
      />
    </div>
  );
};

export default ChronometerPage;