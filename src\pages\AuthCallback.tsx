import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useUserInitialization } from '../hooks/useUserInitialization';
import { supabase } from '../lib/supabase';

export const AuthCallback = () => {
  const navigate = useNavigate();
  const user = useAuthStore(state => state.user);
  const initializeSession = useAuthStore(state => state.initializeSession);
  const { initializeUser } = useUserInitialization();

  useEffect(() => {
    const handleCallback = async () => {
      console.log('🔄 AuthCallback: Iniciando manejo de callback');
      console.log('📍 URL actual:', window.location.href);
      
      try {
        // Verificar si hay un error en la URL
        const errorCode = new URLSearchParams(window.location.search).get('error');
        const errorDescription = new URLSearchParams(window.location.search).get('error_description');
        
        if (errorCode) {
          console.error('❌ Error en la autenticación:', errorCode, errorDescription);
          navigate('/login');
          return;
        }

        // Intentar obtener la sesión actual
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('❌ Error obteniendo sesión:', sessionError);
          navigate('/login');
          return;
        }

        // Si hay una sesión activa, intentar crear el perfil
        if (session?.user) {
          // Verificar si el perfil ya existe
          const { data: existingProfile } = await supabase
            .from('profiles')
            .select()
            .eq('id', session.user.id)
            .single();

          if (!existingProfile) {
            // Crear nuevo perfil si no existe
            const { error: profileError } = await supabase
              .from('profiles')
              .insert([
                {
                  id: session.user.id,
                  email: session.user.email,
                  full_name: session.user.user_metadata.full_name || '',
                  avatar_url: session.user.user_metadata.avatar_url || '',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }
              ]);

            if (profileError) {
              console.error('❌ Error creando perfil:', profileError);
              // No redirigir aquí, seguir con la inicialización
            }
          }

          // Inicializar el usuario (límites y otros datos necesarios)
          await initializeUser();

          // Inicializar la sesión en el store
          await initializeSession();

          // Redirigir a la página principal
          navigate('/');
        } else {
          console.error('❌ No hay usuario en la sesión');
          navigate('/login');
        }

        // Si no hay sesión, intentar establecerla desde la URL
        if (!session) {
          console.log('⚠️ No hay sesión activa, intentando establecer desde URL...');
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          const accessToken = hashParams.get('access_token');
          
          if (accessToken) {
            console.log('🔑 Token encontrado en URL, estableciendo sesión...');
            try {
              const { data, error } = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: hashParams.get('refresh_token') || '',
              });
              
              if (error) throw error;
              
              // Intentar crear perfil después de establecer la sesión
              if (data.user) {
                const { error: profileError } = await supabase
                  .from('profiles')
                  .insert([
                    {
                      id: data.user.id,
                      email: data.user.email,
                      full_name: data.user.user_metadata.full_name || '',
                      avatar_url: data.user.user_metadata.avatar_url || '',
                      created_at: new Date().toISOString(),
                      updated_at: new Date().toISOString()
                    }
                  ]);

                if (profileError) {
                  console.error('❌ Error creando perfil:', profileError);
                  // Continuar aunque haya error, para no bloquear al usuario
                }
              }
            } catch (error) {
              console.error('❌ Error estableciendo sesión:', error);
              navigate('/login');
              return;
            }
          }
        }

      } catch (error) {
        console.error('❌ Error general en callback:', error);
        navigate('/login');
      }
    };

    handleCallback();
  }, [navigate, initializeSession, initializeUser]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Procesando autenticación...</p>
      </div>
    </div>
  );
};
