import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircle2 } from 'lucide-react';
import { Button } from '../components/ui/button';

export const PaymentSuccessPage = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Aquí podrías hacer una llamada a tu API para verificar el estado del pago
    // o actualizar el estado de la aplicación si es necesario
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg">
        <div className="text-center">
          <CheckCircle2 className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            ¡Pago Completado!
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Tu pago se ha procesado correctamente. Los cambios se reflejarán en tu cuenta en breve.
          </p>
        </div>
        <div className="mt-8 space-y-4">
          <Button
            onClick={() => navigate('/profile')}
            className="w-full"
          >
            Volver al Perfil
          </Button>
          <Button
            onClick={() => navigate('/')}
            variant="outline"
            className="w-full"
          >
            Ir al Inicio
          </Button>
        </div>
      </div>
    </div>
  );
};
