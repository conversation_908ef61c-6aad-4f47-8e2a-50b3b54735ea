import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { TimeRecord } from '../types';
import { useStudyStore } from './studyStore';
import { useCreditStore } from './creditStore';

interface TimeRecordState {
  records: Record<string, TimeRecord[]>;
  isLoading: boolean;
  error: string | null;
  fetchRecords: (elementId: string) => Promise<void>;
  saveRecords: (newRecords: Omit<TimeRecord, 'id'>[]) => Promise<void>;
  updateRecord: (recordId: string, updatedRecord: Partial<TimeRecord>) => Promise<void>;
  deleteRecord: (recordId: string) => Promise<void>;
  deleteAllRecords: (elementId: string) => Promise<void>;
  addTimeRecord: (studyId: string, elementId: string, timeRecord: TimeRecord) => Promise<void>;
}

export const useTimeRecordStore = create<TimeRecordState>((set, get) => ({
  records: {},
  isLoading: false,
  error: null,

  fetchRecords: async (elementId) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      const elementRecords = selectedStudy.time_records[elementId] || [];
      
      set(state => ({
        records: {
          ...state.records,
          [elementId]: elementRecords
        },
        isLoading: false
      }));
    } catch (error) {
      console.error('Error fetching records:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  saveRecords: async (newRecords) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      // Create new records with IDs
      const recordsWithIds = newRecords.map(record => ({
        ...record,
        id: crypto.randomUUID()
      }));

      // Update time_records column
      const updatedTimeRecords = { ...selectedStudy.time_records };
      
      recordsWithIds.forEach(record => {
        const elementId = record.elementId;
        if (!updatedTimeRecords[elementId]) {
          updatedTimeRecords[elementId] = [];
        }
        updatedTimeRecords[elementId] = [
          ...updatedTimeRecords[elementId],
          record
        ];
      });

      // Update the study in Supabase
      const { error } = await supabase
        .from('studies')
        .update({ 
          time_records: updatedTimeRecords
        })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update local state
      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          time_records: updatedTimeRecords
        } : null
      }));

      set(state => ({
        records: recordsWithIds.reduce((acc, record) => {
          const elementId = record.elementId;
          if (!acc[elementId]) {
            acc[elementId] = [];
          }
          acc[elementId] = [...acc[elementId], record];
          return acc;
        }, { ...state.records }),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error saving records:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  addTimeRecord: async (studyId: string, elementId: string, timeRecord: TimeRecord) => {
    set({ isLoading: true, error: null });
    try {
      const study = useStudyStore.getState().selectedStudy;
      if (!study) throw new Error('No study selected');

      // Create new records array if it doesn't exist
      const newRecords = [timeRecord];
      const recordsWithIds = newRecords.map(record => ({
        ...record,
        id: crypto.randomUUID()
      }));

      // Update time_records column
      const updatedTimeRecords = { ...study.time_records };
      
      recordsWithIds.forEach(record => {
        const elementId = record.elementId;
        if (!updatedTimeRecords[elementId]) {
          updatedTimeRecords[elementId] = [];
        }
        updatedTimeRecords[elementId] = [
          ...updatedTimeRecords[elementId],
          record
        ];
      });

      // Update the study in Supabase
      const { error } = await supabase
        .from('studies')
        .update({ 
          time_records: updatedTimeRecords
        })
        .eq('id', study.id);

      if (error) throw error;

      // Update local state
      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          time_records: updatedTimeRecords
        } : null
      }));

      set(state => ({
        records: recordsWithIds.reduce((acc, record) => {
          const elementId = record.elementId;
          if (!acc[elementId]) {
            acc[elementId] = [];
          }
          acc[elementId] = [...acc[elementId], record];
          return acc;
        }, { ...state.records }),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  updateRecord: async (recordId, updatedRecord) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      // Update record in time_records column
      const updatedTimeRecords = { ...selectedStudy.time_records };
      
      Object.keys(updatedTimeRecords).forEach(elementId => {
        const records = updatedTimeRecords[elementId] || [];
        const recordIndex = records.findIndex(r => r.id === recordId);
        if (recordIndex !== -1) {
          updatedTimeRecords[elementId] = [
            ...records.slice(0, recordIndex),
            { ...records[recordIndex], ...updatedRecord },
            ...records.slice(recordIndex + 1)
          ];
        }
      });

      // Update the study in Supabase
      const { error } = await supabase
        .from('studies')
        .update({ time_records: updatedTimeRecords })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update local state
      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          time_records: updatedTimeRecords
        } : null
      }));

      set({ isLoading: false });
    } catch (error) {
      console.error('Error updating record:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  deleteRecord: async (recordId) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      // Remove record from time_records column
      const updatedTimeRecords = { ...selectedStudy.time_records };
      
      Object.keys(updatedTimeRecords).forEach(elementId => {
        const records = updatedTimeRecords[elementId] || [];
        updatedTimeRecords[elementId] = records.filter(r => r.id !== recordId);
      });

      // Update the study in Supabase
      const { error } = await supabase
        .from('studies')
        .update({ time_records: updatedTimeRecords })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update local state
      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          time_records: updatedTimeRecords
        } : null
      }));

      set({ isLoading: false });
    } catch (error) {
      console.error('Error deleting record:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  deleteAllRecords: async (elementId) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      // Remove all records for the element from time_records column
      const updatedTimeRecords = { ...selectedStudy.time_records };
      updatedTimeRecords[elementId] = [];

      // Update the study in Supabase
      const { error } = await supabase
        .from('studies')
        .update({ time_records: updatedTimeRecords })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update local state
      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          time_records: updatedTimeRecords
        } : null
      }));

      set({ isLoading: false });
    } catch (error) {
      console.error('Error deleting all records:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  }
}));