import React, { useEffect, useState } from 'react';
import { useRegisterSW } from 'virtual:pwa-register/react';
import { useNavigate } from 'react-router-dom';
import pkg from '/package.json';

export const UpdatePrompt: React.FC = () => {
  const navigate = useNavigate();
  const {
    needRefresh: [needRefresh, setNeedRefresh],
    updateServiceWorker,
  } = useRegisterSW({
    onRegistered(registration) {
      if (registration) {
        setInterval(() => {
          registration.update();
        }, 60 * 60 * 1000);
      }
    },
    onRegisterError(error) {
      console.error('Error al registrar el SW:', error);
    },
  });

  const [version] = useState(pkg.version);

  useEffect(() => {
    if (needRefresh) {
      if (window.confirm('¡Nueva versión disponible! ¿Actualizar ahora?')) {
        updateServiceWorker(true);
        navigate(0);
      }
    }
  }, [needRefresh, updateServiceWorker, navigate]);

  return null;
};
