import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSearchStore } from '../store/searchStore';
import { useStudyStore } from '../store/studyStore';
import { useAuthStore } from '../store/authStore';
import { useFolderStore } from '../store/folderStore';
import { filterStudiesByFolder } from '../utils/folderUtils';
import { supabase } from '../lib/supabase';
import { UserProfile } from '../types/profile';
import { Study, ElementInstance, Folder, FolderTreeNode, StudyWithFolder } from '../types/index';
import { BookOpen, Info, ListTodo, Timer, Gauge, Clock, UserPlus, FileText, Watch, ListChecks, BarChart2, Plus, LayoutGrid, List, ArrowDownUp, Calendar, Briefcase, Filter, FolderPlus, Folder as FolderIcon } from 'lucide-react';
import { Header } from '../components/Header';
import { StudyCard, StudyCardProps } from '../components/StudyCard';
import { Button } from '../components/ui/button';
import { FolderNavigator } from '../components/FolderNavigator';
import { FolderModal } from '../components/FolderModal';
import { MoveToFolderModal } from '../components/MoveToFolderModal';
import { StudyConsolidationModal } from '../components/StudyConsolidationModal';
import { FolderBreadcrumbs } from '../components/FolderBreadcrumbs';
import { DeleteFolderModal } from '../components/DeleteFolderModal';

// Helper function to find a folder in the tree structure
const findFolderInTree = (nodes: FolderTreeNode[], folderId: string): FolderTreeNode | null => {
  for (const node of nodes) {
    if (node.id === folderId) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findFolderInTree(node.children, folderId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// Helper function to get all descendant folder IDs including the parent
const getDescendantFolderIds = (folderNode: FolderTreeNode): string[] => {
    const ids: string[] = [folderNode.id];
    if (folderNode.children && folderNode.children.length > 0) {
        for (const child of folderNode.children) {
            ids.push(...getDescendantFolderIds(child));
        }
    }
    return ids;
};

export const StudyListPage = () => {
  const { t } = useTranslation(['study', 'library', 'common', 'cronoSeguido', 'filters']);
  const location = useLocation();
  const navigate = useNavigate();
  const [selectedStudyId, setSelectedStudyId] = useState<string | null>(null);
  const { studies, selectedStudy, setSelectedStudy, fetchStudies, deleteStudy, createStudy } = useStudyStore();
  const searchQuery = useSearchStore(state => state.searchQuery);
  const user = useAuthStore(state => state.user);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState({
    company: false,
    date: false
  });

  // New states for view and sort
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'date' | 'company'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Folder states from store
  const {
    folders,
    folderTree,
    currentFolder,
    setCurrentFolder,
    fetchFolders,
    createFolder,
    updateFolder,
    deleteFolder,
    moveStudyToFolder,
    moveFolderToFolder,
    bulkExportFolderReports
  } = useFolderStore();
  
  const [showFolderModal, setShowFolderModal] = useState(false);
  const [editingFolder, setEditingFolder] = useState<Folder | undefined>();
  const [parentFolder, setParentFolder] = useState<FolderTreeNode | undefined>();
  
  // Move to folder modal states
  const [showMoveModal, setShowMoveModal] = useState(false);
  const [studyToMove, setStudyToMove] = useState<Study | null>(null);
  
  // Delete folder modal states
  const [showDeleteFolderModal, setShowDeleteFolderModal] = useState(false);
  const [folderToDelete, setFolderToDelete] = useState<FolderTreeNode | null>(null);
  
  // Bulk export modal states
  const [showBulkExportModal, setShowBulkExportModal] = useState(false);
  const [exportFolderId, setExportFolderId] = useState<string | undefined>();
  const [exportFolderName, setExportFolderName] = useState<string | undefined>();
  
  // Ownership filter state - Not used anymore but keeping for future compatibility
  const [ownershipFilter, setOwnershipFilter] = useState<'all' | 'owned' | 'shared'>('all');
  const [showFolderNavigator, setShowFolderNavigator] = useState(false); // Mobile-first: hidden by default
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.id) {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        if (!error && data) setProfile(data as unknown as UserProfile);
      }
    };
    fetchProfile();
  }, [user?.id]);

  // Obtener preferencia de mostrar estudios compartidos
  const showSharedStudies = profile?.show_shared_studies !== false;

  // Mobile detection
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  useEffect(() => {
    console.log('📁 StudyListPage: useEffect triggered');
    fetchStudies();
    console.log('📁 StudyListPage: Calling fetchFolders...');
    fetchFolders();
  }, [fetchStudies, fetchFolders]);

  // Cargar carpeta por defecto del perfil al inicializar (solo UNA vez)
  const [hasLoadedDefaultFolder, setHasLoadedDefaultFolder] = useState(false);
  
  useEffect(() => {
    // Solo ejecutar si:
    // 1. Tenemos perfil con carpeta por defecto configurada
    // 2. Tenemos el árbol de carpetas cargado
    // 3. No hemos cargado aún la carpeta por defecto (evita interferir con navegación manual)
    // 4. No hay carpeta actual (estamos en raíz)
    if (
      profile?.default_folder_id && 
      folderTree.length > 0 && 
      !hasLoadedDefaultFolder && 
      !currentFolder
    ) {
      console.log('📁 StudyListPage: Loading default folder from profile (first time):', profile.default_folder_id);
      
      // Buscar la carpeta por defecto en el árbol de carpetas
      const defaultFolder = findFolderInTree(folderTree, profile.default_folder_id);
      
      if (defaultFolder) {
        console.log('📁 StudyListPage: Found default folder, setting as current:', defaultFolder.name);
        setCurrentFolder(defaultFolder);
        setHasLoadedDefaultFolder(true); // Marcar como cargada para no interferir más
      } else {
        console.log('📁 StudyListPage: Default folder not found in tree, staying at root');
        setHasLoadedDefaultFolder(true); // Marcar como intentada para no reintentar
      }
    }
  }, [profile?.default_folder_id, folderTree, hasLoadedDefaultFolder, currentFolder, setCurrentFolder]);

  // Monitor de navegación para debugging
  useEffect(() => {
    console.log('📁 StudyListPage: currentFolder changed to:', {
      folderId: currentFolder?.id || 'ROOT',
      folderName: currentFolder?.name || 'ROOT',
      isAtRoot: currentFolder === null
    });
  }, [currentFolder]);

  // Close folder navigator when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && showFolderNavigator) {
        const target = event.target as Element;
        if (!target.closest('.folder-navigator') && !target.closest('.folder-toggle-btn')) {
          setShowFolderNavigator(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, showFolderNavigator]);

  useEffect(() => {
    const state = location.state as { selectStudyId?: string, forceSelect?: boolean } | null;
    if (state?.selectStudyId) {
      const studyToSelect = studies.find(s => s.id === state.selectStudyId);
      if (studyToSelect) {
        if (state.forceSelect) {
          setSelectedStudy(null);
          setTimeout(() => {
            setSelectedStudy(studyToSelect);
            setSelectedStudyId(state.selectStudyId ?? null);
          }, 50);
        } else {
          setSelectedStudy(studyToSelect);
          setSelectedStudyId(state.selectStudyId ?? null);
        }
      }
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, studies, setSelectedStudy]);

  const handleStudyClick = (study: Study) => {
    setSelectedStudy(study);
  };

  const handleNewStudy = () => {
    setSelectedStudy(null);
    navigate('/study', { state: { folderId: currentFolder?.id } });
  };

  const handleDeleteStudy = async (studyId: string) => {
    if (window.confirm(t('common:confirmDelete'))) {
      await deleteStudy(studyId);
      if (selectedStudy?.id === studyId) {
        setSelectedStudy(null);
      }
    }
  };

  const handleFilterChange = (filterName: string, value: boolean) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  const handleSearch = (query: string) => {
    setSearchText(query || '');
  };

  const handleSortByChange = (newSortBy: 'date' | 'company') => {
    setSortBy(newSortBy);
  };

  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  // Folder management functions using store
  const handleFolderSelect = (folder: FolderTreeNode | null) => {
    console.log('📁 StudyListPage: handleFolderSelect called with:', {
      folderId: folder?.id || 'ROOT',
      folderName: folder?.name || 'ROOT',
      isNavigatingToRoot: folder === null
    });
    setCurrentFolder(folder);
  };

  const handleCreateFolder = (parentId?: string) => {
    const parent = parentId ? findFolderInTree(folderTree, parentId) : undefined;
    setParentFolder(parent || undefined);
    setEditingFolder(undefined);
    setShowFolderModal(true);
  };

  const handleEditFolder = (folder: FolderTreeNode) => {
    setEditingFolder(folder);
    setParentFolder(undefined);
    setShowFolderModal(true);
  };

  const handleDeleteFolder = async (folder: FolderTreeNode) => {
    console.log('🗑️ StudyListPage: handleDeleteFolder called for folder:', folder.name);
    try {
      // Try simple deletion first
      await deleteFolder(folder.id);
      if (currentFolder?.id === folder.id) {
        setCurrentFolder(null);
      }
      console.log('🗑️ StudyListPage: Folder deleted successfully');
    } catch (error) {
      console.log('🗑️ StudyListPage: Error deleting folder:', error);
      // If it fails because the folder has content, show the modal
      if (error instanceof Error && error.message === 'FOLDER_HAS_CONTENT') {
        console.log('🗑️ StudyListPage: Folder has content, showing modal');
        setFolderToDelete(folder);
        setShowDeleteFolderModal(true);
      } else {
        console.error('🗑️ StudyListPage: Unexpected error deleting folder:', error);
        // For other errors, we don't show the modal
      }
    }
  };

  const handleDeleteFolderWithContent = async (folderId: string, action: 'move' | 'delete', targetFolderId?: string) => {
    console.log('🗑️ StudyListPage: handleDeleteFolderWithContent called:', { folderId, action, targetFolderId });
    try {
      const { deleteFolderWithContent } = useFolderStore.getState();
      await deleteFolderWithContent(folderId, action, targetFolderId);
      console.log('🗑️ StudyListPage: Folder with content deleted/moved successfully');
      
      // If current folder was deleted, navigate away
      if (currentFolder?.id === folderId) {
        console.log('🗑️ StudyListPage: Current folder was deleted, navigating to root');
        setCurrentFolder(null);
      }
      
      // Close the modal
      setShowDeleteFolderModal(false);
      setFolderToDelete(null);
      console.log('🗑️ StudyListPage: Modal closed and state reset');
    } catch (error) {
      console.error('🗑️ StudyListPage: Error deleting folder with content:', error);
      // Modal will stay open if there's an error to allow user to try again
    }
  };

  const handleMoveItem = async (itemId: string, itemType: 'study' | 'folder', targetFolderId?: string) => {
    try {
      if (itemType === 'study') {
        await moveStudyToFolder(itemId, targetFolderId);
        await fetchStudies();
      } else {
        await moveFolderToFolder(itemId, targetFolderId);
      }
    } catch (error) {
      console.error('Error moving item:', error);
    }
  };

  const handleBulkExport = async (folderId?: string) => {
    // Configurar datos del modal
    setExportFolderId(folderId);
    if (folderId) {
      const folder = folderTree.find(f => f.id === folderId);
      setExportFolderName(folder?.name);
    } else {
      setExportFolderName(undefined);
    }
    setShowBulkExportModal(true);
  };

  const handleBulkExportConfirm = async (request: any) => {
    try {
      // Ya no necesitamos esta función, el modal maneja todo
      console.log('Export handled by modal directly');
    } catch (error) {
      console.error('Error in bulk export:', error);
    }
  };

  const handleSaveFolder = async (folderData: Partial<Folder>) => {
    console.log('📁 StudyListPage: handleSaveFolder called with:', { folderData, editingFolder, parentFolder });
    try {
      if (editingFolder) {
        // Para edición, usar los datos exactos del modal (incluye parent_folder_id)
        console.log('📁 StudyListPage: Updating folder', editingFolder.id, 'with data:', folderData);
        await updateFolder(editingFolder.id, folderData);
        console.log('📁 StudyListPage: Folder updated successfully');
      } else {
        // Para creación, usar parent_folder_id del parentFolder si no viene en folderData
        const createData = {
          ...folderData,
          parent_folder_id: folderData.parent_folder_id || parentFolder?.id
        };
        console.log('📁 StudyListPage: Creating folder with data:', createData);
        await createFolder(createData);
        console.log('📁 StudyListPage: Folder created successfully');
      }
      // Cerrar modal
      setShowFolderModal(false);
      setEditingFolder(undefined);
      setParentFolder(undefined);
      
      // Refrescar carpetas para ver cambios inmediatamente
      console.log('📁 StudyListPage: Refreshing folders...');
      await fetchFolders();
      console.log('📁 StudyListPage: Folders refreshed');
    } catch (error) {
      console.error('📁 StudyListPage: Error saving folder:', error);
      // No cerramos el modal en caso de error para que el usuario pueda reintentar
    }
  };

  const handleMoveStudyToFolder = (study: Study) => {
    // Solo permitir mover estudios propios
    if (study.user_id !== user?.id) {
      console.warn('Cannot move study: user is not the owner');
      return;
    }
    
    setStudyToMove(study);
    setShowMoveModal(true);
  };

  const handleConfirmMoveStudy = async (folderId?: string) => {
    if (!studyToMove) return;
    
    try {
      await moveStudyToFolder(studyToMove.id, folderId);
      // Refrescar ambos: estudios y carpetas para actualizar conteos
      await Promise.all([
        fetchStudies(),
        fetchFolders()
      ]);
      setShowMoveModal(false);
      setStudyToMove(null);
    } catch (error) {
      console.error('Error moving study to folder:', error);
    }
  };

  // Función para obtener breadcrumbs de la carpeta actual
  const getBreadcrumbs = (): Array<{ id: string; name: string }> => {
    if (!currentFolder) return [];
    
    const breadcrumbs: Array<{ id: string; name: string }> = [];
    let current: any = currentFolder;
    
    // Construir breadcrumbs desde la carpeta actual hacia arriba
    while (current) {
      breadcrumbs.unshift({ id: current.id, name: current.name });
      
      // Buscar la carpeta padre
      const parentId = (current as any).parent_folder_id;
      current = parentId ? folderTree.find(f => f.id === parentId) || null : null;
    }
    
    return breadcrumbs;
  };

  // Función para subir un nivel en la jerarquía de carpetas
  const handleGoUp = () => {
    if (!currentFolder) {
      console.log('📁 StudyListPage: Already at root, cannot go up');
      return;
    }
    
    const parentId = (currentFolder as any).parent_folder_id;
    if (parentId) {
      // Tiene padre, navegar a esa carpeta
      const parentFolder = folderTree.find(f => f.id === parentId);
      console.log('📁 StudyListPage: Navigating up to parent folder:', {
        currentFolderId: currentFolder.id,
        currentFolderName: currentFolder.name,
        parentId: parentId,
        foundParentFolder: parentFolder ? 'YES' : 'NO',
        parentFolderName: parentFolder?.name || 'NOT_FOUND'
      });
      setCurrentFolder((parentFolder as any) || null);
    } else {
      // No tiene padre, ir a la raíz
      console.log('📁 StudyListPage: Navigating to ROOT via arrow button (no parent)');
      setCurrentFolder(null);
    }
  };

  // Función principal de filtrado - lógica corregida
  const getFilteredStudies = () => {
    console.log('📁 StudyListPage: getFilteredStudies called with:', {
      ownershipFilter,
      currentFolder: currentFolder?.name || null,
      totalStudies: studies.length,
      userId: user?.id
    });

    let filteredStudies = studies;

    // LÓGICA MEJORADA: "Compartidos conmigo" tiene comportamiento especial
    if (ownershipFilter === 'shared') {
      // "Compartidos conmigo": SIEMPRE ignora carpetas, muestra todos los compartidos
      filteredStudies = studies.filter(study => study.user_id !== user?.id);
      console.log('📁 StudyListPage: Final shared studies (ignoring folders):', filteredStudies.length);
    } else {
      // Para "Mis estudios" y "Todos": aplicar filtro de carpeta primero
      if (currentFolder) {
        console.log('📁 StudyListPage: Current folder ID:', currentFolder.id);
        const descendantIds = getDescendantFolderIds(currentFolder as FolderTreeNode);
        console.log('📁 StudyListPage: All studies with folder_id:', studies.map(s => ({ id: s.id, folder_id: s.folder_id, name: s.required_info?.name })));
        filteredStudies = studies.filter(study => study.folder_id && descendantIds.includes(study.folder_id));
        console.log('📁 StudyListPage: Studies in folder after filter:', filteredStudies.length, 'for folder:', currentFolder.name);
      } else {
        // Root view: show only studies without a folder
        filteredStudies = studies.filter(study => !study.folder_id);
      }

      // Luego aplicar filtro de propiedad
      if (ownershipFilter === 'owned') {
        // "Mis estudios": solo estudios propios
        filteredStudies = filteredStudies.filter(study => study.user_id === user?.id);
        console.log('📁 StudyListPage: Final owned studies:', filteredStudies.length);
      } else {
        // "Todos": mantener todos los estudios (ya filtrados por carpeta si aplicaba)
        console.log('📁 StudyListPage: Final all studies:', filteredStudies.length);
      }
    }

    return filteredStudies;
  };

  // Contar estudios por tipo (considerando la nueva lógica de filtros)
  const getStudiesForCounting = () => {
    if (ownershipFilter === 'shared' || ownershipFilter === 'all') {
      // Para "Compartidos conmigo" y "Todos", contar desde todos los estudios
      return studies;
    } else if (ownershipFilter === 'owned' && currentFolder) {
      // Solo para "Mis estudios" con carpeta seleccionada, filtrar por carpeta
      return studies.filter(study => (study as any).folder_id === currentFolder.id);
    } else {
      // Para "Mis estudios" sin carpeta, contar todos
      return studies;
    }
  };
  
  const studiesForCounting = getStudiesForCounting();
  const ownedStudies = studiesForCounting.filter(study => study.user_id === user?.id);
  const sharedStudies = studiesForCounting.filter(study => study.user_id !== user?.id);

  const sortedAndFilteredStudies = getFilteredStudies()
    .filter(study => {
      if (!study.info?.required) return false;
      if (searchText && !study.info.required.name.toLowerCase().includes(searchText.toLowerCase())) {
        return false;
      }
      if (filters.company && selectedStudy && study.info.required.company !== selectedStudy.info.required.company) {
        return false;
      }
      if (filters.date && selectedStudy && study.info.required.date !== selectedStudy.info.required.date) {
        return false;
      }
      
      return true;
    })
    .sort((a, b) => {
      const valA = sortBy === 'date' 
        ? new Date(a.info.required.date).getTime() 
        : (a.info.required.company || '').toLowerCase();
      const valB = sortBy === 'date' 
        ? new Date(b.info.required.date).getTime() 
        : (b.info.required.company || '').toLowerCase();

      let comparison = 0;
      if (valA > valB) {
        comparison = 1;
      } else if (valA < valB) {
        comparison = -1;
      }
      return sortOrder === 'asc' ? comparison : comparison * -1;
    });

  const getNavigationButtons = useCallback(() => {
    if (!selectedStudy) return [];

    const elements: ElementInstance[] = selectedStudy.elements || [];

    const hasRepetitiveElements = elements.some((e: ElementInstance) => 
      e.repetition_type === 'repetitive-type' || e.repetition_type === 'repetitive'
    );
    const hasMachineElements = elements.some((e: ElementInstance) => 
      e.type === 'machine-time' || e.repetition_type === 'machine-type'
    );
    const hasFrequentialElements = elements.some((e: ElementInstance) => 
      e.repetition_type === 'frequency-type' || e.repetition_type === 'frequency'
    );

    const items = [
      { to: 'info', icon: Info, text: t('study:info'), visible: true },
      { to: 'method', icon: ListTodo, text: t('study:method'), visible: true }
    ];

    if (hasRepetitiveElements) {
      items.push({ 
        to: 'repetitive', 
        icon: Timer, 
        text: t('study:repetitive'), 
        visible: true 
      });
    }

    items.push({ 
      to: 'cronoseguido', 
      icon: Watch, 
      text: t('cronoSeguido:title'), 
      visible: true 
    });

    if (hasMachineElements) {
      items.push({ 
        to: 'machine', 
        icon: Gauge, 
        text: t('study:machineTimes'), 
        visible: true 
      });
    }

    if (hasFrequentialElements) {
      items.push({ 
        to: 'frequency', 
        icon: Clock, 
        text: t('study:frequencyTimes'), 
        visible: true 
      });
    }

    if (selectedStudy.elements.length > 0) {
      items.push({ 
        to: 'supplements', 
        icon: UserPlus, 
        text: t('study:supplements'), 
        visible: true 
      });

      // Verificar si todos los elementos tienen tiempos asignados
      let allElementsHaveTime = true;
      for (const element of selectedStudy.elements) {
        const elementTimes = selectedStudy.time_records?.[element.id] || [];
        if (elementTimes.length === 0) {
          allElementsHaveTime = false;
          break;
        }
      }

      // Verificar si hay suplementos configurados
      const hasSupplements = selectedStudy.supplements && 
        Object.values(selectedStudy.supplements).some(supp => 
          typeof supp === 'object' && 
          supp !== null && 
          'percentage' in supp && 
          (supp as { percentage: number }).percentage > 0
        );

      // Mostrar el informe solo si todos los elementos tienen tiempos y hay suplementos
      if (allElementsHaveTime && hasSupplements) {
        items.push({ 
          to: 'report', 
          icon: FileText, 
          text: t('study:report'), 
          visible: true 
        });
      }
    }

    return items;
  }, [selectedStudy, t]);

  // Los estudios ya están filtrados correctamente
  const displayStudies = sortedAndFilteredStudies;

  return (
    <div className="min-h-screen bg-gray-100 overflow-x-hidden">
      <Header 
        title={t('common:appName')}
        subtitle={selectedStudy?.info?.required?.name}
        onSearch={handleSearch}
      />
      
      <div className="relative overflow-x-hidden">
        {/* Overlay - both mobile and desktop */}
        {showFolderNavigator && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setShowFolderNavigator(false)}
          />
        )}

        {/* Folder Navigator Sidebar - Always overlay */}
        <div 
          className={`
            folder-navigator
            fixed top-0 left-0 bottom-0 
            w-80 
            bg-white border-r border-gray-200 
            z-50
            overflow-y-auto
            transition-transform duration-300 ease-in-out
            ${showFolderNavigator ? 'translate-x-0' : '-translate-x-full'}
          `}
        >
          <FolderNavigator
            folders={folderTree}
            currentFolder={currentFolder as any}
            studies={studies.map(study => ({ ...study, folder_id: study.folder_id }))}
            selectedStudy={selectedStudy}
            onFolderSelect={(folder) => {
              handleFolderSelect(folder);
              // Auto-close after selection
              setShowFolderNavigator(false);
            }}
            onStudySelect={(study) => {
              handleStudyClick(study);
              
              // Find and set the current folder for the selected study
              if (study.folder_id) {
                const parentFolder = findFolderInTree(folderTree, study.folder_id);
                setCurrentFolder(parentFolder);
              } else {
                setCurrentFolder(null); // Study is in the root
              }

              setShowFolderNavigator(false);
            }}
            onCreateFolder={handleCreateFolder}
            onEditFolder={handleEditFolder}
            onDeleteFolder={handleDeleteFolder}
            onMoveItem={handleMoveItem}
            onBulkExport={handleBulkExport}
          />
        </div>

        {/* Main Content - always full width */}
        <main className="main-container w-full px-4 py-6">
          <div className="max-w-7xl mx-auto overflow-x-hidden">
            <div className="space-y-6 overflow-x-hidden w-full">
              
          {/* Unified Controls Bar */}
          <div className="flex flex-col gap-y-4 md:flex-row md:flex-wrap md:justify-between md:items-center p-4 bg-white rounded-lg shadow">
            {/* Filters Section */}
            <div className="flex flex-wrap gap-x-4 gap-y-2 items-center">
              <span className="text-sm font-medium text-gray-700 flex items-center"><Filter className="w-4 h-4 mr-1" />{t('filters:title')}:</span>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.company}
                  onChange={(e) => handleFilterChange('company', e.target.checked)}
                  disabled={!selectedStudy}
                  className="rounded text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm font-medium text-gray-700">{t('filters:company')}</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.date}
                  onChange={(e) => handleFilterChange('date', e.target.checked)}
                  className="rounded text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm font-medium text-gray-700">{t('filters:date')}</span>
              </label>
            </div>

            {/* View and Sort Controls Section - Grouped together and allowing wrap */}
            <div className="flex flex-wrap gap-x-2 sm:gap-x-4 gap-y-2 items-center justify-start md:justify-end">
              {/* View Controls */}
              <div className="flex space-x-2">
                <Button 
                  variant={viewMode === 'grid' ? "default" : "outline"} 
                  className={`${viewMode === 'grid' ? 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2' : ''} p-2 sm:px-3 sm:py-1.5 text-sm md:px-4 md:py-2`}
                  onClick={() => setViewMode('grid')} 
                  aria-label={t('study:viewAsGrid')}
                >
                  <LayoutGrid className="w-4 h-4 md:w-5 md:h-5 md:mr-2" />
                  <span className="hidden sm:inline">{t('study:grid')}</span>
                </Button>
                <Button 
                  variant={viewMode === 'list' ? "default" : "outline"} 
                  className={`${viewMode === 'list' ? 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2' : ''} p-2 sm:px-3 sm:py-1.5 text-sm md:px-4 md:py-2`}
                  onClick={() => setViewMode('list')} 
                  aria-label={t('study:viewAsList')}
                >
                  <List className="w-4 h-4 md:w-5 md:h-5 md:mr-2" />
                  <span className="hidden sm:inline">{t('study:list')}</span>
                </Button>
              </div>

              {/* Sort Controls */}
              <div className="flex space-x-2 items-center">
                <span className="text-sm font-medium text-gray-700 hidden sm:inline">{t('study:sortBy')}:</span>
                <Button 
                  variant={sortBy === 'date' ? "default" : "outline"} 
                  className={`${sortBy === 'date' ? 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2' : ''} p-2 sm:px-3 sm:py-1.5 text-sm md:px-4 md:py-2`}
                  onClick={() => handleSortByChange('date')}
                >
                  <Calendar className="w-4 h-4 md:w-5 md:h-5 md:mr-2" /> 
                  <span className="hidden sm:inline">{t('study:date')}</span>
                </Button>
                <Button 
                  variant={sortBy === 'company' ? "default" : "outline"} 
                  className={`${sortBy === 'company' ? 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2' : ''} p-2 sm:px-3 sm:py-1.5 text-sm md:px-4 md:py-2`}
                  onClick={() => handleSortByChange('company')}
                >
                  <Briefcase className="w-4 h-4 md:w-5 md:h-5 md:mr-2" />
                  <span className="hidden sm:inline">{t('study:company')}</span>
                </Button>
                <Button
                  variant="outline"
                  className="p-2 sm:px-3 sm:py-1.5 text-sm md:px-4 md:py-2"
                  onClick={toggleSortOrder}
                  aria-label={t('study:toggleSortOrder')}
                >
                  <ArrowDownUp className="w-4 h-4 md:w-5 md:h-5" />
                </Button>
              </div>
            </div>
          </div>

          {/* Folder Navigation */}
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => setShowFolderNavigator(!showFolderNavigator)}
                className="folder-toggle-btn flex items-center gap-2 px-3 py-2"
                size={isMobile ? "sm" : "default"}
              >
                <FolderPlus className="h-4 w-4" />
                <span className={isMobile ? "text-sm" : ""}>
                  {t('common:folders', { defaultValue: 'Folders' })}
                </span>
              </Button>

              {/* Current Folder Indicator */}
              {currentFolder && (
                <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg border">
                  <FolderIcon className="h-4 w-4" style={{ color: currentFolder.color }} />
                  <span className="text-sm font-medium text-gray-700 truncate max-w-32">
                    {currentFolder.name}
                  </span>
                </div>
              )}
            </div>
            
            {/* Breadcrumbs Navigation */}
            <FolderBreadcrumbs
              currentFolder={currentFolder}
              breadcrumbs={getBreadcrumbs()}
              folderTree={folderTree}
              onFolderSelect={(folder) => handleFolderSelect(folder)}
              onGoUp={handleGoUp}
              className="mt-2"
            />
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 mb-4">
            <button
              onClick={handleNewStudy}
              className="flex-1 bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 flex items-center justify-center space-x-2"
            >
              <Plus className="w-5 h-5" />
              <span>{t('study:new')}</span>
            </button>
            <button
              onClick={() => navigate('/library')}
              className="flex-1 bg-purple-500 text-white py-3 px-4 rounded-lg hover:bg-purple-600 flex items-center justify-center space-x-2"
            >
              <BookOpen className="w-5 h-5" />
              <span>{t('library:title')}</span>
            </button>
          </div>

          {selectedStudy && (
            <div className="w-full my-6">
              <div className="nav-buttons-container">
                <div className="nav-buttons-inner">
                  {getNavigationButtons().map(button => (
                    <button
                      key={button.to}
                      onClick={() => navigate(`/study/${selectedStudy.id}/${button.to}`)}
                      className="nav-button"
                    >
                      <button.icon className="nav-button-icon" />
                      <span className="nav-button-text">{button.text}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className={`gap-6 ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'flex flex-col'}`}>
            {displayStudies.map(study => {
              const folder = study.folder_id ? folders.find(f => f.id === study.folder_id) : undefined;
              return (
                <StudyCard
                  key={study.id}
                  study={study}
                  isSelected={selectedStudy?.id === study.id}
                  onClick={() => handleStudyClick(study)}
                  onDelete={() => handleDeleteStudy(study.id)}
                  onMoveToFolder={() => handleMoveStudyToFolder(study)}
                  viewMode={viewMode}
                  folder={folder}
                  onFolderClick={() => folder && handleFolderSelect(folder as FolderTreeNode)}
                />
              )
            })}
          </div>

          {displayStudies.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              {currentFolder 
                ? `No hay estudios en la carpeta "${currentFolder.name}"`
                : t('study:noStudiesFound')
              }
            </div>
          )}
        </div>
          </div>
        </main>
      </div>

      {/* Folder Modal */}
      {showFolderModal && (
        <FolderModal
          isOpen={showFolderModal}
          folder={editingFolder}
          parentFolder={parentFolder}
          availableFolders={folders}
          onClose={() => setShowFolderModal(false)}
          onSave={handleSaveFolder}
        />
      )}

      {/* Move to Folder Modal */}
      {showMoveModal && studyToMove && (
        <MoveToFolderModal
          isOpen={showMoveModal}
          studyName={(studyToMove as any).info?.required?.name || (studyToMove as any).required_info?.name || 'Estudio sin nombre'}
          folders={folderTree}
          currentFolderId={(studyToMove as any).folder_id}
          onClose={() => {
            setShowMoveModal(false);
            setStudyToMove(null);
          }}
          onMove={handleConfirmMoveStudy}
        />
      )}

      {/* Study Consolidation Modal */}
      {showBulkExportModal && (
        <StudyConsolidationModal
          isOpen={showBulkExportModal}
          folderName={exportFolderName}
          folderId={exportFolderId}
          onClose={() => setShowBulkExportModal(false)}
          onStudyCreated={() => {
            // Actualizar la lista de estudios después de crear un estudio consolidado
            fetchStudies();
          }}
        />
      )}

      {/* Delete Folder Modal */}
      {showDeleteFolderModal && folderToDelete && (
        <DeleteFolderModal
          isOpen={showDeleteFolderModal}
          folder={folderToDelete as any}
          availableFolders={folders}
          onClose={() => {
            setShowDeleteFolderModal(false);
            setFolderToDelete(null);
          }}
          onDelete={handleDeleteFolderWithContent}
        />
      )}
    </div>
  );
};
