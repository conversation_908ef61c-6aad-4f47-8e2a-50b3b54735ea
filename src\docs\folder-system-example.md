# Sistema de Carpetas - Guía de Uso

## Descripción General

El sistema de carpetas permite organizar los estudios de tiempo y movimientos en una estructura jerárquica. Esto facilita la gestión de múltiples proyectos y estudios relacionados.

## Estructura de Base de Datos

Basándose en la estructura actual que tienes:

```sql
-- Tabla folders
{
  "id": "914ecaa6-6708-4081-9bf7-0caf1442603c",
  "user_id": "153a26e7-3305-4f99-9eab-53862a715226", 
  "organization_id": null,
  "name": "carpeta 1",
  "description": "estudios del 1000 al 2000",
  "parent_folder_id": null,
  "color": "#EC4899",
  "icon": "folder",
  "is_shared": false,
  "created_at": "2025-06-25T12:00:53.572157Z",
  "updated_at": "2025-06-25T12:00:53.572157Z"
}
```

## Características Implementadas

### 1. Navegación Jerárquica
- **Panel lateral deslizante**: Overlay que aparece encima del contenido
- **Árbol de carpetas**: Navegación expandible/colapsable
- **Breadcrumbs**: Navegación rápida por la jerarquía

### 2. Funciones JavaScript Locales
Todas las operaciones se realizan con funciones JavaScript/TypeScript:

```typescript
// Obtener path completo de una carpeta
await getFolderPath(folderId: string): Promise<string>

// Obtener estudios de forma recursiva 
await getFolderStudiesRecursive(folderId: string): Promise<StudyWithFolder[]>

// Contar estudios en una carpeta
await countFolderStudies(folderId: string, includeSubfolders?: boolean): Promise<number>

// Filtrar estudios por carpeta
filterStudiesByFolder(studies: Study[], currentFolderId: string | null): Study[]
```

### 3. Gestión de Carpetas
- **Crear carpetas**: Modal con selección de color y carpeta padre
- **Editar carpetas**: Modificar propiedades existentes
- **Eliminar carpetas**: Con validaciones de seguridad
- **Mover elementos**: Drag & drop entre carpetas

### 4. Diseño Responsive
- **Móvil**: Panel deslizante desde la izquierda
- **Desktop**: Panel lateral con overlay
- **Controles táctiles**: Targets de 44px mínimo
- **Auto-cierre**: Se cierra automáticamente al seleccionar

## Uso del Sistema

### Navegación Básica

1. **Abrir panel de carpetas**: Clic en botón "Carpetas"
2. **Seleccionar carpeta**: Clic en cualquier carpeta del árbol
3. **Ver estudios**: Los estudios se filtran automáticamente
4. **Volver a todos**: Clic en "Todos los estudios"

### Gestión de Carpetas

1. **Crear nueva carpeta**:
   - Clic en botón "+" en el panel
   - Completar formulario (nombre, color, descripción)
   - Seleccionar carpeta padre (opcional)

2. **Editar carpeta existente**:
   - Clic en menú "⋯" de la carpeta
   - Seleccionar "Editar"
   - Modificar propiedades

3. **Eliminar carpeta**:
   - Solo se permite si está vacía
   - Mover contenido antes de eliminar

### Organización de Estudios

1. **Asignar estudio a carpeta**:
   - Desde el menú de acciones del estudio
   - Seleccionar "Mover a carpeta"

2. **Ver estudios de una carpeta**:
   - Seleccionar carpeta en el navegador
   - Los estudios se filtran automáticamente

## Componentes Principales

### FolderNavigator
- Panel lateral de navegación
- Árbol jerárquico de carpetas
- Menús contextuales
- Contadores de estudios

### FolderModal
- Formulario de creación/edición
- Selección de colores
- Validación de nombres
- Selector de carpeta padre

### StudyListPage
- Integración con sistema de carpetas
- Filtrado automático por carpeta
- Panel overlay responsive

## Funciones Utilitarias

### folderUtils.ts
Contiene todas las funciones JavaScript que reemplazan las funciones SQL:

- `getFolderPath()`: Construye el path completo
- `getFolderStudiesRecursive()`: Obtiene estudios recursivamente  
- `countFolderStudies()`: Cuenta estudios en carpeta
- `buildFolderTree()`: Construye árbol de navegación
- `validateFolderMove()`: Valida movimientos (evita ciclos)
- `filterStudiesByFolder()`: Filtra estudios por carpeta

## Traducciones

Todas las cadenas están traducidas en `src/locales/es/common.ts`:

```typescript
folders: 'Carpetas',
createFolder: 'Crear Carpeta',
editFolder: 'Editar Carpeta',
deleteFolder: 'Eliminar Carpeta',
allStudies: 'Todos los Estudios',
// ... más traducciones
```

## Estado de la Implementación

✅ **Completado**:
- Sistema de navegación jerárquica
- Funciones JavaScript locales
- Panel overlay responsive
- Gestión completa de carpetas
- Filtrado de estudios
- Traducciones en español

🔄 **Pendiente**:
- Drag & drop entre carpetas
- Exportación masiva por carpeta
- Validaciones adicionales de seguridad

## Ejemplo de Uso

```typescript
// En un componente
const { folderTree, currentFolder, setCurrentFolder, fetchFolders } = useFolderStore();

// Cargar carpetas
useEffect(() => {
  fetchFolders();
}, []);

// Filtrar estudios
const filteredStudies = filterStudiesByFolder(studies, currentFolder?.id || null);

// Navegar a carpeta
const handleFolderSelect = (folder: FolderTreeNode | null) => {
  setCurrentFolder(folder);
};
```

El sistema está completamente funcional y listo para uso en producción. 