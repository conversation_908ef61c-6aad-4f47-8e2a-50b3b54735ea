# Header Component

## Descripción
Componente de encabezado principal que proporciona navegación y acciones globales.

## Props
```typescript
interface HeaderProps {
  showSearch?: boolean;
  showActions?: boolean;
  title?: string;
  subtitle?: string;
  onBack?: () => void;
}
```

## Características
- Logo y título de la aplicación
- Navegación hacia atrás
- Selector de idioma
- Botón de búsqueda (opcional)
- Menú de acciones (opcional)
- Botón de cierre de sesión

## Subcomponentes
- `LanguageSelector`: Selector de idioma
- `SearchButton`: Botón de búsqueda
- `ActionsMenu`: Menú de acciones
- `BackButton`: Botón de retorno

## Uso
```tsx
<Header
  title="Estudio de Tiempos"
  subtitle="Proceso actual"
  showSearch={true}
  showActions={true}
  onBack={() => navigate(-1)}
/>
```