import React from 'react';
import { cn } from '../../lib/utils';

interface EnhancedSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  animation?: 'spin' | 'pulse' | 'bounce' | 'dots';
  className?: string;
}

export const EnhancedSpinner: React.FC<EnhancedSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  animation = 'spin',
  className
}) => {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const variantClasses = {
    default: 'border-gray-300 border-t-gray-600',
    primary: 'border-blue-200 border-t-blue-600',
    success: 'border-green-200 border-t-green-600',
    warning: 'border-yellow-200 border-t-yellow-600',
    danger: 'border-red-200 border-t-red-600'
  };

  if (animation === 'dots') {
    return (
      <div className={cn('flex space-x-1', className)}>
        {[0, 1, 2].map((index) => (
          <div
            key={index}
            className={cn(
              'rounded-full animate-pulse',
              sizeClasses[size],
              {
                'bg-gray-400': variant === 'default',
                'bg-blue-500': variant === 'primary',
                'bg-green-500': variant === 'success',
                'bg-yellow-500': variant === 'warning',
                'bg-red-500': variant === 'danger'
              }
            )}
            style={{
              animationDelay: `${index * 0.2}s`,
              animationDuration: '1.4s'
            }}
          />
        ))}
      </div>
    );
  }

  if (animation === 'bounce') {
    return (
      <div
        className={cn(
          'rounded-full animate-bounce',
          sizeClasses[size],
          {
            'bg-gray-400': variant === 'default',
            'bg-blue-500': variant === 'primary',
            'bg-green-500': variant === 'success',
            'bg-yellow-500': variant === 'warning',
            'bg-red-500': variant === 'danger'
          },
          className
        )}
      />
    );
  }

  if (animation === 'pulse') {
    return (
      <div
        className={cn(
          'rounded-full animate-pulse',
          sizeClasses[size],
          {
            'bg-gray-400': variant === 'default',
            'bg-blue-500': variant === 'primary',
            'bg-green-500': variant === 'success',
            'bg-yellow-500': variant === 'warning',
            'bg-red-500': variant === 'danger'
          },
          className
        )}
      />
    );
  }

  // Default spin animation
  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2',
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      style={{
        animationDuration: '1s'
      }}
    />
  );
};

// Componente específico para estados de carga rápidos
export const QuickLoader: React.FC<{
  message?: string;
  variant?: 'primary' | 'success' | 'warning';
}> = ({ message = 'Cargando...', variant = 'primary' }) => {
  return (
    <div className="flex items-center justify-center p-3 bg-gray-50 rounded-lg border">
      <EnhancedSpinner 
        size="sm" 
        variant={variant} 
        animation="spin"
        className="mr-3" 
      />
      <span className={cn(
        'text-sm font-medium',
        {
          'text-blue-700': variant === 'primary',
          'text-green-700': variant === 'success',
          'text-yellow-700': variant === 'warning'
        }
      )}>
        {message}
      </span>
    </div>
  );
};

// Componente para indicador de progreso
export const ProgressIndicator: React.FC<{
  steps: string[];
  currentStep: number;
  variant?: 'primary' | 'success';
}> = ({ steps, currentStep, variant = 'primary' }) => {
  return (
    <div className="space-y-3">
      {steps.map((step, index) => (
        <div key={index} className="flex items-center space-x-3">
          {index < currentStep ? (
            <div className={cn(
              'w-5 h-5 rounded-full flex items-center justify-center text-white text-xs',
              {
                'bg-blue-500': variant === 'primary',
                'bg-green-500': variant === 'success'
              }
            )}>
              ✓
            </div>
          ) : index === currentStep ? (
            <EnhancedSpinner 
              size="sm" 
              variant={variant} 
              animation="spin"
            />
          ) : (
            <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
          )}
          <span className={cn(
            'text-sm',
            {
              'text-gray-900 font-medium': index <= currentStep,
              'text-gray-500': index > currentStep
            }
          )}>
            {step}
          </span>
        </div>
      ))}
    </div>
  );
}; 