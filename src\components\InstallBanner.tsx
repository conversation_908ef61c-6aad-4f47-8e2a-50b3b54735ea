import React from 'react';
import { useTranslation } from 'react-i18next';
import { Download, X } from 'lucide-react';
import { useInstallPrompt } from '../hooks/useInstallPrompt';

export const InstallBanner: React.FC = () => {
  const { t } = useTranslation('common');
  const { isInstallable, promptToInstall, hideBanner } = useInstallPrompt();

  if (!isInstallable) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg safe-bottom z-50">
      <div className="flex items-center justify-between p-4">
        <div 
          className="flex-1 cursor-pointer hover:bg-gray-50"
          onClick={promptToInstall}
        >
          <h3 className="font-semibold">{t('pwa.installPrompt')}</h3>
          <p className="text-sm text-gray-600">{t('pwa.install')}</p>
        </div>
        <div className="flex items-center gap-4">
          <Download 
            className="w-6 h-6 text-purple-600 cursor-pointer hover:text-purple-700" 
            onClick={promptToInstall}
          />
          <button
            onClick={(e) => {
              e.stopPropagation();
              hideBanner();
            }}
            className="p-1 hover:bg-gray-100 rounded-full"
            title={t('pwa.hideBanner')}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>
      </div>
    </div>
  );
};