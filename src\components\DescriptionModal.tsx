import React, { useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { SpeechRecognitionButton } from './SpeechRecognitionButton';

interface DescriptionModalProps {
  onClose: () => void;
  onSave: (description: string) => void;
  isOpen: boolean;
}

export const DescriptionModal: React.FC<DescriptionModalProps> = ({
  onClose,
  onSave,
  isOpen
}) => {
  const { t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);
  const [description, setDescription] = React.useState('');

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleTranscript = (text: string) => {
    setDescription(text);
    onSave(text);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div ref={modalRef} className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-bold mb-4">{t('cronoSeguido.addDescription')}</h3>
        
        <div className="flex justify-center mb-4">
          <SpeechRecognitionButton onTranscript={handleTranscript} />
        </div>

        <div className="text-center">
          {description ? (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p>{description}</p>
            </div>
          ) : (
            <p className="text-gray-600">{t('cronoSeguido.pressToSpeak')}</p>
          )}
        </div>
      </div>
    </div>
  );
};