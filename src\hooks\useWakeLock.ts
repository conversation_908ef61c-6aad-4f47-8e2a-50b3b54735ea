import { useRef, useCallback, useEffect } from 'react';

export function useWakeLock() {
  const wakeLockRef = useRef<WakeLockSentinel | null>(null);
  const isVisibleRef = useRef(true);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const requestWakeLock = useCallback(async (retryCount = 0) => {
    try {
      // Verificar si el dispositivo está en modo de ahorro de energía
      if ('getBattery' in navigator) {
        const battery = await (navigator as any).getBattery();
        if (battery.charging === false && battery.level < 0.2) {
          console.log('Batería baja, no se solicitará wake lock');
          return;
        }
      }

      if ('wakeLock' in navigator && !wakeLockRef.current && isVisibleRef.current) {
        wakeLockRef.current = await navigator.wakeLock.request('screen');
        console.log('Wake lock acquired');
        
        // Escuchar cuando el wake lock se libera
        wakeLockRef.current.addEventListener('release', () => {
          console.log('Wake lock released by system');
          wakeLockRef.current = null;
          // Intentar recuperar el wake lock si la página sigue visible
          if (isVisibleRef.current) {
            requestWakeLock();
          }
        });
      }
    } catch (error) {
      console.error('Error requesting wake lock:', error);
      // Reintentar si no se pudo obtener el wake lock y la página está visible
      if (isVisibleRef.current && retryCount < 3) {
        console.log(`Retrying wake lock request (${retryCount + 1}/3)...`);
        retryTimeoutRef.current = setTimeout(() => {
          requestWakeLock(retryCount + 1);
        }, 1000 * (retryCount + 1)); // Esperar más tiempo entre reintentos
      }
    }
  }, []);

  const releaseWakeLock = useCallback(async () => {
    try {
      if (wakeLockRef.current) {
        await wakeLockRef.current.release();
        wakeLockRef.current = null;
        console.log('Wake lock released manually');
      }
      // Limpiar cualquier reintento pendiente
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    } catch (error) {
      console.error('Error releasing wake lock:', error);
    }
  }, []);

  useEffect(() => {
    // Actualizar el estado de visibilidad
    const handleVisibilityChange = async () => {
      isVisibleRef.current = document.visibilityState === 'visible';
      
      if (isVisibleRef.current) {
        // Si la página se vuelve visible, intentar obtener el wake lock
        await requestWakeLock();
      } else {
        // Si la página se oculta, liberar el wake lock
        await releaseWakeLock();
      }
    };

    // Establecer el estado inicial de visibilidad
    isVisibleRef.current = document.visibilityState === 'visible';

    // Solicitar wake lock inicial si la página está visible
    if (isVisibleRef.current) {
      requestWakeLock();
    }

    // Escuchar cambios de visibilidad
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Escuchar cuando la aplicación vuelve a primer plano
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
      releaseWakeLock();
    };
  }, [requestWakeLock, releaseWakeLock]);

  return { requestWakeLock, releaseWakeLock };
}
