name: Update Version

on:
  push:
    branches: [ main ]
    paths-ignore:
      - 'src/config/version.ts'
      - 'package.json'
      - 'CHANGELOG.md'

jobs:
  update-version:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.PAT_TOKEN }}
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'
      
      - name: Update Version and Changelog
        run: node scripts/update-version.cjs
      
      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add src/config/version.ts package.json CHANGELOG.md
          git commit -m "chore: update version [skip ci]" || echo "No changes to commit"
          git push
