import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Header } from '../components/Header';
import { useStudyStore } from '../store/studyStore';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl';
import { TimeUnit, ElementStats, ReportStats } from '../types';
import { ReportTable } from '../components/report/ReportTable';
import { ReportResults } from '../components/report/ReportResults';
import { ReportActions } from '../components/report/ReportActions';
import { calculateElementStats } from '../utils/calculateElementStats';

interface ReportPageProps {
  onBack: () => void;
}

export const ReportPage: React.FC<ReportPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['report', 'common']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  useStudyFromUrl();
  
  const [timeUnit, setTimeUnit] = useState<TimeUnit>('seconds');
  const [shiftMinutes, setShiftMinutes] = useState(480);
  const [contingency, setContingency] = useState(0);
  const [elementStats, setElementStats] = useState<ElementStats[]>([]);
  const [reportStats, setReportStats] = useState<ReportStats | null>(null);

  useEffect(() => {
    if (!selectedStudy) return;

    // Calcular estadísticas para cada elemento
    const stats = selectedStudy.elements.map(element => 
      calculateElementStats(
        element,
        selectedStudy.time_records[element.id] || [],
        selectedStudy
      )
    );
    setElementStats(stats);

    // Calcular estadísticas globales del reporte
    const totalTime = stats.reduce((sum, stat) => sum + stat.finalTime, 0);
    const totalTimeWithContingency = totalTime * (1 + contingency / 100);
    
    setReportStats({
      totalTime,
      totalTimeWithContingency,
      shiftTime: shiftMinutes * 60, // Convertir minutos a segundos
      productivity: (totalTimeWithContingency / (shiftMinutes * 60)) * 100
    });
  }, [selectedStudy, timeUnit, shiftMinutes, contingency]);

  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Header title={t('title', { ns: 'report' })} onBack={onBack} />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-gray-600">
            {t('noStudySelected', { ns: 'report' })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header title={t('title', { ns: 'report' })} onBack={onBack} />
      <div className="container mx-auto px-4 py-8 space-y-8">
        <ReportActions
          timeUnit={timeUnit}
          onTimeUnitChange={setTimeUnit}
          shiftMinutes={shiftMinutes}
          onShiftMinutesChange={setShiftMinutes}
          contingency={contingency}
          onContingencyChange={setContingency}
        />
        
        {reportStats && (
          <ReportResults
            stats={reportStats}
            timeUnit={timeUnit}
          />
        )}

        <ReportTable
          elements={elementStats}
          timeUnit={timeUnit}
        />
      </div>
    </div>
  );
};