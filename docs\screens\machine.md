# Pantalla de Tiempos de Máquina

## Descripción
Interfaz especializada para la medición de tiempos de máquina.

## Componentes
- `MachineElementList`: Lista de elementos tipo máquina
- `MachineTimer`: Cronómetro especializado
- `MachineRecordList`: Lista de registros
- `ElementStats`: Estadísticas específicas

## Funcionalidades

### 1. Control de Tiempo
- Iniciar/Pausar/Detener medición
- Registro de tiempos completos de ciclo
- Gestión de interrupciones

### 2. Gestión de Registros
- Visualización de ciclos completos
- Edición de registros
- Eliminación de registros
- Comentarios por ciclo

### 3. Estadísticas
- Tiempo medio de ciclo
- Desviación estándar
- Producción estimada
- Eficiencia del ciclo

### 4. Validaciones
- Control de tiempos mínimos/máximos
- Detección de anomalías
- Alertas de variación excesiva

## Estados
```typescript
{
  selectedElement: WorkElement | null;
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  activity: number;
  records: TimeRecord[];
  editingRecord: TimeRecord | null;
  showCommentModal: boolean;
  showEditModal: boolean;
  showDeleteAllModal: boolean;
}
```

## Flujo de Trabajo
1. Selección de elemento de máquina
2. Medición de ciclo:
   - Iniciar cronómetro
   - Registrar tiempo completo
   - Añadir comentarios si necesario
3. Análisis de resultados:
   - Revisar estadísticas
   - Validar variaciones
   - Ajustar mediciones si necesario