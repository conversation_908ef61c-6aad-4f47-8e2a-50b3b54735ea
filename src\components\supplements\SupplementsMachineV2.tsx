import React, { useEffect, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Study, ElementInstance, ElementSupplements } from '../../types';
import { useTimeRecords } from '../../hooks/useTimeRecords';
import { useStudyStore } from '../../store/studyStore';
import { MachineFatigueCalculation } from './MachineFatigueCalculation';
import { exportToPDF, exportToExcel } from '../../utils/machineFatigueExports';
import { DataSupplementsTable } from './DataSupplementsTable';

interface SupplementsMachineProps {
  element: ElementInstance;
}

interface CalculationResults {
  totalCycleTime: number;
  baseTime: number;
  personalNeedsSupplement: number;
  fatigueSupplement: number;
  remainingFatigue?: number;
  explanation: {
    supplementsExplanation: string;
    baseTimeCalculation: string;
    personalNeedsCalculation: string;
    fatigueCalculation: string;
    totalTimeCalculation: string;
    remainingFatigueCalculation?: string;
  };
}

interface ElementStats {
  totalTime: number;
  averageTime: number;
  recordCount: number;
  averageActivity: number;
  elementId: string;
  normalizedTime: number;
  personalNeedsTime: number;
  fatigueTime: number;
  frequency: {
    repetitions: number;
    cycles: number;
  };
}

export const SupplementsMachineV2: React.FC<SupplementsMachineProps> = ({ element }) => {
  const { t } = useTranslation('supplements');

  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const [machineRunTime, setMachineRunTime] = useState<number>(0);
  const [machineStopTime, setMachineStopTime] = useState<number>(0);
  const [machineTime, setMachineTime] = useState<number>(0);
  const [inactivityTime, setInactivityTime] = useState<number>(0);
  const [personalNeedsPercentage, setPersonalNeedsPercentage] = useState<number>(5);
  const [fatiguePercentage, setFatiguePercentage] = useState<number>(10.00);
  const [calculationCase, setCalculationCase] = useState<string>("case1");
  const [results, setResults] = useState<CalculationResults | null>(null);
  const [fatigueExplanationDetails, setFatigueExplanationDetails] = useState<string>('');
  
  const machineElements = useMemo(() => {
    if (!selectedStudy?.elements) return { running: [], stopped: [], machine: [] };
    return {
      running: selectedStudy.elements.filter((e: ElementInstance) => e.type === 'machine-running'),
      stopped: selectedStudy.elements.filter((e: ElementInstance) => e.type === 'machine-stopped'),
      machine: selectedStudy.elements.filter((e: ElementInstance) => e.type === 'machine-time' && !e.concurrent_machine_time)
    };
  }, [selectedStudy]);

  const runningRecords = machineElements.running.map((element: ElementInstance) =>
    useTimeRecords(selectedStudy?.time_records[element.id] || [])
  );

  const stoppedRecords = machineElements.stopped.map((element: ElementInstance) =>
    useTimeRecords(selectedStudy?.time_records[element.id] || [])
  );

  const machineTimeRecords = machineElements.machine.map((element: ElementInstance) =>
    useTimeRecords(selectedStudy?.time_records[element.id] || [])
  );

  const mapElementsToStats = (elements: ElementInstance[], records: any[]): ElementStats[] => {
    if (!selectedStudy) return [];
    const activityScale = selectedStudy.required_info?.activity_scale?.normal || 100;

    return elements.map((element, index) => {
      const recordStats = records[index];
      const frequencyFactor = (Number(element.frequency_repetitions) || 1) / (Number(element.frequency_cycles) || 1);
      const normalizedTime = (recordStats.averageTime * (recordStats.averageActivity / activityScale)) * frequencyFactor;
      
      const supplements = selectedStudy.supplements?.[element.id];
      const personalNeedsPercentage = supplements?.factors?.find((f: any) => f.type === 'personal_needs')?.percentage || 0;
      const fatiguePercentage = supplements?.factors?.find((f: any) => f.type === 'fatigue')?.percentage || 0;

      return {
        totalTime: recordStats.totalTime,
        averageTime: recordStats.averageTime,
        recordCount: recordStats.recordCount,
        averageActivity: recordStats.averageActivity,
        elementId: element.id,
        frequency: {
          repetitions: Number(element.frequency_repetitions) || 1,
          cycles: Number(element.frequency_cycles) || 1
        },
        normalizedTime: normalizedTime || 0,
        personalNeedsTime: (normalizedTime || 0) * (personalNeedsPercentage / 100),
        fatigueTime: (normalizedTime || 0) * (fatiguePercentage / 100),
      };
    });
  };

  const runningStats = useMemo(() => mapElementsToStats(machineElements.running, runningRecords), [machineElements.running, runningRecords, selectedStudy]);
  const stoppedStats = useMemo(() => mapElementsToStats(machineElements.stopped, stoppedRecords), [machineElements.stopped, stoppedRecords, selectedStudy]);
  const machineTimeStats = useMemo(() => mapElementsToStats(machineElements.machine, machineTimeRecords), [machineElements.machine, machineTimeRecords, selectedStudy]);

  useEffect(() => {
    const calculateAndSetSupplements = () => {
      const totalMachineStopTime = stoppedStats.reduce((sum, stat) => sum + stat.normalizedTime, 0);
      const totalMachineRunTime = runningStats.reduce((sum, stat) => sum + stat.normalizedTime, 0);
      const totalMachineTime = machineTimeStats.reduce((sum, stat) => sum + stat.normalizedTime, 0);
      const inactivity = Math.max(0, totalMachineTime - totalMachineRunTime);
      
      setMachineStopTime(totalMachineStopTime);
      setMachineRunTime(totalMachineRunTime);
      setMachineTime(totalMachineTime);
      setInactivityTime(inactivity);

      const allElementsStats = [...stoppedStats, ...runningStats, ...machineTimeStats];
      const totalPersonalNeedsTime = allElementsStats.reduce((sum, stat) => sum + stat.personalNeedsTime, 0);
      const totalFatigueTime = allElementsStats.reduce((sum, stat) => sum + stat.fatigueTime, 0);
      
      const baseTime = totalMachineStopTime + Math.max(totalMachineTime, totalMachineRunTime);
      let finalCycleTime = baseTime;
      let appliedFatigueSupplement = 0;
      let appliedPersonalNeedsSupplement = totalPersonalNeedsTime;
      let caseKey = '';
      let remainingFatigue = 0;
      let fatigueExplanation = '';
      const inactivityInMinutes = inactivity / 60;

      if (inactivityInMinutes <= 0.5) { // Case 1
        caseKey = 'case1';
        appliedFatigueSupplement = totalFatigueTime;
        finalCycleTime = baseTime + appliedPersonalNeedsSupplement + appliedFatigueSupplement;
      } else if (inactivityInMinutes > 0.5 && inactivityInMinutes <= 1.5) { // Case 2
        caseKey = 'case2';
        const recoverableTime = inactivity - 30;
        const potentialRecovery = recoverableTime * 1.5;
        const absorbedFatigue = Math.min(totalFatigueTime, potentialRecovery);
        remainingFatigue = totalFatigueTime - absorbedFatigue;
        appliedFatigueSupplement = remainingFatigue;
        finalCycleTime = baseTime + appliedPersonalNeedsSupplement + appliedFatigueSupplement;
        fatigueExplanation = `
          Tiempo de Inactividad: ${inactivity.toFixed(2)}s (${inactivityInMinutes.toFixed(2)} min).
          Tiempo recuperable (por encima de 0.5 min): ${recoverableTime.toFixed(2)}s.
          Recuperación potencial de fatiga: (${inactivity.toFixed(2)}s - 30s) * 1.5 = ${potentialRecovery.toFixed(2)}s.
          Fatiga total a recuperar: ${totalFatigueTime.toFixed(2)}s.
          Fatiga absorbida por la espera: ${absorbedFatigue.toFixed(2)}s.
          Fatiga restante a añadir: ${remainingFatigue.toFixed(2)}s.
        `;
      } else if (inactivityInMinutes > 1.5 && inactivityInMinutes <= 10) { // Case 3
        caseKey = 'case3';
        appliedFatigueSupplement = 0;
        finalCycleTime = baseTime + appliedPersonalNeedsSupplement;
      } else { // Case 4
        caseKey = 'case4';
        appliedFatigueSupplement = 0;
        appliedPersonalNeedsSupplement = 0;
        finalCycleTime = baseTime;
      }
      setCalculationCase(caseKey);
      setFatigueExplanationDetails(fatigueExplanation);

      const res: CalculationResults = {
        totalCycleTime: finalCycleTime,
        baseTime: baseTime,
        personalNeedsSupplement: appliedPersonalNeedsSupplement,
        fatigueSupplement: appliedFatigueSupplement,
        remainingFatigue: remainingFatigue > 0 ? remainingFatigue : undefined,
        explanation: {
          supplementsExplanation: `Cálculo basado en ${t(`machineSupplements.caseExplanations.${caseKey}`)}.`,
          baseTimeCalculation: `Tiempo Parada (${totalMachineStopTime.toFixed(2)}) + Máx(Tiempo Máquina (${totalMachineTime.toFixed(2)}), Tiempo Marcha (${totalMachineRunTime.toFixed(2)})) = ${baseTime.toFixed(2)}s`,
          personalNeedsCalculation: `Suma de tiempos de N.P. de cada elemento. Total = ${totalPersonalNeedsTime.toFixed(2)}s. Aplicado = ${appliedPersonalNeedsSupplement.toFixed(2)}s.`,
          fatigueCalculation: `Suma de tiempos de Fatiga de cada elemento. Total = ${totalFatigueTime.toFixed(2)}s. Aplicado = ${appliedFatigueSupplement.toFixed(2)}s.`,
          totalTimeCalculation: `Tiempo Base (${baseTime.toFixed(2)}) + Suplemento N.P. (${appliedPersonalNeedsSupplement.toFixed(2)}) + Suplemento Fatiga (${appliedFatigueSupplement.toFixed(2)}) = ${finalCycleTime.toFixed(2)}s`,
          remainingFatigueCalculation: remainingFatigue > 0 ? `Fatiga Restante: ${remainingFatigue.toFixed(2)}s` : undefined
        }
      };
      setResults(res);
    };

    if (selectedStudy) {
      calculateAndSetSupplements();
    }
  }, [selectedStudy, runningStats, stoppedStats, machineTimeStats, t]);

  const handlePrint = () => { window.print(); };

  const handleExportPDF = () => {
    if (!results || !selectedStudy) return;
    const exportData = {
      results,
      machineStopTime,
      machineRunTime,
      machineTime,
      inactivityTime,
      personalNeedsPercentage,
      fatiguePercentage,
      calculationCase,
      fatigueExplanationDetails,
      studyName: selectedStudy.name,
      timestamp: new Date().toLocaleString()
    };
    exportToPDF(exportData);
  };

  const handleExportExcel = () => {
    if (!results || !selectedStudy) return;
    const exportData = {
      results,
      machineStopTime,
      machineRunTime,
      machineTime,
      inactivityTime,
      personalNeedsPercentage,
      fatiguePercentage,
      calculationCase,
      fatigueExplanationDetails,
      studyName: selectedStudy.name,
      timestamp: new Date().toLocaleString()
    };
    exportToExcel(exportData);
  };

  return (
    <div className="p-4 bg-gray-50 min-h-screen" id="supplements-machine-section">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className='flex-1'>
              <h2 className="text-2xl font-bold text-gray-800">
                {t('machineSupplements.title')}
              </h2>
              <p className="mt-1 text-gray-600">
                {t('machineSupplements.description')}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border flex items-center gap-4">
            <div className="bg-red-100 p-3 rounded-full">
               <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('machineSupplements.machineStopTime')}</p>
              <p className="text-2xl font-bold text-gray-800">{machineStopTime.toFixed(2)}s</p>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border flex items-center gap-4">
            <div className="bg-blue-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('machineSupplements.machineRunTime')}</p>
              <p className="text-2xl font-bold text-gray-800">{machineRunTime.toFixed(2)}s</p>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border flex items-center gap-4">
            <div className="bg-green-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('machineSupplements.machineTime')}</p>
              <p className="text-2xl font-bold text-gray-800">{machineTime.toFixed(2)}s</p>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border flex items-center gap-4">
            <div className="bg-yellow-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('machineSupplements.inactivityTime')}</p>
              <p className="text-2xl font-bold text-gray-800">{inactivityTime.toFixed(2)}s</p>
            </div>
          </div>
        </div>

        <MachineFatigueCalculation
          results={results}
          machineStopTime={machineStopTime}
          machineRunTime={machineRunTime}
          machineTime={machineTime}
          inactivityTime={inactivityTime}
          personalNeedsPercentage={personalNeedsPercentage}
          fatiguePercentage={fatiguePercentage}
          calculationCase={calculationCase}
          fatigueExplanationDetails={fatigueExplanationDetails}
          onPrint={handlePrint}
          onExportPDF={handleExportPDF}
          onExportExcel={handleExportExcel}
        />
        
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('machineSupplements.dataTables')}</h3>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <h4 className="font-medium text-gray-700 mb-2">{t('machineSupplements.machineStopTimeElements')}</h4>
              <DataSupplementsTable elements={machineElements.stopped} stats={stoppedStats} />
            </div>
            <div className="lg:col-span-1">
              <h4 className="font-medium text-gray-700 mb-2">{t('machineSupplements.machineRunTimeElements')}</h4>
              <DataSupplementsTable elements={machineElements.running} stats={runningStats} />
            </div>
            <div className="lg:col-span-1">
              <h4 className="font-medium text-gray-700 mb-2">{t('machineSupplements.machineTimeElements')}</h4>
              <DataSupplementsTable elements={machineElements.machine} stats={machineTimeStats} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 