import { TimeUnit } from '..';

declare module 'dhtmlx-gantt' {
  interface GanttTask {
    id: string;
    text: string;
    start_date: Date;
    end_date: Date;
    duration: number;
    progress: number;
    timeUnit?: TimeUnit;
    [key: string]: any;
  }

  interface GanttConfig {
    xml_date: string;
    scale_unit: string;
    date_scale: string;
    duration_unit: string;
    duration_step: number;
    drag_links: boolean;
    drag_progress: boolean;
    drag_move: boolean;
    drag_resize: boolean;
    [key: string]: any;
  }

  interface GanttTemplates {
    tooltip_text: (start: Date, end: Date, task: GanttTask) => string;
    task_text: (start: Date, end: Date, task: GanttTask) => string;
    [key: string]: any;
  }

  interface Gantt {
    config: GanttConfig;
    templates: GanttTemplates;
    init: (container: HTMLElement) => void;
    parse: (data: { data: GanttTask[] }) => void;
    render: () => void;
    clearAll: () => void;
    getTask: (id: string) => GanttTask;
    updateTask: (id: string) => void;
    attachEvent: (event: string, callback: Function) => void;
    date: {
      date_to_str: (format: string) => (date: Date) => string;
    };
    [key: string]: any;
  }

  export const gantt: Gantt;
} 