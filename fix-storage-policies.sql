/*
=======================================================================
CORRECCIÓN DE POLÍTICAS RLS PARA STORAGE - LOGOS
=======================================================================
Este script corrige los problemas de permisos con los logos en el storage.
Ejecutar directamente en el SQL Editor de Supabase del proyecto Stadler.

PROBLEMA IDENTIFICADO:
- Políticas conflictivas en storage.objects
- Mezcla de criterios owner y name pattern
- Múltiples políticas DELETE causando conflictos

SOLUCIÓN:
- Políticas unificadas basadas únicamente en owner
- Eliminación de políticas específicas por bucket conflictivas
=======================================================================
*/

-- =====================================================
-- LIMPIAR POLÍTICAS EXISTENTES CONFLICTIVAS
-- =====================================================

-- Eliminar todas las políticas existentes en storage.objects
DROP POLICY IF EXISTS "Public bucket read access" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload to public" ON storage.objects;
DROP POLICY IF EXISTS "Public read access for logos" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload own logos" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own logos" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow public viewing" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete own files" ON storage.objects;
DROP POLICY IF EXISTS "Permitir lectura pública de logos" ON storage.objects;
DROP POLICY IF EXISTS "Permitir subida de logos a usuarios autenticados" ON storage.objects;
DROP POLICY IF EXISTS "Permitir eliminación de logos propios" ON storage.objects;
DROP POLICY IF EXISTS "User can delete their own objects" ON storage.objects;
DROP POLICY IF EXISTS "Public read access for all buckets" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload to any bucket" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own files" ON storage.objects;

-- =====================================================
-- CREAR POLÍTICAS UNIFICADAS Y FUNCIONALES
-- =====================================================

-- Política de lectura pública para todos los buckets
CREATE POLICY "Public read access for all buckets" 
ON storage.objects FOR SELECT 
TO public 
USING (true);

-- Política de subida para usuarios autenticados (sin restricciones de nombre)
CREATE POLICY "Authenticated users can upload to any bucket" 
ON storage.objects FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- Política de actualización para archivos propios
CREATE POLICY "Users can update their own files" 
ON storage.objects FOR UPDATE 
TO authenticated 
USING (owner = auth.uid());

-- Política de eliminación para archivos propios (UNIFICADA Y SIMPLE)
CREATE POLICY "Users can delete their own files" 
ON storage.objects FOR DELETE 
TO authenticated 
USING (owner = auth.uid());

-- =====================================================
-- VERIFICACIÓN DE POLÍTICAS
-- =====================================================

-- Mostrar las políticas activas para verificación
DO $$
DECLARE
    policy_record RECORD;
    policy_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== POLÍTICAS RLS ACTIVAS EN STORAGE.OBJECTS ===';
    
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname, cmd, roles, qual, with_check
        FROM pg_policies 
        WHERE schemaname = 'storage' AND tablename = 'objects'
        ORDER BY policyname
    LOOP
        policy_count := policy_count + 1;
        RAISE NOTICE '📋 %: % (%) - Roles: %', 
            policy_count, 
            policy_record.policyname, 
            policy_record.cmd, 
            policy_record.roles;
    END LOOP;
    
    IF policy_count = 0 THEN
        RAISE NOTICE '⚠️  No se encontraron políticas activas';
    ELSE
        RAISE NOTICE '✅ Total de políticas activas: %', policy_count;
    END IF;
    
    RAISE NOTICE '=== VERIFICACIÓN COMPLETADA ===';
END $$;

-- =====================================================
-- COMENTARIOS Y DOCUMENTACIÓN
-- =====================================================

COMMENT ON POLICY "Public read access for all buckets" ON storage.objects IS 
    'Permite lectura pública de todos los archivos en storage para visualización';

COMMENT ON POLICY "Authenticated users can upload to any bucket" ON storage.objects IS 
    'Permite a usuarios autenticados subir archivos a cualquier bucket';

COMMENT ON POLICY "Users can update their own files" ON storage.objects IS 
    'Permite a usuarios actualizar solo sus propios archivos basado en owner';

COMMENT ON POLICY "Users can delete their own files" ON storage.objects IS 
    'Permite a usuarios eliminar solo sus propios archivos basado en owner';

/*
=======================================================================
✅ CORRECCIÓN COMPLETADA - PARTE 1: POLÍTICAS RLS
=======================================================================

CAMBIOS REALIZADOS EN LA BASE DE DATOS:
✅ Eliminadas políticas conflictivas del storage
✅ Creadas políticas unificadas basadas en owner
✅ Simplificada la lógica de permisos
✅ Eliminadas restricciones de nombre de archivo

PRÓXIMO PASO REQUERIDO:
⚠️  TAMBIÉN DEBES APLICAR LA CORRECCIÓN EN EL CÓDIGO DE LA APLICACIÓN
⚠️  Ver archivo: fix-logo-upload-code.js

ARCHIVOS QUE NECESITAN CORRECCIÓN:
📁 src/components/LogoUpload.tsx - Eliminar validación manual duplicada

RESULTADO ESPERADO DESPUÉS DE AMBAS CORRECCIONES:
✅ Los usuarios podrán subir logos sin errores
✅ Los usuarios podrán eliminar sus propios logos
✅ Los logos serán públicamente visibles
✅ No más errores de "Permission denied"

PRUEBAS RECOMENDADAS:
1. Subir un nuevo logo desde la página de perfil
2. Eliminar el logo existente
3. Verificar que el logo se muestra correctamente

=======================================================================
*/ 