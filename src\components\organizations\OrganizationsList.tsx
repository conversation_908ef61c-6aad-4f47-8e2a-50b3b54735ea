import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';
import { LoadingState } from '../ui/skeleton';
import { OrganizationCard } from '../ui/enhanced-card';

export const OrganizationsList: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const {
    organizations,
    fetchOrganizations,
    isLoading,
    error,
    setCurrentOrganization,
  } = useOrganizationStore();

  useEffect(() => {
    try {
      fetchOrganizations();
    } catch (error) {
      console.error('Error al cargar organizaciones:', error);
    }
  }, [fetchOrganizations]);

  const handleCopyInviteCode = (inviteCode: string) => {
    navigator.clipboard.writeText(inviteCode);
    toast({
      title: t('organizations.inviteCodeCopied'),
      description: t('organizations.inviteCodeCopiedDescription'),
    });
  };

  if (error) {
    return (
      <div className="p-4">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <LoadingState 
        message={t('organizations.loading', { defaultValue: 'Loading your organizations...' })} 
        icon="🏢" 
        variant="organizations"
      />
    );
  }

  return (
    <div className="mobile-space-y">
      <h2 className="mobile-text-xl font-semibold">{t('organizations.myOrganizations')}</h2>

      {organizations.length === 0 ? (
        <div className="text-center mobile-padding bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg sm:rounded-xl border-2 border-dashed border-indigo-200">
          <div className="max-w-md mx-auto">
            {/* Ilustración con iconos */}
            <div className="relative mb-4 sm:mb-6">
              <div className="flex justify-center mobile-gap mb-3 sm:mb-4">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-indigo-100 rounded-full flex items-center justify-center">
                  <span className="text-2xl sm:text-3xl">🏢</span>
                </div>
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center self-end">
                  <span className="text-xl sm:text-2xl">👥</span>
                </div>
                <div className="w-11 h-11 sm:w-14 sm:h-14 bg-purple-100 rounded-full flex items-center justify-center self-center">
                  <span className="text-xl sm:text-2xl">📊</span>
                </div>
              </div>
              {/* Líneas conectoras */}
              <div className="absolute top-6 sm:top-8 left-1/2 transform -translate-x-1/2 w-24 sm:w-32 h-1 bg-gradient-to-r from-indigo-200 to-blue-200 rounded-full"></div>
            </div>
            
            {/* Texto principal */}
            <h3 className="mobile-text-lg font-bold text-indigo-900 mb-3">
              ¡Crea tu primera organización!
            </h3>
            <p className="mobile-text-sm text-indigo-700 mb-4 sm:mb-6 leading-relaxed">
              Las organizaciones te permiten colaborar con tu equipo, compartir estudios de tiempo y trabajar de forma más eficiente. ¡Es momento de empezar!
            </p>
            
            {/* Botones de acción */}
            <div className="mobile-flex-col mobile-gap justify-center">
              <Button 
                size="lg" 
                className="mobile-button-primary mobile-button-full shadow-lg mobile-hover-glow"
                onClick={() => {
                  // Activar el tab de crear organización
                  const createTab = document.querySelector('[value="create"]') as HTMLElement;
                  createTab?.click();
                }}
              >
                <span className="mr-2">🚀</span>
                Crear Organización
              </Button>
              <Button 
                variant="outline" 
                size="lg" 
                className="mobile-button-secondary mobile-button-full border-indigo-300 text-indigo-700 hover:bg-indigo-50"
                onClick={() => {
                  // Activar el tab de unirse
                  const joinTab = document.querySelector('[value="join"]') as HTMLElement;
                  joinTab?.click();
                }}
              >
                <span className="mr-2">🤝</span>
                Unirse a una Organización
              </Button>
            </div>
            
            {/* Beneficios */}
            <div className="mt-6 sm:mt-8 mobile-grid-3 mobile-gap text-center">
              <div className="p-2 sm:p-3">
                <span className="text-xl sm:text-2xl block mb-1 sm:mb-2">📈</span>
                <p className="mobile-text-xs text-indigo-600 font-medium">Mejora la productividad</p>
              </div>
              <div className="p-2 sm:p-3">
                <span className="text-xl sm:text-2xl block mb-1 sm:mb-2">🤝</span>
                <p className="mobile-text-xs text-indigo-600 font-medium">Colabora en equipo</p>
              </div>
              <div className="p-2 sm:p-3">
                <span className="text-xl sm:text-2xl block mb-1 sm:mb-2">📊</span>
                <p className="mobile-text-xs text-indigo-600 font-medium">Comparte estudios</p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="mobile-grid-3 mobile-gap-lg">
          {organizations.map((org) => (
            <OrganizationCard
              key={org.id}
              organization={org}
              onManage={() => setCurrentOrganization(org)}
              onCopyCode={handleCopyInviteCode}
              className="h-full mobile-animate-slide-up"
            />
          ))}
        </div>
      )}
    </div>
  );
};
