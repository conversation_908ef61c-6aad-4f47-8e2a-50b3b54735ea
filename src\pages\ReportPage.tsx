import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Header } from '../components/Header';
import { useStudyStore } from '../store/studyStore';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl'; // Import the hook
import { TimeUnit, ElementStats, ReportStats, ElementInstance } from '../types/index';
import { UserProfile } from '../types/profile'; // Corregida la ruta de importación
import { ReportTable } from '../components/report/ReportTable';
import { ReportResults } from '../components/report/ReportResults';
import { ReportActions } from '../components/report/ReportActions';
import { ReportCharts } from '../components/report/ReportCharts';
import { calculateElementStats, calculateReportStats } from '../utils/reportCalculations';
import { supabase } from '../lib/supabaseClient'; // Correct import path for Supabase
import { generatePDF } from '../utils/reportPDF';
import { useNavigate } from 'react-router-dom'; // Importar useNavigate

interface ReportPageProps {
  onBack: () => void;
}

export const ReportPage: React.FC<ReportPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['report', 'common']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const navigate = useNavigate(); // Obtener la función navigate
  useStudyFromUrl(); // Add this hook to handle study from URL

  const [timeUnit, setTimeUnit] = useState<TimeUnit>('seconds');
  const [shiftMinutes, setShiftMinutes] = useState(480);
  const [contingency, setContingency] = useState(0);
  const [elementStats, setElementStats] = useState<ElementStats[]>([]);
  const [reportStats, setReportStats] = useState<ReportStats | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [defaultValues, setDefaultValues] = useState<{timeUnit: TimeUnit, shiftMinutes: number, contingency: number} | null>(null);
  const { updateStudy } = useStudyStore();

  const handleStatsUpdate = (updatedStats: ReportStats) => {
    setReportStats(updatedStats);
  };

  // Funciones para manejar cambios y guardar configuraciones específicas del estudio
  const saveStudySettings = async (newSettings: {timeUnit?: TimeUnit, shiftMinutes?: number, contingency?: number}) => {
    if (!selectedStudy || !defaultValues) return;
    
    // Verificar si algún valor difiere de los valores por defecto del usuario
    const needsCustomSettings = 
      (newSettings.timeUnit && newSettings.timeUnit !== defaultValues.timeUnit) ||
      (newSettings.shiftMinutes && newSettings.shiftMinutes !== defaultValues.shiftMinutes) ||
      (newSettings.contingency !== undefined && newSettings.contingency !== defaultValues.contingency);
    
    console.log('💾 Verificando si guardar configuraciones específicas:', {
      newSettings,
      defaultValues,
      needsCustomSettings
    });

    const currentReportSettings = selectedStudy.optional_info?.reportSettings || {};
    
    let updatedReportSettings;
    if (needsCustomSettings) {
      // Guardar configuraciones específicas del estudio
      updatedReportSettings = {
        ...currentReportSettings,
        ...newSettings
      };
      console.log('✅ Guardando configuraciones específicas del estudio:', updatedReportSettings);
    } else {
      // Si todas las configuraciones coinciden con los valores por defecto, 
      // remover configuraciones específicas para este tipo
      updatedReportSettings = { ...currentReportSettings };
      if (newSettings.timeUnit) delete updatedReportSettings.timeUnit;
      if (newSettings.shiftMinutes) delete updatedReportSettings.shiftMinutes;
      if (newSettings.contingency !== undefined) delete updatedReportSettings.contingency;
      
      // Si no hay configuraciones específicas, remover toda la sección
      if (Object.keys(updatedReportSettings).length === 0) {
        updatedReportSettings = undefined;
      }
      console.log('🧹 Limpiando configuraciones que coinciden con valores por defecto');
    }

    try {
      const updatedOptionalInfo = {
        ...selectedStudy.optional_info,
        reportSettings: updatedReportSettings
      };

      await updateStudy(selectedStudy.id, {
        optional_info: updatedOptionalInfo
      });
      
      console.log('📝 Configuraciones del estudio guardadas exitosamente');
    } catch (error) {
      console.error('❌ Error guardando configuraciones del estudio:', error);
    }
  };

  const handleTimeUnitChange = (newTimeUnit: TimeUnit) => {
    setTimeUnit(newTimeUnit);
    saveStudySettings({ timeUnit: newTimeUnit });
  };

  const handleShiftMinutesChange = (newShiftMinutes: number) => {
    setShiftMinutes(newShiftMinutes);
    saveStudySettings({ shiftMinutes: newShiftMinutes });
  };

  const handleContingencyChange = (newContingency: number) => {
    setContingency(newContingency);
    saveStudySettings({ contingency: newContingency });
  };

  // CORREGIDO: Combinar la carga de perfil y configuraciones del estudio en un solo useEffect
  useEffect(() => {
    const fetchProfileAndApplySettings = async () => {
      if (!selectedStudy) return;

      const { data, error } = await supabase.auth.getUser();
      if (data?.user) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('default_time_unit, minutes_per_shift, default_contingency, points_per_hour')
          .eq('id', data.user.id)
          .single<UserProfile>(); // Tipar la respuesta con UserProfile

        if (profileError) {
          console.error('Error fetching profile data:', profileError);
          return;
        }

        if (profileData) { // profileData ahora está correctamente tipado
          const defaultTimeUnit = profileData.default_time_unit || 'seconds';
          const defaultShiftMinutes = profileData.minutes_per_shift || 480;
          const defaultContingency = profileData.default_contingency || 0;
          const pointsPerHour = profileData.points_per_hour ?? 100;
          // Guardar valores por defecto para comparar luego
          setDefaultValues({
            timeUnit: defaultTimeUnit,
            shiftMinutes: defaultShiftMinutes,
            contingency: defaultContingency
          });
          setUserProfile({ ...profileData, points_per_hour: pointsPerHour });

          console.log('🔧 Verificando configuraciones específicas del estudio...');
          
          // Verificar si hay configuraciones específicas guardadas para este estudio
          const studySettings = selectedStudy.optional_info?.reportSettings;
          
          if (studySettings) {
            console.log('✅ Encontradas configuraciones específicas del estudio:', studySettings);
            
            // Aplicar configuraciones específicas del estudio (prioridad sobre valores por defecto)
            setTimeUnit(studySettings.timeUnit || defaultTimeUnit);
            setShiftMinutes(studySettings.shiftMinutes || defaultShiftMinutes);
            setContingency(typeof studySettings.contingency === 'number' ? studySettings.contingency : defaultContingency);
          } else {
            console.log('ℹ️ No se encontraron configuraciones específicas del estudio, usando valores por defecto del usuario');
            
            // Aplicar valores por defecto del perfil del usuario
            setTimeUnit(defaultTimeUnit);
            setShiftMinutes(defaultShiftMinutes);
            setContingency(defaultContingency);
          }
        }
      } else if (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchProfileAndApplySettings();
  }, [selectedStudy]);

  useEffect(() => {
    console.log('🔄 useEffect ReportPage ejecutándose:', {
      hasSelectedStudy: !!selectedStudy,
      studyId: selectedStudy?.id,
      timeUnit,
      shiftMinutes,
      contingency
    });
    
    if (!selectedStudy) return;

    // Calculate statistics for each element
    const stats = selectedStudy.elements?.map((element: ElementInstance) =>
      calculateElementStats(
        element,
        selectedStudy.time_records?.[element.id] || [],
        selectedStudy.supplements?.[element.id],
        timeUnit,
        selectedStudy?.required_info?.activity_scale
      )
    ) || [];
    setElementStats(stats);

    console.log('📊 Antes de llamar calculateReportStats:', {
      statsLength: stats.length,
      studyHasSupplements: !!selectedStudy.supplements,
      machineDataExists: !!selectedStudy.supplements?.['__machine_cycle_data__']
    });

    // Calculate overall report statistics
    const overallStats = calculateReportStats(
      stats,
      selectedStudy?.required_info?.activity_scale,
      shiftMinutes,
      contingency,
      timeUnit,
      userProfile?.points_per_hour || 100,
      selectedStudy
    );
    
    console.log('✅ calculateReportStats completado:', {
      hasOverallStats: !!overallStats,
      cycleDiscrepancy: overallStats?.cycleDiscrepancy
    });
    
    setReportStats(overallStats);
  }, [selectedStudy, timeUnit, shiftMinutes, contingency]);

  const handleGoToGantt = () => {
    if (elementStats && elementStats.length > 0 && selectedStudy) {
      const ganttTasks = elementStats
        .filter(el => !el.concurrent_machine_time) // Excluir tiempos de máquina concurrentes del Gantt
        .map(el => ({
          id: el.id,
          name: el.description,
          // TODO: Implementar una lógica de secuenciación real para start_date
          // Por ahora, todas las tareas empiezan "ahora" para el ejemplo.
          // Una librería de Gantt podría necesitar un objeto Date.
          start_date: new Date().toISOString(),
          duration: el.finalTime, // finalTime ya está en la el.timeUnit correcta
          progress: 100, // Asumimos completado para el ejemplo, o podría ser 0
          timeUnit: el.timeUnit,
        }));

      // Debug: Comparar elementStats originales con los datos formateados para Gantt
      console.log('Original elementStats:', elementStats);
      console.log('Datos formateados para Gantt:', ganttTasks);

      if (ganttTasks.length === 0) {
        // Esto puede pasar si todos los elementos son concurrent_machine_time
        alert(t('noTasksForGantt', { ns: 'report', defaultValue: 'No hay tareas válidas para mostrar en el diagrama de Gantt después de filtrar.' }));
        return;
      }

      // Navegar a la página de Gantt con el ID del estudio
      // Usamos tanto state como parámetros de URL para mayor confiabilidad
      navigate(`/gantt?studyId=${selectedStudy.id}`, {
        state: {
          tasks: ganttTasks,
          studyName: selectedStudy?.required_info?.name || '',
          studyTimeUnit: timeUnit, // La unidad de tiempo general del informe
          studyId: selectedStudy.id // Agregar el ID del estudio
        }
      });
    } else {
      alert(t('noDataForGantt', { ns: 'report', defaultValue: 'No hay datos de estudio o elementos calculados para generar el diagrama de Gantt.' }));
    }
  };

  const handleGeneratePDF = async () => {
    if (!reportStats || !selectedStudy) return;

    try {
      await generatePDF(
        selectedStudy,
        elementStats,
        reportStats,
        timeUnit,
        shiftMinutes,
        contingency,
        t,
        undefined,
        undefined,
        userProfile?.points_per_hour ?? 100
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="mt-4">{t('noStudySelected', { ns: 'report' })}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('title', { ns: 'report' })}
        subtitle={selectedStudy?.required_info?.name || ''}
        showSearch={false}
        showActions={true}
        onBack={onBack}
      />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Configuration */}
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('timeUnit', { ns: 'report' })}
                </label>
                <select
                  value={timeUnit}
                  onChange={(e) => handleTimeUnitChange(e.target.value as TimeUnit)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                >
                  <option value="seconds">{t('units.seconds', { ns: 'report' })}</option>
                  <option value="minutes">{t('units.minutes', { ns: 'report' })}</option>
                  <option value="mmm">{t('units.mmm', { ns: 'report' })}</option>
                  <option value="cmm">{t('units.cmm', { ns: 'report' })}</option>
                  <option value="tmu">{t('units.tmu', { ns: 'report' })}</option>
                  <option value="dmh">{t('units.dmh', { ns: 'report' })}</option>
                </select>
              </div>
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('shiftMinutes', { ns: 'report' })}
                </label>
                <input
                  type="number"
                  value={shiftMinutes}
                  onChange={(e) => handleShiftMinutesChange(parseInt(e.target.value))}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('contingency', { ns: 'report' })} (%)
                </label>
                <input
                  type="number"
                  value={contingency}
                  onChange={(e) => handleContingencyChange(parseInt(e.target.value))}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
            </div>
          </div>

          {/* Elements Table */}
          <ReportTable
            elements={elementStats}
            timeUnit={timeUnit}
          />

          {/* Results */}
          {reportStats && (
            <ReportResults
              stats={reportStats}
              timeUnit={timeUnit}
              shiftMinutes={shiftMinutes}
              contingency={contingency}
              pointsPerHour={userProfile?.points_per_hour ?? 100}
              onStatsUpdate={handleStatsUpdate}
            />
          )}

          {/* Actions */}
          <ReportActions
            study={selectedStudy}
            elementStats={elementStats}
            reportStats={reportStats}
            timeUnit={timeUnit}
            shiftMinutes={shiftMinutes}
            contingency={contingency}
            pointsPerHour={userProfile?.points_per_hour ?? 100}
          />

          {/* Charts */}
          {reportStats && elementStats.length > 0 && (
            <ReportCharts
              stats={reportStats}
              elements={elementStats}
              timeUnit={timeUnit}
            />
          )}

          {/* Botón para Diagrama de Gantt - Temporalmente oculto */}
          {/*
          <div className="mt-4 flex justify-center">
            <button
              onClick={handleGoToGantt}
              className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              {t('viewGanttChart', { ns: 'report' })}
            </button>
          </div>
          */}
        </div>
      </main>
    </div>
  );
};