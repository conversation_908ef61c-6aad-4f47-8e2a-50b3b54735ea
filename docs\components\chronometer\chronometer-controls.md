# Chronometer Controls Component

## Descripción
Controles principales para el cronómetro de elementos repetitivos.

## Props
```typescript
interface ChronometerControlsProps {
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  activity: number;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onLap: () => void;
  onActivityChange: (increment: boolean) => void;
}
```

## Características
- Botones de control principales
  - Start/Pause
  - Stop
  - Lap (siguiente elemento)
- Control de actividad (+/-)
- Display de tiempo actual
- Display de actividad actual

## Uso
```tsx
<ChronometerControls
  isRunning={isRunning}
  isPaused={isPaused}
  time={time}
  activity={activity}
  onStart={handleStart}
  onPause={handlePause}
  onStop={handleStop}
  onLap={handleLap}
  onActivityChange={handleActivityChange}
/>
```