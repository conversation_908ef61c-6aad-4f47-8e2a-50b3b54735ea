import React from 'react';
import { useTranslation } from 'react-i18next';
import { AlertTriangle, Info, Calculator } from 'lucide-react';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';

interface SupplementsDataAlertProps {
  hasData: boolean;
  hasIncompleteData: boolean;
  onNavigateToSupplements?: () => void;
}

export const SupplementsDataAlert: React.FC<SupplementsDataAlertProps> = ({
  hasData,
  hasIncompleteData,
  onNavigateToSupplements
}) => {
  const { t } = useTranslation(['report']);

  // Solo mostrar alerta si hay datos incompletos (con timestamp pero totalCycleTime = 0)
  // NO mostrar alerta si no hay datos de suplementos ejecutados
  if (hasData && !hasIncompleteData) {
    return null; // No need to show alert if data is complete
  }
  
  if (!hasData && !hasIncompleteData) {
    return null; // No show alert if no supplements data exists at all
  }

  const alertType = hasIncompleteData ? 'warning' : 'info';
  const bgColor = alertType === 'warning' ? 'bg-orange-50' : 'bg-blue-50';
  const borderColor = alertType === 'warning' ? 'border-orange-200' : 'border-blue-200';
  const textColor = alertType === 'warning' ? 'text-orange-800' : 'text-blue-800';
  const iconColor = alertType === 'warning' ? 'text-orange-600' : 'text-blue-600';
  const buttonColor = alertType === 'warning' ? 'bg-orange-600 hover:bg-orange-700' : 'bg-blue-600 hover:bg-blue-700';

  const icon = hasIncompleteData ? AlertTriangle : Info;
  const IconComponent = icon;

  const title = hasIncompleteData 
    ? t('supplementsAlert.incompleteDataTitle', { defaultValue: 'Datos de Suplementos Incompletos' })
    : t('supplementsAlert.noDataTitle', { defaultValue: 'Cálculo de Suplementos No Ejecutado' });

  const message = hasIncompleteData
    ? t('supplementsAlert.incompleteDataMessage', { 
        defaultValue: 'Se encontraron datos de suplementos pero el cálculo parece estar incompleto. Para detectar discordancias entre el ciclo normal del reporte y el cálculo de fatiga, necesita completar el cálculo de suplementos de máquina.' 
      })
    : t('supplementsAlert.noDataMessage', { 
        defaultValue: 'No se encontraron datos del cálculo de suplementos de máquina. Para detectar discordancias entre el ciclo normal del reporte y el cálculo de fatiga, debe ejecutar primero el cálculo de suplementos de máquina.' 
      });

  const buttonText = t('supplementsAlert.buttonText', { defaultValue: 'Ir a Suplementos de Máquina' });

  const tooltipContent = hasIncompleteData
    ? t('supplementsAlert.incompleteTooltip', { 
        defaultValue: 'Los datos de suplementos existen pero el tiempo total del ciclo es 0. Esto puede ocurrir si el cálculo se interrumpió o si no hay registros de tiempo válidos.' 
      })
    : t('supplementsAlert.noDataTooltip', { 
        defaultValue: 'La detección de discordancias requiere que se haya ejecutado el cálculo de suplementos de máquina al menos una vez.' 
      });

  return (
    <div className={`${bgColor} border ${borderColor} rounded-lg p-4 mb-4`}>
      <div className="flex items-start gap-3">
        <IconComponent className={`h-5 w-5 ${iconColor} mt-0.5 flex-shrink-0`} />
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className={`text-sm font-medium ${textColor}`}>
              {title}
            </h3>
            <Tippy content={
              <div className="p-2 max-w-[300px] text-sm">
                {tooltipContent}
              </div>
            }>
              <div>
                <Info className={`h-4 w-4 ${iconColor} cursor-help`} />
              </div>
            </Tippy>
          </div>
          
          <p className={`text-sm ${textColor} mb-3`}>
            {message}
          </p>

          {onNavigateToSupplements && (
            <button
              onClick={onNavigateToSupplements}
              className={`inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-white ${buttonColor} rounded-md transition-colors duration-200`}
            >
              <Calculator className="h-4 w-4" />
              {buttonText}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}; 