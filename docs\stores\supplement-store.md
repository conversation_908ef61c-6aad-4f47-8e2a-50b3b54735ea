# Supplement Store

## Descripción
Gestiona los suplementos y sus cálculos.

## Estado
```typescript
interface SupplementState {
  isLoading: boolean;
  error: string | null;
  saveSupplement: (elementId: string, supplement: Supplement) => Promise<void>;
  copySupplements: (fromElementId: string, toElementIds: string[]) => Promise<void>;
  resetSupplement: (elementId: string) => Promise<void>;
}
```

## Funcionalidades

### 1. Gestión de Suplementos
- Guardado de configuración
- Copia entre elementos
- Reseteo de valores
- Cálculo de porcentajes

### 2. Cálculos
- Conversión puntos a porcentaje
- Validación de rangos
- Acumulación de factores
- Ajustes por tipo

### 3. Persistencia
- Almacenamiento en Supabase
- Validación de datos
- Sincronización automática

## Uso
```typescript
const { 
  saveSupplement, 
  copySupplements 
} = useSupplementStore();

// Guardar suplemento
await saveSupplement(elementId, {
  points: { A1: 5, B2: 3 },
  percentage: 8,
  isForced: false
});

// Copiar suplementos
await copySupplements(sourceId, [targetId1, targetId2]);
```