import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Download, FileText, FileSpreadsheet, Calculator, Clock, Settings, Info } from 'lucide-react';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';

interface CalculationResults {
  totalCycleTime: number;
  baseTime: number;
  personalNeedsSupplement: number;
  fatigueSupplement: number;
  remainingFatigue?: number;
  explanation: {
    supplementsExplanation: string;
    baseTimeCalculation: string;
    personalNeedsCalculation: string;
    fatigueCalculation: string;
    totalTimeCalculation: string;
    remainingFatigueCalculation?: string;
  };
}

interface MachineFatigueCalculationProps {
  results: CalculationResults | null;
  machineStopTime: number;
  machineRunTime: number;
  machineTime: number;
  inactivityTime: number;
  personalNeedsPercentage: number;
  fatiguePercentage: number;
  calculationCase: string;
  fatigueExplanationDetails?: string;
  onPrint: () => void;
  onExportPDF: () => void;
  onExportExcel: () => void;
}

export const MachineFatigueCalculation: React.FC<MachineFatigueCalculationProps> = ({
  results,
  machineStopTime,
  machineRunTime,
  machineTime,
  inactivityTime,
  personalNeedsPercentage,
  fatiguePercentage,
  calculationCase,
  fatigueExplanationDetails,
  onPrint,
  onExportPDF,
  onExportExcel
}) => {
  const { t } = useTranslation(['supplements']);
  const [activeTab, setActiveTab] = useState<'summary' | 'details' | 'parameters'>('summary');

  const getCaseInfo = () => {
    switch (calculationCase) {
      case 'case1':
        return {
          title: t('machineSupplements.caseExplanations.case1'),
          description: 'Ambos suplementos se aplican fuera del ciclo',
          color: 'bg-red-50 border-red-200 text-red-800',
          icon: '🔴'
        };
      case 'case2':
        return {
          title: t('machineSupplements.caseExplanations.case2'),
          description: 'Fatiga parcialmente dentro del ciclo',
          color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          icon: '🟡'
        };
      case 'case3':
        return {
          title: t('machineSupplements.caseExplanations.case3'),
          description: 'Solo necesidades personales fuera del ciclo',
          color: 'bg-blue-50 border-blue-200 text-blue-800',
          icon: '🔵'
        };
      case 'case4':
        return {
          title: t('machineSupplements.caseExplanations.case4'),
          description: 'Ambos suplementos dentro del ciclo',
          color: 'bg-green-50 border-green-200 text-green-800',
          icon: '🟢'
        };
      default:
        return {
          title: 'Caso no determinado',
          description: '',
          color: 'bg-gray-50 border-gray-200 text-gray-800',
          icon: '⚪'
        };
    }
  };

  const caseInfo = getCaseInfo();

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Calculator className="h-6 w-6 text-blue-600" />
              {t('machineSupplements.fatigueCalculation')}
            </h2>
            <p className="text-gray-600 mt-1">
              Cálculo detallado del ciclo normal considerando tiempos de espera de máquina
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            <button
              type="button"
              onClick={onPrint}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FileText className="h-4 w-4" />
              Imprimir
            </button>
            <button
              type="button"
              onClick={onExportPDF}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              PDF
            </button>
            <button
              type="button"
              onClick={onExportExcel}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <FileSpreadsheet className="h-4 w-4" />
              Excel
            </button>
          </div>
        </div>
      </div>

      {/* Case Information Card */}
      <div className={`rounded-lg border-2 p-6 ${caseInfo.color}`}>
        <div className="flex items-start gap-4">
          <div className="text-3xl">{caseInfo.icon}</div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-2">
              Caso Aplicado: {caseInfo.title}
            </h3>
            <p className="text-sm opacity-90 mb-3">{caseInfo.description}</p>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">Tiempo de inactividad:</span>
                <div className="font-mono">{inactivityTime.toFixed(2)}s</div>
              </div>
              <div>
                <span className="font-medium">Necesidades personales:</span>
                <div className="font-mono">{personalNeedsPercentage}%</div>
              </div>
              <div>
                <span className="font-medium">Fatiga:</span>
                <div className="font-mono">{fatiguePercentage}%</div>
              </div>
              <div>
                <span className="font-medium">Ciclo total:</span>
                <div className="font-mono font-bold text-lg">
                  {results?.totalCycleTime.toFixed(2)}s
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {[
              { id: 'summary', label: 'Resumen', icon: Calculator },
              { id: 'details', label: 'Detalles', icon: Info },
              { id: 'parameters', label: 'Parámetros', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id as any)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'summary' && (
            <SummaryTab results={results} />
          )}
          {activeTab === 'details' && (
            <DetailsTab results={results} fatigueExplanationDetails={fatigueExplanationDetails} />
          )}
                        {activeTab === 'parameters' && (
                <ParametersTab
                  machineStopTime={machineStopTime}
                  machineRunTime={machineRunTime}
                  machineTime={machineTime}
                  inactivityTime={inactivityTime}
                  personalNeedsPercentage={personalNeedsPercentage}
                  fatiguePercentage={fatiguePercentage}
                  results={results}
                />
              )}
        </div>
      </div>
    </div>
  );
};

// Summary Tab Component
const SummaryTab: React.FC<{ results: CalculationResults | null }> = ({ results }) => {
  const { t } = useTranslation(['supplements']);

  if (!results) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No hay resultados de cálculo disponibles</p>
      </div>
    );
  }

  const summaryItems = [
    {
      label: t('machineSupplements.baseTime'),
      value: results.baseTime,
      description: 'Tiempo base del ciclo (máquina parada + máximo entre tiempo de máquina y tiempo en marcha)',
      color: 'bg-blue-50 border-blue-200'
    },
    {
      label: t('machineSupplements.personalNeedsSeparated'),
      value: results.personalNeedsSupplement,
      description: t('machineSupplements.personalNeedsDescription'),
      color: 'bg-green-50 border-green-200'
    },
    {
      label: t('machineSupplements.fatigueSeparated'),
      value: results.fatigueSupplement,
      description: t('machineSupplements.fatigueDescription'),
      color: 'bg-yellow-50 border-yellow-200'
    }
  ];

  if (results.remainingFatigue !== undefined) {
    summaryItems.push({
      label: t('machineSupplements.remainingFatigue'),
      value: results.remainingFatigue,
      description: 'Fatiga restante que se aplica fuera del ciclo',
      color: 'bg-orange-50 border-orange-200'
    });
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {summaryItems.map((item, index) => (
          <div key={index} className={`rounded-lg border-2 p-4 ${item.color}`}>
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-900">{item.label}</h4>
              <Tippy content={item.description}>
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
              </Tippy>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {item.value.toFixed(2)}
              <span className="text-sm font-normal text-gray-600 ml-1">seg</span>
            </div>
          </div>
        ))}
      </div>

      {/* Final Result */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold mb-1">Ciclo Normal Total</h3>
            <p className="text-blue-100 text-sm">
              Resultado final considerando tiempos de espera de máquina
            </p>
          </div>
          <div className="text-right">
            <div className="text-4xl font-bold">
              {results.totalCycleTime.toFixed(2)}
            </div>
            <div className="text-blue-100">segundos</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Details Tab Component
const DetailsTab: React.FC<{
  results: CalculationResults | null;
  fatigueExplanationDetails?: string;
}> = ({ results, fatigueExplanationDetails }) => {
  const { t } = useTranslation(['supplements']);

  if (!results) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Info className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No hay detalles de cálculo disponibles</p>
      </div>
    );
  }

  const explanationSections = [
    {
      title: 'Distribución de Suplementos',
      content: results.explanation.supplementsExplanation,
      icon: '📊',
      color: 'bg-blue-50 border-blue-200'
    },
    {
      title: 'Cálculo del Tiempo Base',
      content: results.explanation.baseTimeCalculation,
      icon: '⏱️',
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Necesidades Personales',
      content: results.explanation.personalNeedsCalculation,
      icon: '👤',
      color: 'bg-purple-50 border-purple-200'
    },
    {
      title: 'Cálculo de Fatiga',
      content: results.explanation.fatigueCalculation,
      icon: '💪',
      color: 'bg-orange-50 border-orange-200'
    },
    {
      title: 'Tiempo Total del Ciclo',
      content: results.explanation.totalTimeCalculation,
      icon: '🔄',
      color: 'bg-indigo-50 border-indigo-200'
    }
  ];

  if (results.explanation.remainingFatigueCalculation) {
    explanationSections.push({
      title: 'Fatiga Restante',
      content: results.explanation.remainingFatigueCalculation,
      icon: '⚠️',
      color: 'bg-red-50 border-red-200'
    });
  }



  return (
    <div className="space-y-6">
      {/* Main calculation sections */}
      {explanationSections.map((section, index) => (
        <div key={index} className={`rounded-lg p-6 border-2 ${section.color}`}>
          <div className="flex items-center gap-3 mb-4">
            <span className="text-2xl">{section.icon}</span>
            <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
          </div>
          <div className="prose prose-sm max-w-none">
            <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono bg-white p-4 rounded border leading-relaxed">
              {section.content}
            </pre>
          </div>
        </div>
      ))}

      {/* Fatigue detailed explanation - Complete */}
      {fatigueExplanationDetails && (
        <>
          <div className="border-t-2 border-gray-200 pt-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <span className="text-2xl">🧮</span>
              Cálculo Completo de Fatiga Ponderada
            </h2>
            <p className="text-gray-600 mb-4">
              Análisis detallado paso a paso del cálculo de fatiga considerando todos los elementos del estudio
            </p>
          </div>

          <div className="bg-gradient-to-br from-yellow-50 to-orange-50 border-2 border-yellow-300 rounded-lg p-6">
            <div className="prose prose-sm max-w-none">
              <div className="bg-white p-6 rounded-lg border shadow-sm">
                <div
                  className="text-sm text-gray-700 leading-relaxed overflow-x-auto"
                  style={{
                    fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                    whiteSpace: 'pre-wrap',
                    lineHeight: '1.6'
                  }}
                  dangerouslySetInnerHTML={{
                    __html: fatigueExplanationDetails
                      .replace(/<hr>/g, '<div style="border-top: 2px solid #e5e7eb; margin: 16px 0;"></div>')
                      .replace(/▸/g, '<span style="color: #3b82f6; font-weight: 600;">▸</span>')
                      .replace(/(\d+\.\s)/g, '<span style="color: #059669; font-weight: 600;">$1</span>')
                      .replace(/(Elemento\s"[^"]+"):/g, '<span style="color: #dc2626; font-weight: 600;">$1:</span>')
                      .replace(/(Cálculo de fatiga ponderada|Datos de entrada por elemento|Normalización de tiempos|Cálculo del tiempo total|Cálculo de pesos|Cálculo del suplemento ponderado|Ajuste final|Información teórica)/gi,
                        '<span style="color: #7c3aed; font-weight: 700; font-size: 1.1em;">$1</span>')
                  }}
                />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// Parameters Tab Component
const ParametersTab: React.FC<{
  machineStopTime: number;
  machineRunTime: number;
  machineTime: number;
  inactivityTime: number;
  personalNeedsPercentage: number;
  fatiguePercentage: number;
  results: CalculationResults | null;
}> = ({
  machineStopTime,
  machineRunTime,
  machineTime,
  inactivityTime,
  personalNeedsPercentage,
  fatiguePercentage,
  results
}) => {
  const { t } = useTranslation(['supplements']);

  const timeParameters = [
    {
      label: 'Tiempo Máquina Parada',
      value: machineStopTime,
      unit: 'segundos',
      description: 'Tiempo total de elementos de máquina parada',
      color: 'bg-red-50 border-red-200'
    },
    {
      label: 'Tiempo Máquina en Marcha',
      value: machineRunTime,
      unit: 'segundos',
      description: 'Tiempo total de elementos de máquina en marcha',
      color: 'bg-green-50 border-green-200'
    },
    {
      label: 'Tiempo de Máquina',
      value: machineTime,
      unit: 'segundos',
      description: 'Tiempo total de elementos de tiempo de máquina',
      color: 'bg-blue-50 border-blue-200'
    },
    {
      label: 'Tiempo de Inactividad',
      value: inactivityTime,
      unit: 'segundos',
      description: 'Diferencia entre tiempo de máquina y tiempo en marcha',
      color: 'bg-yellow-50 border-yellow-200'
    }
  ];

  const supplementParameters = [
    {
      label: 'Necesidades Personales',
      value: personalNeedsPercentage,
      unit: '%',
      description: 'Porcentaje aplicado para necesidades personales',
      color: 'bg-purple-50 border-purple-200'
    },
    {
      label: 'Fatiga',
      value: fatiguePercentage,
      unit: '%',
      description: 'Porcentaje calculado de fatiga ponderada',
      color: 'bg-orange-50 border-orange-200'
    }
  ];

  const calculatedValues = [
    {
      label: 'Tiempo Base',
      value: machineStopTime + Math.max(machineTime, machineRunTime),
      unit: 'segundos',
      description: 'Tiempo máquina parada + máximo(tiempo máquina, tiempo en marcha)',
      formula: `${machineStopTime.toFixed(2)} + max(${machineTime.toFixed(2)}, ${machineRunTime.toFixed(2)}) = ${(machineStopTime + Math.max(machineTime, machineRunTime)).toFixed(2)}`
    },
    {
      label: 'Suplemento NP (segundos)',
      value: (machineStopTime + Math.max(machineTime, machineRunTime)) * (personalNeedsPercentage / 100),
      unit: 'segundos',
      description: 'Tiempo base × porcentaje de necesidades personales',
      formula: `${(machineStopTime + Math.max(machineTime, machineRunTime)).toFixed(2)} × ${personalNeedsPercentage}% = ${((machineStopTime + Math.max(machineTime, machineRunTime)) * (personalNeedsPercentage / 100)).toFixed(2)}`
    },
    {
      label: 'Suplemento Fatiga (segundos)',
      value: results?.fatigueSupplement || 0,
      unit: 'segundos',
      description: 'Fatiga calculada sobre elementos de trabajo activos (ver resumen para valor exacto)',
      formula: `Valor exacto mostrado en pestaña Resumen: ${results?.fatigueSupplement?.toFixed(2) || '0.00'} seg`
    }
  ];

  return (
    <div className="space-y-8">
      {/* Time Parameters */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Clock className="h-5 w-5 text-blue-600" />
          Parámetros de Tiempo
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {timeParameters.map((param, index) => (
            <div key={index} className={`rounded-lg border-2 p-4 ${param.color}`}>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900 text-sm">{param.label}</h4>
                <Tippy content={param.description}>
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                </Tippy>
              </div>
              <div className="text-xl font-bold text-gray-900">
                {param.value.toFixed(2)}
                <span className="text-sm font-normal text-gray-600 ml-1">{param.unit}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Supplement Parameters */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Settings className="h-5 w-5 text-green-600" />
          Parámetros de Suplementos
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {supplementParameters.map((param, index) => (
            <div key={index} className={`rounded-lg border-2 p-4 ${param.color}`}>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{param.label}</h4>
                <Tippy content={param.description}>
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                </Tippy>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {param.value.toFixed(2)}
                <span className="text-sm font-normal text-gray-600 ml-1">{param.unit}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Calculated Values */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Calculator className="h-5 w-5 text-purple-600" />
          Valores Calculados
        </h3>
        <div className="space-y-4">
          {calculatedValues.map((calc, index) => (
            <div key={index} className="bg-white rounded-lg border-2 border-gray-200 p-6">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">{calc.label}</h4>
                  <p className="text-sm text-gray-600">{calc.description}</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {calc.value.toFixed(2)}
                    <span className="text-sm font-normal text-gray-600 ml-1">{calc.unit}</span>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 rounded p-3 font-mono text-sm text-gray-700">
                {calc.formula}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};