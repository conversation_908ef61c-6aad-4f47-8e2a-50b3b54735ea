export default {
  title: 'Study',
  new: 'New study',
  info: 'Information',
  method: 'Method',
  chronometer: 'Repetitive',
  machineTimes: 'Machine times',
  frequencyTimes: 'Frequency times',
  supplements: 'Supplements',
  report: 'Report',
  edit: 'Edit study',
  create: 'Create Study',
  update: 'Update Study',
  name: 'Name',
  namePlaceholder: 'Enter study name',
  description: 'Description',
  date: 'Date',
  analyst: 'Analyst',
  company: 'Company',
  companyPlaceholder: 'Enter company name',
  studyNumber: 'Study number',
  studyNumberPlaceholder: 'Enter study number',
  department: 'Department',
  operation: 'Operation',
  part: 'Part',
  machine: 'Machine',
  machinePlaceholder: 'Enter machine details',
  shiftMinutes: 'Minutes per Shift',
  contingency: 'Contingency',
  observations: 'Observations',
  saveSuccess: 'Study saved successfully',
  saveError: 'Error saving study',
  delete: 'Delete study',
  deleteSuccess: 'Study deleted successfully',
  deleteError: 'Error deleting study',
  confirmDelete: 'Are you sure you want to delete this study?',
  noStudies: 'No studies available',
  loadError: 'Error loading studies',
  operator: 'Operator',
  operatorPlaceholder: 'Enter operator name',
  section: 'Section',
  sectionPlaceholder: 'Enter section',
  reference: 'Reference',
  referencePlaceholder: 'Enter reference',
  tools: 'Tools',
  toolsPlaceholder: 'Enter tools used',
  technician: 'Technician',
  technicianPlaceholder: 'Enter technician name',
  normalActivity: 'Normal activity',
  optimalActivity: 'Optimal activity',
  credits: 'Available credits',
  repetitive: 'Repetitive',
  no_credits: {
    title: "No Credits Available",
    message: "You don't have enough credits to create a new study. Please purchase more credits to continue."
  },
  creditCheckError: 'Error checking credits',
  export: 'Export file',
  import: 'Import',
  exportStudy: 'Export study',
  importStudy: 'Import study',
  exportError: 'Error exporting study',
  importError: 'Error importing study',
  importSuccess: '{{count}} study successfully imported',
  importSuccess_plural: '{{count}} studies successfully imported',
  exportAllStudies: 'General Backup',
  permission_error: {
    title: 'Permission denied',
    message: 'You do not have permission to modify this study because you are not the owner. Only the owner can make changes to the study.'
  },
  changes_not_saved: 'The changes you are trying to make will not be saved because you are not the owner of the study',
  autoSharedWithOrganization: 'The study has been automatically shared with your organization',
  createError: 'Error creating study',
  createErrorDescription: 'An error occurred while trying to create the study. Please try again.',
  credit_limit_reached: {
    title: 'Credit limit reached',
    message: 'You do not have enough credits to create a new study. Please contact the administrator to get more credits.',
    text: 'No credits available'
  },
  no_credits_modal: {
    title: 'No credits available',
    message: 'You do not have credits available to create a new study. Please contact the administrator to get more credits.'
  },
  customUnits: {
    title: 'Report Measurement Units',
    description: 'Select the additional measurement units you want to use in reports. You will be able to see values per cycle and per selected units.',
    bySquareMeter: 'By Square Meter',
    byLinearMeter: 'By Linear Meter',
    byCubicMeter: 'By Cubic Meter',
    byKilograms: 'By Kilograms',
    byPerimeter: 'By Perimeter',
    configuration: {
      squareMeter: 'Square Meter Configuration',
      linearMeter: 'Linear Meter Configuration',
      cubicMeter: 'Cubic Meter Configuration',
      kilograms: 'Kilograms Configuration',
      perimeter: 'Perimeter Configuration',
      length: 'Length (meters)',
      width: 'Width (meters)',
      height: 'Height (meters)',
      weight: 'Weight (kilograms)',
      total: 'Total'
    }
  },
  includeInLibrary: 'Include in Library',
  movingStudy: 'Moving study',
  ownedStudies: 'My studies',
  sharedStudies: 'Shared with me',
  consolidation: {
    title: 'Consolidate Studies',
    folder: 'Folder:',
    studyFound: 'study found',
    studiesFound: 'studies found',
    includesSubfolders: '(includes subfolders)',
    totalElements: 'total elements',
    timeRecords: 'time records',
    elementsWithSupplements: 'elements with supplements',
    consolidatedName: 'Consolidated study name',
    namePlaceholder: 'Ex: Complete Process - Consolidated',
    loadingStudies: 'Loading studies...',
    noStudiesFound: 'No studies found in this folder',
    consolidationOrder: 'Consolidation order:',
    orderInstructions: 'Use arrows to change the order in which elements will be added',
    noName: 'No name',
    elements: 'elements',
    noCompany: 'No company',
    times: 'times',
    withSupplements: 'with supplements',
    noDate: 'No date',
    type: 'Type:',
    frequency: 'Frequency:',
    supplements: 'Supplements:',
    creating: 'Creating...',
    createConsolidated: 'Create Consolidated Study',
    nameRequired: 'Please enter a name for the consolidated study',
    successMessage: 'Consolidated study "{name}" created successfully with:',
    elementsCount: '• {count} elements',
    timeRecordsCount: '• {count} time records',
    supplementsCount: '• {count} elements with supplements',
    errorMessage: 'Error creating consolidated study: {error}'
  }
} as const;
