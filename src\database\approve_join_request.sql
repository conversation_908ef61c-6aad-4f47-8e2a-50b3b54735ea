-- Función para aprobar una solicitud de unión
CREATE OR REPLACE FUNCTION public.approve_join_request(p_request_id UUID, p_processor_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_count INTEGER;
  v_request RECORD;
BEGIN
  -- Obtener información de la solicitud
  SELECT * INTO v_request
  FROM public.organization_join_requests
  WHERE id = p_request_id;
  
  -- Verificar si la solicitud existe
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Insertar el nuevo miembro en la organización
  INSERT INTO public.organization_members
    (organization_id, user_id, user_email, role)
  VALUES
    (v_request.organization_id, v_request.user_id, v_request.user_email, 'member');
  
  -- Actualizar el estado de la solicitud
  UPDATE public.organization_join_requests
  SET 
    status = 'approved',
    processed_at = NOW(),
    processed_by = p_processor_id
  WHERE id = p_request_id;
  
  -- Verificar si la actualización fue exitosa
  GET DIAGNOSTICS v_count = ROW_COUNT;
  
  -- Devolver true si se actualizó al menos una fila
  RETURN v_count > 0;
END;
$$;
