# Página de Biblioteca

## Descripción
Componente para la gestión de elementos reutilizables.

## Estructura
```
LibraryPage
├── LibraryElementList
├── LibraryElementForm
└── DeleteConfirmModal
```

## Flujo de Datos
1. Carga de elementos
2. Creación/Edición
3. Compartición
4. Eliminación
5. Reutilización

## Interacciones
- Gestión CRUD de elementos
- Control de compartición
- Búsqueda y filtrado
- Integración con estudios

## Estados Principales
```typescript
{
  elements: LibraryElement[];
  showForm: boolean;
  editingElement: LibraryElement | null;
  showDeleteModal: boolean;
}
```

## Hooks Utilizados
- useLibraryStore
- useMethodStore