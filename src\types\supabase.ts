export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          logo_url?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          logo_url?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          logo_url?: string;
          updated_at?: string;
        };
      };
      user_devices: {
        Row: {
          id: number;
          user_email: string;
          device_id: string;
          created_at: string;
        };
        Insert: {
          user_email: string;
          device_id: string;
        };
        Update: {
          user_email?: string;
          device_id?: string;
        };
      };
      studies: {
        Row: {
          id: string;
          name: string;
          company: string;
          date: string;
          operator?: string;
          section?: string;
          reference?: string;
          norm_number?: string;
          machine?: string;
          tools?: string;
          technician?: string;
          activity_scale_normal: number;
          activity_scale_optimal: number;
          user_id: string;
          created_at: string;
        };
      };
      work_elements: {
        Row: {
          id: string;
          study_id: string;
          description: string;
          type: string;
          frequency_repetitions: number;
          frequency_cycles: number;
          repetition_type: string;
          created_at: string;
        };
      };
      time_records: {
        Row: {
          id: string;
          element_id: string;
          time: number;
          timestamp: string;
          activity: number;
          comment?: string;
          created_at: string;
        };
      };
    };
  };
}