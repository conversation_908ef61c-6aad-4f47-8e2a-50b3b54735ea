import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, FileText, FileSpreadsheet, FileDown, Loader2 } from 'lucide-react';
import { Button } from './ui/button';
import { BulkExportRequest } from '../types/folder';
import { bulkExportStudies } from '../utils/bulkExport';

interface BulkExportModalProps {
  isOpen: boolean;
  folderName?: string;
  folderId?: string;
  studyCount: number;
  onClose: () => void;
  onExport: (request: BulkExportRequest) => Promise<void>;
}

export const BulkExportModal: React.FC<BulkExportModalProps> = ({
  isOpen,
  folderName,
  folderId,
  studyCount,
  onClose,
  onExport
}) => {
  const { t } = useTranslation(['common', 'report']);
  const [selectedFormat, setSelectedFormat] = useState<'excel' | 'pdf'>('excel');
  const [includeSubfolders, setIncludeSubfolders] = useState(true);
  const [calculatedStudyCount, setCalculatedStudyCount] = useState(studyCount);
  const [isLoading, setIsLoading] = useState(false);

  // Recalcular contador cuando cambie la opción de subcarpetas
  React.useEffect(() => {
    const calculateStudyCount = async () => {
      if (!folderId) {
        setCalculatedStudyCount(studyCount);
        return;
      }

      try {
        // Simular cálculo recursivo - aquí llamarías a una función del store
        if (includeSubfolders) {
          // Por ahora usaremos el conteo base + estimación
          // En una implementación real llamarías: getFolderStudiesRecursive(folderId)
          const estimatedSubfolderStudies = Math.floor(studyCount * 0.3); // Estimación
          setCalculatedStudyCount(studyCount + estimatedSubfolderStudies);
        } else {
          setCalculatedStudyCount(studyCount);
        }
      } catch (error) {
        console.error('Error calculating study count:', error);
        setCalculatedStudyCount(studyCount);
      }
    };

    calculateStudyCount();
  }, [includeSubfolders, folderId, studyCount]);

  const handleExport = async () => {
    setIsLoading(true);
    try {
      const request: BulkExportRequest = {
        folder_id: folderId,
        include_subfolders: includeSubfolders,
        export_format: selectedFormat
      };
      
      // Usar directamente la función de exportación
      await bulkExportStudies(request);
      onClose();
    } catch (error) {
      console.error('Error during bulk export:', error);
      alert('Error en la exportación: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {t('common:exportReports')}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Folder Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 text-blue-800">
              <FileDown className="h-5 w-5" />
              <div>
                <div className="font-medium">
                  {folderName ? `${t('common:folders', { defaultValue: 'Folders' })}: ${folderName}` : t('common:allStudies', { defaultValue: 'All Studies' })}
                </div>
                <div className="text-sm">
                  {calculatedStudyCount} {calculatedStudyCount === 1 ? 'estudio' : 'estudios'} para exportar
                  {includeSubfolders && folderId && calculatedStudyCount > studyCount && (
                    <span className="text-blue-600 ml-1">(incluye subcarpetas)</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Format Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Formato de exportación
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => setSelectedFormat('excel')}
                className={`p-4 border-2 rounded-lg transition-colors ${
                  selectedFormat === 'excel' 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex flex-col items-center gap-2">
                  <FileSpreadsheet className="h-8 w-8 text-green-600" />
                  <span className="font-medium">Excel</span>
                  <span className="text-xs text-gray-500">Archivo .xlsx</span>
                </div>
              </button>
              
              <button
                type="button"
                onClick={() => setSelectedFormat('pdf')}
                className={`p-4 border-2 rounded-lg transition-colors ${
                  selectedFormat === 'pdf' 
                    ? 'border-red-500 bg-red-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex flex-col items-center gap-2">
                  <FileText className="h-8 w-8 text-red-600" />
                  <span className="font-medium">PDF</span>
                  <span className="text-xs text-gray-500">Archivo .pdf</span>
                </div>
              </button>
            </div>
          </div>

          {/* Options */}
          <div>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={includeSubfolders}
                onChange={(e) => setIncludeSubfolders(e.target.checked)}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                Incluir subcarpetas
              </span>
            </label>
            <p className="text-xs text-gray-500 mt-1 ml-6">
              Exportar también los estudios de las subcarpetas
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="button"
            onClick={handleExport}
            disabled={isLoading || calculatedStudyCount === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exportando...
              </>
            ) : (
              <>
                <FileDown className="h-4 w-4 mr-2" />
                Exportar {calculatedStudyCount} {calculatedStudyCount === 1 ? 'informe' : 'informes'}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}; 