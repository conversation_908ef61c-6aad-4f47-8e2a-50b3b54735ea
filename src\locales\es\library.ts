export default {
  title: 'Biblioteca',
  subtitle: 'Busca y reutiliza elementos de tus estudios',
  searchPlaceholder: 'Buscar elementos por nombre o descripción',
  selectedElements: 'Elementos seleccionados: {{count}}',
  createNewStudy: 'Crear nuevo estudio',
  studyName: 'Nombre del estudio',
  studyCompany: 'Empresa',
  studyDate: 'Fecha',
  studyDescription: 'Descripción del estudio',
  normalActivity: 'Actividad normal',
  optimalActivity: 'Actividad óptima',
  fromStudy: 'De: {{name}}',
  noResults: 'No se encontraron elementos',
  loading: 'Cargando elementos...',
  error: 'Error al cargar los elementos',
  studyCreated: 'Estudio creado correctamente',
  createError: 'Error al crear el estudio',
  companies: 'Empresas',
  departments: 'Departamentos',
  operations: 'Operaciones',
  machines: 'Máquinas',
  parts: 'Piezas',
  analysts: 'Analistas',
  new: 'Nuevo',
  edit: 'Editar',
  delete: 'Eliminar',
  name: 'Nombre',
  description: 'Descripción',
  code: 'Código',
  type: 'Tipo',
  saveSuccess: 'Elemento guardado correctamente',
  saveError: 'Error al guardar el elemento',
  deleteSuccess: 'Elemento eliminado correctamente',
  deleteError: 'Error al eliminar el elemento',
  confirmDelete: '¿Está seguro de que desea eliminar este elemento?',
  noItems: 'No hay elementos disponibles',
  loadError: 'Error al cargar los elementos',
  search: 'Buscar',
  filter: 'Filtrar',
  sort: 'Ordenar',
  fromLibrary: 'De la Biblioteca',
  library: 'Biblioteca',
  libraryTab: 'Biblioteca',
  averageTab: 'Promedios',
  calculateAverage: 'Calcular Promedio',
  averagedElement: 'Elemento Promediado',
  sendToLibrary: 'Enviar a Biblioteca',
  sendToAverage: 'Enviar a Promediar',
  baseTime: 'Tiempo Base',
  totalTakes: 'Total de Tomas',
  clear: 'Limpiar',
  elements: 'elementos',
  integration: 'Integración con biblioteca',
  addTimesToElement: 'Sumar tiempos a un elemento de la biblioteca',
  addTimesToElementDescription: 'Sumar los tiempos de este elemento a un elemento de la biblioteca',
  selectLibraryElement: 'Selecciona el elemento de la biblioteca al que sumar los tiempos',
  selectElement: 'Selecciona un elemento',
  searchLibraryElement: 'Buscar elemento de biblioteca...',
  clearSearch: 'Limpiar búsqueda',
  removeSelection: 'Quitar selección',
  copyingTimes: 'Copiando tiempos a la biblioteca...',
  noTimeRecords: 'No hay registros de tiempo para copiar en el elemento actual.',
  libraryElementNotFound: 'No se encontró el elemento de la biblioteca seleccionado.',
  errorAccessingStudy: 'Error al acceder a los datos del estudio de la biblioteca.',
  noNewTimes: 'No hay nuevos tiempos para copiar. Todos los registros ya existen en la biblioteca.',
  timesAddedSuccess: 'Tiempos agregados exitosamente al elemento de la biblioteca.',
  errorAddingTimes: 'Error al agregar tiempos al elemento de la biblioteca.',
  copyTimesToLibrary: 'Copiar tiempos a la biblioteca',
  recordsCopiedSuccess: '{{count}} registro(s) de tiempo copiado(s) con éxito.',
  unexpectedError: 'Error inesperado: {{message}}',

  // Nuevas traducciones para la biblioteca rediseñada
  noStudiesInLibrary: 'No hay estudios en la biblioteca',
  noStudiesInLibraryDescription: 'Los estudios aparecerán aquí cuando tengan elementos marcados como visibles en la biblioteca',
  noElementsInStudy: 'Este estudio no tiene elementos disponibles',
  searchStudiesAndElements: 'Buscar estudios y elementos...',
  viewSelection: 'Ver selección',
  selectedElements: 'Elementos seleccionados',
  noElementsSelected: 'No hay elementos seleccionados',
  selectElementsToCreateStudy: 'Selecciona elementos de diferentes estudios para crear uno nuevo',
  createStudyFromSelection: 'Crear estudio con selección',
  allCompanies: 'Todas las empresas',
  dateFrom: 'Fecha desde',
  dateTo: 'Fecha hasta',
  onlyStudiesWithElements: 'Solo estudios con elementos',
  activeFilters: 'Filtros activos',
  applyFilters: 'Aplicar filtros',
  withElements: 'Con elementos',
  errorCreatingStudy: 'Error al crear el estudio',
  fillRequiredFields: 'Por favor completa todos los campos requeridos',
  selectAtLeastOneElement: 'Selecciona al menos un elemento',

  // Traducciones para el modal de creación
  studyNamePlaceholder: 'Nombre del nuevo estudio...',
  companyPlaceholder: 'Nombre de la empresa...',
  companyRequired: 'La empresa es requerida',
  dateRequired: 'La fecha es requerida',
  activityRangeError: 'La actividad debe estar entre 1 y 200',
  optimalActivityError: 'La actividad óptima debe ser mayor que la normal',
  creating: 'Creando...',

  // Traducciones para importador de Excel
  importFromExcel: 'Importar desde Excel',
  uploadExcelFile: 'Subir archivo Excel',
  supportedFormats: 'Formatos soportados',
  invalidFileType: 'Tipo de archivo no válido. Use .xlsx o .xls',
  fileNeedsHeaders: 'El archivo debe tener al menos una fila de encabezados y datos',
  errorReadingFile: 'Error al leer el archivo Excel',
  studyInformation: 'Información del estudio',
  columnMapping: 'Mapeo de columnas',
  elementDescription: 'Descripción del elemento',
  observedTime: 'Tiempo observado',
  observedActivity: 'Actividad observada',
  elementType: 'Tipo de elemento',
  repetitionType: 'Tipo de repetición',
  repetitionsPerCycle: 'Repeticiones por ciclo',
  frequencyCycles: 'Ciclos de frecuencia',
  selectColumn: 'Seleccionar columna',
  dataPreview: 'Vista previa de datos',
  rows: 'filas',
  preview: 'Vista previa',
  readyToImport: 'Listo para importar',
  elementsPreview: 'Vista previa de elementos',
  andMoreElements: 'y {{count}} elementos más...',
  importing: 'Importando...',
  importStudy: 'Importar estudio',
  errorsFound: 'Errores encontrados',
  descriptionColumnRequired: 'La columna de descripción es requerida',
  studyNameRequired: 'El nombre del estudio es requerido',
  errorImporting: 'Error al importar el archivo',
  downloadTemplate: 'Descargar plantilla',

  // Traducciones para gestión de plantillas
  templateManager: 'Gestor de Plantillas',
  manageTemplates: 'Gestionar Plantillas',
  createTemplate: 'Crear Plantilla',
  newTemplate: 'Nueva Plantilla',
  templateName: 'Nombre de la Plantilla',
  studyDefaults: 'Configuraciones por Defecto',
  timeUnit: 'Unidad de Tiempo',
  shiftMinutes: 'Minutos por Turno',
  contingency: 'Contingencia (%)',
  pointsPerHour: 'Puntos por Hora',
  normalActivity: 'Actividad Normal',
  optimalActivity: 'Actividad Óptima',
  setAsDefault: 'Establecer como Predeterminada',
  duplicate: 'Duplicar',
  confirmDeleteTemplate: '¿Está seguro de que desea eliminar esta plantilla?',

  // Nuevos campos de mapeo
  timeRecords: 'Registros de Tiempo',
  activityRecords: 'Registros de Actividad',
  supplementsPercentage: 'Porcentaje de Suplementos',
  frequencyRepetitions: 'Repeticiones de Frecuencia',

  // Placeholders para campos de mapeo
  descriptionPlaceholder: 'Columna con descripción del elemento',
  typePlaceholder: 'Columna con tipo de elemento',
  repetition_typePlaceholder: 'Columna con tipo de repetición',
  frequency_cyclesPlaceholder: 'Columna con ciclos de frecuencia',
  frequency_repetitionsPlaceholder: 'Columna con repeticiones',
  time_recordsPlaceholder: 'Columna con tiempos observados',
  activity_recordsPlaceholder: 'Columna con actividades',
  supplements_percentagePlaceholder: 'Columna con porcentaje de suplementos',
  commentsPlaceholder: 'Columna con comentarios'
};
