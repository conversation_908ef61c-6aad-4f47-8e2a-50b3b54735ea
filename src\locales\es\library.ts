export default {
  title: 'Biblioteca',
  subtitle: 'Busca y reutiliza elementos de tus estudios',
  searchPlaceholder: 'Buscar elementos por nombre o descripción',
  selectedElements: 'Elementos seleccionados: {{count}}',
  createNewStudy: 'Crear nuevo estudio',
  studyName: 'Nombre del estudio',
  studyCompany: 'Empresa',
  studyDate: 'Fecha',
  studyDescription: 'Descripción del estudio',
  normalActivity: 'Actividad normal',
  optimalActivity: 'Actividad óptima',
  fromStudy: 'De: {{name}}',
  noResults: 'No se encontraron elementos',
  loading: 'Cargando elementos...',
  error: 'Error al cargar los elementos',
  studyCreated: 'Estudio creado correctamente',
  createError: 'Error al crear el estudio',
  companies: 'Empresas',
  departments: 'Departamentos',
  operations: 'Operaciones',
  machines: 'Máquinas',
  parts: 'Piezas',
  analysts: 'Analistas',
  new: 'Nuevo',
  edit: 'Editar',
  delete: 'Eliminar',
  name: 'Nombre',
  description: 'Descripción',
  code: 'Código',
  type: 'Tipo',
  saveSuccess: 'Elemento guardado correctamente',
  saveError: 'Error al guardar el elemento',
  deleteSuccess: 'Elemento eliminado correctamente',
  deleteError: 'Error al eliminar el elemento',
  confirmDelete: '¿Está seguro de que desea eliminar este elemento?',
  noItems: 'No hay elementos disponibles',
  loadError: 'Error al cargar los elementos',
  search: 'Buscar',
  filter: 'Filtrar',
  sort: 'Ordenar',
  fromLibrary: 'De la Biblioteca',
  library: 'Biblioteca',
  libraryTab: 'Biblioteca',
  averageTab: 'Promedios',
  calculateAverage: 'Calcular Promedio',
  averagedElement: 'Elemento Promediado',
  sendToLibrary: 'Enviar a Biblioteca',
  sendToAverage: 'Enviar a Promediar',
  baseTime: 'Tiempo Base',
  totalTakes: 'Total de Tomas',
  clear: 'Limpiar',
  elements: 'elementos',
  integration: 'Integración con biblioteca',
  addTimesToElement: 'Sumar tiempos a un elemento de la biblioteca',
  addTimesToElementDescription: 'Sumar los tiempos de este elemento a un elemento de la biblioteca',
  selectLibraryElement: 'Selecciona el elemento de la biblioteca al que sumar los tiempos',
  selectElement: 'Selecciona un elemento',
  searchLibraryElement: 'Buscar elemento de biblioteca...',
  clearSearch: 'Limpiar búsqueda',
  removeSelection: 'Quitar selección',
  copyingTimes: 'Copiando tiempos a la biblioteca...',
  noTimeRecords: 'No hay registros de tiempo para copiar en el elemento actual.',
  libraryElementNotFound: 'No se encontró el elemento de la biblioteca seleccionado.',
  errorAccessingStudy: 'Error al acceder a los datos del estudio de la biblioteca.',
  noNewTimes: 'No hay nuevos tiempos para copiar. Todos los registros ya existen en la biblioteca.',
  timesAddedSuccess: 'Tiempos agregados exitosamente al elemento de la biblioteca.',
  errorAddingTimes: 'Error al agregar tiempos al elemento de la biblioteca.',
  copyTimesToLibrary: 'Copiar tiempos a la biblioteca',
  recordsCopiedSuccess: '{{count}} registro(s) de tiempo copiado(s) con éxito.',
  unexpectedError: 'Error inesperado: {{message}}',

  // Nuevas traducciones para la biblioteca rediseñada
  noStudiesInLibrary: 'No hay estudios en la biblioteca',
  noStudiesInLibraryDescription: 'Los estudios aparecerán aquí cuando tengan elementos marcados como visibles en la biblioteca',
  noElementsInStudy: 'Este estudio no tiene elementos disponibles',
  searchStudiesAndElements: 'Buscar estudios y elementos...',
  viewSelection: 'Ver selección',
  selectedElements: 'Elementos seleccionados',
  noElementsSelected: 'No hay elementos seleccionados',
  selectElementsToCreateStudy: 'Selecciona elementos de diferentes estudios para crear uno nuevo',
  createStudyFromSelection: 'Crear estudio con selección',
  allCompanies: 'Todas las empresas',
  dateFrom: 'Fecha desde',
  dateTo: 'Fecha hasta',
  onlyStudiesWithElements: 'Solo estudios con elementos',
  activeFilters: 'Filtros activos',
  applyFilters: 'Aplicar filtros',
  withElements: 'Con elementos',
  errorCreatingStudy: 'Error al crear el estudio',
  fillRequiredFields: 'Por favor completa todos los campos requeridos',
  selectAtLeastOneElement: 'Selecciona al menos un elemento'
};
