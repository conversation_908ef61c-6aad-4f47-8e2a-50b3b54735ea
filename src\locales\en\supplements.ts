import machine from "./machine";
import talFactors from "./talFactors";
import oitFactors from "./oitFactors";

export default {
  title: 'Supplements Tables',
  personal: 'Personal',
  fatigue: 'Fatigue',
  fatigue_calculation_explanation: 'The fatigue percentage is calculated as a weighted average based on the time of each element:\n\n1. For each element we use:\n- The supplement percentage already assigned to the element\n- The normalized time (considering frequency and activity)\n\n2. Weight = Element time / Total time\n\n3. Final % = Weighted average - 5% (Personal Needs)',
  standing: 'Standing',
  abnormalPosition: 'Abnormal Position',
  useOfForce: 'Use of Force',
  lighting: 'Lighting',
  atmosphericConditions: 'Atmospheric Conditions',
  closeAttention: 'Close Attention',
  noiseLevel: 'Noise Level',
  mentalStrain: 'Mental Strain',
  monotony: 'Monotony',
  tediousness: 'Tediousness',
  total: 'Total',
  save: 'Save',
  cancel: 'Cancel',
  oitTables: 'ILO Tables',
  oitDescription: 'Supplements tables according to the International Labour Organization',
  talTables: 'TAL Tables',
  talDescription: 'Adaptation of the ILO tables made by the Tribunal de Arbitraje Laboral de Valencia',
  reset: 'Reset',
  saveSuccess: 'Supplements saved successfully',
  saveError: 'Error saving supplements',
  loadError: 'Error loading supplements',
  openTables: 'Open Tables',
  copy: 'Copy',
  copySuccess: 'Supplements copied successfully',
  copyError: 'Error copying supplements',
  percentage: 'Percentage',
  forceDescription: 'Force description',
  copySupplements: {
    title: 'Copy supplements',
    description: 'Select the elements to copy the supplements to',
    copyAll: 'Copy to all',
    copySelected: 'Copy selected'
  },
  bulkCopyError: 'Error copying supplements',
  resetSupplements: {
    "title": "Reset supplements",
    "confirmation": "Are you sure you want to reset the supplements for this element? This action cannot be undone."
  },
  resetSuccess: 'Supplements reset successfully',
  resetError: 'Error resetting supplements',
  points: 'Points',
  totalPoints: 'Total Points',
  force: 'Force',
  machine: 'Machine',
  calculoFatiga: 'Fatigue Calculation',
  viewTable: 'View Table',
  tableMismatch: {
    title: 'Different Table Detected',
    message: 'This element already has supplements assigned from the {{table}} table. You cannot edit supplements from a different table type.',
    close: 'Close'
  },
  factors: {
    A1: 'Factor A1 - Use of Force',
    A2: 'A.2. Posture',
    A3: 'A.3. Vibrations',
    A4: 'A.4. Short cycle',
    A5: 'A.5. Restrictive clothing',
    B1: 'B.1. Concentration/Anxiety',
    B2: 'B.2. Monotony',
    B3: 'B.3. Visual strain',
    B4: 'B.4. Noise',
    C1: 'C.1. Temperature and Humidity',
    C2: 'C.2. Gas fumes',
    C3: 'C.3. Gas fumes',
    C4: 'C.4. Dust',
    C5: 'C.5. Dirt',
    C6: 'C.6. Water',
  },
  tal: talFactors,
  oit: oitFactors,
  machineSupplements: {
    title: 'Normalized Supplements Calculation',
    inputTimes: 'Input Times',
    concept: 'Concept',
    value: 'Value',
    seconds: 'sec',
    machineRunningTime: 'Machine Running Time',
    machineStopTime: 'Machine Stop Time',
    machineTime: 'Machine Time',
    machineRunTime: 'Machine Run Time',
    inactivityTime: 'Inactivity Time',
    personalNeedsPercentage: 'Personal Needs %',
    fatiguePercentage: 'Fatigue %',
    results: 'Results',
    baseTime: 'Base Time',
    personalNeedsSupplement: 'Personal Needs Supplement',
    fatigueSupplement: 'Fatigue Supplement',
    remainingFatigue: 'Remaining Fatigue',
    totalCycleTime: 'Total Cycle Time',
    caseTitle: 'Case Summary',
    element: 'Element',
    type: 'Type',
    averageTime: 'Average Time',
    activity: 'Activity',
    repetitions: 'Repetitions',
    cycles: 'Cycles',
    assignedSupplement: 'Assigned Supplement',
    machineRunning: 'Machine Running',
    machineStopped: 'Machine Stopped',
    unknown: 'Unknown',
    frequencyFactor: 'Frequency Factor',
    normalizedTime: 'Normalized Time',
    totalTime: 'Total Time',
    weight: 'Weight',
    contribution: 'Contribution',
    totalSupplement: 'Total Weighted Supplement',
    result: 'Final Result',
    detailedCalculation: 'Detailed Calculation',
    weightedFatigueCalculation: 'WEIGHTED FATIGUE SUPPLEMENT CALCULATION',
    personalNeedsCalculation: 'PERSONAL NEEDS SUPPLEMENT CALCULATION (fixed 5%)',
    theoreticalInformation: 'THEORETICAL INFORMATION ABOUT TIMES AND SUPPLEMENTS IN MACHINE OPERATIONS',
    timeScales: '1. Time Scales',
    timeBase: 'Base Time Calculation',
    maxMachineTime: 'Maximum between Machine Time and Machine Run Time',
    baseTimeExplanation: 'The base time is calculated as the sum of the machine stop time plus the maximum value between machine time and machine run time.',
    npSupplement: "NP Supplement",
    totalFatigue: "Total Fatigue",
    inactivity: "Inactivity",
    print: 'Print',
    supplements: '2. Supplements:',
    applicationOfSupplements: '3. Application of Supplements Based on Inactivity Duration:',
    distributionOfSupplements: 'DISTRIBUTION OF SUPPLEMENTS',
    baseTimeCalculation: "Total base time = max(Machine Running Time, Machine Time) + Machine Stop Time",
    fatigueCalculation: 'FATIGUE SUPPLEMENT (4%)',
    totalCycleTimeCalculation: 'TOTAL CYCLE TIME',
    remainingFatigueCalculation: 'REMAINING FATIGUE CALCULATION',
    calculationHeaders: {
      onlyWorkElements: "(Only considers work elements: Machine Running and Machine Stopped)",
      inputDataByElement: "1. INPUT DATA BY ELEMENT",
      timeNormalization: "2. TIME NORMALIZATION WITH APPRECIATED ACTIVITY",
      totalTimeCalculation: "3. TOTAL TIME CALCULATION",
      weightCalculation: "4. WEIGHT CALCULATION",
      weightedSupplementCalculation: "5. WEIGHTED SUPPLEMENT CALCULATION",
      finalAdjustment: "6. FINAL ADJUSTMENT"
    },
    caseExplanations: {
      case1: "CASE 1: Inactivity Periods Less than 0.5 minutes (30 seconds)\nIn this case, personal needs and fatigue supplements are fully applied.",
      case2: "CASE 2: Inactivity Periods between 0.5 and 1.5 minutes (30-90 seconds)\nIn this case, the personal needs supplement is fully applied, but the fatigue supplement is partially reduced based on the duration of inactivity.",
      case3: "CASE 3: Inactivity Periods from 1.5 to 10 minutes (90-600 seconds)\nIn this case, the personal needs supplement is fully applied, but the fatigue supplement is completely absorbed by the inactivity time.",
      case4: "CASE 4: Inactivity Periods Greater than 10 minutes (more than 600 seconds)\nIn this case, both the personal needs and fatigue supplements are completely absorbed by the inactivity time."
    },
    explanationText: {
      introduction: 'In the analysis of times for machine operations, various time scales are used to understand the interaction of the worker with the machine and apply supplements appropriately.',
      timeScales: {
        cycleTime: 'Cycle Time: Total time to complete an operation, including machine time and worker time.',
        machineTime: 'Machine Conditioned Time (MT): The time when the machine operates automatically and the worker waits or performs interior work.',
        manualWork: 'Manual Work Time: The time when the worker performs manual activity:',
        externalWork: '▸ External Work (MS): Outside the machine conditioned time.',
        internalWork: '▸ Internal Work (MR): Within the machine conditioned time.',
        unusedTime: 'Unused Time: The time within the conditioned time where the worker does not perform any productive activity.'
      },
      supplements: {
        title: '2. Supplements',
        personalNeeds: 'Personal Needs Supplements: Additional time for needs (bathroom, water).',
        fatigue: 'Fatigue Supplements: Additional time to recover from fatigue.'
      },
      supplementsApplication: {
        title: '3. Application of Supplements Based on Inactivity Duration',
        case1: 'CASE 1: Inactivity Periods Less than 0.5 minutes: They are discarded from being compensated for the fatigue supplement.',
        case2: 'CASE 2: Inactivity Periods between 0.5 and 1.5 minutes: They are adjusted for the fatigue supplement: (Real Duration - 0.5) * 1.5.',
        case3: 'CASE 3: Inactivity Periods of 1.5 minutes or more (up to 10 minutes): They are fully used for the fatigue supplement.',
        case4: 'CASE 4: Inactivity Periods Greater than 10 minutes: They can completely absorb both the fatigue and personal needs supplements. This is viable if the operator can leave the machine unattended without danger and fully recover during this time.'
      }
    }
  }
};