import machine from "./machine";
import talFactors from "./talFactors";
import oitFactors from "./oitFactors";

export default {
  tal: talFactors,
  oit: oitFactors,
  machine: 'Equipo',
  calculoFatiga: 'Cálculo de fatiga',
  title: 'Tablas de Suplementos',
  personal: 'Personal',
  fatigue: 'Fatiga',
  fatigue_calculation_explanation: 'El porcentaje de fatiga se calcula como un promedio ponderado basado en el tiempo normalizado de cada elemento:\n\n1. Para cada elemento:\n- Se obtiene su porcentaje de suplementos asignado\n- Se calcula su tiempo normalizado = Tiempo × (Actividad/100) × (Repeticiones/Ciclos)\n\n2. Peso del elemento = Tiempo normalizado del elemento / Suma total de tiempos normalizados\n\n3. Fatiga final = Suma de (Peso × Suplementos) de cada elemento',
  standing: 'De pie',
  abnormalPosition: 'Posición Anormal',
  useOfForce: 'Uso de Fuerza',
  lighting: 'Iluminación',
  atmosphericConditions: 'Condiciones Atmosféricas',
  closeAttention: 'Atención Cercana',
  noiseLevel: 'Nivel de Ruido',
  mentalStrain: 'Tensión Mental',
  monotony: 'Monotonía',
  tediousness: 'Tedio',
  total: 'Total',
  save: 'Guardar',
  cancel: 'Cancelar',
  oitTables: 'Tablas OIT',
  oitDescription: 'Tablas de suplementos según la Organización Internacional del Trabajo',
  talTables: 'Tablas TAL',
  talDescription: 'Adaptación de las tablas de OIT realizada por el Tribunal de Arbitraje Laboral de Valencia',
  reset: 'Reiniciar',
  saveSuccess: 'Suplementos guardados correctamente',
  saveError: 'Error al guardar los suplementos',
  loadError: 'Error al cargar los suplementos',
  openTables: 'Abrir Tablas',
  copy: 'Copiar',
  copySuccess: 'Suplementos copiados correctamente',
  copyError: 'Error al copiar los suplementos',
  percentage: 'Porcentaje',
  forceDescription: 'Descripción de la fuerza',
  copySupplements: {
    title: 'Copiar suplementos',
    description: 'Selecciona los elementos a los que quieres copiar los suplementos',
    copyAll: 'Copiar a todos',
    copySelected: 'Copiar seleccionados'
  },
  resetSupplements: {
    title: "Reiniciar suplementos",
    confirmation: "¿Estás seguro de que quieres reiniciar los suplementos de este elemento? Esta acción no se puede deshacer."
  },
  factors: {
    A1: 'Factor A1 - Uso de Fuerza',
    A2: 'A.2. Postura',
    A3: 'A.3. Vibraciones',
    A4: 'A.4. Ciclo breve',
    A5: 'A.5. Ropa molesta',
    B1: 'B.1. Concentración/Ansiedad',
    B2: 'B.2. Monotonía',
    B3: 'B.3. Tensión visual',
    B4: 'B.4. Ruido',
    C1: 'C.1. Temperatura y Humedad',
    C2: 'C.2. Ventilación',
    C3: 'C.3. Emanaciones de gases',
    C4: 'C.4. Polvo',
    C5: 'C.5. Suciedad',
    C6: 'C.6. Presencia de agua'
  },
  tableMismatch: {
    title: 'Tabla diferente detectada',
    message: 'Este elemento ya tiene suplementos asignados de la tabla {{table}}. No puedes editar suplementos de un tipo de tabla diferente.',
    close: 'Cerrar'
  },
  points: 'Puntos',
  totalPoints: 'Puntos Totales',
  force: 'Fuerza',
  viewTable: 'Ver Tabla',
  resetSuccess: 'Suplementos reiniciados correctamente',
  resetError: 'Error al reiniciar los suplementos',
  machineSupplements: {
    title: 'Cálculo de Suplementos Normalizados',
    inputTimes: 'Tiempos de Entrada',
    concept: 'Concepto',
    value: 'Valor',
    seconds: 'seg',
    machineRunningTime: 'Tiempo Máquina en Marcha',
    machineStopTime: 'Tiempo Máquina Parada',
    machineTime: 'Tiempo de Máquina',
    machineRunTime: 'Máquina en Marcha',
    inactivityTime: 'Tiempo Inactividad',
    personalNeedsPercentage: 'Necesidades Personales %',
    fatiguePercentage: 'Fatiga %',
    results: 'Resultados',
    baseTime: 'Tiempo Base',
    personalNeedsSupplement: 'Suplemento por Necesidades Personales',
    fatigueSupplement: 'Suplemento por Fatiga',
    remainingFatigue: 'Fatiga Restante',
    totalCycleTime: 'Tiempo Total del Ciclo',
    caseTitle: 'Resumen del Caso',
    element: 'Elemento',
    type: 'Tipo',
    averageTime: 'Tiempo Promedio',
    activity: 'Actividad',
    repetitions: 'Repeticiones',
    cycles: 'Ciclos',
    assignedSupplement: 'Suplemento Asignado',
    machineRunning: 'Máquina en Marcha',
    machineStopped: 'Máquina Parada',
    unknown: 'Desconocido',
    frequencyFactor: 'Factor de Frecuencia',
    normalizedTime: 'Tiempo Normalizado',
    totalTime: 'Tiempo Total',
    weight: 'Peso',
    contribution: 'Contribución',
    totalSupplement: 'Suplemento Total Ponderado',
    result: 'Resultado Final',
    detailedCalculation: 'Cálculo Detallado',
    weightedFatigueCalculation: 'CÁLCULO DEL SUPLEMENTO DE FATIGA PONDERADO',
    personalNeedsCalculation: 'CÁLCULO DEL SUPLEMENTO POR NECESIDADES PERSONALES (5% fijo)',
    theoreticalInformation: 'INFORMACIÓN TEÓRICA SOBRE TIEMPOS Y SUPLEMENTOS EN OPERACIONES CON MÁQUINAS',
    timeScales: '1. Escalas de Tiempo',
    timeBase: 'Cálculo del Tiempo Base',
    maxMachineTime: 'Máximo entre Tiempo Máquina y Máquina en Marcha',
    baseTimeExplanation: 'El tiempo base se calcula como la suma del tiempo de máquina parada más el valor máximo entre el tiempo de máquina y el tiempo de máquina en marcha.',
    npSupplement: "Suplemento NP",
    totalFatigue: "Fatiga Total",
    inactivity: "Inactividad",
    print: 'Imprimir',
    supplements: '2. Suplementos:',
    applicationOfSupplements: '3. Aplicación de Suplementos Según la Duración de la Inactividad:',
    distributionOfSupplements: 'DISTRIBUCIÓN DE SUPLEMENTOS',
    baseTimeCalculation: "Tiempo base total = max(Tiempo Máquina en marcha, Tiempo Máquina) + Tiempo Máquina Parada",
    fatigueCalculation: 'SUPLEMENTO FATIGA (4%)',
    totalCycleTimeCalculation: 'TIEMPO CICLO TOTAL',
    remainingFatigueCalculation: 'CÁLCULO FATIGA RESTANTE',
    calculationHeaders: {
      onlyWorkElements: "(Solo considera elementos de trabajo: Máquina en Marcha y Máquina Parada)",
      inputDataByElement: "1. DATOS DE ENTRADA POR ELEMENTO",
      timeNormalization: "2. NORMALIZACIÓN DE TIEMPOS CON LA ACTIVIDAD APRECIADA",
      totalTimeCalculation: "3. CÁLCULO DEL TIEMPO TOTAL",
      weightCalculation: "4. CÁLCULO DE PESOS",
      weightedSupplementCalculation: "5. CÁLCULO DEL SUPLEMENTO PONDERADO",
      finalAdjustment: "6. AJUSTE FINAL"
    },
    dataSync: {
      savedSuccessfully: 'Datos guardados correctamente',
      savingData: 'Guardando datos del cálculo...',
      saveError: 'Error al guardar los datos',
      dataInconsistency: 'Detectada inconsistencia en los datos guardados',
      recheckCalculation: 'Si ves datos incorrectos en el informe, vuelve a ejecutar este cálculo'
    },
    caseExplanations: {
      case1: "CASO 1: Periodos de Inactividad Menores a 0.5 minutos (30 segundos)\nEn este caso, los suplementos por necesidades personales y fatiga se aplican completamente.",
      case2: "CASO 2: Periodos de Inactividad entre 0.5 y 1.5 minutos (30-90 segundos)\nEn este caso, el suplemento por necesidades personales se aplica completamente, pero el suplemento por fatiga se reduce parcialmente según la duración de la inactividad.",
      case3: "CASO 3: Periodos de Inactividad de 1.5 a 10 minutos (90-600 segundos)\nEn este caso, el suplemento por necesidades personales se aplica completamente, pero el suplemento por fatiga está completamente absorbido por el tiempo de inactividad.",
      case4: "CASO 4: Periodos de Inactividad Superiores a 10 minutos (más de 600 segundos)\nEn este caso, tanto el suplemento por necesidades personales como el de fatiga están completamente absorbidos por el tiempo de inactividad."
    },
    explanationText: {
      introduction: 'En el análisis de tiempos para operaciones con máquinas, se utilizan diversas escalas de tiempo para entender la interacción del trabajador con la máquina y aplicar los suplementos de manera adecuada.',
      timeScales: {
        cycleTime: 'Tiempo de Ciclo: Tiempo total para completar una operación, incluyendo el tiempo de la máquina y el del trabajador.',
        machineTime: 'Tiempo Condicionado por la Máquina (TM): El tiempo en que la máquina opera automáticamente y el trabajador espera o realiza trabajo interior.',
        manualWork: 'Tiempo de Trabajo Manual: El tiempo en que el trabajador realiza actividad manual:',
        externalWork: '▸ Trabajo Exterior (MP): Fuera del tiempo condicionado por la máquina.',
        internalWork: '▸ Trabajo Interior (MM): Dentro del tiempo condicionado por la máquina.',
        unusedTime: 'Tiempo No Ocupado: El tiempo dentro del tiempo condicionado donde el trabajador no realiza ninguna actividad productiva.'
      },
      supplements: {
        title: '2. Suplementos',
        personalNeeds: 'Suplementos por Necesidades Personales: Tiempo adicional para necesidades (baño, agua).',
        fatigue: 'Suplementos por Fatiga: Tiempo adicional para recuperarse de la fatiga.'
      },
      supplementsApplication: {
        title: '3. Aplicación de Suplementos Según la Duración de la Inactividad',
        case1: 'CASO 1: Periodos de Inactividad Menores a 0.5 minutos: Se descartan el que sean compensados para el suplemento por fatiga.',
        case2: 'CASO 2: Periodos de Inactividad entre 0.5 y 1.5 minutos: Se ajustan para el suplemento por fatiga: (Duración Real - 0.5) * 1.5.',
        case3: 'CASO 3: Periodos de Inactividad de 1.5 minutos o más (hasta 10 minutos): Se utilizan por completo para el suplemento por fatiga.',
        case4: 'CASO 4: Periodos de Inactividad Superiores a 10 minutos: Pueden absorber completamente tanto el suplemento por fatiga como por necesidades personales. Esto es viable si el operario puede dejar la máquina sin atención y sin peligro y recuperarse completamente durante este tiempo.'
      }
    }
  }
};