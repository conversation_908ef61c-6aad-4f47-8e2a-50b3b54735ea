import React, { useEffect, useState } from 'react';
import { Download } from 'lucide-react';
import { usePWAStore } from '../store/pwaStore';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<{ outcome: 'accepted' | 'dismissed' }>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent;
    appinstalled: Event;
  }
}

export const InstallPWA: React.FC = () => {
  const [showBanner, setShowBanner] = useState(false);
  const { isInstallable, setDeferredPrompt, handleInstall } = usePWAStore();

  useEffect(() => {
    // Check if app is already installed
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInstalled = localStorage.getItem('pwa-installed') === 'true';
    
    if (!isStandalone && !isInstalled) {
      // If not installed, show banner after a delay
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, []);

  useEffect(() => {
    const handler = (e: Event) => {
      // Prevent the default browser install prompt
      e.preventDefault();
      // Store the event for later use
      setDeferredPrompt(e as any);
      setShowBanner(true);
    };

    window.addEventListener('beforeinstallprompt', handler);

    // Listen for successful installation
    const installedHandler = () => {
      console.log('PWA was installed');
      setShowBanner(false);
      localStorage.setItem('pwa-installed', 'true');
    };

    window.addEventListener('appinstalled', installedHandler);

    return () => {
      window.removeEventListener('beforeinstallprompt', handler);
      window.removeEventListener('appinstalled', installedHandler);
    };
  }, [setDeferredPrompt]);

  // Don't show if user recently dismissed
  const lastDismissed = localStorage.getItem('pwa-dismissed-time');
  if (lastDismissed) {
    const timeSinceDismiss = Date.now() - parseInt(lastDismissed);
    if (timeSinceDismiss < 24 * 60 * 60 * 1000) { // 24 hours
      return null;
    }
  }

  if (!showBanner || !isInstallable) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-white rounded-lg shadow-lg p-4 z-50 animate-fade-in">
      <div className="flex items-start justify-between">
        <div className="flex-1 mr-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            Instalar aplicación
          </h3>
          <p className="text-sm text-gray-600">
            Instala esta aplicación en tu dispositivo para un acceso más rápido y una mejor experiencia.
          </p>
        </div>
        <button
          onClick={() => {
            setShowBanner(false);
            localStorage.setItem('pwa-dismissed-time', Date.now().toString());
          }}
          className="text-gray-400 hover:text-gray-500"
        >
          <span className="sr-only">Cerrar</span>
          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
      <div className="mt-4 flex justify-end space-x-3">
        <button
          onClick={() => {
            setShowBanner(false);
            localStorage.setItem('pwa-dismissed-time', Date.now().toString());
          }}
          className="text-gray-700 hover:text-gray-900 text-sm font-medium"
        >
          Más tarde
        </button>
        <button
          onClick={handleInstall}
          className="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 flex items-center"
        >
          <Download className="w-4 h-4 mr-2" />
          Instalar ahora
        </button>
      </div>
    </div>
  );
};
