# Pruebas de Suplementos

## Descripción
Suite de pruebas para cálculos de suplementos.

## Casos de Prueba

### 1. Conversión de Puntos
```typescript
describe('Supplements Calculations', () => {
  test('should convert points to percentage', () => {
    const points = {
      A1: 5,
      B2: 3,
      C1: 2
    };
    
    const percentage = convertPointsToPercentage(
      calculateTotalPoints(points)
    );
    
    expect(percentage).toBe(10);
  });
});
```

### 2. Validaciones
```typescript
test('should validate point ranges', () => {
  const validPoints = {
    A1: 5,
    B2: 3
  };
  
  const invalidPoints = {
    A1: 25,
    B2: -1
  };
  
  expect(validatePoints(validPoints)).toBe(true);
  expect(validatePoints(invalidPoints)).toBe(false);
});
```

### 3. Cálculos Especiales
```typescript
test('should handle special cases', () => {
  const points = {
    C1: {
      temperature: 28,
      humidity: 80
    }
  };
  
  const result = calculateSpecialFactor('C1', points.C1);
  expect(result).toBe(10);
});
```