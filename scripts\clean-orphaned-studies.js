// Script para limpiar estudios huérfanos
// Este script identifica estudios que pertenecen a usuarios que ya no son miembros
// de la organización y actualiza su estado para desvincularlos de la organización.

// Para ejecutar este script:
// 1. Configurar las variables de entorno SUPABASE_URL y SUPABASE_KEY
// 2. Ejecutar: node scripts/clean-orphaned-studies.js [organization_id]

const { createClient } = require('@supabase/supabase-js');

// Configuración de Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Se requieren las variables de entorno SUPABASE_URL y SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Obtener ID de organización de los argumentos
const organizationId = process.argv[2];

if (!organizationId) {
  console.error('Error: Se requiere un ID de organización como argumento');
  console.log('Uso: node scripts/clean-orphaned-studies.js [organization_id]');
  process.exit(1);
}

async function cleanOrphanedStudies() {
  try {
    console.log(`Iniciando limpieza de estudios huérfanos para la organización: ${organizationId}`);
    
    // 1. Obtener todos los miembros actuales de la organización
    const { data: members, error: membersError } = await supabase
      .from('organization_members')
      .select('user_id')
      .eq('organization_id', organizationId);
    
    if (membersError) {
      console.error('Error al obtener miembros:', membersError);
      throw membersError;
    }
    
    // Crear un array con los IDs de los miembros actuales
    const memberIds = members.map(member => member.user_id);
    console.log('Miembros actuales:', memberIds);
    
    // 2. Obtener todos los estudios asociados a esta organización
    const { data: studies, error: studiesError } = await supabase
      .from('studies')
      .select('id, user_id, organization_id, is_shared')
      .eq('organization_id', organizationId);
    
    if (studiesError) {
      console.error('Error al obtener estudios:', studiesError);
      throw studiesError;
    }
    
    console.log('Estudios encontrados para la organización:', studies?.length || 0);
    
    // 3. Filtrar los estudios que pertenecen a usuarios que ya no son miembros
    const orphanedStudies = studies?.filter(study => !memberIds.includes(study.user_id)) || [];
    console.log('Estudios huérfanos encontrados:', orphanedStudies.length);
    
    if (orphanedStudies.length === 0) {
      console.log('No hay estudios huérfanos para limpiar');
      return { cleaned: 0 };
    }
    
    // 4. Actualizar los estudios huérfanos para desvincularlos de la organización
    const orphanedStudyIds = orphanedStudies.map(study => study.id);
    console.log('IDs de estudios huérfanos:', orphanedStudyIds);
    
    const { data: updateResult, error: updateError } = await supabase
      .from('studies')
      .update({
        organization_id: null,
        is_shared: false
      })
      .in('id', orphanedStudyIds);
    
    if (updateError) {
      console.error('Error al actualizar estudios huérfanos:', updateError);
      throw updateError;
    }
    
    console.log(`✅ ${orphanedStudies.length} estudios huérfanos han sido limpiados correctamente`);
    return { cleaned: orphanedStudies.length };
  } catch (error) {
    console.error('Error en cleanOrphanedStudies:', error);
    return { cleaned: 0, error };
  }
}

// Ejecutar la función principal
cleanOrphanedStudies()
  .then(() => {
    console.log('Proceso completado');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error en el proceso:', error);
    process.exit(1);
  });
