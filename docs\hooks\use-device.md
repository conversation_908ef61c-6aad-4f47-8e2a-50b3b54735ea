# useDevice Hook

## Descripción
Hook para gestionar información y permisos del dispositivo.

## Funcionalidades
- Generación de ID único de dispositivo
- Detección de plataforma
- Información del navegador
- Estado de permisos

## Retorno
```typescript
{
  deviceId: string;
  platform: string;
  userAgent: string;
  screenInfo: {
    width: number;
    height: number;
    colorDepth: number;
  };
  generateDeviceId: () => string;
}
```

## Uso
```typescript
const { deviceId, platform, generateDeviceId } = useDevice();

// Generar nuevo ID
const newDeviceId = generateDeviceId();

console.log(`Platform: ${platform}`);
console.log(`Device ID: ${deviceId}`);
```