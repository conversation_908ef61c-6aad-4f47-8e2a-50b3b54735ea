import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Routes, Route, useNavigate, useLocation, Navigate } from 'react-router-dom';
import { Info, ListTodo, Timer, Gauge, Clock, FileText, UserPlus, Watch } from 'lucide-react';
import { HelmetProvider } from 'react-helmet-async';
import { Header } from './components/Header';
import { StudyCard } from './components/StudyCard';
import { StudyPage } from './pages/StudyPage';
import { StudyListPage } from './pages/StudyListPage';
import { LoginPage } from './pages/LoginPage';
import { DeleteConfirmModal } from './components/DeleteConfirmModal';
import { StudyFilters } from './components/StudyFilters';
import { LoginForm } from './components/LoginForm';
import { MethodPage } from './pages/MethodPage';
import { ChronometerPage } from './pages/ChronometerPage';
import { FrequencyPage } from './pages/FrequencyPage';
import { MachinePage } from './pages/MachinePage';
import { SupplementsPage } from './pages/SupplementsPage';
import { LibraryPage } from './pages/LibraryPage';
import { ReportPage } from './pages/ReportPage';
import { CronoSeguidoPage } from './pages/CronoSeguidoPage';
import { useAuthStore } from './store/authStore';
import { useStudyStore } from './store/studyStore';
import { Study } from './types';
import { useWakeLock } from './hooks/useWakeLock';
import { ProfilePage } from './pages/ProfilePage';
import { DocsPage } from './pages/DocsPage';
import { ChangelogPage } from './pages/ChangelogPage';
import { Toaster } from './components/ui/Toaster';
import { Toaster as HotToaster } from 'react-hot-toast';
import { UpdatePrompt } from './components/UpdatePrompt';
import { InstallBanner } from './components/InstallBanner';
import { InstallPWA } from './components/InstallPWA';
import { usePWAStore } from './store/pwaStore';
import { AuthCallback } from './pages/AuthCallback';
import { useUserInitialization } from './hooks/useUserInitialization';
import { useSearchStore } from './store/searchStore';
import { ResetPassword } from './pages/ResetPassword';
import { UpdatePassword } from './pages/UpdatePassword';
import { BudgetPage } from './pages/BudgetPage';
import { FAQ } from './components/FAQ';
import { PrivateRoute } from './components/PrivateRoute';
import { PrivacyPolicyPage } from './pages/PrivacyPolicyPage';
import { useOrganizationStore } from './store/organizationStore';
import { useStudyFromUrl } from './hooks/useStudyFromUrl';
import GanttPage from './pages/GanttPage';
import { TestFolders } from './pages/TestFolders';
import { DiagnosticFolders } from './pages/DiagnosticFolders';

function App() {
  const { t } = useTranslation(['study', 'library', 'cronoSeguido', 'common']);
  const navigate = useNavigate();
  const location = useLocation();
  const user = useAuthStore(state => state.user);
  const isLoading = useAuthStore(state => state.isLoading);
  const isInitialized = useAuthStore(state => state.isInitialized);
  const initializeSession = useAuthStore(state => state.initializeSession);
  const initializeUser = useAuthStore(state => state.initializeUser);
  const { studies, selectedStudy, setSelectedStudy, fetchStudies, deleteStudy } = useStudyStore();
  const { searchQuery } = useSearchStore();
  const { requestInstall } = usePWAStore();
  useWakeLock();
  
  // Usar el hook mejorado para cargar el estudio desde la URL
  useStudyFromUrl();

  // Estado para controlar la inicialización
  const [initializing, setInitializing] = React.useState(true);

  // Inicializar sesión y usuario
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setInitializing(true);
        
        // Inicializar el usuario y sus datos
        await initializeUser();
        
        // Crear las funciones RPC necesarias
        try {
          const { checkOrganizationExists } = useOrganizationStore.getState();
          await checkOrganizationExists();
        } catch (error) {
          console.error('Error al verificar acceso a organizaciones:', error);
          // No bloqueamos la inicialización si esto falla
        }
        
        setInitializing(false);
      } catch (error) {
        console.error('Error al inicializar la aplicación:', error);
        setInitializing(false);
      }
    };
    
    initializeApp();
  }, [initializeUser]);

  useUserInitialization();

  // Fetch studies when user is logged in and session is initialized
  useEffect(() => {
    const loadStudies = async () => {
      if (user?.id && isInitialized && !isLoading) {
        console.log('Loading studies for user:', user.id);
        await fetchStudies();
      }
    };
    loadStudies();
  }, [user?.id, isInitialized, isLoading, fetchStudies]);

  // Fetch studies when navigating to home page
  useEffect(() => {
    const loadStudies = async () => {
      if (location.pathname === '/' && user?.id && isInitialized && !isLoading) {
        console.log('Reloading studies on home page navigation');
        await fetchStudies();
      }
    };
    loadStudies();
  }, [location.pathname, user?.id, isInitialized, isLoading, fetchStudies]);

  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [studyToDelete, setStudyToDelete] = React.useState<string | null>(null);
  const [filters, setFilters] = React.useState({
    company: false,
    date: false,
    process: false
  });

  // Filtrar estudios basados en la búsqueda y filtros
  const filteredStudies = useMemo(() => {
    let filtered = studies;

    // Aplicar filtro de búsqueda
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(study => {
        const name = study.required_info?.name?.toLowerCase() || '';
        const company = study.required_info?.company?.toLowerCase() || '';
        const date = study.required_info?.date?.toLowerCase() || '';
        return name.includes(query) || company.includes(query) || date.includes(query);
      });
    }

    // Aplicar filtros adicionales
    if (filters.company && selectedStudy) {
      filtered = filtered.filter(study => study.required_info?.company === selectedStudy.required_info?.company);
    }
    if (filters.date && selectedStudy) {
      filtered = filtered.filter(study => study.required_info?.date === selectedStudy.required_info?.date);
    }

    return filtered;
  }, [studies, searchQuery, filters, selectedStudy]);

  const handleSearch = (query: string) => {
    useSearchStore.getState().setSearchQuery(query);
  };

  // Efecto para seleccionar el estudio cuando se navega a la página principal
  useEffect(() => {
    const state = location.state as { selectStudyId?: string } | null;
    
    if (state?.selectStudyId && studies.length > 0) {
      const study = studies.find(s => s.id === state.selectStudyId);
      if (study) {
        setSelectedStudy(study);
        // Limpiar el estado después de seleccionar el estudio
        navigate(location.pathname, { replace: true });
      }
    }
  }, [location.state, studies, setSelectedStudy, navigate, location.pathname]);

  const handleNavigateToHome = () => {
    navigate('/');
  };

  const handleStudyClick = (study: Study) => {
    setSelectedStudy(study);
  };

  const handleDeleteStudy = (studyId: string) => {
    setStudyToDelete(studyId);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (studyToDelete) {
      await deleteStudy(studyToDelete);
      setShowDeleteModal(false);
      setStudyToDelete(null);
    }
  };

  const handleStudyInfo = () => {
    if (selectedStudy) {
      navigate(`/study/${selectedStudy.id}/info`);
    }
  };

  const handleNewStudy = () => {
    setSelectedStudy(null);
    navigate('/study');
  };

  const getNavigationButtons = () => {
    if (!selectedStudy) return [];

    const hasRepetitiveElements = selectedStudy.elements.some(e => 
      e.repetition_type === 'repetitive-type' || e.repetition_type === 'repetitive'
    );
    const hasMachineElements = selectedStudy.elements.some(e => 
      e.type === 'machine-time' || e.repetition_type === 'machine-type'
    );
    const hasFrequentialElements = selectedStudy.elements.some(e => 
      e.repetition_type === 'frequency-type' || e.repetition_type === 'frequency'
    );

    const buttons = [
      { to: 'info', text: t('study:info'), icon: Info },
      { to: 'method', text: t('study:method'), icon: ListTodo }
    ];

    if (hasRepetitiveElements) {
      buttons.push({ to: 'repetitive', text: t('study:chronometer'), icon: Timer });
    }

    // Cronómetro seguido siempre debe estar presente
    buttons.push({ to: 'cronoseguido', text: t('cronoSeguido:title'), icon: Watch });

    if (hasMachineElements) {
      buttons.push({ to: 'machine', text: t('study:machineTimes'), icon: Gauge });
    }

    if (hasFrequentialElements) {
      buttons.push({ to: 'frequency', text: t('study:frequencyTimes'), icon: Clock });
    }

    if (selectedStudy.elements.length > 0) {
      buttons.push({ to: 'supplements', text: t('study:supplements'), icon: UserPlus });
    }

    if (selectedStudy.elements.length > 0 && selectedStudy.elements.every(element => {
      const hasTimeRecords = selectedStudy.time_records[element.id]?.length > 0;
      const hasSupplements = selectedStudy.supplements[element.id]?.percentage > 0;
      return hasTimeRecords && hasSupplements;
    })) {
      buttons.push({ to: 'report', text: t('study:report'), icon: FileText });
    }

    return buttons;
  };

  const renderContent = () => {
    if (initializing) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      );
    }

    return (
      <div className="flex">
        <div className="flex-1 flex flex-col min-h-screen">
          <Routes>
            {/* Rutas públicas */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/update-password" element={<UpdatePassword />} />
            <Route path="/faq" element={<FAQ />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />

            {/* Rutas protegidas */}
            <Route path="/" element={
              <PrivateRoute>
                <StudyListPage />
              </PrivateRoute>
            } />

            <Route path="/study" element={
              <PrivateRoute>
                <StudyPage />
              </PrivateRoute>
            } />

            <Route path="/library" element={
              <PrivateRoute>
                <LibraryPage />
              </PrivateRoute>
            } />

            <Route path="/profile" element={
              <PrivateRoute>
                <ProfilePage />
              </PrivateRoute>
            } />

            <Route path="/docs" element={
              <PrivateRoute>
                <DocsPage />
              </PrivateRoute>
            } />

            <Route path="/budget" element={
              <PrivateRoute>
                <BudgetPage />
              </PrivateRoute>
            } />

            <Route path="/changelog" element={
              <PrivateRoute>
                <ChangelogPage />
              </PrivateRoute>
            } />

            <Route path="/gantt" element={
              <PrivateRoute>
                <GanttPage />
              </PrivateRoute>
            } />

            <Route path="/test-folders" element={
              <PrivateRoute>
                <TestFolders />
              </PrivateRoute>
            } />

            <Route path="/diagnostic-folders" element={
              <PrivateRoute>
                <DiagnosticFolders />
              </PrivateRoute>
            } />

            {/* Rutas de estudio específicas */}
            <Route path="/study/:studyId/*" element={
              <PrivateRoute>
                <Routes>
                  <Route path="info" element={<StudyPage />} />
                  <Route path="method" element={<MethodPage onBack={() => navigate('/')} />} />
                  <Route path="repetitive" element={<ChronometerPage onBack={() => navigate('/')} />} />
                  <Route path="frequency" element={<FrequencyPage onBack={() => navigate('/')} />} />
                  <Route path="cronoseguido" element={<CronoSeguidoPage onBack={() => navigate('/')} />} />
                  <Route path="machine" element={<MachinePage onBack={() => navigate('/')} />} />
                  <Route path="supplements" element={<SupplementsPage onBack={() => navigate('/')} />} />
                  <Route path="report" element={<ReportPage onBack={() => navigate('/')} />} />
                </Routes>
              </PrivateRoute>
            } />

            {/* Ruta por defecto - redirige a login si no está autenticado */}
            <Route path="*" element={
              <Navigate to="/login" replace />
            } />
          </Routes>
        </div>
      </div>
    );
  };

  return (
    <HelmetProvider>
      {renderContent()}
      <Toaster />
      <HotToaster 
        position="top-center"
        toastOptions={{
          style: {
            zIndex: 99999, // zIndex muy alto
          },
        }}
      />
      <UpdatePrompt />
      <InstallBanner />
      {showDeleteModal && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleConfirmDelete}
        />
      )}
    </HelmetProvider>
  );
}

export default App;
