import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock, Hash } from 'lucide-react';

interface StatsDisplayProps {
  totalTime: number;
  totalTakes: number;
}

export const StatsDisplay: React.FC<StatsDisplayProps> = ({
  totalTime,
  totalTakes
}) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6">
      <h3 className="text-lg font-semibold mb-4">{t('cronoSeguido.stats')}</h3>
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <Clock className="w-5 h-5 text-purple-600" />
          <div>
            <div className="text-sm text-gray-600">{t('cronoSeguido.totalTime')}</div>
            <div className="text-lg font-bold">{totalTime.toFixed(2)}s</div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Hash className="w-5 h-5 text-purple-600" />
          <div>
            <div className="text-sm text-gray-600">{t('cronoSeguido.totalTakes')}</div>
            <div className="text-lg font-bold">{totalTakes}</div>
          </div>
        </div>
      </div>
    </div>
  );
};