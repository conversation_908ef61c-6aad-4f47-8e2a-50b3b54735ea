# Security Policy

## Supported Versions

We currently support the following versions with security updates:

| Version | Supported          |
| ------- | ------------------ |
| 42.x.x  | :white_check_mark: |
| 41.x.x  | :white_check_mark: |
| 40.x.x  | :white_check_mark: |
| < 40.0  | :x:                |

## Reporting a Vulnerability

If you discover a security vulnerability, please follow these steps:

1. **Do not** disclose the vulnerability publicly
2. Send an <NAME_EMAIL> with:
   - Description of the vulnerability
   - Steps to reproduce
   - Potential impact
   - Your contact information

### What to Expect

Once a vulnerability is reported:

- You will receive an initial response within 48 hours
- Our team will investigate and evaluate the vulnerability
- We will keep you informed of progress
- If the vulnerability is accepted:
  - We will apply necessary patches within 48 hours
  - Your report will be acknowledged in our security records
- If the vulnerability is declined, we will provide a detailed explanation

## Security Features

Cronometras implements several security measures:

### Continuous Security Analysis
- Automated code analysis through Deepsource
- Dependency monitoring with Dependabot
- Regular security audits

### Patch and Vulnerability Management
- Critical patches applied within 48 hours
- Regular dependency updates

### Data Protection
- Data encryption in transit (HTTPS/TLS)
- Robust authentication through Supabase Auth
- Data storage in AWS-eu central region (Frankfurt)
- Compliance with European data protection regulations

### Infrastructure
- Services hosted on Supabase (SOC2 certified)
- Automatic daily backups
- Continuous monitoring and auditing

## Best Practices

We recommend following these security best practices:

1. Use strong, unique passwords
2. Enable two-factor authentication
3. Keep your browser updated
4. Log out when using shared devices
5. Regularly review your account activity

## Contact

For security-related inquiries:
- Email: <EMAIL>
- Response time: 48 hours maximum for critical issues

## Security Reports

- [Deepsource OWASP Top 10 Report](https://app.deepsource.com/report/8ce1ce4a-2819-4555-9c8b-c3732a948bfb/owasp-top-10)
