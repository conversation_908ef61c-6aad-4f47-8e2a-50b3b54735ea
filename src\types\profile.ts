import { TimeUnit } from './index';

export interface ChartVisibilitySettings {
  machineTypes: boolean;
  saturation: boolean;
  elements: boolean;
  supplements: boolean;
  cycleDistribution: boolean;
  flowchartSymbols: boolean;
  operationPercentages: boolean;
  operationCount: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  company_name: string | null;
  logo_url?: string;
  created_at: string;
  updated_at: string;
  default_time_unit: TimeUnit;
  default_language: string;
  default_contingency: number;
  minutes_per_shift: number;
  default_normal_activity?: number;
  default_optimal_activity?: number;
  show_shared_studies?: boolean;
  default_folder_id?: string | null;
  companies_list?: string[];
  default_company?: string | null;
  points_per_hour: number;
  chart_visibility?: ChartVisibilitySettings;
}
