import { create } from 'zustand';
import { supabase } from '../lib/supabase';

interface FactorSelection {
  value: number;
  selections?: number[];
  temperature?: number;
  humidity?: number;
}

interface Supplement {
  points: Record<string, number>;
  percentage: number;
  isForced: boolean;
  standingWork?: boolean;
  factorSelections: Record<string, FactorSelection>;
}

interface SupplementState {
  isLoading: boolean;
  error: string | null;
  saveSupplement: (elementId: string, supplement: Supplement) => Promise<void>;
  copySupplements: (fromElementId: string, toElementIds: string[]) => Promise<void>;
  resetSupplement: (elementId: string) => Promise<void>;
}

export const useSupplementStore = create<SupplementState>((set) => ({
  isLoading: false,
  error: null,

  saveSupplement: async (elementId: string, supplement: Supplement) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('study_elements')
        .update({ supplements: supplement })
        .eq('id', elementId);

      if (error) throw error;
      set({ isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  copySupplements: async (fromElementId: string, toElementIds: string[]) => {
    set({ isLoading: true, error: null });
    try {
      // Get source element supplements
      const { data: sourceElement } = await supabase
        .from('study_elements')
        .select('supplements')
        .eq('id', fromElementId)
        .single();

      if (!sourceElement?.supplements) throw new Error('Source supplements not found');

      // Update all target elements
      const { error } = await supabase
        .from('study_elements')
        .update({ supplements: sourceElement.supplements })
        .in('id', toElementIds);

      if (error) throw error;
      set({ isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  resetSupplement: async (elementId: string) => {
    set({ isLoading: true, error: null });
    try {
      const { error } = await supabase
        .from('study_elements')
        .update({ supplements: {} })
        .eq('id', elementId);

      if (error) throw error;
      set({ isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  }
}));