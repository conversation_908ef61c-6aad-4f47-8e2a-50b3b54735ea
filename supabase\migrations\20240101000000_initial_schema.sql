-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set up storage for public uploads
INSERT INTO storage.buckets (id, name) VALUES ('public', 'public') ON CONFLICT DO NOTHING;

-- Create tables
CREATE TABLE IF NOT EXISTS public.user_study_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    user_email TEXT NOT NULL,
    total_credits INTEGER NOT NULL DEFAULT 10,
    used_credits INTEGER NOT NULL DEFAULT 0,
    password_hash TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT credits_check CHECK (used_credits <= total_credits)
);

CREATE TABLE IF NOT EXISTS public.library_elements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    is_shared BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.studies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    required_info JSONB NOT NULL DEFAULT '{}'::jsonb,
    optional_info JSONB DEFAULT '{}'::jsonb,
    elements JSONB[] DEFAULT ARRAY[]::jsonb[],
    time_records JSONB DEFAULT '{}'::jsonb,
    supplements JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT required_fields_check CHECK (
        required_info::jsonb ? 'name' AND
        required_info::jsonb ? 'company' AND
        required_info::jsonb ? 'date' AND
        required_info::jsonb ? 'activity_scale'
    )
);

CREATE TABLE IF NOT EXISTS public.user_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_name TEXT NOT NULL,
    device_id TEXT NOT NULL UNIQUE,
    last_used TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_study_limits_user_id ON public.user_study_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_library_elements_user_id ON public.library_elements(user_id);
CREATE INDEX IF NOT EXISTS idx_studies_user_id ON public.studies(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);

-- Enable Row Level Security
ALTER TABLE public.user_study_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.library_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.studies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_study_limits
CREATE POLICY "Users can view their own study limits"
    ON public.user_study_limits
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admin full access"
    ON public.user_study_limits
    AS PERMISSIVE
    FOR ALL
    TO authenticated
    USING (
        auth.email() = '<EMAIL>' OR 
        auth.role() = 'service_role'
    )
    WITH CHECK (
        auth.email() = '<EMAIL>' OR 
        auth.role() = 'service_role'
    );

-- RLS Policies for library_elements
CREATE POLICY "Users can view their own and shared library elements"
    ON public.library_elements
    FOR SELECT
    TO authenticated
    USING ((user_id = auth.uid()) OR (is_shared = true));

CREATE POLICY "Users can insert their own library elements"
    ON public.library_elements
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own library elements"
    ON public.library_elements
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own library elements"
    ON public.library_elements
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- RLS Policies for studies
CREATE POLICY "Users can view their own studies"
    ON public.studies
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own studies"
    ON public.studies
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own studies"
    ON public.studies
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own studies"
    ON public.studies
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- RLS Policies for user_devices
CREATE POLICY "Users can view their own devices"
    ON public.user_devices
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can register their own devices"
    ON public.user_devices
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own devices"
    ON public.user_devices
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own devices"
    ON public.user_devices
    FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_user_study_limits_updated_at
    BEFORE UPDATE ON public.user_study_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_library_elements_updated_at
    BEFORE UPDATE ON public.library_elements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_studies_updated_at
    BEFORE UPDATE ON public.studies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_devices_updated_at
    BEFORE UPDATE ON public.user_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE public.user_study_limits IS 'Stores user credit limits for time studies';
COMMENT ON TABLE public.library_elements IS 'Stores reusable elements for time studies';
COMMENT ON TABLE public.studies IS 'Stores time study data with JSON structure';
COMMENT ON TABLE public.user_devices IS 'Stores registered user devices';

-- Tabla de perfiles de usuario
CREATE TABLE profiles (
  id uuid PRIMARY KEY,
  email text NOT NULL,
  company_name text,
  logo_url text,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  default_time_unit text DEFAULT 'seconds' NOT NULL,
  default_language text DEFAULT 'es' NOT NULL,
  default_contingency numeric DEFAULT 0 NOT NULL,
  minutes_per_shift integer DEFAULT 480 NOT NULL,
  default_normal_activity integer DEFAULT 100,
  default_optimal_activity integer DEFAULT 133,
  show_shared_studies boolean DEFAULT false,
  default_folder_id uuid,
  companies_list text[],
  default_company text,
  points_per_hour integer DEFAULT 100 NOT NULL
);
