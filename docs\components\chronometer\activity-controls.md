# Activity Controls Component

## Descripción
Controles para ajustar la actividad durante la medición.

## Props
```typescript
interface ActivityControlsProps {
  activity: number;
  normalActivity: number;
  optimalActivity: number;
  onChange: (increment: boolean) => void;
  disabled?: boolean;
}
```

## Características
- Botones de incremento/decremento
- Validación de rangos
- Ajuste por pasos de 5
- Límites basados en actividad normal/óptima
- Feedback visual del rango

## Uso
```tsx
<ActivityControls
  activity={activity}
  normalActivity={100}
  optimalActivity={133}
  onChange={handleActivityChange}
  disabled={!isRunning}
/>
```