# Touch Feedback Component

## Descripción
Componente wrapper que proporciona feedback táctil y visual para interacciones móviles.

## Props
```typescript
interface TouchFeedbackProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  activeClassName?: string;
  disabled?: boolean;
}
```

## Características
- Feedback visual en toque
- Prevención de doble toque
- Soporte para dispositivos táctiles
- Manejo de estados deshabilitados
- Personalización de estilos activos

## Estados
- Normal
- Activo (tocando)
- Deshabilitado

## Uso
```tsx
<TouchFeedback
  onClick={handleClick}
  activeClassName="opacity-70"
  disabled={false}
>
  <button>Click me</button>
</TouchFeedback>
```