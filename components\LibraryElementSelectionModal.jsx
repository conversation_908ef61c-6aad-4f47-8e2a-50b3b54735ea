const handleElementSelection = (element) => {
  // Crear una copia profunda del elemento seleccionado para asegurar que todos los datos se transfieran
  const elementWithAllData = JSON.parse(JSON.stringify(element));
  
  // Asegurarse de que los registros de tiempos se incluyan
  if (element.timeRecords) {
    elementWithAllData.timeRecords = [...element.timeRecords];
  }
  
  // Asegurarse de que los suplementos se incluyan
  if (element.supplements) {
    elementWithAllData.supplements = [...element.supplements];
  }
  
  // Llama a la función onSelect con la copia completa del elemento
  onSelect(elementWithAllData);
  
  // Cerrar el modal después de la selección
  setIsOpen(false);
}; 