import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CronoSeguidoRecord } from '../types/index';
import { useStudyStore } from '../store/studyStore';
import { Check, Settings, Plus, Minus, Clock, Activity, Trash2, CheckCircle2, XCircle, CheckSquare, Eye, ChevronRight, ArrowLeft } from 'lucide-react';
import { getFlowchartSymbols, FlowchartSymbolIcon, FlowchartSymbol } from './FlowchartSymbols';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Card } from './ui/enhanced-card';
import { cn } from '../lib/utils';

interface ElementConfiguration {
  isNewElement: boolean;
  elementId?: string;
  type: string;
  repetitionType: string;
  frequencyRepetitions: number;
  frequencyCycles: number;
  flowchartSymbol?: FlowchartSymbol;
}

interface BulkAddToMethodConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: { recordsToAdd: any[], recordsToRemove: CronoSeguidoRecord[] }) => void;
  records: CronoSeguidoRecord[];
  loading?: boolean;
}

export const BulkAddToMethodConfigModal: React.FC<BulkAddToMethodConfigModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  records,
  loading = false,
}) => {
  const { t } = useTranslation(['cronoSeguido', 'method', 'common']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  
  // Estado para elementos seleccionados y configurados
  const [selectedRecords, setSelectedRecords] = useState<Set<string>>(new Set());
  const [configuredRecords, setConfiguredRecords] = useState<Record<string, ElementConfiguration>>({});
  const [elementsToRemove, setElementsToRemove] = useState<Set<string>>(new Set());
  
  // Estado para el panel de configuración
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<ElementConfiguration>({
    isNewElement: true,
    type: 'machine-stopped',
    repetitionType: 'repetitive',
    frequencyRepetitions: 1,
    frequencyCycles: 1,
    flowchartSymbol: undefined
  });

  // Estados para navegación por pasos en móvil
  const [currentStep, setCurrentStep] = useState<'selection' | 'configuration' | 'review'>('selection');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isOpen) {
      // Reset state when modal opens
      setSelectedRecords(new Set());
      setConfiguredRecords({});
      setElementsToRemove(new Set());
      setShowConfigPanel(false);
      setCurrentStep('selection');
    }
  }, [isOpen]);

  const handleToggleSelect = (recordId: string) => {
    const record = records.find(r => r.id === recordId);
    if (!record) return;

    // Si el elemento ya está configurado en esta sesión, no permitir cambiarlo
    if (configuredRecords[recordId]) return;

    const newSelection = new Set(selectedRecords);
    const newElementsToRemove = new Set(elementsToRemove);

    if (record.addedToMethod) {
      // Si está agregado al método, manejar como "remover"
      if (newElementsToRemove.has(recordId)) {
        newElementsToRemove.delete(recordId);
      } else {
        newElementsToRemove.add(recordId);
      }
      setElementsToRemove(newElementsToRemove);
    } else {
      // Si no está agregado, manejar como selección normal
      if (newSelection.has(recordId)) {
        newSelection.delete(recordId);
      } else {
        newSelection.add(recordId);
      }
      setSelectedRecords(newSelection);
    }
  };

  const handleSelectAll = () => {
    // Seleccionar todos los elementos no configurados
    const unconfiguredRecords = records.filter(record => !configuredRecords[record.id]);
    const notAddedRecords = unconfiguredRecords.filter(record => !record.addedToMethod);
    const addedRecords = unconfiguredRecords.filter(record => record.addedToMethod);
    
    setSelectedRecords(new Set(notAddedRecords.map(r => r.id)));
    setElementsToRemove(new Set(addedRecords.map(r => r.id)));
  };

  const handleDeselectAll = () => {
    setSelectedRecords(new Set());
    setElementsToRemove(new Set());
  };

  const handleApplyConfiguration = () => {
    // Aplicar la configuración actual a todos los elementos seleccionados
    const newConfiguredRecords = { ...configuredRecords };
    selectedRecords.forEach(recordId => {
      newConfiguredRecords[recordId] = { ...currentConfig };
    });
    
    setConfiguredRecords(newConfiguredRecords);
    setSelectedRecords(new Set()); // Limpiar selección
    setShowConfigPanel(false);
    
    // Si estamos en móvil y hay elementos configurados, ir automáticamente a revisión
    if (isMobile && Object.keys(newConfiguredRecords).length > 0) {
      setCurrentStep('review');
    }
  };

  const handleRemoveConfiguration = (recordId: string) => {
    const newConfiguredRecords = { ...configuredRecords };
    delete newConfiguredRecords[recordId];
    setConfiguredRecords(newConfiguredRecords);
  };

  const handleReconfigureAllElements = () => {
    // Seleccionar todos los elementos configurados para reconfiguración
    const configuredIds = Object.keys(configuredRecords);
    setSelectedRecords(new Set(configuredIds));
    
    // Usar la configuración del primer elemento como base
    const firstElementConfig = Object.values(configuredRecords)[0];
    if (firstElementConfig) {
      setCurrentConfig(firstElementConfig);
    }
    
    // Limpiar configuraciones para permitir reconfiguración
    setConfiguredRecords({});
    
    // Ir al panel de configuración
    if (isMobile) {
      setCurrentStep('configuration');
    }
    setShowConfigPanel(true);
  };

  const handleConfirm = () => {
    // Crear array con todos los elementos configurados en el orden original
    const recordsToAdd = records
      .filter(record => configuredRecords[record.id])
      .map(record => ({
        ...record,
        config: configuredRecords[record.id]
      }));

    // Array de elementos a eliminar
    const recordsToRemove = Array.from(elementsToRemove).map(id => 
      records.find(record => record.id === id)!
    );
    
    onConfirm({ recordsToAdd, recordsToRemove });
  };

  const getConfiguredCount = () => Object.keys(configuredRecords).length;
  const getSelectedCount = () => selectedRecords.size;
  const getToRemoveCount = () => elementsToRemove.size;

  // Funciones para navegación por pasos
  const goToConfiguration = () => {
    if (selectedRecords.size > 0) {
      setCurrentStep('configuration');
      setShowConfigPanel(true);
    }
  };

  const goToReview = () => {
    setCurrentStep('review');
    setShowConfigPanel(false);
  };

  const goBackToSelection = () => {
    setCurrentStep('selection');
    setShowConfigPanel(false);
  };

  const goBackToConfiguration = () => {
    setCurrentStep('configuration');
    setShowConfigPanel(true);
  };

  // Verificar si puede avanzar al siguiente paso
  const canGoToConfiguration = () => selectedRecords.size > 0;
  const canGoToReview = () => getConfiguredCount() > 0 || getToRemoveCount() > 0;
  const hasAnyAction = () => getConfiguredCount() > 0 || getToRemoveCount() > 0;

  // Componente para mostrar los pasos en móvil
  const StepIndicator = () => {
    if (!isMobile) return null;

    // Si solo hay eliminaciones, saltar el paso de configuración
    const hasOnlyRemovals = getToRemoveCount() > 0 && getSelectedCount() === 0;
    
    const steps = hasOnlyRemovals ? [
      { id: 'selection', label: 'Seleccionar', icon: CheckSquare },
      { id: 'review', label: 'Revisar', icon: Eye }
    ] : [
      { id: 'selection', label: 'Seleccionar', icon: CheckSquare },
      { id: 'configuration', label: 'Configurar', icon: Settings },
      { id: 'review', label: 'Revisar', icon: Eye }
    ];

    return (
      <div className="flex items-center justify-center py-4 bg-gray-50 border-b">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = currentStep === step.id;
          const isCompleted = 
            (step.id === 'selection' && (getSelectedCount() > 0 || getConfiguredCount() > 0)) ||
            (step.id === 'configuration' && getConfiguredCount() > 0) ||
            (step.id === 'review' && false); // El último paso nunca está "completado"

          return (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                isActive 
                  ? 'bg-blue-100 text-blue-700' 
                  : isCompleted 
                  ? 'bg-green-100 text-green-700'
                  : 'text-gray-500'
              }`}>
                <Icon className="h-4 w-4" />
                <span className="text-sm font-medium">{step.label}</span>
              </div>
              {index < steps.length - 1 && (
                <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl w-[98vw] lg:w-[95vw] h-[95vh] lg:h-[90vh] p-0 gap-0" showCloseButton={false}>
        <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-bold text-gray-900">
                {t('bulkAddToMethod', { ns: 'cronoSeguido' })}
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                Configuración masiva de elementos para el método
              </p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="rounded-full"
            >
              <XCircle className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>

        <StepIndicator />

        <div className={`flex flex-1 min-h-0 ${isMobile ? 'flex-col' : 'flex-col lg:flex-row'}`}>
          {/* Panel izquierdo: Lista de elementos */}
          <div className={`${
            isMobile 
              ? (currentStep === 'selection' ? 'flex' : 'hidden') + ' flex-col h-full'
              : 'h-1/2 lg:h-full lg:flex-none lg:w-1/2 xl:w-3/5 flex flex-col'
          } lg:border-r border-gray-200 min-h-0`}>
            <div className="p-4 border-b bg-gray-50 flex-shrink-0">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Elementos disponibles
                  </h3>
                  <p className="text-xs text-gray-600">
                    {getConfiguredCount()} configurados, {getToRemoveCount()} a eliminar de {records.length} totales
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAll}
                    disabled={records.filter(r => !configuredRecords[r.id]).length === 0}
                    className="text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Todos
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeselectAll}
                    disabled={selectedRecords.size === 0 && elementsToRemove.size === 0}
                    className="text-xs"
                  >
                    <Minus className="h-3 w-3 mr-1" />
                    Ninguno
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {records.map(record => {
                const isConfigured = !!configuredRecords[record.id];
                const isSelected = selectedRecords.has(record.id);
                const isToRemove = elementsToRemove.has(record.id);
                const isAddedToMethod = record.addedToMethod;
                
                return (
                  <Card
                    key={record.id}
                    variant="outlined"
                    hover
                    interactive
                    onClick={() => handleToggleSelect(record.id)}
                    className={cn(
                      "cursor-pointer transition-all duration-200",
                      isConfigured && "border-green-400 bg-green-50 shadow-green-100",
                      isToRemove && "border-red-400 bg-red-50 shadow-red-100",
                      isSelected && "border-blue-400 bg-blue-50 shadow-blue-100",
                      isAddedToMethod && !isConfigured && !isToRemove && "border-yellow-400 bg-yellow-50 shadow-yellow-100",
                      !isConfigured && !isToRemove && !isSelected && !isAddedToMethod && "hover:border-gray-300"
                    )}
                  >
                    <div className="flex items-center space-x-4 p-4">
                      {/* Checkbox personalizado */}
                      <div className="flex-shrink-0">
                        {isConfigured ? (
                          <CheckCircle2 className="h-5 w-5 text-green-600" />
                        ) : (
                          <div 
                            className={cn(
                              "h-5 w-5 rounded border-2 flex items-center justify-center transition-colors",
                              (isSelected || isToRemove) ? "border-blue-500 bg-blue-500" : "border-gray-300"
                            )}
                          >
                            {(isSelected || isToRemove) && <Check className="h-3 w-3 text-white" />}
                          </div>
                        )}
                      </div>

                      {/* Contenido principal */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900 truncate">
                            {record.description || 'Registro sin descripción'}
                          </h4>
                          {isAddedToMethod && !isConfigured && (
                            <span className={cn(
                              "text-xs px-2 py-1 rounded-full font-medium",
                              isToRemove ? "bg-red-100 text-red-700" : "bg-yellow-100 text-yellow-700"
                            )}>
                              {isToRemove ? 'A eliminar' : 'En método'}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{(record.time / 1000).toFixed(2)}s</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Activity className="h-3 w-3" />
                            <span>{record.activity}%</span>
                          </div>
                        </div>

                        {isConfigured && (
                          <div className="mt-2 flex items-center gap-2 text-xs text-green-700">
                            <span>
                              ✓ {configuredRecords[record.id].isNewElement ? 'Nuevo' : 'Existente'} - 
                              {t(`typesLong.${configuredRecords[record.id].type}`, { ns: 'method' })} - 
                              {t(`types.${configuredRecords[record.id].repetitionType}`, { ns: 'method' })}
                            </span>
                            {configuredRecords[record.id].flowchartSymbol && (
                              <div className="flex items-center gap-1">
                                <FlowchartSymbolIcon 
                                  symbol={configuredRecords[record.id].flowchartSymbol!} 
                                  className="text-green-600" 
                                  size={12} 
                                />
                                <span>
                                  {t(`flowchartSymbols.${configuredRecords[record.id].flowchartSymbol}.name`, { ns: 'cronoSeguido' })}
                                </span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Acciones */}
                      <div className="flex-shrink-0">
                        {isConfigured && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveConfiguration(record.id);
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            {/* Navegación del paso de selección en móvil */}
            {isMobile && currentStep === 'selection' && (
              <div className="p-4 border-t bg-gray-50 flex-shrink-0 space-y-3">
                {getSelectedCount() > 0 && (
                  <Button
                    onClick={goToConfiguration}
                    disabled={!canGoToConfiguration()}
                    className="w-full gap-2"
                    size="lg"
                  >
                    Configurar {getSelectedCount()} elemento(s)
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                )}
                
                {getToRemoveCount() > 0 && (
                  <Button
                    onClick={goToReview}
                    disabled={!canGoToReview()}
                    variant="destructive"
                    className="w-full gap-2"
                    size="lg"
                  >
                    Eliminar {getToRemoveCount()} elemento(s)
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                )}

                {getConfiguredCount() > 0 && (
                  <Button
                    onClick={goToReview}
                    variant="outline"
                    className="w-full gap-2"
                    size="lg"
                  >
                    Revisar {getConfiguredCount()} configurado(s)
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                )}

                {!hasAnyAction() && getSelectedCount() === 0 && getToRemoveCount() === 0 && (
                  <div className="text-center text-gray-500 py-4">
                    <p className="text-sm">Selecciona elementos para continuar</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Panel derecho: Configuración */}
          <div className={`${
            isMobile 
              ? (currentStep === 'configuration' ? 'flex' : 'hidden') + ' flex-col h-full'
              : 'h-1/2 lg:h-full lg:flex-none lg:w-1/2 xl:w-2/5 flex flex-col'
          } border-t lg:border-t-0 border-gray-200 min-h-0`}>
            <div className="p-4 border-b bg-gray-50 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={goBackToSelection}
                      className="gap-2"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Volver
                    </Button>
                  )}
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Panel de configuración
                    </h3>
                    <p className="text-xs text-gray-600">
                      {getSelectedCount()} para agregar{getToRemoveCount() > 0 ? `, ${getToRemoveCount()} para eliminar` : ''}
                    </p>
                  </div>
                </div>
                {!isMobile && (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => setShowConfigPanel(!showConfigPanel)}
                    disabled={selectedRecords.size === 0}
                    className="gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    Configurar
                  </Button>
                )}
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4 min-h-0">
              {(isMobile || showConfigPanel) && selectedRecords.size > 0 && (
                <Card variant="elevated" className="space-y-4 h-fit">
                  <div className="p-4">
                    <p className="text-sm text-gray-600 mb-4">
                      Aplicar esta configuración a {getSelectedCount()} elemento(s) seleccionado(s)
                    </p>

                    {/* Tipo de elemento */}
                    <div className="space-y-2 mb-4">
                      <label className="text-sm font-medium text-gray-900">
                        {t('elementType', { ns: 'cronoSeguido' })}
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant={currentConfig.isNewElement ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentConfig({...currentConfig, isNewElement: true})}
                          className="justify-center"
                        >
                          {t('newElement', { ns: 'cronoSeguido' })}
                        </Button>
                        <Button
                          variant={!currentConfig.isNewElement ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentConfig({...currentConfig, isNewElement: false})}
                          className="justify-center"
                        >
                          {t('existingElement', { ns: 'cronoSeguido' })}
                        </Button>
                      </div>
                    </div>

                    {currentConfig.isNewElement ? (
                      <>
                        {/* Tipo de relación hombre-máquina */}
                        <div className="space-y-2 mb-4">
                          <label className="text-sm font-medium text-gray-900">
                            {t('type', { ns: 'method' })}
                          </label>
                          <select
                            value={currentConfig.type}
                            onChange={(e) => setCurrentConfig({...currentConfig, type: e.target.value})}
                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                            title={t('type', { ns: 'method' })}
                          >
                            <option value="machine-stopped">{t('typesLong.machine-stopped', { ns: 'method' })}</option>
                            <option value="machine-running">{t('typesLong.machine-running', { ns: 'method' })}</option>
                            <option value="machine-time">{t('typesLong.machine-time', { ns: 'method' })}</option>
                          </select>
                        </div>

                        {/* Tipo de regularidad */}
                        <div className="space-y-2 mb-4">
                          <label className="text-sm font-medium text-gray-900">
                            {t('repetitionType', { ns: 'method' })}
                          </label>
                          <select
                            value={currentConfig.repetitionType}
                            onChange={(e) => setCurrentConfig({...currentConfig, repetitionType: e.target.value})}
                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                            title={t('repetitionType', { ns: 'method' })}
                          >
                            <option value="repetitive">{t('types.repetitive', { ns: 'method' })}</option>
                            <option value="frequency">{t('types.frequency', { ns: 'method' })}</option>
                            <option value="machine">{t('types.machine', { ns: 'method' })}</option>
                          </select>
                        </div>

                        {/* Frecuencia */}
                        <div className="grid grid-cols-2 gap-3 mb-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-900">
                              {t('repetitions', { ns: 'method' })}
                            </label>
                            <input
                              type="number"
                              min="1"
                              value={currentConfig.frequencyRepetitions}
                              onChange={(e) => setCurrentConfig({
                                ...currentConfig, 
                                frequencyRepetitions: e.target.value ? parseInt(e.target.value) : 1
                              })}
                              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                              title={t('repetitions', { ns: 'method' })}
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-900">
                              {t('cycles', { ns: 'method' })}
                            </label>
                            <input
                              type="number"
                              min="1"
                              value={currentConfig.frequencyCycles}
                              onChange={(e) => setCurrentConfig({
                                ...currentConfig, 
                                frequencyCycles: e.target.value ? parseInt(e.target.value) : 1
                              })}
                              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                              title={t('cycles', { ns: 'method' })}
                            />
                          </div>
                        </div>

                        {/* Símbolo de diagrama de flujo */}
                        <div className="space-y-2 mb-4">
                          <label className="text-sm font-medium text-gray-900">
                            {t('flowchartSymbol', { ns: 'cronoSeguido' })}
                          </label>
                          <div className="grid grid-cols-3 gap-2 mb-2">
                            {getFlowchartSymbols((key: string, options?: any) => 
                              String(t(key, { ...options, ns: 'cronoSeguido' }))
                            ).map((symbol) => (
                              <Button
                                key={symbol.id}
                                variant={currentConfig.flowchartSymbol === symbol.id ? "default" : "outline"}
                                size="sm"
                                onClick={() => setCurrentConfig({...currentConfig, flowchartSymbol: symbol.id})}
                                className="flex flex-col items-center p-2 h-auto"
                                title={symbol.description}
                              >
                                <FlowchartSymbolIcon symbol={symbol.id} className="mb-1" size={16} />
                                <span className="text-xs">{symbol.name}</span>
                              </Button>
                            ))}
                          </div>
                          <Button
                            variant={!currentConfig.flowchartSymbol ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentConfig({...currentConfig, flowchartSymbol: undefined})}
                            className="w-full"
                          >
                            {t('noSymbol', { ns: 'cronoSeguido' })}
                          </Button>
                          
                          {currentConfig.flowchartSymbol && (
                            <div className="mt-2 p-3 bg-blue-50 rounded-md">
                              <div className="flex items-center gap-2 mb-1">
                                <FlowchartSymbolIcon symbol={currentConfig.flowchartSymbol} className="text-blue-600" size={16} />
                                <span className="font-medium text-sm text-blue-900">
                                  {getFlowchartSymbols((key: string, options?: any) => 
                                    String(t(key, { ...options, ns: 'cronoSeguido' }))
                                  ).find(s => s.id === currentConfig.flowchartSymbol)?.name}
                                </span>
                              </div>
                              <p className="text-xs text-blue-700">
                                {getFlowchartSymbols((key: string, options?: any) => 
                                  String(t(key, { ...options, ns: 'cronoSeguido' }))
                                ).find(s => s.id === currentConfig.flowchartSymbol)?.description}
                              </p>
                            </div>
                          )}
                        </div>
                      </>
                    ) : (
                      <div className="space-y-2 mb-4">
                        <label className="text-sm font-medium text-gray-900">
                          {t('selectElement', { ns: 'cronoSeguido' })}
                        </label>
                        <select
                          value={currentConfig.elementId || ''}
                          onChange={(e) => setCurrentConfig({...currentConfig, elementId: e.target.value})}
                          className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                          title={t('selectElement', { ns: 'cronoSeguido' })}
                        >
                          <option value="">{t('select', { ns: 'common' })}</option>
                          {selectedStudy?.elements?.map((element: any) => (
                            <option key={element.id} value={element.id}>
                              {element.description}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}

                    <div className="space-y-3">
                      <Button
                        onClick={handleApplyConfiguration}
                        disabled={!currentConfig.isNewElement && !currentConfig.elementId}
                        className="w-full gap-2"
                      >
                        <CheckCircle2 className="h-4 w-4" />
                        Aplicar configuración a {getSelectedCount()} elemento(s)
                      </Button>
                    </div>
                  </div>
                </Card>
              )}

              {!(isMobile || showConfigPanel) && (
                <div className="text-center text-gray-500 mt-8">
                  <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <div className="space-y-2">
                    <p className="text-sm">Selecciona elementos para mostrar las opciones de configuración</p>
                    {getConfiguredCount() > 0 && (
                      <div className="space-y-1">
                        <p className="text-xs text-blue-600">
                          {getConfiguredCount()} elemento(s) ya configurado(s)
                        </p>
                        <p className="text-xs text-gray-400">
                          Ve al panel de revisión para reconfigurar todos los elementos
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {isMobile && selectedRecords.size === 0 && getConfiguredCount() > 0 && (
                <div className="text-center text-blue-600 mt-8">
                  <CheckCircle2 className="h-12 w-12 mx-auto mb-4" />
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Configuración completada</p>
                    <p className="text-xs">
                      {getConfiguredCount()} elemento(s) configurado(s)
                    </p>
                    <p className="text-xs text-gray-600 mt-2">
                      Selecciona más elementos para configurar o ve a revisar para reconfigurar todos
                    </p>
                    <Button
                      onClick={goToReview}
                      variant="outline"
                      className="mt-4 gap-2"
                    >
                      Revisar elementos configurados
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Panel de revisión (solo móvil) */}
          {isMobile && currentStep === 'review' && (
            <div className="flex flex-col h-full min-h-0">
              <div className="p-4 border-b bg-gray-50 flex-shrink-0">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goBackToSelection}
                    className="gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Volver
                  </Button>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Revisar cambios
                    </h3>
                    <p className="text-xs text-gray-600">
                      {getConfiguredCount()} para agregar{getToRemoveCount() > 0 ? `, ${getToRemoveCount()} para eliminar` : ''}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {getConfiguredCount() > 0 && (
                  <Card variant="elevated">
                    <div className="p-4">
                      <h4 className="font-semibold text-green-700 mb-3 flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Elementos a agregar ({getConfiguredCount()})
                      </h4>
                      <div className="space-y-3">
                        {Object.entries(configuredRecords).map(([recordId, config]) => {
                          const record = records.find(r => r.id === recordId);
                          if (!record) return null;
                          
                          return (
                            <div key={recordId} className="p-3 bg-green-50 rounded-lg border border-green-200">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h5 className="font-medium text-green-900 mb-1">
                                    {record.description || 'Registro sin descripción'}
                                  </h5>
                                  <div className="text-xs text-green-700 space-y-1">
                                    <div>
                                      {config.isNewElement ? 'Nuevo elemento' : 'Elemento existente'} - 
                                      {t(`typesLong.${config.type}`, { ns: 'method' })} - 
                                      {t(`types.${config.repetitionType}`, { ns: 'method' })}
                                    </div>
                                    <div>
                                      Frecuencia: {config.frequencyRepetitions}/{config.frequencyCycles}
                                    </div>
                                    {config.flowchartSymbol && (
                                      <div className="flex items-center gap-1">
                                        <FlowchartSymbolIcon symbol={config.flowchartSymbol} size={12} />
                                        <span>
                                          {t(`flowchartSymbols.${config.flowchartSymbol}.name`, { ns: 'cronoSeguido' })}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </Card>
                )}

                {getToRemoveCount() > 0 && (
                  <Card variant="elevated">
                    <div className="p-4">
                      <h4 className="font-semibold text-red-700 mb-3 flex items-center gap-2">
                        <Minus className="h-4 w-4" />
                        Elementos a eliminar ({getToRemoveCount()})
                      </h4>
                      <div className="space-y-3">
                        {Array.from(elementsToRemove).map(recordId => {
                          const record = records.find(r => r.id === recordId);
                          if (!record) return null;
                          
                          return (
                            <div key={recordId} className="p-3 bg-red-50 rounded-lg border border-red-200">
                              <h5 className="font-medium text-red-900 mb-1">
                                {record.description || 'Registro sin descripción'}
                              </h5>
                              <div className="text-xs text-red-700">
                                Tiempo: {(record.time / 1000).toFixed(2)}s - Actividad: {record.activity}%
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </Card>
                )}
              </div>

              <div className="p-4 border-t bg-gray-50 flex-shrink-0 space-y-3">
                {getConfiguredCount() > 0 && (
                  <Button
                    onClick={handleReconfigureAllElements}
                    variant="outline"
                    className="w-full gap-2"
                    size="lg"
                  >
                    <Settings className="h-4 w-4" />
                    Reconfigurar {getConfiguredCount()} elemento(s)
                  </Button>
                )}
                
                <Button
                  onClick={handleConfirm}
                  disabled={loading || (getConfiguredCount() === 0 && getToRemoveCount() === 0)}
                  className="w-full gap-2"
                  size="lg"
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Procesando...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="h-4 w-4" />
                      {getConfiguredCount() > 0 && getToRemoveCount() > 0 
                        ? `Agregar ${getConfiguredCount()} y eliminar ${getToRemoveCount()}`
                        : getConfiguredCount() > 0 
                        ? `Agregar ${getConfiguredCount()} elemento(s)`
                        : `Eliminar ${getToRemoveCount()} elemento(s)`}
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Footer (solo desktop) */}
        {!isMobile && (
          <div className="px-6 py-4 border-t bg-gray-50 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 flex-shrink-0">
          <div className="text-sm text-gray-600">
            {getConfiguredCount()} configurados, {getToRemoveCount()} a eliminar de {records.length} elementos totales
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              {t('cancel', { ns: 'common' })}
            </Button>
            {getConfiguredCount() > 0 && (
              <Button
                onClick={handleReconfigureAllElements}
                variant="outline"
                disabled={loading}
                className="gap-2"
              >
                <Settings className="h-4 w-4" />
                Reconfigurar {getConfiguredCount()}
              </Button>
            )}
            <Button
              onClick={handleConfirm}
              disabled={loading || (getConfiguredCount() === 0 && getToRemoveCount() === 0)}
              className="gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  {t('loading', { ns: 'common' })}
                </>
              ) : (
                <>
                  <CheckCircle2 className="h-4 w-4" />
                  {getConfiguredCount() > 0 && getToRemoveCount() > 0 
                    ? `Agregar ${getConfiguredCount()} y eliminar ${getToRemoveCount()}`
                    : getConfiguredCount() > 0 
                    ? `Agregar ${getConfiguredCount()} elemento(s)`
                    : `Eliminar ${getToRemoveCount()} elemento(s)`}
                </>
              )}
            </Button>
          </div>
        </div>
        )}
      </DialogContent>
    </Dialog>
  );
}; 