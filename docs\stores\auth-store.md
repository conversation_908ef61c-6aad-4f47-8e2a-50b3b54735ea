# Auth Store

## Descripción
Gestiona el estado de autenticación y control de dispositivos.

## Estado
```typescript
interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}
```

## Funcionalidades

### 1. Autenticación
- Inicio de sesión con Supabase Auth
- Cierre de sesión
- Persistencia de sesión
- Manejo de errores

### 2. Control de Dispositivos
- Verificación de dispositivo registrado
- Registro automático del primer dispositivo
- Validación de licencia

## Uso
```typescript
const { user, signIn, signOut } = useAuthStore();

// Iniciar sesión
await signIn(email, password);

// Cerrar sesión
await signOut();
```