export default {
  title: 'Profile',
  userInfo: {
    title: 'User Information',
    description: 'Basic information about your account',
    email: 'Email',
    registeredAt: 'Registration date',
    subscriptionStatus: 'Subscription status'
  },
  profile: {
    title: 'Profile Settings',
    description: 'Customize your profile and preferences',
    logo: 'Logo',
    companyLogo: 'Company Logo',
    preferences: 'Preferences'
  },

  basicInfo: 'Basic Information',
  companyName: 'Company Name',
  companyLogo: 'Company Logo',
  joinDate: 'Join Date',
  subscriptionStatus: 'Subscription Status',
  validUntil: 'Valid until',
  cancelSubscription: 'Cancel Subscription',
  preferences: {
    title: 'Preferences',
    description: 'Customize the application to your needs',
    timeAndUnitsSection: 'Time and Units Configuration',
    languageSection: 'Language Configuration',
    studiesSection: 'Studies Configuration',
    companiesSection: 'Companies/Departments Management',
    foldersSection: 'Folders Configuration',
    defaultTimeUnit: 'Default Time Unit',
    defaultLanguage: 'Default Language',
    defaultContingency: 'Default Contingency Percentage',
    minutesPerShift: 'Minutes per Shift',
    pointsPerHour: 'Points per Hour',
    pointsPerHourHelp: 'Points per hour equivalence for calculations (e.g: 100 points = 1 hour)',
    defaultActivityScale: 'Default Activity Scale',
    normalActivity: 'Normal Activity',
    optimalActivity: 'Optimal Activity',
    updateSuccess: 'Preferences Updated',
    updateSuccessMessage: 'Your preferences have been updated successfully',
    updateError: 'Update Error',
    updateErrorMessage: 'An error occurred while updating your preferences',
    companiesList: 'Companies / Departments',
    noCompanies: 'No companies/departments saved',
    addCompanyPlaceholder: 'Add company/department...',
    addCompany: 'Add company',
    removeCompany: 'Remove company',
    add: 'Add',
    defaultCompany: 'Default Company/Department',
    noDefaultCompany: 'None (type manually)',
    defaultCompanyHelp: 'This company will be automatically selected in new studies',
    defaultFolder: 'Default folder when opening the app',
    defaultFolderHelp: 'This folder will automatically open when starting the application'
  },
  units: {
    seconds: 'Seconds',
    minutes: 'Minutes',
    mmm: 'MMM (Thousandth of Minutes)',
    cmm: 'CMM (Centesimal Minutes)',
    tmu: 'TMU (Time Measurement Unit)',
    dmh: 'DMH (Ten-thousandths of an Hour)'
  },
  subscription: {
    title: 'Subscription',
    status: 'Subscription Status',
    active: 'Active subscription',
    inactive: 'No active subscription',
    request: 'Request subscription',
    cancel: 'Cancel subscription',
    validUntil: 'Valid Until',
    requestInfo: 'Request Information',
    noActiveSubscription: 'You don\'t have an active subscription'
  },
  logoUpload: {
    upload: 'Upload Logo',
    uploading: 'Uploading...',
    remove: 'Remove Logo',
    invalidType: 'Please select a valid image',
    error: 'Error uploading image',
    optimizationError: 'Error optimizing image',
    notAuthenticated: 'You must be logged in to upload images',
    permissionDenied: 'You don\'t have permission to perform this action',
    removeError: 'Error removing image',
    dragAndDrop: 'Drag and drop a file here, or click to select one'
  },
  credits: {
    title: 'Credits',
    monthly: 'Monthly',
    extra: 'Extra',
    used: 'Used',
    available: 'Available',
    total: 'Total',
    nextReset: 'Next Reset',
    buyCredits: 'Buy Credits',
    unlimited: 'Unlimited Credits',
    unlimitedDescription: 'With your active subscription, you have unlimited access to all studies.',
    validUntil: 'Valid until',
    buy: 'Buy Credits'
  },
  updateSuccess: 'Profile updated successfully',
  updateError: 'Error updating profile',
  logoUpdateSuccess: 'Logo updated successfully',
  logoUpdateError: 'Error updating logo',
  noProfile: 'No profile found',
  pleaseLogin: 'Please log in to view your profile',
  retry: 'Retry',
  errors: {
    fetch: 'Error fetching user data',
    update: 'Error updating profile'
  },
  changePassword: {
    title: 'Change Password',
    description: 'Keep your account secure',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm New Password',
    submit: 'Change Password',
    passwordMismatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 6 characters long',
    passwordUpdateSuccess: 'Password updated successfully',
    passwordUpdateError: 'Error updating password',
    invalidCurrentPassword: 'Current password is incorrect',
    currentPasswordHint: 'Enter your current password to verify your identity',
    newPasswordSection: 'New Password',
    newPasswordHint: 'Minimum 6 characters',
    confirmPasswordHint: 'Repeat the new password'
  }
} as const;
