import React from 'react';
import { useTranslation } from 'react-i18next';
import { UserCredits } from '../types/credits';
import { Button } from './ui/button';
import { format } from 'date-fns';
import { es, enUS } from 'date-fns/locale';

interface CreditsCardProps {
  credits: UserCredits;
  hasActiveSubscription: boolean;
  onBuyCredits: () => void;
}

export const CreditsCard: React.FC<CreditsCardProps> = ({
  credits,
  hasActiveSubscription,
  onBuyCredits
}) => {
  const { t, i18n } = useTranslation(['profile']);
  const locale = i18n.language === 'es' ? es : enUS;

  // Formatear la fecha de reinicio
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPP', { locale });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">{t('profile:credits.title')}</h2>
      
      {hasActiveSubscription ? (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md mb-4">
          <h3 className="font-semibold text-green-700">{t('profile:credits.unlimited')}</h3>
          <p className="text-green-600 text-sm mt-1">{t('profile:credits.unlimitedDescription')}</p>
          {credits.subscription_end_date && (
            <p className="text-green-600 text-sm mt-2">
              {t('profile:credits.validUntil')}: {formatDate(credits.subscription_end_date)}
            </p>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-gray-50 rounded-md">
              <h3 className="text-sm font-medium text-gray-500">{t('profile:credits.monthly')}</h3>
              <div className="mt-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t('profile:credits.available')}:</span>
                  <span className="font-medium">{credits.monthly_credits - credits.used_monthly_credits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t('profile:credits.used')}:</span>
                  <span className="font-medium">{credits.used_monthly_credits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t('profile:credits.total')}:</span>
                  <span className="font-medium">{credits.monthly_credits}</span>
                </div>
              </div>
              {credits.reset_date && (
                <div className="mt-2 text-xs text-gray-500">
                  {t('profile:credits.nextReset')}: {formatDate(credits.reset_date)}
                </div>
              )}
            </div>

            <div className="p-4 bg-gray-50 rounded-md">
              <h3 className="text-sm font-medium text-gray-500">{t('profile:credits.extra')}</h3>
              <div className="mt-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t('profile:credits.available')}:</span>
                  <span className="font-medium">{credits.extra_credits - credits.used_extra_credits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t('profile:credits.used')}:</span>
                  <span className="font-medium">{credits.used_extra_credits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t('profile:credits.total')}:</span>
                  <span className="font-medium">{credits.extra_credits}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <Button onClick={onBuyCredits} className="w-full">
              {t('profile:credits.buy')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
