-- Eliminar las políticas existentes para studies
DROP POLICY IF EXISTS "Users can view shared organization studies v2" ON studies;
DROP POLICY IF EXISTS "Users can view shared organization studies v3" ON studies;
DROP POLICY IF EXISTS "Users can update their own studies" ON studies;
DROP POLICY IF EXISTS "Users can delete their own studies" ON studies;
DROP POLICY IF EXISTS "Users can insert studies" ON studies;

-- Crear una nueva política mejorada para studies
CREATE POLICY "Users can view shared organization studies v3"
    ON studies FOR SELECT
    TO authenticated
    USING (
        -- El usuario puede ver sus propios estudios
        auth.uid() = user_id
        OR (
            -- El usuario puede ver estudios compartidos de organizaciones a las que pertenece
            is_shared = true
            AND organization_id IS NOT NULL
            AND is_organization_member(organization_id, auth.uid())
        )
    );

-- Crear una política para permitir la actualización de estudios
CREATE POLICY "Users can update their own studies"
    ON studies FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Crear una política para permitir la eliminación de estudios
CREATE POLICY "Users can delete their own studies"
    ON studies FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);

-- Crear una política para permitir la inserción de estudios
CREATE POLICY "Users can insert studies"
    ON studies FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Crear una función RPC para limpiar estudios huérfanos
DROP FUNCTION IF EXISTS rpc_clean_orphaned_studies(uuid);

CREATE OR REPLACE FUNCTION rpc_clean_orphaned_studies(org_id uuid)
RETURNS int
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    cleaned_count int := 0;
    orphaned_user_ids uuid[];
BEGIN
    -- Verificar que el usuario actual es propietario de la organización
    IF NOT EXISTS (
        SELECT 1 FROM organization_members
        WHERE organization_id = org_id
        AND user_id = auth.uid()
        AND role = 'owner'
    ) THEN
        RAISE EXCEPTION 'Solo los propietarios de la organización pueden limpiar estudios huérfanos';
    END IF;
    
    -- Encontrar usuarios que tienen estudios compartidos con la organización pero ya no son miembros
    SELECT ARRAY_AGG(DISTINCT s.user_id)
    INTO orphaned_user_ids
    FROM studies s
    WHERE s.organization_id = org_id
    AND s.is_shared = true
    AND NOT EXISTS (
        SELECT 1 FROM organization_members om
        WHERE om.organization_id = org_id
        AND om.user_id = s.user_id
    );
    
    -- Si hay usuarios con estudios huérfanos, actualizar esos estudios
    IF orphaned_user_ids IS NOT NULL AND array_length(orphaned_user_ids, 1) > 0 THEN
        WITH updated_studies AS (
            UPDATE studies
            SET organization_id = NULL, 
                is_shared = false
            WHERE organization_id = org_id
            AND user_id = ANY(orphaned_user_ids)
            RETURNING *
        )
        SELECT COUNT(*) INTO cleaned_count FROM updated_studies;
    END IF;
    
    RETURN cleaned_count;
END;
$$;

-- Crear un trigger para limpiar automáticamente los estudios cuando un miembro es eliminado de una organización
DROP FUNCTION IF EXISTS clean_member_studies_on_removal() CASCADE;

CREATE OR REPLACE FUNCTION clean_member_studies_on_removal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Actualizar los estudios del miembro eliminado
    UPDATE studies
    SET organization_id = NULL,
        is_shared = false
    WHERE user_id = OLD.user_id
    AND organization_id = OLD.organization_id;
    
    RETURN OLD;
END;
$$;

CREATE TRIGGER clean_studies_on_member_removal
AFTER DELETE ON organization_members
FOR EACH ROW
EXECUTE FUNCTION clean_member_studies_on_removal();

-- Función para limpiar solicitudes de unión antiguas cuando un miembro es eliminado
DROP FUNCTION IF EXISTS clean_join_requests_on_member_removal() CASCADE;

CREATE OR REPLACE FUNCTION clean_join_requests_on_member_removal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Eliminar todas las solicitudes de unión pendientes del usuario para esta organización
    DELETE FROM organization_join_requests
    WHERE user_id = OLD.user_id
    AND organization_id = OLD.organization_id;
    
    RETURN OLD;
END;
$$;

CREATE TRIGGER clean_join_requests_on_member_removal
AFTER DELETE ON organization_members
FOR EACH ROW
EXECUTE FUNCTION clean_join_requests_on_member_removal();

-- Actualizar la función join_organization_by_code para manejar mejor los conflictos
DROP FUNCTION IF EXISTS join_organization_by_code(uuid, uuid, text);

CREATE OR REPLACE FUNCTION join_organization_by_code(
    p_invite_code uuid,
    p_user_id uuid,
    p_user_email text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_organization_id uuid;
BEGIN
    -- Get organization ID from invite code
    SELECT id INTO v_organization_id
    FROM organizations
    WHERE invite_code = p_invite_code;

    IF v_organization_id IS NULL THEN
        RAISE EXCEPTION 'Invalid invite code';
    END IF;

    -- Eliminar cualquier solicitud pendiente anterior para evitar conflictos
    DELETE FROM organization_join_requests
    WHERE organization_id = v_organization_id
    AND user_id = p_user_id;

    -- Create join request
    INSERT INTO organization_join_requests (
        organization_id,
        user_id,
        user_email,
        status
    )
    VALUES (
        v_organization_id,
        p_user_id,
        p_user_email,
        'pending'
    );
END;
$$;
