import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Study } from '../../types';
import { supabase } from '../../lib/supabase';
import { useStudyStore } from '../../store/studyStore';
import { useAuthStore } from '../../store/authStore';
import { useToast } from '../../components/ui/use-toast';

interface OrganizationStudyListProps {
  organizationId: string;
}

const OrganizationStudyList: React.FC<OrganizationStudyListProps> = ({ organizationId }) => {
  const { t } = useTranslation('common');
  const { toast } = useToast();
  const { studies, fetchStudies, isLoading } = useStudyStore();
  const { user } = useAuthStore();
  const [selectedStudies, setSelectedStudies] = useState<Set<string>>(new Set());
  const [filteredStudies, setFilteredStudies] = useState<Study[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    fetchStudies();
  }, [fetchStudies]);

  useEffect(() => {
    // Initialize selected studies based on existing organization_id
    const initialSelected = new Set(
      studies
        .filter(study => study.organization_id === organizationId)
        .map(study => study.id)
    );
    setSelectedStudies(initialSelected);
    
    // Filter studies to show ONLY the user's own studies
    if (user) {
      const userStudies = studies.filter(study => 
        // Only include studies owned by the current user
        study.user_id === user.id
      );
      setFilteredStudies(userStudies);
    }
  }, [studies, organizationId, user]);

  const handleToggleStudy = async (studyId: string) => {
    const newSelected = new Set(selectedStudies);
    const isCurrentlySelected = newSelected.has(studyId);

    setIsUpdating(true);
    try {
      const { error } = await supabase
        .from('studies')
        .update({
          organization_id: isCurrentlySelected ? null : organizationId,
          is_shared: isCurrentlySelected ? false : true
        })
        .eq('id', studyId);

      if (error) throw error;

      if (isCurrentlySelected) {
        newSelected.delete(studyId);
      } else {
        newSelected.add(studyId);
      }
      setSelectedStudies(newSelected);
      
      // Mostrar notificación de éxito
      toast({
        description: isCurrentlySelected 
          ? t('organization.studyUnshared') 
          : t('organization.studyShared'),
      });
      
      // Refresh studies after update
      fetchStudies();
    } catch (error) {
      console.error('Error updating study:', error);
      toast({
        title: t('errors.updatingRecord'),
        description: error instanceof Error ? error.message : t('errors.updatingRecord'),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleToggleAll = async (select: boolean) => {
    // Si no hay estudios, no hacer nada
    if (filteredStudies.length === 0) return;
    
    setIsUpdating(true);
    try {
      // Only toggle user's own studies
      const userStudyIds = filteredStudies.map(study => study.id);
      
      const { error } = await supabase
        .from('studies')
        .update({
          organization_id: select ? organizationId : null,
          is_shared: select
        })
        .in('id', userStudyIds);

      if (error) throw error;

      setSelectedStudies(select ? new Set(userStudyIds) : new Set());
      
      // Mostrar notificación de éxito
      toast({
        description: select 
          ? t('organization.allStudiesShared') 
          : t('organization.allStudiesUnshared'),
      });
      
      // Refresh studies after update
      fetchStudies();
    } catch (error) {
      console.error('Error updating studies:', error);
      toast({
        title: t('errors.updatingRecord'),
        description: error instanceof Error ? error.message : t('errors.updatingRecord'),
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
        <span className="ml-2">{t('loading')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">{t('organization.shareStudies')}</h3>
        <div className="space-x-4">
          <button
            onClick={() => handleToggleAll(true)}
            className="text-sm text-blue-600 hover:text-blue-800"
            disabled={isUpdating || filteredStudies.length === 0}
          >
            {t('common.selectAll')}
          </button>
          <button
            onClick={() => handleToggleAll(false)}
            className="text-sm text-blue-600 hover:text-blue-800"
            disabled={isUpdating || filteredStudies.length === 0}
          >
            {t('common.deselectAll')}
          </button>
        </div>
      </div>
      
      {isUpdating && (
        <div className="text-center py-2 text-sm text-blue-600">
          {t('saving')}...
        </div>
      )}
      
      <div className="space-y-2">
        {filteredStudies.length > 0 ? (
          filteredStudies.map(study => (
            <div key={study.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded">
              <input
                type="checkbox"
                checked={selectedStudies.has(study.id)}
                onChange={() => handleToggleStudy(study.id)}
                className="h-4 w-4 text-blue-600"
                disabled={isUpdating}
              />
              <span className="flex-1">{study.required_info?.name || t('study.untitled')}</span>
              {study.user_id === user?.id && (
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  {t('common.ownedByYou')}
                </span>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500">
            {t('organization.noStudiesFound')}
          </div>
        )}
      </div>
    </div>
  );
};

export default OrganizationStudyList;