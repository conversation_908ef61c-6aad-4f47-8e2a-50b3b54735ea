# Study Store

## Descripción
Gestiona los estudios de tiempos y sus operaciones CRUD.

## Estado
```typescript
interface StudyState {
  studies: Study[];
  selectedStudy: Study | null;
  isLoading: boolean;
  error: string | null;
  fetchStudies: () => Promise<void>;
  createStudy: (study: Partial<Study>) => Promise<Study>;
  updateStudy: (id: string, study: Partial<Study>) => Promise<Study>;
  deleteStudy: (id: string) => Promise<void>;
  setSelectedStudy: (study: Study | null) => void;
}
```

## Funcionalidades

### 1. Gestión de Estudios
- Carga de estudios del usuario
- Creación de nuevos estudios
- Actualización de estudios existentes
- Eliminación de estudios

### 2. Selección Activa
- Mantenimiento del estudio seleccionado
- Actualización de estado global
- Sincronización entre componentes

### 3. Persistencia
- Almacenamiento en Supabase
- Caché local
- Sincronización automática

## Uso
```typescript
const { 
  studies, 
  selectedStudy,
  createStudy,
  updateStudy 
} = useStudyStore();

// Crear estudio
const newStudy = await createStudy({
  name: "Nuevo Estudio",
  company: "Empresa",
  date: "2024-03-26"
});

// Actualizar estudio
await updateStudy(id, {
  name: "Estudio Actualizado"
});
```