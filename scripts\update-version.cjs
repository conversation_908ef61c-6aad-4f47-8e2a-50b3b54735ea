const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// Get the commit message from the last commit
const getLastCommitMessage = () => {
  try {
    return execSync('git log -1 --pretty=%B').toString().trim();
  } catch (error) {
    console.error('Error getting last commit message:', error);
    return '';
  }
};

// Parse the current version from version.ts
const getCurrentVersion = () => {
  try {
    const versionFile = path.join(__dirname, '..', 'src', 'config', 'version.ts');
    const content = fs.readFileSync(versionFile, 'utf8');
    const match = content.match(/APP_VERSION = '(.+)'/);
    return match ? match[1] : '0.0.0';
  } catch (error) {
    console.error('Error reading version file:', error);
    return '0.0.0';
  }
};

// Increment version based on commit message
const incrementVersion = (currentVersion) => {
  const [major, minor, patch] = currentVersion.split('.').map(Number);
  const commitMsg = getLastCommitMessage().toLowerCase();
  
  if (commitMsg.includes('breaking change') || commitMsg.includes('major')) {
    return `${major + 1}.0.0`;
  } else if (commitMsg.includes('feat') || commitMsg.includes('feature') || commitMsg.includes('minor')) {
    return `${major}.${minor + 1}.0`;
  } else {
    return `${major}.${minor}.${patch + 1}`;
  }
};

// Update version.ts file
const updateVersionFile = (newVersion) => {
  try {
    const versionFile = path.join(__dirname, '..', 'src', 'config', 'version.ts');
    const content = `// This file is automatically updated by the release workflow
export const APP_VERSION = '${newVersion}';`;
    fs.writeFileSync(versionFile, content);
    console.log('Updated version.ts');
  } catch (error) {
    console.error('Error updating version file:', error);
    process.exit(1);
  }
};

// Update package.json version
const updatePackageJson = (newVersion) => {
  try {
    const packageFile = path.join(__dirname, '..', 'package.json');
    const content = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
    content.version = newVersion;
    fs.writeFileSync(packageFile, JSON.stringify(content, null, 2) + '\n');
    console.log('Updated package.json');
  } catch (error) {
    console.error('Error updating package.json:', error);
    process.exit(1);
  }
};

// Update CHANGELOG.md
const updateChangelog = (newVersion) => {
  try {
    const changelogFile = path.join(__dirname, '..', 'CHANGELOG.md');
    let content = '';
    
    if (fs.existsSync(changelogFile)) {
      content = fs.readFileSync(changelogFile, 'utf8');
    } else {
      content = '# Changelog\n\nAll notable changes to this project will be documented in this file.\n\n';
    }

    const date = new Date().toISOString().split('T')[0];
    const commitMsg = getLastCommitMessage();
    const entry = `\n## [${newVersion}] - ${date}\n\n${commitMsg}\n`;
    
    // Insert the new entry after the header
    const lines = content.split('\n');
    const headerEndIndex = lines.findIndex((line) => line.startsWith('All notable changes'));
    if (headerEndIndex !== -1) {
      lines.splice(headerEndIndex + 2, 0, entry);
      content = lines.join('\n');
    } else {
      content = content + entry;
    }
    
    fs.writeFileSync(changelogFile, content);
    console.log('Updated CHANGELOG.md');
  } catch (error) {
    console.error('Error updating CHANGELOG.md:', error);
    process.exit(1);
  }
};

// Main function
const main = () => {
  const currentVersion = getCurrentVersion();
  const newVersion = incrementVersion(currentVersion);
  console.log(`Updating version from ${currentVersion} to ${newVersion}`);
  
  updateVersionFile(newVersion);
  updatePackageJson(newVersion);
  updateChangelog(newVersion);
};

main();
