# useSwipe Hook

## Descripción
Hook personalizado para detectar y manejar gestos de deslizamiento en dispositivos táctiles.

## Parámetros
```typescript
interface SwipeHandlers {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
}
```

## Funcionalidades
- Detección de dirección del swipe
- Umbral configurable
- Soporte para gestos verticales y horizontales
- Prevención de conflictos con scroll

## Retorno
```typescript
{
  handleTouchStart: (e: TouchEvent) => void;
  handleTouchMove: (e: TouchEvent) => void;
  handleTouchEnd: () => void;
}
```

## Uso
```typescript
const { handleTouchStart, handleTouchMove, handleTouchEnd } = useSwipe({
  onSwipeLeft: () => console.log('Swipe Left'),
  onSwipeRight: () => console.log('Swipe Right'),
  threshold: 50
});

return (
  <div
    onTouchStart={handleTouchStart}
    onTouchMove={handleTouchMove}
    onTouchEnd={handleTouchEnd}
  >
    Swipeable content
  </div>
);
```