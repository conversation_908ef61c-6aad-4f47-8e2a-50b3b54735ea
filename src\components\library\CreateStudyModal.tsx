import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle, Loader2 } from 'lucide-react';
import { ElementInstance } from '../../types';

interface CreateStudyModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  onCreateStudy: (studyData: {
    name: string;
    company: string;
    date: string;
    normalActivity: number;
    optimalActivity: number;
  }) => Promise<void>;
  isCreating?: boolean;
}

export const CreateStudyModal: React.FC<CreateStudyModalProps> = ({
  isOpen,
  onClose,
  selectedElements,
  onCreateStudy,
  isCreating = false
}) => {
  const { t } = useTranslation(['library', 'common']);
  const [formData, setFormData] = useState({
    name: '',
    company: '',
    date: new Date().toISOString().split('T')[0],
    normalActivity: 100,
    optimalActivity: 133
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('nameRequired', { ns: 'common' });
    }

    if (!formData.company.trim()) {
      newErrors.company = t('companyRequired', { ns: 'library' });
    }

    if (!formData.date) {
      newErrors.date = t('dateRequired', { ns: 'library' });
    }

    if (formData.normalActivity < 1 || formData.normalActivity > 200) {
      newErrors.normalActivity = t('activityRangeError', { ns: 'library' });
    }

    if (formData.optimalActivity < 1 || formData.optimalActivity > 200) {
      newErrors.optimalActivity = t('activityRangeError', { ns: 'library' });
    }

    if (formData.optimalActivity <= formData.normalActivity) {
      newErrors.optimalActivity = t('optimalActivityError', { ns: 'library' });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onCreateStudy(formData);
      handleClose();
    } catch (error) {
      console.error('Error creating study:', error);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      company: '',
      date: new Date().toISOString().split('T')[0],
      normalActivity: 100,
      optimalActivity: 133
    });
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Agrupar elementos por estudio para mostrar resumen
  const elementsByStudy = selectedElements.reduce((acc, item) => {
    if (!acc[item.studyId]) {
      acc[item.studyId] = {
        studyName: item.studyName,
        elements: []
      };
    }
    acc[item.studyId].elements.push(item.element);
    return acc;
  }, {} as Record<string, { studyName: string; elements: ElementInstance[] }>);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {t('createStudyFromSelection', { ns: 'library' })}
          </h2>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            disabled={isCreating}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Resumen de elementos seleccionados */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">
              {t('selectedElements', { ns: 'library' })} ({selectedElements.length})
            </h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {Object.entries(elementsByStudy).map(([studyId, { studyName, elements }]) => (
                <div key={studyId} className="text-sm">
                  <span className="font-medium text-gray-700">{studyName}:</span>
                  <span className="text-gray-600 ml-2">
                    {elements.length} {t('elements', { ns: 'library' })}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Formulario */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nombre del estudio */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('studyName', { ns: 'library' })} *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors.name ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                }`}
                placeholder={t('studyNamePlaceholder', { ns: 'library' })}
                disabled={isCreating}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Empresa */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('studyCompany', { ns: 'library' })} *
              </label>
              <input
                type="text"
                value={formData.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors.company ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                }`}
                placeholder={t('companyPlaceholder', { ns: 'library' })}
                disabled={isCreating}
              />
              {errors.company && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.company}
                </p>
              )}
            </div>

            {/* Fecha */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('studyDate', { ns: 'library' })} *
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors.date ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                }`}
                disabled={isCreating}
              />
              {errors.date && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.date}
                </p>
              )}
            </div>

            {/* Actividad normal */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('normalActivity', { ns: 'library' })} *
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={formData.normalActivity}
                onChange={(e) => handleInputChange('normalActivity', parseInt(e.target.value) || 100)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors.normalActivity ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                }`}
                disabled={isCreating}
              />
              {errors.normalActivity && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.normalActivity}
                </p>
              )}
            </div>

            {/* Actividad óptima */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('optimalActivity', { ns: 'library' })} *
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={formData.optimalActivity}
                onChange={(e) => handleInputChange('optimalActivity', parseInt(e.target.value) || 133)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors.optimalActivity ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                }`}
                disabled={isCreating}
              />
              {errors.optimalActivity && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.optimalActivity}
                </p>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              disabled={isCreating}
            >
              {t('cancel', { ns: 'common' })}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              disabled={isCreating || selectedElements.length === 0}
            >
              {isCreating && <Loader2 className="w-4 h-4 animate-spin" />}
              <span>{isCreating ? t('creating', { ns: 'common' }) : t('create', { ns: 'common' })}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
