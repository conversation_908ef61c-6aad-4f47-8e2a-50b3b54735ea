-- Permitir que los usuarios actualicen sus propios créditos
create policy "Users can update their own study limits"
on user_study_limits
for update
to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

-- Permitir que los usuarios inserten sus propios registros
create policy "Users can insert their own study limits"
on user_study_limits
for insert
to authenticated
with check (auth.uid() = user_id);
