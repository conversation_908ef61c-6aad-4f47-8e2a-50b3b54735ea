import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import Stripe from 'https://esm.sh/stripe@12.0.0'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')
const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')
const supabaseUrl = Deno.env.get('SUPABASE_URL')
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

if (!stripeSecretKey || !webhookSecret || !supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing environment variables')
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false
  }
})

async function saveLog(event_type: string, data: any, error?: any) {
  try {
    const { data: logData, error: logError } = await supabase
      .from('webhook_logs')
      .insert([{
        event_type,
        data,
        error: error ? JSON.stringify(error) : null,
        created_at: new Date().toISOString()
      }])

    if (logError) {
      console.error('Error saving log:', logError)
    }
  } catch (e) {
    console.error('Error in saveLog:', e)
  }
}

async function getUserByStripeIds(subscriptionId: string, customerId: string) {
  console.log('Looking up user by stripe IDs:', { subscriptionId, customerId })
  
  // Primero intentar por subscription_id
  const { data: userBySub } = await supabase
    .from('user_study_limits')
    .select('user_id')
    .eq('stripe_subscription_id', subscriptionId)
    .single()

  if (userBySub) {
    console.log('User found by subscription ID:', userBySub)
    return userBySub
  }

  // Si no se encuentra, intentar por customer_id
  const { data: userByCust } = await supabase
    .from('user_study_limits')
    .select('user_id')
    .eq('stripe_customer_id', customerId)
    .single()

  console.log('User found by customer ID:', userByCust)
  return userByCust
}

async function updateSubscriptionInDatabase(userId: string, subscription: any) {
  console.log('Updating subscription:', {
    userId,
    subscriptionId: subscription.id,
    status: subscription.status,
    customerId: subscription.customer,
    planId: subscription.items.data[0]?.price.id,
    cancelAt: subscription.cancel_at,
    canceledAt: subscription.canceled_at
  })

  try {
    // Determinar el nombre del plan basado en el intervalo
    const interval = subscription.items.data[0]?.price.recurring.interval
    const planName = interval === 'year' ? 'Suscripción Anual' : 'Suscripción Mensual'

    // Si la suscripción está cancelada o inactiva, resetear los valores
    const isActive = subscription.status === 'active' && !subscription.canceled_at
    
    const subscriptionData = {
      monthly_credits: isActive ? 999999 : 1,
      subscription_plan: isActive ? planName : null,
      subscription_start_date: isActive ? new Date(subscription.current_period_start * 1000).toISOString() : null,
      subscription_end_date: isActive ? new Date(subscription.current_period_end * 1000).toISOString() : null,
      stripe_subscription_id: isActive ? subscription.id : null,
      stripe_customer_id: subscription.customer, // Mantenemos el customer_id para futuras suscripciones
      updated_at: new Date().toISOString()
    }

    console.log('Subscription data to update:', subscriptionData)

    const { data: existingUser } = await supabase
      .from('user_study_limits')
      .select()
      .eq('user_id', userId)
      .single()

    if (!existingUser) {
      console.log('Creating new user record...')
      const { data, error: insertError } = await supabase
        .from('user_study_limits')
        .insert([{
          user_id: userId,
          extra_credits: 0,
          used_monthly_credits: 0,
          used_extra_credits: 0,
          created_at: new Date().toISOString(),
          ...subscriptionData
        }])
        .select()
        .single()

      if (insertError) {
        console.error('Error inserting new user:', insertError)
        await saveLog('subscription_insert_error', { userId, subscriptionData }, insertError)
        throw insertError
      }
      console.log('New user record created:', data)
      return data
    }

    console.log('Updating existing user record...')
    const { data, error } = await supabase
      .from('user_study_limits')
      .update(subscriptionData)
      .eq('user_id', userId)

    if (error) {
      console.error('Error updating subscription:', error)
      await saveLog('subscription_update_error', { userId, subscriptionData }, error)
      throw error
    }

    await saveLog('subscription_updated', { userId, subscriptionData })
    console.log('Subscription updated successfully')
    return data
  } catch (error) {
    console.error('Error in updateSubscriptionInDatabase:', error)
    await saveLog('subscription_update_error', { userId, subscription: subscription.id }, error)
    throw error
  }
}

async function handleSubscriptionEvent(subscription: any) {
  console.log('Processing subscription:', {
    id: subscription.id,
    status: subscription.status,
    customerId: subscription.customer
  })

  try {
    const user = await getUserByStripeIds(subscription.id, subscription.customer)
    
    if (!user) {
      console.log('No user found, waiting for checkout.session.completed')
      return null
    }

    const result = await updateSubscriptionInDatabase(user.user_id, subscription)
    await saveLog('subscription_event_success', { subscription })
    return result
  } catch (error) {
    console.error('Error in handleSubscriptionEvent:', error)
    await saveLog('subscription_event_error', { subscription }, error)
    throw error
  }
}

async function handlePayment(session: any) {
  console.log('Processing payment:', {
    userId: session.client_reference_id,
    email: session.customer_email,
    paymentStatus: session.payment_status,
    amount: session.amount_total
  })

  if (!session.client_reference_id) {
    console.error('No user ID in session')
    return null
  }

  if (session.payment_status !== 'paid') {
    console.log('Payment not completed:', session.payment_status)
    return null
  }

  try {
    // Cada crédito cuesta 30 EUR (3000 cents)
    const creditsToAdd = Math.floor(session.amount_total / 3000)
    console.log('Credits to add:', creditsToAdd)

    const { data, error } = await supabase.rpc(
      'increment_extra_credits',
      { 
        user_id_param: session.client_reference_id,
        credits_to_add: creditsToAdd
      }
    )

    if (error) {
      console.error('Error updating credits:', error)
      await saveLog('payment_error', { session }, error)
      throw error
    }

    await saveLog('payment_success', { session })
    console.log('Credits updated successfully:', data)
    return data
  } catch (error) {
    console.error('Error in handlePayment:', error)
    await saveLog('payment_error', { session }, error)
    throw error
  }
}

async function handleCheckoutCompleted(session: any) {
  console.log('Processing checkout completion:', {
    mode: session.mode,
    userId: session.client_reference_id,
    customerId: session.customer,
    status: session.status,
    subscriptionId: session.subscription
  })

  try {
    if (!session.client_reference_id) {
      console.error('No user ID in session')
      return null
    }

    // Actualizar el customer_id del usuario
    if (session.customer) {
      console.log('Updating customer ID for user:', session.client_reference_id)
      const { error: updateError } = await supabase
        .from('user_study_limits')
        .update({ stripe_customer_id: session.customer })
        .eq('user_id', session.client_reference_id)

      if (updateError) {
        console.error('Error updating customer ID:', updateError)
        await saveLog('checkout_completed_error', { session }, updateError)
        throw updateError
      }
    }

    if (session.mode === 'subscription' && session.subscription) {
      console.log('Handling subscription checkout...')
      const subscription = await stripe.subscriptions.retrieve(session.subscription)
      const result = await updateSubscriptionInDatabase(session.client_reference_id, subscription)
      await saveLog('checkout_completed_success', { session })
      return result
    } else if (session.mode === 'payment') {
      console.log('Handling payment checkout...')
      const result = await handlePayment(session)
      await saveLog('checkout_completed_success', { session })
      return result
    }

    return null
  } catch (error) {
    console.error('Error in handleCheckoutCompleted:', error)
    await saveLog('checkout_completed_error', { session }, error)
    throw error
  }
}

serve(async (req) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
  }

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const body = await req.text()
    console.log('Webhook payload received')
    
    const event = JSON.parse(body)
    console.log('Processing event:', event.type)

    let result = null
    switch (event.type) {
      case 'checkout.session.completed':
        result = await handleCheckoutCompleted(event.data.object)
        break
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
      case 'customer.subscription.canceled':
        result = await handleSubscriptionEvent(event.data.object)
        break
    }

    return new Response(
      JSON.stringify({ received: true, result }), 
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (err) {
    console.error('Error:', err)
    await saveLog('webhook_error', { error: err.message })
    return new Response(
      JSON.stringify({ error: err.message }), 
      { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
