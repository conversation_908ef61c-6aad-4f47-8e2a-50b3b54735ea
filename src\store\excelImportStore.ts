import { create } from 'zustand';
import * as XLSX from 'xlsx';

interface ExcelRow {
  [key: string]: any;
}

interface ColumnMapping {
  description: string;
  time: string;
  activity: string;
  frequency: string;
  type: string;
  repetitionType: string;
  supplements: string;
  repetitions: string;
  cycles: string;
}

interface ProcessedElement {
  id: string;
  name: string;
  description: string;
  time: number;
  activity: number;
  type: string;
  position: number;
  repetition_type: string;
  frequency_cycles: number;
  frequency_repetitions: number;
  supplements: Array<{
    id: string;
    percentage: number;
    is_forced: boolean;
    points: Record<string, any>;
    factor_selections: Record<string, any>;
  }>;
}

interface ExcelImportState {
  // Estado del archivo
  file: File | null;
  excelData: ExcelRow[];
  headers: string[];
  
  // Estado del proceso
  step: 'upload' | 'mapping' | 'preview';
  isProcessing: boolean;
  errors: string[];
  
  // Información del estudio
  studyInfo: {
    name: string;
    company: string;
    date: string;
  };
  
  // Mapeo de columnas
  columnMapping: ColumnMapping;
  
  // Elementos procesados
  processedElements: ProcessedElement[];
  
  // Acciones
  setFile: (file: File | null) => void;
  processExcelFile: (file: File) => Promise<void>;
  setStep: (step: 'upload' | 'mapping' | 'preview') => void;
  setStudyInfo: (info: Partial<ExcelImportState['studyInfo']>) => void;
  setColumnMapping: (mapping: Partial<ColumnMapping>) => void;
  autoMapColumns: (headers: string[]) => void;
  processElements: () => void;
  validateMapping: () => boolean;
  addError: (error: string) => void;
  clearErrors: () => void;
  reset: () => void;
}

const initialState = {
  file: null,
  excelData: [],
  headers: [],
  step: 'upload' as const,
  isProcessing: false,
  errors: [],
  studyInfo: {
    name: '',
    company: '',
    date: new Date().toISOString().split('T')[0]
  },
  columnMapping: {
    description: '',
    time: '',
    activity: '',
    frequency: '',
    type: '',
    repetitionType: '',
    supplements: '',
    repetitions: '',
    cycles: ''
  },
  processedElements: []
};

export const useExcelImportStore = create<ExcelImportState>((set, get) => ({
  ...initialState,

  setFile: (file) => set({ file }),

  processExcelFile: async (file) => {
    set({ isProcessing: true, errors: [] });
    
    try {
      // Validar tipo de archivo
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];
      
      if (!validTypes.includes(file.type)) {
        throw new Error('Tipo de archivo no válido. Use .xlsx o .xls');
      }

      // Leer archivo
      const arrayBuffer = await file.arrayBuffer();
      const data = new Uint8Array(arrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // Convertir a JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
      
      if (jsonData.length < 2) {
        throw new Error('El archivo debe tener al menos una fila de encabezados y datos');
      }

      // Primera fila como headers
      const fileHeaders = jsonData[0].map((header: any) => String(header || '').trim());
      const rows = jsonData.slice(1).map(row => {
        const rowObj: ExcelRow = {};
        fileHeaders.forEach((header, index) => {
          rowObj[header] = row[index] || '';
        });
        return rowObj;
      }).filter(row => Object.values(row).some(val => val !== ''));

      set({
        file,
        headers: fileHeaders,
        excelData: rows,
        step: 'mapping',
        isProcessing: false
      });

      // Auto-mapear columnas
      get().autoMapColumns(fileHeaders);
      
    } catch (error) {
      set({
        errors: [error instanceof Error ? error.message : 'Error al procesar el archivo'],
        isProcessing: false
      });
    }
  },

  setStep: (step) => set({ step }),

  setStudyInfo: (info) => set(state => ({
    studyInfo: { ...state.studyInfo, ...info }
  })),

  setColumnMapping: (mapping) => set(state => ({
    columnMapping: { ...state.columnMapping, ...mapping }
  })),

  autoMapColumns: (fileHeaders) => {
    const mapping: Partial<ColumnMapping> = {};
    
    fileHeaders.forEach(header => {
      const lowerHeader = header.toLowerCase();
      
      if (lowerHeader.includes('descripci') || lowerHeader.includes('description')) {
        mapping.description = header;
      } else if (lowerHeader.includes('tiempo') || lowerHeader.includes('time')) {
        mapping.time = header;
      } else if (lowerHeader.includes('actividad') || lowerHeader.includes('activity')) {
        mapping.activity = header;
      } else if (lowerHeader.includes('frecuencia') || lowerHeader.includes('frequency')) {
        mapping.frequency = header;
      } else if (lowerHeader.includes('tipo') || lowerHeader.includes('type')) {
        mapping.type = header;
      } else if (lowerHeader.includes('suplemento') || lowerHeader.includes('supplement')) {
        mapping.supplements = header;
      } else if (lowerHeader.includes('repeticion') || lowerHeader.includes('repetition')) {
        mapping.repetitions = header;
      } else if (lowerHeader.includes('ciclo') || lowerHeader.includes('cycle')) {
        mapping.cycles = header;
      }
    });
    
    set(state => ({
      columnMapping: { ...state.columnMapping, ...mapping }
    }));
  },

  processElements: () => {
    const { excelData, columnMapping } = get();
    
    const elements = excelData.map((row, index) => {
      const element: ProcessedElement = {
        id: crypto.randomUUID(),
        name: row[columnMapping.description] || `Elemento ${index + 1}`,
        description: row[columnMapping.description] || '',
        time: parseFloat(row[columnMapping.time]) || 0,
        activity: parseFloat(row[columnMapping.activity]) || 100,
        type: row[columnMapping.type] || 'machine-stopped',
        position: index + 1,
        repetition_type: row[columnMapping.repetitionType] || 'repetitive',
        frequency_cycles: parseInt(row[columnMapping.cycles]) || 1,
        frequency_repetitions: parseInt(row[columnMapping.repetitions]) || 1,
        supplements: row[columnMapping.supplements] ? [{
          id: crypto.randomUUID(),
          percentage: parseFloat(row[columnMapping.supplements]) || 0,
          is_forced: true,
          points: {},
          factor_selections: {}
        }] : []
      };
      
      return element;
    });
    
    set({ processedElements: elements });
  },

  validateMapping: () => {
    const { columnMapping, studyInfo } = get();
    const errors: string[] = [];
    
    if (!columnMapping.description) {
      errors.push('La columna de descripción es requerida');
    }
    
    if (!studyInfo.name.trim()) {
      errors.push('El nombre del estudio es requerido');
    }
    
    if (!studyInfo.company.trim()) {
      errors.push('La empresa es requerida');
    }
    
    set({ errors });
    return errors.length === 0;
  },

  addError: (error) => set(state => ({
    errors: [...state.errors, error]
  })),

  clearErrors: () => set({ errors: [] }),

  reset: () => set(initialState)
}));
