import { useEffect, useRef, useState } from 'react';

interface TouchPosition {
  x: number;
  y: number;
}

interface SwipeState {
  isActive: boolean;
  direction: 'left' | 'right' | 'up' | 'down' | null;
  distance: number;
  velocity: number;
}

interface UseMobileGesturesOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onTap?: () => void;
  onLongPress?: () => void;
  onPinch?: (scale: number) => void;
  swipeThreshold?: number;
  velocityThreshold?: number;
  longPressDelay?: number;
  tapDelay?: number;
}

export const useMobileGestures = (options: UseMobileGesturesOptions = {}) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onTap,
    onLongPress,
    onPinch,
    swipeThreshold = 50,
    velocityThreshold = 0.3,
    longPressDelay = 500,
    tapDelay = 300
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const [swipeState, setSwipeState] = useState<SwipeState>({
    isActive: false,
    direction: null,
    distance: 0,
    velocity: 0
  });

  const touchStartRef = useRef<TouchPosition | null>(null);
  const touchTimeRef = useRef<number>(0);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const initialDistanceRef = useRef<number>(0);

  // Utilidades
  const getTouchPosition = (touch: Touch): TouchPosition => ({
    x: touch.clientX,
    y: touch.clientY
  });

  const getDistance = (touch1: Touch, touch2: Touch): number => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  const calculateSwipeDirection = (
    start: TouchPosition,
    end: TouchPosition
  ): 'left' | 'right' | 'up' | 'down' => {
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'down' : 'up';
    }
  };

  const calculateVelocity = (distance: number, time: number): number => {
    return time > 0 ? distance / time : 0;
  };

  // Manejadores de eventos
  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    if (!touch) return;

    touchStartRef.current = getTouchPosition(touch);
    touchTimeRef.current = Date.now();

    // Limpiar timer anterior
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
    }

    // Configurar long press
    if (onLongPress && e.touches.length === 1) {
      longPressTimerRef.current = setTimeout(() => {
        onLongPress();
        longPressTimerRef.current = null;
      }, longPressDelay);
    }

    // Configurar pinch (dos dedos)
    if (e.touches.length === 2) {
      initialDistanceRef.current = getDistance(e.touches[0], e.touches[1]);
    }

    setSwipeState(prev => ({ ...prev, isActive: true }));
  };

  const handleTouchMove = (e: TouchEvent) => {
    // Cancelar long press si hay movimiento
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    const touch = e.touches[0];
    if (!touch || !touchStartRef.current) return;

    // Manejar pinch
    if (e.touches.length === 2 && onPinch) {
      const currentDistance = getDistance(e.touches[0], e.touches[1]);
      const scale = currentDistance / initialDistanceRef.current;
      onPinch(scale);
      return;
    }

    // Manejar swipe
    const currentPos = getTouchPosition(touch);
    const dx = currentPos.x - touchStartRef.current.x;
    const dy = currentPos.y - touchStartRef.current.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    if (distance > 10) { // Umbral mínimo para considerar movimiento
      const direction = calculateSwipeDirection(touchStartRef.current, currentPos);
      const time = Date.now() - touchTimeRef.current;
      const velocity = calculateVelocity(distance, time);
      
      setSwipeState({
        isActive: true,
        direction,
        distance,
        velocity
      });
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    // Limpiar long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    if (!touchStartRef.current) return;

    const touch = e.changedTouches[0];
    if (!touch) return;

    const currentPos = getTouchPosition(touch);
    const dx = currentPos.x - touchStartRef.current.x;
    const dy = currentPos.y - touchStartRef.current.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const time = Date.now() - touchTimeRef.current;
    const velocity = calculateVelocity(distance, time);

    // Determinar si es un tap
    if (distance < 10 && time < tapDelay && onTap) {
      onTap();
    }
    // Determinar si es un swipe válido
    else if (distance > swipeThreshold && velocity > velocityThreshold) {
      const direction = calculateSwipeDirection(touchStartRef.current, currentPos);
      
      switch (direction) {
        case 'left':
          onSwipeLeft?.();
          break;
        case 'right':
          onSwipeRight?.();
          break;
        case 'up':
          onSwipeUp?.();
          break;
        case 'down':
          onSwipeDown?.();
          break;
      }
    }

    // Resetear estado
    touchStartRef.current = null;
    touchTimeRef.current = 0;
    setSwipeState({
      isActive: false,
      direction: null,
      distance: 0,
      velocity: 0
    });
  };

  // Configurar event listeners
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Opciones para eventos táctiles
    const options: AddEventListenerOptions = {
      passive: false,
      capture: true
    };

    element.addEventListener('touchstart', handleTouchStart, options);
    element.addEventListener('touchmove', handleTouchMove, options);
    element.addEventListener('touchend', handleTouchEnd, options);
    element.addEventListener('touchcancel', handleTouchEnd, options);

    return () => {
      element.removeEventListener('touchstart', handleTouchStart, options);
      element.removeEventListener('touchmove', handleTouchMove, options);
      element.removeEventListener('touchend', handleTouchEnd, options);
      element.removeEventListener('touchcancel', handleTouchEnd, options);
    };
  }, []);

  // Cleanup en unmount
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
    };
  }, []);

  return {
    ref: elementRef,
    swipeState,
    isTouch: 'ontouchstart' in window
  };
};

// Hook simplificado para swipes básicos
export const useSwipe = (
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  threshold: number = 50
) => {
  return useMobileGestures({
    onSwipeLeft,
    onSwipeRight,
    swipeThreshold: threshold
  });
};

// Hook para pull-to-refresh
export const usePullToRefresh = (
  onRefresh: () => void,
  threshold: number = 80
) => {
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);

  const handleSwipeDown = () => {
    if (pullDistance > threshold) {
      onRefresh();
    }
    setIsPulling(false);
    setPullDistance(0);
  };

  const gestures = useMobileGestures({
    onSwipeDown: handleSwipeDown,
    swipeThreshold: 20
  });

  return {
    ...gestures,
    isPulling,
    pullDistance
  };
};

// Hook para gestos de navegación
export const useNavigationGestures = (
  onBack?: () => void,
  onForward?: () => void
) => {
  return useMobileGestures({
    onSwipeRight: onBack,
    onSwipeLeft: onForward,
    swipeThreshold: 100,
    velocityThreshold: 0.5
  });
}; 