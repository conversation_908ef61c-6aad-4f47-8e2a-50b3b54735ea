import { supabase } from '../lib/supabaseClient';
import { Study, StudyExport } from '../types/study';
import { useCreditStore } from '../store/creditStore';
import i18n from '../i18n';
import { toast } from '../components/ui/use-toast';

export const exportStudies = async (studies: Study[] | Study): Promise<void> => {
  const dataToExport = Array.isArray(studies) ? studies : [studies];
  console.log('Estudios a exportar:', dataToExport);
  
  // Verificar y limpiar los datos antes de exportar
  const cleanedStudies = dataToExport.map(study => {
    console.log('Procesando estudio:', study);
    
    // Si required_info y optional_info ya son strings JSON, los parseamos
    let requiredInfo;
    let optionalInfo;
    
    if (typeof study.required_info === 'string') {
      requiredInfo = JSON.parse(study.required_info);
    } else if (study.required_info) {
      requiredInfo = study.required_info;
    } else if (study.info?.required) {
      requiredInfo = {
        name: study.info.required.name || study.name || 'Estudio sin nombre',
        company: study.info.required.company || '',
        date: study.info.required.date || new Date().toISOString().split('T')[0],
        activity_scale: study.info.required.activity_scale || {
          normal: 100,
          optimal: 133
        }
      };
    } else {
      requiredInfo = {
        name: study.name || 'Estudio sin nombre',
        company: '',
        date: new Date().toISOString().split('T')[0],
        activity_scale: {
          normal: 100,
          optimal: 133
        }
      };
    }

    if (typeof study.optional_info === 'string') {
      optionalInfo = JSON.parse(study.optional_info);
    } else if (study.optional_info) {
      optionalInfo = study.optional_info;
    } else if (study.info?.optional) {
      optionalInfo = study.info.optional;
    } else {
      optionalInfo = {
        study_number: '',
        operator: '',
        section: '',
        reference: '',
        machine: '',
        tools: '',
        technician: ''
      };
    }

    console.log('Required info:', requiredInfo);
    console.log('Optional info:', optionalInfo);

    // Validar que los campos requeridos no sean undefined o null
    if (!requiredInfo.name || typeof requiredInfo.name !== 'string') {
      throw new Error('El nombre del estudio es requerido y debe ser un string');
    }

    if (!requiredInfo.company || typeof requiredInfo.company !== 'string') {
      throw new Error('La compañía es requerida y debe ser un string');
    }

    if (!requiredInfo.date || typeof requiredInfo.date !== 'string') {
      throw new Error('La fecha es requerida y debe ser un string');
    }

    if (!requiredInfo.activity_scale || typeof requiredInfo.activity_scale !== 'object') {
      throw new Error('La escala de actividad es requerida y debe ser un objeto');
    }

    // Asegurarnos de que todos los campos requeridos existen
    const cleanStudy = {
      id: study.id,
      user_id: study.user_id,
      required_info: JSON.stringify(requiredInfo),
      optional_info: JSON.stringify(optionalInfo),
      elements: Array.isArray(study.elements) ? study.elements : [],
      time_records: typeof study.time_records === 'object' ? study.time_records : {},
      supplements: typeof study.supplements === 'object' ? study.supplements : { elements: {} },
      crono_seguido_records: Array.isArray(study.crono_seguido_records) ? study.crono_seguido_records : [],
      created_at: study.created_at || new Date().toISOString(),
      updated_at: study.updated_at || new Date().toISOString()
    };

    console.log('Estudio limpio:', cleanStudy);
    console.log('required_info (stringified):', cleanStudy.required_info);
    console.log('optional_info (stringified):', cleanStudy.optional_info);

    return cleanStudy;
  });

  // Crear el objeto de exportación con metadata
  const exportData = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    studies: cleanedStudies
  };

  console.log('Datos finales a exportar:', exportData);

  // Convertir a JSON y crear el blob
  const jsonString = JSON.stringify(exportData, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });

  // Crear el nombre del archivo usando el nombre del estudio o backup general
  let fileName = '';
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  if (cleanedStudies.length === 1) {
    const studyName = JSON.parse((cleanedStudies[0] as any).required_info).name || 'estudio';
    fileName = `${studyName}_${timestamp}.json`;
  } else {
    fileName = `backup_general_${timestamp}.json`;
  }

  // Crear un enlace temporal y descargarlo
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  
  // Limpiar
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export const importStudies = async (file: File): Promise<Study[]> => {
  const { consumeCredit, fetchCredits, fetchUserLimits } = useCreditStore.getState();
  let createdStudiesCount = 0;
  let failedStudiesCount = 0;
  const errorMessages: string[] = [];

  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error(i18n.t('studyexportimport:no_active_session', { defaultValue: 'No hay sesión activa' }));

    const text = await file.text();
    const importedData = JSON.parse(text);
    console.log('Datos importados:', importedData);

    if (!importedData.studies || !Array.isArray(importedData.studies)) {
      throw new Error(i18n.t('studyexportimport:invalid_file_format', { defaultValue: 'Formato de archivo inválido' }));
    }

    const studiesToProcess: Study[] = importedData.studies;
    const processedStudies: (Study | null)[] = [];

    for (const study of studiesToProcess) {
      try {
        console.log('Procesando estudio para importación:', study.required_info?.name || 'Estudio sin nombre');

        // 1. Consumir crédito ANTES de intentar procesar e insertar el estudio
        const creditConsumed = await consumeCredit();
        if (!creditConsumed) {
          failedStudiesCount++;
          const noCreditsMsg = i18n.t('study:no_credits.message', { ns: 'study', studyName: study.required_info?.name || 'este estudio' });
          console.warn(`No se pudo consumir crédito para el estudio "${study.required_info?.name || 'Desconocido'}": ${noCreditsMsg}`);
          errorMessages.push(i18n.t('studyexportimport:import_failed_no_credits', { studyName: study.required_info?.name || 'Un estudio', defaultValue: `Falló la importación de "${study.required_info?.name || 'Un estudio'}" por falta de créditos.` }));
          processedStudies.push(null); // Marcar como nulo para no procesar
          continue; // Saltar al siguiente estudio
        }

        // Asegurarse de que required_info y optional_info sean objetos
        let requiredInfo = typeof (study as any).required_info === 'string' 
          ? JSON.parse((study as any).required_info)
          : (study as any).required_info;

        let optionalInfo = typeof (study as any).optional_info === 'string'
          ? JSON.parse((study as any).optional_info)
          : (study as any).optional_info;

        console.log('Required info antes de procesar:', requiredInfo);
        console.log('Optional info antes de procesar:', optionalInfo);

        // Asegurarse de que requiredInfo tenga la estructura correcta
        requiredInfo = {
          name: requiredInfo.name || '',
          company: requiredInfo.company || '',
          date: requiredInfo.date || new Date().toISOString().split('T')[0],
          activity_scale: {
            normal: requiredInfo.activity_scale?.normal || 100,
            optimal: requiredInfo.activity_scale?.optimal || 133
          }
        };

        // Asegurarse de que optionalInfo tenga la estructura correcta
        optionalInfo = {
          study_number: optionalInfo.study_number || '',
          operator: optionalInfo.operator || '',
          section: optionalInfo.section || '',
          reference: optionalInfo.reference || '',
          machine: optionalInfo.machine || '',
          tools: optionalInfo.tools || '',
          technician: optionalInfo.technician || ''
        };

        console.log('Required info procesado:', requiredInfo);
        console.log('Optional info procesado:', optionalInfo);

        // Validar campos requeridos
        if (!requiredInfo.name || typeof requiredInfo.name !== 'string') {
          throw new Error(i18n.t('studyexportimport:name_required', { defaultValue: 'El nombre del estudio es requerido' }));
        }

        if (!requiredInfo.company || typeof requiredInfo.company !== 'string') {
          throw new Error('La compañía es requerida y debe ser un string');
        }

        if (!requiredInfo.date || typeof requiredInfo.date !== 'string') {
          throw new Error('La fecha es requerida y debe ser un string');
        }

        if (!requiredInfo.activity_scale || typeof requiredInfo.activity_scale !== 'object') {
          throw new Error('La escala de actividad es requerida y debe ser un objeto');
        }

        // Crear el objeto de estudio para la base de datos
        const studyData = {
          user_id: user.id,
          required_info: requiredInfo,
          optional_info: optionalInfo,
          elements: Array.isArray((study as any).elements) ? (study as any).elements : [],
          time_records: (study as any).time_records || {},
          supplements: (study as any).supplements || { elements: {} },
          crono_seguido_records: Array.isArray((study as any).crono_seguido_records) ? (study as any).crono_seguido_records : []
        };

        // CORREGIDO: Verificar si ya existe un estudio con el mismo nombre en TUS PROPIOS estudios
        // para evitar duplicados al importar estudios compartidos
        let existingStudy: any = null;
        try {
          const { data: found, error: findError } = await supabase
            .from('studies')
            .select('id, required_info')
            .eq('user_id', user.id) // Solo buscar en TUS estudios
            .eq('required_info->name', requiredInfo.name) // Buscar por nombre
            .eq('required_info->company', requiredInfo.company) // Y compañía para mayor precisión
            .single();
          
          if (findError && findError.code !== 'PGRST116') { // PGRST116 = no rows found
            console.warn('Error verificando duplicados en tus estudios:', findError.message);
          } else {
            existingStudy = found;
          }
        } catch (error) {
          console.warn('Error en verificación de duplicados:', error);
        }

        let dbResult: any, dbError: any;
        if (existingStudy) {
          // Si ya tienes un estudio con el mismo nombre, actualizarlo con los nuevos datos
          ({ data: dbResult, error: dbError } = await supabase
            .from('studies')
            .update(studyData as any)
            .eq('id', existingStudy.id)
            .select('*')
            .single());
        } else {
          // Si no lo tienes, crear nuevo estudio para ti
          // No usar el ID original para que PostgreSQL genere uno nuevo
          ({ data: dbResult, error: dbError } = await supabase
            .from('studies')
            .insert(studyData as any) // Sin especificar ID original
            .select('*')
            .single());
        }

        if (dbError) {
          failedStudiesCount++;
          // Si hay error de DB, el crédito ya se consumió. ¿Debería revertirse? Por ahora no.
          console.error('Error de Supabase al importar estudio:', dbError);
          errorMessages.push(i18n.t('studyexportimport:import_failed_db_error', { studyName: requiredInfo.name, message: dbError.message, defaultValue: `Error al importar "${requiredInfo.name}": ${dbError.message}` }));
          processedStudies.push(null);
          continue;
        }

        if (!dbResult) {
          failedStudiesCount++;
          // Crédito consumido, pero no se creó el estudio.
           errorMessages.push(i18n.t('studyexportimport:import_failed_no_data', { studyName: requiredInfo.name, defaultValue: `No se pudo crear el estudio "${requiredInfo.name}" (sin datos de respuesta).` }));
          processedStudies.push(null);
          continue;
        }
        
        createdStudiesCount++;
        console.log('Estudio creado:', dbResult);
        const transformedStudy = {
          ...dbResult,
          info: {
            required: dbResult.required_info,
            optional: dbResult.optional_info || {}
          }
        } as Study;
        processedStudies.push(transformedStudy);

      } catch (error: any) {
        failedStudiesCount++;
        const studyName = (typeof study.required_info === 'object' && study.required_info?.name) ? study.required_info.name : 'Un estudio';
        console.error(`Error procesando estudio individual "${studyName}":`, error);
        errorMessages.push(i18n.t('studyexportimport:import_failed_general_error', { studyName, message: error.message, defaultValue: `Error al procesar "${studyName}": ${error.message}` }));
        processedStudies.push(null); // Marcar como nulo en caso de error de parseo o validación
      }
    }

    // Filtrar los que no se pudieron procesar (nulls)
    const successfulImports = processedStudies.filter(s => s !== null) as Study[];

    if (createdStudiesCount > 0) {
      toast({
        title: i18n.t('studyexportimport:import_summary_title_success', { defaultValue: 'Importación completada' }),
        description: i18n.t('studyexportimport:import_summary_desc_success', { count: createdStudiesCount, total: studiesToProcess.length, defaultValue: `Se importaron ${createdStudiesCount} de ${studiesToProcess.length} estudios.` }),
        variant: 'default'
      });
    }

    if (failedStudiesCount > 0) {
      const generalFailMessage = i18n.t('studyexportimport:import_summary_title_failed', { defaultValue: 'Algunos estudios no se pudieron importar' });
      const detailedErrors = errorMessages.join('\n');
      toast({
        title: generalFailMessage,
        description: i18n.t('studyexportimport:import_summary_desc_failed', { count: failedStudiesCount, total: studiesToProcess.length, errors: detailedErrors, defaultValue: `${failedStudiesCount} de ${studiesToProcess.length} estudios fallaron. Errores:\n${detailedErrors}` }),
        variant: 'destructive',
        duration: 10000 // Mostrar más tiempo si hay errores
      });
    }
    
    if (createdStudiesCount === 0 && failedStudiesCount === 0 && studiesToProcess.length > 0) {
        toast({
            title: i18n.t('studyexportimport:import_nothing_imported_title', {defaultValue: "Importación vacía"}),
            description: i18n.t('studyexportimport:import_nothing_imported_desc', {defaultValue: "El archivo no contenía estudios válidos o no se pudieron procesar."}),
            variant: 'destructive'
        });
    }

    // Refrescar créditos en el store al final
    await fetchCredits();
    await fetchUserLimits();

    return successfulImports;

  } catch (error: any) {
    console.error('Error general en importStudies:', error);
    toast({
      title: i18n.t('studyexportimport:import_fatal_error_title', { defaultValue: 'Error fatal de importación' }),
      description: error.message,
      variant: 'destructive'
    });
    // Refrescar créditos incluso si hay un error fatal, por si alguno se consumió antes del error.
    await fetchCredits();
    await fetchUserLimits();
    return []; // Devuelve un array vacío en caso de error fatal
  }
};
