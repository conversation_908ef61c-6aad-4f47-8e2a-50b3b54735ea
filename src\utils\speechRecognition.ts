import 'regenerator-runtime/runtime';

class SpeechRecognitionService {
  private recognition: SpeechRecognition | null = null;
  private onResultCallback: ((text: string) => void) | null = null;
  private onEndCallback: (() => void) | null = null;
  private currentLanguage: string = navigator.language.substring(0, 2);

  constructor() {
    if ('webkitSpeechRecognition' in window) {
      this.recognition = new webkitSpeechRecognition();
      this.setupRecognition();
    } else if ('SpeechRecognition' in window) {
      this.recognition = new SpeechRecognition();
      this.setupRecognition();
    }
  }

  private setupRecognition() {
    if (!this.recognition) return;

    this.recognition.continuous = false;
    this.recognition.interimResults = true;
    this.recognition.lang = this.currentLanguage;

    this.recognition.onresult = (event) => {
      const transcript = Array.from(event.results)
        .map(result => result[0].transcript)
        .join(' ');

      if (this.onResultCallback) {
        this.onResultCallback(transcript);
      }
    };

    this.recognition.onend = () => {
      if (this.onEndCallback) {
        this.onEndCallback();
      }
    };

    this.recognition.onerror = (event) => {
      console.error('Speech Recognition Error:', event.error);
      if (this.onEndCallback) {
        this.onEndCallback();
      }
    };
  }

  public updateLanguage(language: string) {
    if (!this.recognition) return;
    
    // Tomar solo los primeros dos caracteres del código de idioma (ej: 'es' de 'es-ES')
    this.currentLanguage = language.substring(0, 2);
    this.recognition.lang = this.currentLanguage;
  }

  public start(onResult: (text: string) => void, onEnd: () => void) {
    if (!this.recognition) {
      console.error('Speech recognition not supported');
      return;
    }

    this.onResultCallback = onResult;
    this.onEndCallback = onEnd;

    try {
      this.recognition.start();
    } catch (error) {
      console.error('Speech recognition error:', error);
      if (onEnd) onEnd();
    }
  }

  public stop() {
    if (!this.recognition) return;

    try {
      this.recognition.stop();
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
    }
  }

  public isSupported(): boolean {
    return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
  }
}

export const speechRecognition = new SpeechRecognitionService();