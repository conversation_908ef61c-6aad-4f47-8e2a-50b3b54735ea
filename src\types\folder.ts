export interface Folder {
  id: string;
  user_id: string;
  organization_id?: string;
  name: string;
  description?: string;
  parent_folder_id?: string;
  color: string;
  icon: string;
  is_shared: boolean;
  created_at: string;
  updated_at: string;
  
  // Computed properties (not from DB)
  path?: string; // Full path for breadcrumbs
  children?: Folder[]; // Child folders
  study_count?: number; // Total studies in this folder and subfolders
  level?: number; // Depth level in hierarchy
}

export interface FolderTreeNode extends Folder {
  children: FolderTreeNode[];
  studies?: Study[];
  isExpanded?: boolean;
  hasChildren?: boolean;
}

export interface FolderStats {
  total_studies: number;
  total_subfolders: number;
  last_updated: string;
  avg_completion_rate?: number;
}

export interface MoveItemRequest {
  item_id: string;
  item_type: 'study' | 'folder';
  target_folder_id?: string; // null means root level
  target_position?: number;
}

export interface FolderBreadcrumb {
  id: string;
  name: string;
  path: string;
}

export interface BulkExportRequest {
  folder_id?: string; // null means all studies
  include_subfolders: boolean;
  export_format: 'excel' | 'pdf' | 'csv';
  date_range?: {
    start: string;
    end: string;
  };
}

// Extended Study type to include folder information
export interface StudyWithFolder extends Study {
  folder_id?: string;
  folder?: Pick<Folder, 'id' | 'name' | 'color' | 'icon'>;
  folder_path?: string;
} 