// Script para migrar toda la estructura de base de datos al nuevo proyecto Stadler
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtener el directorio actual
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración del nuevo proyecto Supabase (Stadler)
const stadlerSupabaseUrl = process.env.STADLER_SUPABASE_URL;
const stadlerSupabaseKey = process.env.STADLER_SUPABASE_SERVICE_KEY;

if (!stadlerSupabaseUrl || !stadlerSupabaseKey) {
  console.error('Error: Se requieren las variables de entorno STADLER_SUPABASE_URL y STADLER_SUPABASE_SERVICE_KEY');
  console.error('Ejecuta este script con:');
  console.error('STADLER_SUPABASE_URL=tu_url_stadler STADLER_SUPABASE_SERVICE_KEY=tu_clave_secreta node scripts/migrate_to_stadler.js');
  process.exit(1);
}

const supabase = createClient(stadlerSupabaseUrl, stadlerSupabaseKey);

// Lista de migraciones en orden cronológico
const migrations = [
  '20240101000000_initial_schema.sql',
  '20240102000000_add_study_columns.sql', 
  '20240103000000_add_crono_seguido_column.sql',
  '20240118_fix_rls.sql',
  '20240120_create_webhook_logs.sql',
  '20241220_create_policies.sql',
  '20241220_update_user_credits.sql',
  '20241220_update_study_limits_policy.sql',
  '20250225_organizations.sql',
  '20250225_organizations_fix_policies.sql',
  '20250225_organizations_fix_policies_v2.sql',
  '20250225_organizations_fix_rpc.sql',
  '20250225_organizations_v2.sql',
  '20250227_fix_recursion_studies.sql',
  '20250304_fix_studies_rls.sql'
];

async function executeSql(sql, description) {
  console.log(`\n🔄 Ejecutando: ${description}`);
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { 
      query: sql 
    });
    
    if (error) {
      console.error(`❌ Error en ${description}:`, error);
      return false;
    }
    
    console.log(`✅ Completado: ${description}`);
    return true;
  } catch (err) {
    console.error(`❌ Error inesperado en ${description}:`, err);
    return false;
  }
}

async function applyMigrations() {
  console.log('🚀 Iniciando migración completa al proyecto Stadler...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const migrationFile of migrations) {
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', migrationFile);
    
    if (!fs.existsSync(migrationPath)) {
      console.log(`⚠️  Archivo no encontrado: ${migrationFile} - Saltando...`);
      continue;
    }
    
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    const success = await executeSql(sql, migrationFile);
    
    if (success) {
      successCount++;
    } else {
      failCount++;
      console.log('⚠️  Continuando con la siguiente migración...');
    }
    
    // Pequeña pausa entre migraciones
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Resumen de la migración:');
  console.log(`✅ Migraciones exitosas: ${successCount}`);
  console.log(`❌ Migraciones fallidas: ${failCount}`);
  
  return failCount === 0;
}

async function setupEdgeFunctions() {
  console.log('\n🔧 Configurando Edge Functions...');
  console.log('⚠️  Las Edge Functions deben ser desplegadas manualmente usando:');
  console.log('   supabase functions deploy stripe-webhook');
  console.log('   supabase functions deploy stripe-payment');
  console.log('   supabase functions deploy stripe-payment-v2');
  console.log('   supabase functions deploy stripe-cancel');
}

async function setupStorage() {
  console.log('\n💾 Configurando Storage...');
  
  try {
    // Verificar si el bucket 'public' existe
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error al listar buckets:', listError);
      return false;
    }
    
    const publicBucketExists = buckets.some(bucket => bucket.name === 'public');
    
    if (!publicBucketExists) {
      const { data, error } = await supabase.storage.createBucket('public', {
        public: true,
        allowedMimeTypes: ['image/*', 'application/pdf'],
        fileSizeLimit: 52428800 // 50MB
      });
      
      if (error) {
        console.error('❌ Error al crear bucket público:', error);
        return false;
      }
      
      console.log('✅ Bucket público creado exitosamente');
    } else {
      console.log('✅ Bucket público ya existe');
    }
    
    return true;
  } catch (err) {
    console.error('❌ Error inesperado en configuración de storage:', err);
    return false;
  }
}

async function main() {
  console.log('🎯 Migración completa al proyecto Stadler de Cronometras');
  console.log('=' .repeat(60));
  
  // Aplicar todas las migraciones
  const migrationsSuccess = await applyMigrations();
  
  // Configurar storage
  const storageSuccess = await setupStorage();
  
  // Mostrar instrucciones para Edge Functions
  await setupEdgeFunctions();
  
  console.log('\n' + '='.repeat(60));
  
  if (migrationsSuccess && storageSuccess) {
    console.log('🎉 ¡Migración completada exitosamente!');
    console.log('\n📝 Próximos pasos:');
    console.log('1. Desplegar las Edge Functions manualmente');
    console.log('2. Configurar las variables de entorno en el dashboard de Supabase');
    console.log('3. Configurar los webhooks de Stripe para el nuevo dominio');
    console.log('4. Verificar que todas las políticas RLS funcionan correctamente');
  } else {
    console.log('⚠️  Migración completada con algunos errores');
    console.log('Revisa los logs anteriores para más detalles');
  }
}

main().catch(console.error); 