import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Study, UserStudyLimits, SupplementsData, CronoSeguidoRecord, WorkElement } from '../types';
import { toast } from '../components/ui/use-toast';
import i18n from '../i18n';
import { useCreditStore } from './creditStore';

interface StudyState {
  studies: Study[];
  selectedStudy: Study | null;
  credits: number;
  studyLimits: UserStudyLimits | null;
  isLoading: boolean;
  error: string | null;
  fetchStudies: () => Promise<void>;
  fetchCredits: () => Promise<number>;
  checkCredits: () => Promise<{ hasAvailableCredits: boolean }>;
  createStudy: (study: Partial<Study>) => Promise<Study>;
  updateStudy: (id: string, study: Partial<Study>) => Promise<Study>;
  deleteStudy: (id: string) => Promise<void>;
  fetchStudyById: (studyId: string) => Promise<Study | null>;
  clearSelectedStudy: () => void;
  setSelectedStudy: (study: Study | null) => void;
  setSelectedStudyById: (studyId: string) => Promise<Study | null>;
  resetError: () => void;
  updateSupplements: (studyId: string, supplements: SupplementsData) => Promise<void>;
  updateCronoSeguido: (studyId: string, records: CronoSeguidoRecord[]) => Promise<void>;
  averagedElements: WorkElement[];
  addAveragedElement: (element: WorkElement) => void;
  saveCronoSeguidoRecords: (studyId: string, records: CronoSeguidoRecord[]) => Promise<void>;
  cloneStudy: (study: Study) => Promise<Study>;
}

export const useStudyStore = create<StudyState>((set, get) => ({
  studies: [],
  selectedStudy: null,
  credits: 0,
  studyLimits: null,
  isLoading: false,
  error: null,
  averagedElements: [],

  fetchStudies: async () => {
    set({ isLoading: true, error: null });
    try {
      // Get the current user's session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Error getting session:', sessionError);
        set({ 
          studies: [], 
          isLoading: false,
          error: sessionError.message 
        });
        return;
      }

      if (!session?.user?.id) {
        console.log('No active session or user ID');
        set({ 
          studies: [], 
          isLoading: false,
          error: 'No hay sesión activa' 
        });
        return;
      }
      
      console.log('Fetching studies for user:', session.user.id);

      // Get user profile to check show_shared_studies preference
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('show_shared_studies')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        // Continue without profile data (default behavior)
      }

      const showSharedStudies = (profile as any)?.show_shared_studies !== false; // Default to true if not set
      console.log('User preference show_shared_studies:', showSharedStudies);

      let query = supabase
        .from('studies')
        .select(`
          *,
          required_info,
          optional_info
        `);

      // Always include user's own studies
      if (showSharedStudies) {
        // Get all current organization memberships for the user
        const { data: memberships, error: membershipError } = await supabase
          .from('organization_members')
          .select('organization_id, role')
          .eq('user_id', session.user.id);

        if (membershipError) {
          console.error('Error fetching memberships:', membershipError);
          throw membershipError;
        }

        if (memberships && Array.isArray(memberships) && memberships.length > 0) {
          // Get all organization IDs where the user is currently a member
          const orgIds = memberships
            .filter(m => m && typeof m === 'object' && 'organization_id' in m)
            .map(m => m.organization_id);
            
          console.log('User is member of organizations:', orgIds);
          
          // Fetch studies that are either:
          // 1. Created by the current user, OR
          // 2. Shared with an organization where the user is a member
          if (orgIds.length > 0) {
            query = query.or(`user_id.eq.${session.user.id},and(is_shared.eq.true,organization_id.in.(${orgIds.join(',')}))`);
          } else {
            query = query.eq('user_id', session.user.id);
          }
        } else {
          // If user is not a member of any organization, only fetch their own studies
          query = query.eq('user_id', session.user.id);
        }
      } else {
        // If user preference is to NOT show shared studies, only fetch their own studies
        console.log('User disabled shared studies, fetching only own studies');
        query = query.eq('user_id', session.user.id);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching studies:', error);
        throw error;
      }

      if (!data) {
        console.log('No studies found');
        set({ studies: [], isLoading: false });
        return;
      }

      console.log('Found studies:', data.length);
      
      // Add backwards compatibility for .info
      const studies = data.map(study => {
        // Process required_info
        let required_info = study.required_info;
        if (typeof required_info === 'string') {
          try {
            required_info = JSON.parse(required_info);
          } catch (e) {
            console.error('Error parsing required_info:', e);
          }
        }

        // Process optional_info
        let optional_info = study.optional_info;
        if (typeof optional_info === 'string') {
          try {
            optional_info = JSON.parse(optional_info);
          } catch (e) {
            console.error('Error parsing optional_info:', e);
          }
        }

        return {
          ...study,
          required_info,
          optional_info,
          info: {
            required: required_info,
            optional: optional_info || {}
          }
        };
      });

      set({ studies, isLoading: false });
    } catch (error) {
      console.error('Error in fetchStudies:', error);
      set({ error: (error as Error).message, isLoading: false, studies: [] });
    }
  },

  fetchCredits: async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) return 0;

      const { data: limits, error } = await supabase
        .from('user_study_limits')
        .select('*')
        .eq('user_id', session.user.id)
        .single();

      if (error) {
        console.error('Error fetching credits:', error);
        return 0;
      }

      if (limits && typeof limits === 'object' && !('error' in limits)) {
        // Si tiene suscripción activa y está vigente, devolver Infinity
        if (limits.subscription_plan && limits.subscription_end_date) {
          const endDateStr = String(limits.subscription_end_date);
          const endDate = new Date(endDateStr);
          if (endDate > new Date()) {
            set({ credits: Infinity });
            return Infinity;
          }
        }
        
        // Verificar si los límites de estudio existen y no son un error
        if (limits && typeof limits === 'object' && !('error' in limits)) {
          // Acceder a las propiedades solo si existen y convertirlas a número
          const monthlyCredits = 'monthly_credits' in limits ? Number(limits.monthly_credits) || 0 : 0;
          const usedMonthlyCredits = 'used_monthly_credits' in limits ? Number(limits.used_monthly_credits) || 0 : 0;
          const extraCredits = 'extra_credits' in limits ? Number(limits.extra_credits) || 0 : 0;
          const usedExtraCredits = 'used_extra_credits' in limits ? Number(limits.used_extra_credits) || 0 : 0;
          
          // Realizar cálculos con los valores numéricos
          const availableMonthlyCredits = Math.max(0, monthlyCredits - usedMonthlyCredits);
          const availableExtraCredits = Math.max(0, extraCredits - usedExtraCredits);
          const totalCredits = availableMonthlyCredits + availableExtraCredits;
          
          set({ credits: totalCredits });
          return totalCredits;
        }
      }
      return 0;
    } catch (error) {
      console.error('Error fetching credits:', error);
      return 0;
    }
  },

  checkCredits: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No hay sesión activa');

      const { data, error } = await supabase
        .from('user_study_limits')
        .select('monthly_credits, extra_credits, used_monthly_credits, used_extra_credits')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;

      // Verificar si los límites de estudio existen y no son un error
      if (data && typeof data === 'object' && !('error' in data)) {
        // Acceder a las propiedades solo si existen y convertirlas a número
        const monthlyCredits = 'monthly_credits' in data ? Number(data.monthly_credits) || 0 : 0;
        const usedMonthlyCredits = 'used_monthly_credits' in data ? Number(data.used_monthly_credits) || 0 : 0;
        const extraCredits = 'extra_credits' in data ? Number(data.extra_credits) || 0 : 0;
        const usedExtraCredits = 'used_extra_credits' in data ? Number(data.used_extra_credits) || 0 : 0;
        
        // Realizar cálculos con los valores numéricos
        const remainingCredits = (monthlyCredits - usedMonthlyCredits) + 
                               (extraCredits - usedExtraCredits);
        return { hasAvailableCredits: remainingCredits > 0 };
      }
      return { hasAvailableCredits: false };
    } catch (error) {
      set({ error: (error as Error).message });
      return { hasAvailableCredits: false };
    }
  },

  createStudy: async (study: Partial<Study>) => {
    console.log('Iniciando createStudy:', study);
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No hay sesión activa');

      // Consumir un crédito ANTES de intentar crear el estudio
      const { consumeCredit, fetchUserLimits, fetchCredits } = useCreditStore.getState();
      const creditConsumed = await consumeCredit();

      if (!creditConsumed) {
        // Actualizar los límites/créditos en el estado por si acaso
        await fetchUserLimits(); 
        const noCreditsMessage = i18n.t('study:no_credits.message', { ns: 'study', defaultValue: "No tienes suficientes créditos para crear un nuevo estudio. Por favor, compra más créditos para continuar." });
        console.error('Error al consumir crédito para createStudy:', noCreditsMessage);
        set({ error: noCreditsMessage, isLoading: false });
        throw new Error(noCreditsMessage);
      }

      // Asegurarse de que todos los campos necesarios estén presentes
      const studyToCreate = {
        ...study,
        user_id: user.id,
        // Asegurar que required_info y optional_info sean objetos, incluso si están vacíos
        required_info: study.required_info || {},
        optional_info: {
          ...(study.optional_info || {}),
          // Si visibleEnBiblioteca no viene en optional_info, por defecto es false
          visibleEnBiblioteca: study.optional_info?.visibleEnBiblioteca === true ? true : false,
        },
        elements: study.elements || [],
        time_records: study.time_records || {},
        supplements: study.supplements || {}
      };

      // Si organizationId está definido, añadirlo al estudio
      const organizationsStore = get().organizationsStore;
      if (organizationsStore?.selectedOrganization?.id) {
        studyToCreate.organization_id = organizationsStore.selectedOrganization.id;
      }

      // Crear el estudio en la base de datos
      const { data, error } = await supabase
        .from('studies')
        .insert([studyToCreate])
        .select()
        .single();

      if (error) throw error;

      // Actualizar el estado
      set((state) => ({
        isLoading: false,
        studies: [...state.studies, data],
        selectedStudy: data
      }));

      // Refrescar los créditos después de una creación exitosa
      await fetchCredits();
      await fetchUserLimits();

      console.log('Study created successfully:', data);
      return data;
    } catch (error: any) {
      console.error('Error creando el estudio:', error);
      set({ error: error.message, isLoading: false });
      throw error; // Re-lanzar para que StudyForm pueda manejarlo si es necesario
    }
  },

  updateStudy: async (id: string, updates: Partial<Study>) => {
    console.log('Iniciando updateStudy:', id, updates);
    set({ isLoading: true, error: null });
    try {
      // Combinar actualizaciones con los datos existentes del estudio
      const existingStudy = get().studies.find(s => s.id === id);
      if (!existingStudy) throw new Error('Estudio no encontrado');

      // Crear una estructura limpia de datos para actualizar
      const updatedStudy = {
        ...updates,
        optional_info: {
          ...(existingStudy.optional_info || {}),
          ...(updates.optional_info || {}),
          // Asegurar que visibleEnBiblioteca se actualiza si está presente en optional_info
          visibleEnBiblioteca: updates.optional_info?.visibleEnBiblioteca !== undefined 
            ? updates.optional_info.visibleEnBiblioteca 
            : existingStudy.optional_info?.visibleEnBiblioteca || false,
        }
      };

      // Filtrar campos que no deben enviarse a la base de datos
      const cleanUpdatedStudy = { ...updatedStudy };
      delete (cleanUpdatedStudy as any).info;

      // Actualizar solo los campos proporcionados
      const { data, error } = await supabase
        .from('studies')
        .update(cleanUpdatedStudy)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Actualizar el estado
      set((state) => ({
        isLoading: false,
        studies: state.studies.map(s => s.id === id ? { ...s, ...data } : s),
        selectedStudy: state.selectedStudy?.id === id ? { ...state.selectedStudy, ...data } : state.selectedStudy
      }));

      console.log('Study updated successfully:', data);
      return data;
    } catch (error: any) {
      console.error('Error actualizando el estudio:', error);
      set({ error: error.message, isLoading: false });
      throw error; // Re-lanzar para que StudyForm pueda manejarlo
    }
  },

  deleteStudy: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No hay sesión activa');

      // Get all organization memberships for the user
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        throw membershipError;
      }

      // First, get the study to check if it belongs to an organization where the user is a member
      let query = supabase
        .from('studies')
        .select('id, user_id, organization_id')
        .eq('id', id);

      if (memberships && Array.isArray(memberships) && memberships.length > 0) {
        const orgIds = memberships
          .filter(m => m && typeof m === 'object' && 'organization_id' in m)
          .map(m => m.organization_id);

        console.log('User is member of organizations:', orgIds);
        
        // Fetch studies that are either:
        // 1. Created by the current user, OR
        // 2. Shared with an organization where the user is a member
        if (orgIds.length > 0) {
          query = query.or(`user_id.eq.${user.id},organization_id.in.(${orgIds.join(',')})`);
        } else {
          query = query.eq('user_id', user.id);
        }
      } else {
        // If not in any organization, only fetch their own studies
        query = query.eq('user_id', user.id);
      }

      const { data: studyToDelete, error: fetchError } = await query.single();

      if (fetchError) {
        console.error('Error fetching study to delete:', fetchError);
        throw fetchError;
      }

      if (!studyToDelete) {
        throw new Error('No se encontró el estudio o no tienes permisos para eliminarlo');
      }

      // Verificar si el usuario es el propietario del estudio
      if (studyToDelete.user_id !== user.id) {
        // Si no es el propietario, mostrar una notificación y no continuar con la eliminación
        console.warn('El usuario no es propietario del estudio. No se puede eliminar.');
        toast({
          title: i18n.t('study:permission_error.title'),
          description: i18n.t('study:permission_error.message'),
          variant: "destructive",
        });
        set({ isLoading: false });
        return;
      }

      // Now delete the study
      const { error } = await supabase
        .from('studies')
        .delete()
        .eq('id', id);

      if (error) throw error;

      set(state => ({
        studies: state.studies.filter(s => s.id !== id),
        selectedStudy: state.selectedStudy?.id === id ? null : state.selectedStudy,
        isLoading: false
      }));
    } catch (error) {
      console.error('Error deleting study:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  fetchStudyById: async (id: string) => {
    console.log('Iniciando fetchStudyById para:', id);
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No hay sesión activa');

      // Obtener el estudio por su ID
      const { data, error } = await supabase
        .from('studies')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      // Verificar si el usuario es el propietario del estudio
      if (data && 'user_id' in data && data.user_id !== user.id) {
        // Si no es el propietario, verificar si pertenece a la organización que comparte el estudio
        const { data: memberData, error: memberError } = await supabase
          .from('organization_members')
          .select('organization_id, role')
          .eq('user_id', user.id)
          .single();

        // Si hay un error o el usuario no pertenece a ninguna organización, denegar acceso
        if (memberError || !memberData || !('organization_id' in memberData)) {
          throw new Error('No tienes permiso para acceder a este estudio');
        }

        // Verificar si el estudio está compartido con la organización del usuario
        if (
          !('organization_id' in data) || 
          !data.organization_id || 
          data.organization_id !== memberData.organization_id || 
          !('is_shared' in data) || 
          !data.is_shared
        ) {
          throw new Error('No tienes permiso para acceder a este estudio');
        }
      }

      // Asegurarse de que el estudio tenga la estructura correcta
      if (data) {
        // Verificar si required_info existe
        if (!('required_info' in data) || !data.required_info) {
          (data as any).required_info = {};
        }
        
        // Verificar si optional_info existe
        if (!('optional_info' in data) || !data.optional_info) {
          (data as any).optional_info = {};
        }
        
        // Establecer el estudio seleccionado
        set({ selectedStudy: data as Study });
        return data as Study;
      }
      
      return null;
    } catch (error: any) {
      console.error('Error fetching study:', error);
      set({ error: error.message || 'Error al obtener el estudio' });
      return null;
    } finally {
      set({ isLoading: false });
    }
  },

  clearSelectedStudy: () => {
    set({ selectedStudy: null });
  },

  setSelectedStudy: (study) => set({ selectedStudy: study }),
  setSelectedStudyById: async (id: string) => {
    console.log('Iniciando setSelectedStudyById para:', id);
    if (!id) {
      console.warn('setSelectedStudyById: ID no proporcionado');
      set({ selectedStudy: null });
      return null;
    }

    try {
      // Utilizamos fetchStudyById para obtener el estudio con la estructura correcta
      const study = await get().fetchStudyById(id);
      
      if (!study) {
        console.warn('setSelectedStudyById: No se encontró el estudio con ID:', id);
        return null;
      }
      
      console.log('Estudio seleccionado en setSelectedStudyById:', study);
      // Actualizar el estado con el estudio seleccionado
      set({ selectedStudy: study });
      return study;
    } catch (error) {
      console.error('Error en setSelectedStudyById:', error);
      set({ error: (error as Error).message });
      return null;
    }
  },
  resetError: () => set({ error: null }),

  updateSupplements: async (studyId: string, supplements: SupplementsData) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No hay sesión activa');

      // Get all organization memberships for the user
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        throw membershipError;
      }

      // First, get the study to check if it belongs to an organization where the user is a member
      let query = supabase
        .from('studies')
        .select('id, user_id, organization_id')
        .eq('id', studyId);

      if (memberships && Array.isArray(memberships) && memberships.length > 0) {
        const orgIds = memberships
          .filter(m => m && typeof m === 'object' && 'organization_id' in m)
          .map(m => m.organization_id);

        console.log('User is member of organizations:', orgIds);
        
        // Fetch studies that are either:
        // 1. Created by the current user, OR
        // 2. Shared with an organization where the user is a member
        if (orgIds.length > 0) {
          query = query.or(`user_id.eq.${user.id},organization_id.in.(${orgIds.join(',')})`);
        } else {
          query = query.eq('user_id', user.id);
        }
      } else {
        // If not in any organization, only fetch their own studies
        query = query.eq('user_id', user.id);
      }

      const { data: studyToUpdate, error: fetchError } = await query.single();

      if (fetchError) {
        console.error('Error fetching study to update supplements:', fetchError);
        throw fetchError;
      }

      if (!studyToUpdate) {
        throw new Error('No se encontró el estudio o no tienes permisos para actualizarlo');
      }

      // Verificar si el usuario es el propietario del estudio
      if (studyToUpdate.user_id !== user.id) {
        // Si no es el propietario, mostrar una notificación y no continuar con la actualización
        console.warn('El usuario no es propietario del estudio. No se pueden guardar los cambios en suplementos.');
        toast({
          title: i18n.t('study:permission_error.title'),
          description: i18n.t('study:changes_not_saved'),
          variant: "destructive",
        });
        set({ isLoading: false });
        // Devolver el estudio sin cambios para que la UI se actualice correctamente
        return;
      }

      // Now update the supplements
      const { error } = await supabase
        .from('studies')
        .update({ supplements })
        .eq('id', studyId);

      if (error) throw error;

      // Update the local state
      const { studies } = get();
      const updatedStudies = studies.map(study => 
        study.id === studyId 
          ? { ...study, supplements }
          : study
      );

      set({ 
        studies: updatedStudies,
        selectedStudy: get().selectedStudy 
          ? { ...get().selectedStudy, supplements }
          : null,
        isLoading: false 
      });
    } catch (error) {
      console.error('Error updating supplements:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  updateCronoSeguido: async (studyId: string, records: CronoSeguidoRecord[]) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No hay sesión activa');

      // Get all organization memberships for the user
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        throw membershipError;
      }

      // First, get the study to check if it belongs to an organization where the user is a member
      let query = supabase
        .from('studies')
        .select('id, user_id, organization_id')
        .eq('id', studyId);

      if (memberships && Array.isArray(memberships) && memberships.length > 0) {
        const orgIds = memberships
          .filter(m => m && typeof m === 'object' && 'organization_id' in m)
          .map(m => m.organization_id);

        console.log('User is member of organizations:', orgIds);
        
        // Fetch studies that are either:
        // 1. Created by the current user, OR
        // 2. Shared with an organization where the user is a member
        if (orgIds.length > 0) {
          query = query.or(`user_id.eq.${user.id},organization_id.in.(${orgIds.join(',')})`);
        } else {
          query = query.eq('user_id', user.id);
        }
      } else {
        // If not in any organization, only fetch their own studies
        query = query.eq('user_id', user.id);
      }

      const { data: studyToUpdate, error: fetchError } = await query.single();

      if (fetchError) {
        console.error('Error fetching study to update crono seguido:', fetchError);
        throw fetchError;
      }

      if (!studyToUpdate) {
        throw new Error('No se encontró el estudio o no tienes permisos para actualizarlo');
      }

      // Verificar si el usuario es el propietario del estudio
      if (studyToUpdate.user_id !== user.id) {
        // Si no es el propietario, mostrar una notificación y no continuar con la actualización
        console.warn('El usuario no es propietario del estudio. No se pueden guardar los cambios en crono seguido.');
        toast({
          title: i18n.t('study:permission_error.title'),
          description: i18n.t('study:changes_not_saved'),
          variant: "destructive",
        });
        // Devolver el estudio sin cambios para que la UI se actualice correctamente
        return get().selectedStudy;
      }

      // Ensure each record has an ID
      const recordsWithIds = records.map(record => ({
        ...record,
        id: record.id || Math.random().toString(36).substring(2) + Date.now().toString(36)
      }));

      // Now update the crono seguido records
      const { data: study, error } = await supabase
        .from('studies')
        .update({
          crono_seguido_records: recordsWithIds
        })
        .eq('id', studyId)
        .select()
        .single();

      if (error) throw error;

      if (study) {
        set(state => ({
          ...state,
          selectedStudy: {
            ...state.selectedStudy!,
            crono_seguido_records: recordsWithIds
          }
        }));
      }

      return study;
    } catch (error) {
      console.error('Error updating crono seguido records:', error);
      throw error;
    }
  },

  addAveragedElement: (element: WorkElement) => 
    set((state) => ({
      averagedElements: [...state.averagedElements, element]
    })),

  saveCronoSeguidoRecords: async (studyId: string, records: CronoSeguidoRecord[]): Promise<void> => {
    try {
      // Actualizar los registros en la base de datos
      const { error } = await supabase
        .from('studies')
        .update({
          crono_seguido_records: records as any,
          updated_at: new Date().toISOString(),
        } as any)
        .eq('id', studyId);

      if (error) {
        console.error('Error saving crono seguido records:', error);
        return;
      }

      // Actualizar el estudio seleccionado si es el mismo
      const state = useStudyStore.getState();
      if (state.selectedStudy?.id === studyId) {
        useStudyStore.getState().set({
          selectedStudy: {
            ...state.selectedStudy,
            crono_seguido_records: records,
            updated_at: new Date().toISOString(),
          }
        });
      }
    } catch (error) {
      console.error('Error saving crono seguido records:', error);
    }
  },

  saveSupplements: async (studyId: string, supplements: SupplementsData): Promise<void> => {
    try {
      // Actualizar los suplementos en la base de datos
      const { error } = await supabase
        .from('studies')
        .update({
          supplements: supplements as any,
          updated_at: new Date().toISOString(),
        } as any)
        .eq('id', studyId);

      if (error) {
        console.error('Error saving supplements:', error);
        return;
      }

      // Actualizar el estudio seleccionado si es el mismo
      const state = get();
      if (state.selectedStudy?.id === studyId) {
        set({
          selectedStudy: {
            ...state.selectedStudy,
            supplements: supplements,
            updated_at: new Date().toISOString(),
          }
        });
      }
    } catch (error) {
      console.error('Error in saveSupplements:', error);
    }
  },

  cloneStudy: async (studyToClone: Study) => {
    console.log('Iniciando cloneStudy para:', studyToClone.id);
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error(i18n.t('study:no_active_session', { defaultValue: 'No hay sesión activa' }));

      const { consumeCredit, fetchCredits, fetchUserLimits } = useCreditStore.getState();
      const creditConsumed = await consumeCredit();

      if (!creditConsumed) {
        await fetchUserLimits();
        const noCreditsMessage = i18n.t('study:no_credits_clone', { ns: 'study', defaultValue: "No tienes suficientes créditos para clonar el estudio." });
        console.error('Error al consumir crédito para cloneStudy:', noCreditsMessage);
        set({ error: noCreditsMessage, isLoading: false });
        throw new Error(noCreditsMessage);
      }

      // Parsear required_info y optional_info si son strings
      const parsedRequiredInfo = typeof studyToClone.required_info === 'string'
        ? JSON.parse(studyToClone.required_info)
        : studyToClone.required_info;
      const parsedOptionalInfo = typeof studyToClone.optional_info === 'string'
        ? JSON.parse(studyToClone.optional_info)
        : studyToClone.optional_info;

      // Preparar datos del estudio clonado explícitamente con columnas válidas
      const clonedStudyData: Omit<Study, 'id' | 'created_at' | 'updated_at' | 'user_id' | 'info'> & { user_id: string, created_at: string, updated_at: string, required_info: any, optional_info: any } = {
        // Copiar campos existentes que son válidos para la tabla 'studies'
        elements: studyToClone.elements || [],
        time_records: studyToClone.time_records || {},
        supplements: studyToClone.supplements || {},
        crono_seguido_records: studyToClone.crono_seguido_records || [],
        is_shared: studyToClone.is_shared !== undefined ? studyToClone.is_shared : false, // Mantener o resetear
        organization_id: studyToClone.organization_id || null, // Mantener si existe
        // Campos específicos para el clon
        user_id: user.id,
        required_info: {
          ...parsedRequiredInfo,
          name: `${parsedRequiredInfo.name} (Copia)`,
          date: new Date().toISOString().split('T')[0],
        },
        optional_info: parsedOptionalInfo,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      // Asegurarse de que organization_id sea null si no está presente, en lugar de undefined
      if (!clonedStudyData.organization_id) {
        clonedStudyData.organization_id = null;
      }

      // Insertar el estudio clonado en la base de datos
      const { data: newStudy, error: insertError } = await supabase
        .from('studies')
        .insert(clonedStudyData as any) // Usar 'as any' si TypeScript se queja por la estructura exacta
        .select('*')
        .single();

      if (insertError) {
        console.error('Error de Supabase al clonar estudio:', insertError);
        set({ error: insertError.message, isLoading: false });
        throw new Error(`Error al clonar el estudio: ${insertError.message}`);
      }

      if (!newStudy) {
        set({ error: 'No se pudo clonar el estudio: no se recibieron datos del servidor', isLoading: false });
        throw new Error('No se pudo clonar el estudio: no se recibieron datos del servidor');
      }

      // Transformar el estudio para que coincida con la estructura esperada en el cliente (incluyendo `info`)
      const createdStudy = {
        ...newStudy,
        required_info: typeof newStudy.required_info === 'string' ? JSON.parse(newStudy.required_info) : newStudy.required_info,
        optional_info: typeof newStudy.optional_info === 'string' ? JSON.parse(newStudy.optional_info) : newStudy.optional_info,
        info: {
          required: typeof newStudy.required_info === 'string' ? JSON.parse(newStudy.required_info) : newStudy.required_info,
          optional: typeof newStudy.optional_info === 'string' ? JSON.parse(newStudy.optional_info) : newStudy.optional_info || {}
        }
      } as Study;

      set(state => ({
        studies: [createdStudy, ...state.studies],
        selectedStudy: createdStudy,
        isLoading: false,
        error: null
      }));

      await fetchCredits();
      await fetchUserLimits();

      toast({
        title: i18n.t('study:cloneSuccess', { defaultValue: 'Estudio clonado con éxito' }),
        variant: "default",
      });

      return createdStudy;
    } catch (error: any) {
      console.error('Error en cloneStudy:', error);
      if (!get().error) {
        set({ error: error.message, isLoading: false });
      }
      throw error;
    }
  },
}));

// Función para obtener los límites de estudios del usuario
const fetchUserStudyLimits = async () => {
  useStudyStore.getState().set({ isLoading: true, error: null });
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No hay sesión activa');

    // Obtener los límites de estudios del usuario
    const { data, error } = await supabase
      .from('user_study_limits')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) {
      // Si no hay límites definidos, crear un registro por defecto
      if (error.code === 'PGRST116') {
        const defaultLimits: UserStudyLimits = {
          user_id: user.id,
          max_studies: 5,
          current_studies: 0,
          limit_reset_date: new Date(
            new Date().getFullYear(),
            new Date().getMonth() + 1,
            1
          ).toISOString(),
        };

        const { data: newLimits, error: insertError } = await supabase
          .from('user_study_limits')
          .insert(defaultLimits)
          .select()
          .single();

        if (insertError) throw insertError;
        
        if (newLimits) {
          useStudyStore.getState().set({ userStudyLimits: newLimits as UserStudyLimits });
          return newLimits as UserStudyLimits;
        }
        
        throw new Error('No se pudieron crear los límites de estudios');
      } else {
        throw error;
      }
    }

    // Verificar si es necesario resetear los límites
    if (data && 'limit_reset_date' in data && data.limit_reset_date) {
      // Asegurarse de que limit_reset_date sea una cadena antes de convertirla
      const resetDateStr = typeof data.limit_reset_date === 'string' 
        ? data.limit_reset_date 
        : String(data.limit_reset_date);
        
      const resetDate = new Date(resetDateStr);
      const now = new Date();

      if (now > resetDate) {
        // Actualizar la fecha de reset y resetear el contador de estudios
        const updatedLimits = {
          ...data,
          current_studies: 0,
          limit_reset_date: new Date(
            now.getFullYear(),
            now.getMonth() + 1,
            1
          ).toISOString(),
        };

        const { data: updated, error: updateError } = await supabase
          .from('user_study_limits')
          .update(updatedLimits as any)
          .eq('user_id', user.id)
          .select()
          .single();

        if (updateError) throw updateError;
        
        if (updated) {
          useStudyStore.getState().set({ userStudyLimits: updated as UserStudyLimits });
          return updated as UserStudyLimits;
        }
        
        throw new Error('No se pudieron actualizar los límites de estudios');
      }
    }

    if (data) {
      useStudyStore.getState().set({ userStudyLimits: data as UserStudyLimits });
      return data as UserStudyLimits;
    }
    
    throw new Error('No se pudieron obtener los límites de estudios');
  } catch (error: any) {
    console.error('Error fetching user study limits:', error);
    useStudyStore.getState().set({ error: error.message || 'Error al obtener los límites de estudios' });
    return null;
  } finally {
    useStudyStore.getState().set({ isLoading: false });
  }
};