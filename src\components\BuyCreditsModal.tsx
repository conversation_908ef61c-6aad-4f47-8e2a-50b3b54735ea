import React from 'react';
import { Dialog, DialogContent, DialogTitle, DialogClose } from './ui/dialog';
import { Button } from './ui/button';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useToast } from './ui/use-toast';

interface BuyCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  type?: 'credits' | 'subscription';
  onSuccess?: () => void;
}

export function BuyCreditsModal({
  isOpen,
  onClose,
  type = 'subscription',
  onSuccess
}: BuyCreditsModalProps) {
  const { t } = useTranslation(['subscription', 'credits']);
  const { toast } = useToast();

  const handleRequest = () => {
    try {
      const subject = t(type === 'subscription' ? 'subscription:requestSubject' : 'credits:requestSubject');
      const body = t(type === 'subscription' ? 'subscription:requestBody' : 'credits:requestBody');

      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.location.href = mailtoLink;

      toast({
        description: t(type === 'subscription' ? 'subscription:requestSent' : 'credits:requestSent')
      });

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error:', error);
      toast({
        description: t(type === 'subscription' ? 'subscription:requestError' : 'credits:requestError'),
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogTitle className="flex items-center justify-between">
          {t(type === 'subscription' ? 'subscription:title' : 'credits:title')}
          <DialogClose asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogClose>
        </DialogTitle>

        <div className="space-y-4 py-4">
          <p className="text-gray-600">
            {t(type === 'subscription' ? 'subscription:description' : 'credits:description')}
          </p>

          <div className="flex justify-end">
            <Button onClick={handleRequest}>
              {t(type === 'subscription' ? 'subscription:request' : 'credits:request')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
