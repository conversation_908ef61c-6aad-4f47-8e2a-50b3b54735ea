-- Eliminar tabla transactions y sus políticas
-- Esta tabla ya no se usa después de remover Stripe

-- Primero eliminamos las políticas RLS si existen
DROP POLICY IF EXISTS "Service role has full access" ON public.transactions;
DROP POLICY IF EXISTS "System can insert transactions" ON public.transactions;
DROP POLICY IF EXISTS "Users can view their own transactions" ON public.transactions;

-- Eliminamos la tabla transactions
DROP TABLE IF EXISTS public.transactions CASCADE;

-- Si hay funciones relacionadas con transacciones, también las eliminamos
DROP FUNCTION IF EXISTS public.handle_transaction_insert() CASCADE;
DROP FUNCTION IF EXISTS public.process_payment_transaction() CASCADE;

-- <PERSON><PERSON><PERSON> cualquier tipo relacionado si existe
DROP TYPE IF EXISTS public.transaction_status CASCADE;
DROP TYPE IF EXISTS public.payment_method CASCADE; 