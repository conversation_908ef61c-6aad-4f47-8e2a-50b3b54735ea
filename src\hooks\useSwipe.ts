import { useState, useCallback } from 'react';

interface SwipeHandlers {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
}

export const useSwipe = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50
}: SwipeHandlers) => {
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent | React.TouchEvent) => {
    setTouchStart({
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    });
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent | React.TouchEvent) => {
    if (!touchStart) return;

    const deltaX = touchStart.x - e.touches[0].clientX;
    const deltaY = touchStart.y - e.touches[0].clientY;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Horizontal swipe
      if (Math.abs(deltaX) > threshold) {
        if (deltaX > 0 && onSwipeLeft) {
          onSwipeLeft();
          setTouchStart(null);
        } else if (deltaX < 0 && onSwipeRight) {
          onSwipeRight();
          setTouchStart(null);
        }
      }
    } else {
      // Vertical swipe
      if (Math.abs(deltaY) > threshold) {
        if (deltaY > 0 && onSwipeUp) {
          onSwipeUp();
          setTouchStart(null);
        } else if (deltaY < 0 && onSwipeDown) {
          onSwipeDown();
          setTouchStart(null);
        }
      }
    }
  }, [touchStart, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

  const handleTouchEnd = useCallback(() => {
    setTouchStart(null);
  }, []);

  return {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd
  };
};