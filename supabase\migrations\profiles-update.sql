-- =====================================================
-- ACTUALIZACIÓN TABLA PROFILES - PROYECTO STADLER
-- =====================================================
-- Ejecutar en SQL Editor del dashboard de Supabase Stadler

-- Agregar todas las columnas faltantes a la tabla profiles
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS default_time_unit TEXT DEFAULT 'dmh',
ADD COLUMN IF NOT EXISTS default_language TEXT DEFAULT 'es',
ADD COLUMN IF NOT EXISTS default_contingency NUMERIC(5,2) DEFAULT 5.00,
ADD COLUMN IF NOT EXISTS minutes_per_shift INTEGER DEFAULT 480,
ADD COLUMN IF NOT EXISTS company_name TEXT,
ADD COLUMN IF NOT EXISTS default_normal_activity NUMERIC(5,2) DEFAULT 100.00,
ADD COLUMN IF NOT EXISTS default_optimal_activity NUMERIC(5,2) DEFAULT 133.00,
ADD COLUMN IF NOT EXISTS show_shared_studies BOOLEAN DEFAULT true;

-- Verificar las columnas actualizadas
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position; 