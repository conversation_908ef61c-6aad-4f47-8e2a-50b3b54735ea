import * as XLSX from 'xlsx';
import { ElementInstance } from '../types';

export interface ExcelRow {
  [key: string]: any;
}

export interface ColumnMapping {
  description: string;
  time: string;
  activity: string;
  frequency: string;
  type: string;
  repetitionType: string;
  supplements: string;
  repetitions: string;
  cycles: string;
}

export interface ExcelProcessingResult {
  headers: string[];
  data: ExcelRow[];
  errors: string[];
}

/**
 * Procesa un archivo Excel y extrae los datos
 */
export const processExcelFile = async (file: File): Promise<ExcelProcessingResult> => {
  const errors: string[] = [];
  
  try {
    // Validar tipo de archivo
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    
    if (!validTypes.includes(file.type)) {
      errors.push('Tipo de archivo no válido. Use .xlsx o .xls');
      return { headers: [], data: [], errors };
    }

    // Leer archivo
    const arrayBuffer = await file.arrayBuffer();
    const data = new Uint8Array(arrayBuffer);
    const workbook = XLSX.read(data, { type: 'array' });
    
    // Obtener primera hoja
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convertir a JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
    
    if (jsonData.length < 2) {
      errors.push('El archivo debe tener al menos una fila de encabezados y datos');
      return { headers: [], data: [], errors };
    }

    // Primera fila como headers
    const headers = jsonData[0].map((header: any) => String(header || '').trim());
    
    // Resto de filas como datos
    const rows = jsonData.slice(1).map(row => {
      const rowObj: ExcelRow = {};
      headers.forEach((header, index) => {
        rowObj[header] = row[index] || '';
      });
      return rowObj;
    }).filter(row => Object.values(row).some(val => val !== ''));

    return {
      headers,
      data: rows,
      errors
    };
    
  } catch (error) {
    errors.push(error instanceof Error ? error.message : 'Error al procesar el archivo');
    return { headers: [], data: [], errors };
  }
};

/**
 * Auto-mapea columnas basándose en nombres comunes
 */
export const autoMapColumns = (headers: string[]): Partial<ColumnMapping> => {
  const mapping: Partial<ColumnMapping> = {};
  
  headers.forEach(header => {
    const lowerHeader = header.toLowerCase();
    
    if (lowerHeader.includes('descripci') || lowerHeader.includes('description')) {
      mapping.description = header;
    } else if (lowerHeader.includes('tiempo') || lowerHeader.includes('time')) {
      mapping.time = header;
    } else if (lowerHeader.includes('actividad') || lowerHeader.includes('activity')) {
      mapping.activity = header;
    } else if (lowerHeader.includes('frecuencia') || lowerHeader.includes('frequency')) {
      mapping.frequency = header;
    } else if (lowerHeader.includes('tipo') || lowerHeader.includes('type')) {
      mapping.type = header;
    } else if (lowerHeader.includes('suplemento') || lowerHeader.includes('supplement')) {
      mapping.supplements = header;
    } else if (lowerHeader.includes('repeticion') || lowerHeader.includes('repetition')) {
      mapping.repetitions = header;
    } else if (lowerHeader.includes('ciclo') || lowerHeader.includes('cycle')) {
      mapping.cycles = header;
    }
  });
  
  return mapping;
};

/**
 * Convierte datos de Excel a elementos del estudio
 */
export const convertExcelToElements = (
  data: ExcelRow[], 
  mapping: ColumnMapping
): ElementInstance[] => {
  return data.map((row, index) => {
    const element: ElementInstance = {
      id: crypto.randomUUID(),
      name: row[mapping.description] || `Elemento ${index + 1}`,
      description: row[mapping.description] || '',
      time: parseFloat(row[mapping.time]) || 0,
      activity: parseFloat(row[mapping.activity]) || 100,
      type: mapElementType(row[mapping.type]),
      position: index + 1,
      repetition_type: mapRepetitionType(row[mapping.repetitionType]),
      frequency_cycles: parseInt(row[mapping.cycles]) || 1,
      frequency_repetitions: parseInt(row[mapping.repetitions]) || 1,
      supplements: row[mapping.supplements] ? [{
        id: crypto.randomUUID(),
        name: 'Suplemento importado',
        description: 'Suplemento importado desde Excel',
        percentage: parseFloat(row[mapping.supplements]) || 0,
        is_forced: true,
        points: {},
        factor_selections: {}
      }] : []
    };
    
    return element;
  });
};

/**
 * Mapea tipos de elemento desde Excel a tipos válidos
 */
const mapElementType = (excelType: string): string => {
  if (!excelType) return 'machine-stopped';
  
  const type = excelType.toLowerCase();
  
  if (type.includes('parada') || type.includes('stopped')) {
    return 'machine-stopped';
  } else if (type.includes('marcha') || type.includes('running')) {
    return 'machine-running';
  } else if (type.includes('tiempo') || type.includes('time')) {
    return 'machine-time';
  }
  
  return 'machine-stopped';
};

/**
 * Mapea tipos de repetición desde Excel a tipos válidos
 */
const mapRepetitionType = (excelType: string): string => {
  if (!excelType) return 'repetitive';
  
  const type = excelType.toLowerCase();
  
  if (type.includes('repetitiv') || type.includes('repetitive')) {
    return 'repetitive';
  } else if (type.includes('frecuen') || type.includes('frequency')) {
    return 'frequency';
  } else if (type.includes('maquin') || type.includes('machine')) {
    return 'machine';
  }
  
  return 'repetitive';
};

/**
 * Valida el mapeo de columnas
 */
export const validateColumnMapping = (
  mapping: ColumnMapping,
  studyInfo: { name: string; company: string; date: string }
): string[] => {
  const errors: string[] = [];
  
  if (!mapping.description) {
    errors.push('La columna de descripción es requerida');
  }
  
  if (!studyInfo.name.trim()) {
    errors.push('El nombre del estudio es requerido');
  }
  
  if (!studyInfo.company.trim()) {
    errors.push('La empresa es requerida');
  }
  
  if (!studyInfo.date) {
    errors.push('La fecha es requerida');
  }
  
  return errors;
};

/**
 * Genera un resumen de los datos procesados
 */
export const generateDataSummary = (elements: ElementInstance[]) => {
  const summary = {
    totalElements: elements.length,
    elementsWithTime: elements.filter(el => el.time > 0).length,
    elementsWithSupplements: elements.filter(el => el.supplements && el.supplements.length > 0).length,
    averageTime: elements.reduce((sum, el) => sum + (el.time || 0), 0) / elements.length,
    typeDistribution: {} as Record<string, number>
  };
  
  // Contar distribución por tipos
  elements.forEach(element => {
    summary.typeDistribution[element.type] = (summary.typeDistribution[element.type] || 0) + 1;
  });
  
  return summary;
};
