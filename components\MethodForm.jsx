const handleLibraryElementSelect = (selectedElement) => {
  console.log("Elemento seleccionado para copiar:", selectedElement);
  
  // Mantenemos la estructura exacta del elemento seleccionado
  // pero asegurándonos de que es una copia completa
  const elementCopy = JSON.parse(JSON.stringify(selectedElement));
  
  // Asegurar que los campos importantes estén presentes
  setFormData(prevData => {
    return elementCopy;
  });
  
  console.log("Formulario actualizado con el elemento copiado:", elementCopy);
};

const handleUseSelectedElement = (element) => {
  if (!element) {
    console.error("ERROR: Elemento no válido, es null o undefined");
    return;
  }
  
  console.log("=== PASO 1: Elemento seleccionado ===");
  console.log("Elemento completo:", element);
  
  // Creamos un nuevo ID para el elemento (para evitar conflictos)
  const newElementId = crypto.randomUUID();
  
  // Creamos el elemento base a utilizar con el nuevo ID
  const elementToUse = {
    id: newElementId, // Usamos un nuevo ID
    name: element.name || "",
    description: element.description || "",
    type: element.type || "machine-stopped",
    position: element.position || 0,
    repetition_type: element.repetition_type || "repetitive",
    frequency_repetitions: element.frequency_repetitions || 1,
    frequency_cycles: element.frequency_cycles || 1,
    time: element.time || 0
  };
  
  // IMPORTANTE: Crear la estructura correcta para time_records
  if (element.timeRecords && element.timeRecords.length > 0) {
    // Primero creamos copias de los registros con el nuevo elementId
    const updatedRecords = element.timeRecords.map(record => ({
      ...record,
      id: crypto.randomUUID(), // Nuevo ID para el registro
      elementId: newElementId // Usamos el nuevo ID del elemento
    }));
    
    // Luego creamos la estructura adecuada
    const timeRecordsObj = {};
    timeRecordsObj[newElementId] = updatedRecords;
    elementToUse.time_records = timeRecordsObj;
    
    console.log("=== PASO 2: Registros de tiempo actualizados con nuevo ID ===");
    console.log("Estructura de time_records:", elementToUse.time_records);
  }
  
  // IMPORTANTE: Crear la estructura correcta para supplements
  if (element.supplements && element.supplements.length > 0) {
    // Creamos un objeto con los suplementos actualizados
    const supplementsObj = {};
    const elementSupplements = {};
    
    element.supplements.forEach(supp => {
      // Copia del suplemento con un nuevo ID si es necesario
      const suppCopy = {
        ...supp,
        // Si quieres generar un nuevo ID para los suplementos, descomenta la siguiente línea
        // id: crypto.randomUUID()
      };
      elementSupplements[suppCopy.id] = suppCopy;
    });
    
    supplementsObj[newElementId] = elementSupplements;
    elementToUse.supplements = supplementsObj;
    
    console.log("=== PASO 3: Suplementos actualizados con nuevo ID ===");
    console.log("Estructura de supplements:", elementToUse.supplements);
  }
  
  console.log("=== PASO 4: Elemento final preparado para envío ===", elementToUse);
  
  // Verificación adicional de que la estructura es correcta
  if (elementToUse.time_records) {
    const recordsArray = elementToUse.time_records[newElementId];
    console.log("Verificación de time_records:", recordsArray.length, "registros");
  }
  
  if (elementToUse.supplements) {
    const supplementsObj = elementToUse.supplements[newElementId];
    console.log("Verificación de supplements:", Object.keys(supplementsObj).length, "suplementos");
  }
  
  // Enviar a proceso de guardado con waitForRefresh en true
  console.log("=== PASO 5: Enviando elemento ===");
  try {
    // Importante: Aquí usamos true como segundo parámetro para forzar un refresh
    onSubmit(elementToUse, true)
      .then(() => {
        console.log("=== PASO 6: Elemento guardado exitosamente ===");
        onClose();
      })
      .catch(error => {
        console.error('=== ERROR: Error al guardar elemento ===', error);
      });
  } catch (error) {
    console.error('=== ERROR CRÍTICO: Excepción al intentar guardar ===', error);
  }
};

const handleUseAveragedElement = () => {
  if (!averagedElement) return;
  
  console.log("=== PASO 1: Preparando elemento promediado ===");
  
  // Creamos un nuevo ID para el elemento
  const newElementId = crypto.randomUUID();
  
  // Crear estructura base del elemento con nuevo ID
  const elementToUse = {
    id: newElementId,
    name: averagedElement.name || "",
    description: averagedElement.description || "",
    type: averagedElement.type || "machine-stopped",
    position: averagedElement.position || 0,
    repetition_type: averagedElement.repetition_type || "repetitive",
    frequency_repetitions: averagedElement.frequency_repetitions || 1,
    frequency_cycles: averagedElement.frequency_cycles || 1,
    time: averagedElement.time || 0
  };
  
  // Crear estructura correcta para timeRecords con nuevo ID
  if (averagedElement.timeRecords && averagedElement.timeRecords.length > 0) {
    // Actualizar los registros con el nuevo ID
    const updatedRecords = averagedElement.timeRecords.map(record => ({
      ...record,
      id: crypto.randomUUID(),
      elementId: newElementId
    }));
    
    const timeRecordsObj = {};
    timeRecordsObj[newElementId] = updatedRecords;
    elementToUse.time_records = timeRecordsObj;
    
    console.log("=== PASO 2: Registros de tiempo promediados actualizados ===");
  }
  
  // Crear estructura correcta para supplements con nuevo ID
  if (averagedElement.supplements && averagedElement.supplements.length > 0) {
    const supplementsObj = {};
    const elementSupplements = {};
    
    averagedElement.supplements.forEach(supp => {
      elementSupplements[supp.id] = {...supp};
    });
    
    supplementsObj[newElementId] = elementSupplements;
    elementToUse.supplements = supplementsObj;
    
    console.log("=== PASO 3: Suplementos promediados actualizados ===");
  }
  
  console.log("=== PASO 4: Datos promediados finales ===", elementToUse);
  
  // Enviar a proceso de guardado con waitForRefresh en true
  onSubmit(elementToUse, true)
    .then(() => {
      console.log("=== PASO 5: Elemento promediado guardado exitosamente ===");
      onClose();
    })
    .catch(error => {
      console.error('=== ERROR: Error al guardar elemento promediado ===', error);
    });
}; 