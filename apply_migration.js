// Script para aplicar la migración a Supabase
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtener el directorio actual
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.SUPABASE_URL || 'https://sspglhphufmvctveyhug.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || '';

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY es requerido');
  console.error('Ejecuta este script con: SUPABASE_SERVICE_KEY=tu_clave_secreta node apply_migration.js');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  try {
    // Leer el archivo SQL
    const migrationPath = path.join(__dirname, 'supabase', 'migrations', '20250227_fix_recursion_studies.sql');
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Aplicando migración para corregir recursión infinita...');
    
    // Ejecutar la consulta SQL directamente
    const { data, error } = await supabase.rpc('exec_sql', { query: sql });
    
    if (error) {
      console.error('Error al aplicar la migración:', error);
      process.exit(1);
    }
    
    console.log('Migración aplicada con éxito');
    console.log('Resultado:', data);
  } catch (err) {
    console.error('Error inesperado:', err);
    process.exit(1);
  }
}

applyMigration();
