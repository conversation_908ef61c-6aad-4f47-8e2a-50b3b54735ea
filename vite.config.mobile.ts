import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';

// Crear patrón de URL dinámico basado en la variable de entorno
const getApiUrlPattern = () => {
  const apiUrl = process.env.VITE_API_URL || 'https://api.cronometras.com';
  try {
    const url = new URL(apiUrl);
    // Escapar caracteres especiales para regex
    const escapedHost = url.host.replace(/\./g, '\\.');
    return new RegExp(`^https:\\/\\/${escapedHost}`);
  } catch (error) {
    console.warn('Error creating API URL pattern:', error);
    // Fallback al patrón original
    return /^https:\/\/api\.cronometras\.com/;
  }
};

export default defineConfig({
  plugins: [
    react(),
    // Sin basicSsl() para evitar problemas con certificados en móvil
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: [
        '/icons/icon-48x48.png',
        '/icons/icon-72x72.png',
        '/icons/icon-96x96.png',
        '/icons/icon-128x128.png',
        '/icons/icon-144x144.png', 
        '/icons/icon-167x167.png',
        '/icons/icon-180x180.png',
        '/icons/icon-192x192.png', 
        '/icons/icon-512x512.png'
      ],
      manifest: {
        name: 'Cronometras',
        short_name: 'Cronometras',
        description: 'Cronometras - Gestión de Estudios de Tiempo',
        theme_color: '#ffffff',
        icons: [
          {
            src: '/icons/icon-48x48.png',
            sizes: '48x48',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-72x72.png',
            sizes: '72x72',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-96x96.png',
            sizes: '96x96',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-128x128.png',
            sizes: '128x128',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-144x144.png',
            sizes: '144x144',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-167x167.png',
            sizes: '167x167',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-180x180.png',
            sizes: '180x180',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-192x192.png',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: '/icons/icon-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ],
      },
      workbox: {
        maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5MB
        runtimeCaching: [
          {
            urlPattern: getApiUrlPattern(),
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 * 24 // 24 horas
              }
            }
          }
        ],
        navigateFallback: null,
        cleanupOutdatedCaches: true,
        sourcemap: false,
        navigateFallbackDenylist: [/\/api\/.*/],
      }
    }),
  ],
  assetsInclude: ['**/*.md'],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    // Sin HTTPS para desarrollo móvil
    port: 5173,
    host: '0.0.0.0', // Permitir acceso desde cualquier IP de la red
    cors: false,
    hmr: {
      host: 'localhost',
    },
    proxy: {
      '/api': {
        target: process.env.VITE_API_URL || 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
    fs: {
      allow: ['..'],
    },
  },
}); 