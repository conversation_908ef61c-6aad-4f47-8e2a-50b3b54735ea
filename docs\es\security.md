# Política de Seguridad

## Versiones Soportadas

Actualmente, las siguientes versiones de Cronometras están siendo mantenidas con actualizaciones de seguridad:

| Versión | Soportada          |
| ------- | ------------------ |
| 42.x.x  | :white_check_mark: |
| 41.x.x  | :white_check_mark: |
| 40.x.x  | :white_check_mark: |
| < 40.0  | :x:                |

## Reportando una Vulnerabilidad

Si descubres una vulnerabilidad de seguridad en Cronometras, por favor:

1. **NO** reportes la vulnerabilidad públicamente a través de los issues de GitHub
2. Envía un correo electró<NAME_EMAIL> con los detalles de la vulnerabilidad

### Qué Esperar

Una vez reportada una vulnerabilidad:

- Recibirás una respuesta inicial dentro de las 48 horas
- Nuestro equipo investigará y evaluará la vulnerabilidad
- Te mantendremos informado del progreso
- Si la vulnerabilidad es aceptada:
  - Aplicaremos los parches necesarios en un plazo de 48 horas
  - Tu reporte será reconocido en nuestros registros de seguridad
- Si la vulnerabilidad es declinada, te proporcionaremos una explicación detallada

## Medidas de Seguridad Implementadas

### Análisis Continuo de Seguridad
- Análisis automatizado de código mediante Deepsource
- Monitoreo de dependencias con Dependabot
- Auditorías de seguridad periódicas

### Gestión de Parches y Vulnerabilidades
- Aplicación de parches críticos en 48 horas
- Actualizaciones regulares de dependencias

### Protección de Datos
- Cifrado de datos en tránsito (HTTPS/TLS)
- Autenticación robusta mediante Supabase Auth
- Almacenamiento de datos en la región AWS-eu central (Frankfurt)
- Cumplimiento con normativas europeas de protección de datos

### Infraestructura
- Servicios alojados en Supabase (certificación SOC2)
- Copias de seguridad automáticas diarias
- Monitorización y auditoría continuas

## Contacto

Para consultas relacionadas con la seguridad:
- Email: <EMAIL>
- Tiempo de respuesta: 48 horas máximo para problemas críticos

## Informes de Seguridad

- [Informe OWASP Top 10 de Deepsource](https://app.deepsource.com/report/8ce1ce4a-2819-4555-9c8b-c3732a948bfb/owasp-top-10)
