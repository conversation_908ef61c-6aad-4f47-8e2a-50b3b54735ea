import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { useOrganizationStore } from '../store/organizationStore';
import { useAuthStore } from '../store/authStore';
import { OrganizationMembers } from '../components/organizations/OrganizationMembers';
import { PendingJoinRequests } from '../components/organizations/PendingJoinRequests';
import OrganizationStudyList from '../components/organizations/OrganizationStudyList';
import { Helmet } from 'react-helmet-async';
import { Button } from '../components/ui/button';
import { Header } from '../components/Header';
import { OrganizationMember } from '../types/organization';

const OrganizationPage: React.FC = () => {
  const { t } = useTranslation('common');
  const { organizationId } = useParams<{ organizationId: string }>();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { 
    organizations, 
    fetchOrganizations, 
    members,
    isLoading, 
    error 
  } = useOrganizationStore();
  const [organization, setOrganization] = useState<any>(null);
  const [organizationsLoaded, setOrganizationsLoaded] = useState(false);

  useEffect(() => {
    const loadOrganization = async () => {
      if (!organizationsLoaded) {
        if (organizations.length === 0) {
          console.log('Cargando organizaciones...');
          try {
            await fetchOrganizations();
          } catch (error) {
            console.error('Error al cargar organizaciones:', error);
          }
        }
        setOrganizationsLoaded(true);
      }
      
      if (organizationId) {
        console.log('Buscando organización con ID:', organizationId);
        const org = organizations.find(o => o.id === organizationId);
        
        if (org) {
          console.log('Organización encontrada:', org);
          setOrganization(org);
        } else {
          console.log('Organización no encontrada en la lista. Intentando obtener directamente...');
          
          try {
            // Intentar obtener la organización directamente desde la base de datos
            const { getOrganizationName } = useOrganizationStore.getState();
            const name = await getOrganizationName(organizationId);
            
            if (name && name !== 'Organización desconocida' && name !== 'Error al obtener nombre') {
              console.log('Nombre de organización obtenido directamente:', name);
              // Crear un objeto de organización básico con los datos que tenemos
              setOrganization({
                id: organizationId,
                name: name,
                // Propiedades mínimas necesarias
                description: '',
                invite_code: '',
                created_at: new Date().toISOString(),
                userRole: 'member' // Asumimos rol de miembro por defecto
              });
            } else {
              console.error('No se pudo obtener el nombre de la organización');
              setOrganization(null);
            }
          } catch (error) {
            console.error('Error al obtener la organización directamente:', error);
            setOrganization(null);
          }
        }
      }
    };
    
    loadOrganization();
  }, [organizationId, organizations, fetchOrganizations, organizationsLoaded]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <div className="text-red-500 text-xl font-semibold mb-4">{t('errors.error')}</div>
          <div className="text-gray-600">{error}</div>
          <button 
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            {t('retry')}
          </button>
        </div>
      );
    }

    if (!organization) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <div className="text-gray-600 text-xl font-semibold mb-4">{t('notFound')}</div>
        </div>
      );
    }

    // Verificar si el usuario actual es admin o propietario
    const organizationMembers = members[organization.id] || [];
    const isAdminOrOwner = user && organizationMembers.some(m => 
      m.user_id === user.id && (m.role === 'admin' || m.role === 'owner')
    );

    return (
      <div className="container mx-auto py-8 px-4">
        <Helmet>
          <title>{organization.name} | {t('appName')}</title>
        </Helmet>
        
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-3xl font-bold">{organization.name}</h1>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              {t('back')}
            </Button>
          </div>
          {organization.description && (
            <p className="text-gray-600">{organization.description}</p>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            {/* Información de la organización */}
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">{t('organization.details')}</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">{t('organization.inviteCode')}</h3>
                  <div className="mt-1 flex items-center">
                    <span className="font-medium mr-2">{organization.invite_code}</span>
                    <button 
                      className="text-sm text-blue-500 hover:text-blue-700"
                      onClick={() => {
                        navigator.clipboard.writeText(organization.invite_code);
                        alert(t('organization.copied'));
                      }}
                    >
                      {t('organization.copy')}
                    </button>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">{t('organization.createdAt')}</h3>
                  <div className="mt-1">
                    <span className="font-medium">
                      {new Date(organization.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Miembros de la organización - Componente completo */}
            <OrganizationMembers organizationId={organizationId || ''} />
            
            {/* Estudios compartidos de la organización */}
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <OrganizationStudyList organizationId={organizationId || ''} />
            </div>
          </div>
          
          <div>
            {/* Solicitudes pendientes */}
            <PendingJoinRequests organizationId={organizationId || ''} />
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <Header />
      <div className="flex-1 overflow-auto">
        {renderContent()}
      </div>
    </>
  );
};

export default OrganizationPage;
