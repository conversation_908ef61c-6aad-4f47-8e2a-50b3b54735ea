# Supplements Machine Component

## Descripción
Gestión de suplementos para elementos de máquina.

## Props
```typescript
interface SupplementsMachineProps {
  element: WorkElement;
  onSaved: () => void;
  supplement?: {
    points: Record<string, number>;
    percentage: number;
    isForced: boolean;
    standingWork: boolean;
  };
}
```

## Características
- Control de trabajo en pie
- Cálculos específicos para máquina
- Ajustes por tipo de máquina
- Validaciones especializadas
- Persistencia de configuración

## Uso
```tsx
<SupplementsMachine
  element={currentElement}
  onSaved={handleSave}
  supplement={currentSupplement}
/>
```