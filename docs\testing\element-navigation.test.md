# Pruebas de Navegación de Elementos

## Descripción
Suite de pruebas para la navegación entre elementos.

## Casos de Prueba

### 1. Navegación Básica
```typescript
describe('Element Navigation', () => {
  const elements = [
    { id: '1', description: 'Element 1' },
    { id: '2', description: 'Element 2' },
    { id: '3', description: 'Element 3' }
  ];

  test('should navigate forward and backward', () => {
    const { result } = renderHook(() => 
      useElementNavigation(elements)
    );
    
    act(() => {
      result.current.nextElement();
    });
    
    expect(result.current.currentIndex).toBe(1);
    
    act(() => {
      result.current.previousElement();
    });
    
    expect(result.current.currentIndex).toBe(0);
  });
});
```

### 2. Modo Circular
```typescript
test('should loop in circular mode', () => {
  const { result } = renderHook(() => 
    useElementNavigation(elements, { loop: true })
  );
  
  act(() => {
    result.current.previousElement();
  });
  
  expect(result.current.currentIndex).toBe(elements.length - 1);
});
```

### 3. Validaciones
```typescript
test('should validate boundaries', () => {
  const { result } = renderHook(() => 
    useElementNavigation(elements)
  );
  
  expect(result.current.canGoPrevious).toBe(false);
  
  act(() => {
    result.current.goToElement(elements.length - 1);
  });
  
  expect(result.current.canGoNext).toBe(false);
});
```