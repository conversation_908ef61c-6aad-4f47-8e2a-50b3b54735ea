-- Add machine cycle data and cycle preference fields to studies table
ALTER TABLE public.studies
ADD COLUMN IF NOT EXISTS machine_cycle_data JSONB DEFAULT NULL,
ADD COLUMN IF NOT EXISTS cycle_preference TEXT DEFAULT NULL;

-- Add comment to explain the structure of the machine_cycle_data JSONB column
COMMENT ON COLUMN public.studies.machine_cycle_data IS 'Machine cycle calculation data from supplements. Structure: {"totalCycleTime": number, "baseTime": number, "personalNeedsSupplement": number, "fatigueSupplement": number, "remainingFatigue": number, "inactivityTime": number, "calculationCase": string, "timestamp": string}';

-- Add comment to explain the cycle_preference column
COMMENT ON COLUMN public.studies.cycle_preference IS 'User preference for which cycle calculation to use in reports: "report" or "supplements"';

-- Add constraint to ensure cycle_preference has valid values
ALTER TABLE public.studies
ADD CONSTRAINT cycle_preference_check CHECK (cycle_preference IN ('report', 'supplements') OR cycle_preference IS NULL);

-- Update existing RLS policies to include the new columns
-- The existing policies should automatically cover these new columns since they use the table name
-- But we'll explicitly mention them for clarity

-- Add index for cycle_preference for better query performance
CREATE INDEX IF NOT EXISTS idx_studies_cycle_preference ON public.studies(cycle_preference);
