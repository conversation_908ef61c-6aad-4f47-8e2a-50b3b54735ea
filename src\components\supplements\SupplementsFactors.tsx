import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { WorkElement } from '../../types';
import { FactorModal } from './FactorModal';
import { FactorA1Modal } from './FactorA1Modal';
import { FactorA5Modal } from './FactorA5Modal';
import { FactorC1Modal } from './FactorC1Modal';
import { useStudyStore } from '../../store/studyStore';
import { convertPointsToPercentage } from '../../utils/supplements';

interface Selection {
  index?: number;
  indices?: number[];
  intensity?: string;
  temperature?: number;
  humidity?: number;
}

interface SupplementsFactorsProps {
  element: WorkElement;
  tableType: 'oit' | 'tal';
  onSaved: () => void;
  supplement?: {
    points: Record<string, number>;
    percentage: number;
    is_forced: boolean;
    factor_selections: Record<string, Selection>;
    table_type: 'oit' | 'tal';
  };
}

export const SupplementsFactors: React.FC<SupplementsFactorsProps> = ({
  element,
  tableType,
  onSaved,
  supplement
}) => {
  const { t, i18n } = useTranslation(['supplements', 'common', 'tal', 'method', 'talFactors', 'oitFactors']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const { updateSupplements } = useStudyStore();
  const [selectedFactor, setSelectedFactor] = useState<string | null>(null);
  const [points, setPoints] = useState<Record<string, number>>(
    selectedStudy?.supplements?.[element.id]?.points || {}
  );
  const [supplementPercentage, setSupplementPercentage] = useState<number>(
    selectedStudy?.supplements?.[element.id]?.percentage || 0
  );
  const [factorSelections, setFactorSelections] = useState<Record<string, Selection>>(
    selectedStudy?.supplements?.[element.id]?.factor_selections || {}
  );

  const factors = [
    { id: 'A1', name: 'factors.A1', ns: 'supplements' },
    { id: 'A2', name: 'factors.A2', ns: 'supplements' },
    { id: 'A3', name: 'factors.A3', ns: 'supplements' },
    { id: 'A4', name: 'factors.A4', ns: 'supplements' },
    { id: 'A5', name: 'factors.A5', ns: 'supplements' },
    { id: 'B1', name: 'factors.B1', ns: 'supplements' },
    { id: 'B2', name: 'factors.B2', ns: 'supplements' },
    { id: 'B3', name: 'factors.B3', ns: 'supplements' },
    { id: 'B4', name: 'factors.B4', ns: 'supplements' },
    { id: 'C1', name: 'factors.C1', ns: 'supplements' },
    { id: 'C2', name: 'factors.C2', ns: 'supplements' },
    { id: 'C3', name: 'factors.C3', ns: 'supplements' },
    { id: 'C4', name: 'factors.C4', ns: 'supplements' },
    { id: 'C5', name: 'factors.C5', ns: 'supplements' },
    { id: 'C6', name: 'factors.C6', ns: 'supplements' },
  ];

  const handleFactorClick = useCallback((factorId: string, intensity?: string) => {
    if (factorId === 'A1' && intensity) {
      setSelectedFactor(`A1-${intensity}`);
    } else {
      setSelectedFactor(factorId);
    }
  }, []);

  // Función auxiliar para extraer el índice real, sin importar el nivel de anidamiento
  const extractIndex = (selection: Selection | number): number | undefined => {
    if (typeof selection === 'number') {
      return selection;
    }
    if (selection && typeof selection === 'object' && 'index' in selection) {
      return selection.index;
    }
    return undefined;
  };

  const handlePointsUpdate = useCallback((factorId: string, value: number, selection?: Selection) => {
    const baseFactorId = factorId.split('-')[0];
    setPoints(prev => {
      const newPoints = {
        ...prev,
        [baseFactorId]: value
      };
      const newTotalPoints = Object.values(newPoints).reduce((sum, p) => sum + p, 0);
      setSupplementPercentage(convertPointsToPercentage(newTotalPoints));
      return newPoints;
    });

    if (factorId.startsWith('A1-')) {
      const intensity = factorId.split('-')[1];
      setFactorSelections(prev => ({
        ...prev,
        [baseFactorId]: {
          intensity,
          index: extractIndex(selection)
        }
      }));
    } else if (factorId === 'A5') {
      setFactorSelections(prev => ({
        ...prev,
        [baseFactorId]: {
          indices: selection?.indices
        }
      }));
    } else if (factorId === 'C1') {
      setFactorSelections(prev => ({
        ...prev,
        [baseFactorId]: selection
      }));
    } else if (selection !== undefined) {
      setFactorSelections(prev => ({
        ...prev,
        [baseFactorId]: selection
      }));
    }

    setSelectedFactor(null);
  }, []);

  const handleResetFactor = useCallback((factorId: string) => {
    const baseFactorId = factorId.split('-')[0];
    setPoints(prev => {
      const newPoints = { ...prev };
      delete newPoints[baseFactorId];
      const newTotalPoints = Object.values(newPoints).reduce((sum, p) => sum + p, 0);
      setSupplementPercentage(convertPointsToPercentage(newTotalPoints));
      return newPoints;
    });
    setFactorSelections(prev => {
      const newSelections = { ...prev };
      delete newSelections[baseFactorId];
      return newSelections;
    });
    setSelectedFactor(null);
  }, []);

  const handleSave = async () => {
    if (!selectedStudy) return;

    const updatedSupplements = {
      ...(selectedStudy.supplements || {}),
      [element.id]: {
        points,
        percentage: supplementPercentage,
        is_forced: false,
        factor_selections: factorSelections,
        table_type: tableType
      }
    };

    try {
      await updateSupplements(selectedStudy.id, updatedSupplements);
      onSaved();
    } catch (error) {
      console.error('Error saving supplements:', error);
    }
  };

  return (
    <div className="pb-20">
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex items-center space-x-4">
          <img
            src={`/${tableType}-logo.png`}
            alt={tableType.toUpperCase()}
            className="w-16 h-16 object-contain"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold">
              {t(`${tableType}Tables`, { ns: 'supplements' })}
            </h3>
            <p className="text-sm text-gray-600">
              {t(`${tableType}Description`, { ns: 'supplements' })}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <span className="text-lg font-semibold text-purple-600">{element.position + 1}</span>
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="font-semibold">{element.description}</h3>
            </div>
            <p className="text-sm text-gray-600">
              {t(`types.${element.repetition_type}`, { ns: 'method' })}
              {element.frequency && (
                <span className="ml-2">
                  ({t('frequency', { ns: 'method' })}: {element.frequency})
                </span>
              )}
              {element.machine && (
                <span className="ml-2">
                  ({t('machine', { ns: 'method' })}: {element.machine})
                </span>
              )}
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        {factors.map((factor) => (
          <div key={factor.id} className="bg-white rounded-lg shadow p-3">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">{t(factor.name, { ns: factor.ns })}</h4>
                <div className="text-sm text-gray-600">
                  {t('points', { ns: 'supplements' })}: {points[factor.id] || 0}
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                {factor.id === 'A1' ? (
                  <div className="w-full flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                    <button
                      onClick={() => handleFactorClick('A1', 'Reducido')}
                      className={`w-full sm:w-auto px-3 py-2 text-sm rounded-lg ${
                        factorSelections.A1?.intensity === 'Reducido'
                          ? 'bg-purple-600 text-white'
                          : 'bg-green-500 text-white hover:bg-green-600'
                      }`}
                    >
                      {tableType === 'tal' 
                        ? t('A1.intensities.Reducido', { ns: 'talFactors' })
                        : t('A1.intensities.Reducido', { ns: 'oitFactors' })}
                    </button>
                    <button
                      onClick={() => handleFactorClick('A1', 'Mediano')}
                      className={`w-full sm:w-auto px-3 py-2 text-sm rounded-lg ${
                        factorSelections.A1?.intensity === 'Mediano'
                          ? 'bg-purple-600 text-white'
                          : 'bg-green-500 text-white hover:bg-green-600'
                      }`}
                    >
                      {tableType === 'tal'
                        ? t('A1.intensities.Mediano', { ns: 'talFactors' })
                        : t('A1.intensities.Mediano', { ns: 'oitFactors' })}
                    </button>
                    <button
                      onClick={() => handleFactorClick('A1', 'Intenso')}
                      className={`w-full sm:w-auto px-3 py-2 text-sm rounded-lg ${
                        factorSelections.A1?.intensity === 'Intenso'
                          ? 'bg-purple-600 text-white'
                          : 'bg-green-500 text-white hover:bg-green-600'
                      }`}
                    >
                      {tableType === 'tal'
                        ? t('A1.intensities.Intenso', { ns: 'talFactors' })
                        : t('A1.intensities.Intenso', { ns: 'oitFactors' })}
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => handleFactorClick(factor.id)}
                    className="w-full sm:w-auto px-3 py-2 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600"
                  >
                    {t('viewTable', { ns: 'supplements' })}
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-3 flex items-center justify-between safe-bottom">
        <div className="flex gap-4">
          <div>
            <div className="text-sm text-gray-600">{t('totalPoints', { ns: 'supplements' })}</div>
            <div className="text-lg font-bold">{Object.values(points).reduce((sum, p) => sum + p, 0)}</div>
          </div>
          <div>
            <div className="text-sm text-gray-600">{t('percentage', { ns: 'supplements' })}</div>
            <div className="text-lg font-bold">{supplementPercentage}%</div>
          </div>
        </div>
        <button
          onClick={handleSave}
          className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
        >
          {t('save', { ns: 'common' })}
        </button>
      </div>

      {selectedFactor && selectedFactor.startsWith('A1') ? (
        <FactorA1Modal
          intensity={selectedFactor.split('-')[1] as 'Reducido' | 'Mediano' | 'Intenso'}
          onClose={() => setSelectedFactor(null)}
          onSelect={(value, index) => handlePointsUpdate(selectedFactor, value, index)}
          onReset={() => handleResetFactor('A1')}
          currentValue={points['A1']}
          currentSelection={factorSelections['A1']?.index}
        />
      ) : selectedFactor === 'A5' ? (
        <FactorA5Modal
          onClose={() => setSelectedFactor(null)}
          onSelect={(value, selections) => handlePointsUpdate('A5', value, { indices: selections })}
          onReset={() => handleResetFactor('A5')}
          currentValue={points['A5']}
          currentSelections={factorSelections['A5']?.indices || []}
        />
      ) : selectedFactor === 'C1' ? (
        <FactorC1Modal
          onClose={() => setSelectedFactor(null)}
          onSelect={(value, selection) => handlePointsUpdate('C1', value, selection)}
          onReset={() => handleResetFactor('C1')}
          currentValue={points['C1']}
          currentSelection={factorSelections['C1']}
        />
      ) : selectedFactor && (
        <FactorModal
          factor={selectedFactor}
          tableType={tableType}
          onClose={() => setSelectedFactor(null)}
          onSelect={(value, selection) => handlePointsUpdate(selectedFactor, value, selection)}
          onReset={() => handleResetFactor(selectedFactor)}
          currentValue={points[selectedFactor.split('-')[0]]}
          currentSelection={factorSelections[selectedFactor.split('-')[0]]}
        />
      )}
    </div>
  );
};