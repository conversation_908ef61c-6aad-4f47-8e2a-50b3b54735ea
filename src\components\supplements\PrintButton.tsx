import React from 'react';
import { useTranslation } from 'react-i18next';
import '../../styles/print.css';

interface PrintButtonProps {
  onPrint: () => void;
}

const PrintButton: React.FC<PrintButtonProps> = ({ onPrint }) => {
  const { t } = useTranslation('supplements');

  return (
    <button
      onClick={onPrint}
      className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded no-print"
    >
      {t('print')}
    </button>
  );
};

export default PrintButton;
