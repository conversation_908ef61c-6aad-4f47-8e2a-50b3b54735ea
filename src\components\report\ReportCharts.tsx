import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Chart } from 'react-chartjs-2';
import { Pie, Bar } from 'react-chartjs-2';
import { ReportStats, ElementStats, TimeUnit } from '../../types/index';
import { FlowchartSymbol } from '../FlowchartSymbols';
import { ChartVisibilitySettings } from '../../types/profile';
import { DEFAULT_CHART_VISIBILITY } from '../../config/chartDefaults';

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartDataLabels
);

interface ReportChartsProps {
  stats: ReportStats;
  elements: ElementStats[];
  timeUnit: TimeUnit;
  chartVisibility?: ChartVisibilitySettings;
}

export const ReportCharts: React.FC<ReportChartsProps> = ({
  stats,
  elements,
  timeUnit,
  chartVisibility = DEFAULT_CHART_VISIBILITY
}) => {
  const { t } = useTranslation(['report']);

  // Cleanup charts on unmount to prevent canvas reuse errors
  useEffect(() => {
    return () => {
      // Destroy all Chart.js instances
      Object.values(ChartJS.instances).forEach((chart: any) => {
        if (chart && typeof chart.destroy === 'function') {
          chart.destroy();
        }
      });
    };
  }, []);

  // Filtrar elementos concurrentes
  const filteredElements = elements.filter(e => !e.concurrent_machine_time);

  // Colores consistentes para los gráficos
  const machineTypeColors = {
    'machine-stopped': '#ef4444', // Rojo
    'machine-running': '#22c55e', // Verde
    'machine-time': '#3b82f6'     // Azul
  };

  // Colores para símbolos de diagrama de flujo
  const flowchartSymbolColors = {
    'operation': '#3b82f6',      // Azul
    'inspection': '#f59e0b',     // Amarillo/Naranja
    'transport': '#10b981',      // Verde esmeralda
    'delay': '#ef4444',          // Rojo
    'storage': '#8b5cf6',        // Púrpura
    'combined': '#6b7280'        // Gris
  };

  // Calcular datos de símbolos de diagrama de flujo
  const calculateFlowchartData = () => {
    const symbolData: Record<FlowchartSymbol, { time: number, count: number }> = {
      'operation': { time: 0, count: 0 },
      'inspection': { time: 0, count: 0 },
      'transport': { time: 0, count: 0 },
      'delay': { time: 0, count: 0 },
      'storage': { time: 0, count: 0 },
      'combined': { time: 0, count: 0 }
    };
    
    let totalTime = 0;

    filteredElements.forEach(element => {
      if (element.flowchartSymbol && element.finalTime > 0) {
        symbolData[element.flowchartSymbol].time += element.finalTime;
        symbolData[element.flowchartSymbol].count += 1;
        totalTime += element.finalTime;
      }
    });

    return { symbolData, totalTime };
  };

  const { symbolData, totalTime } = calculateFlowchartData();

  // Filtrar solo símbolos que tienen tiempo asignado
  const activeSymbols = Object.entries(symbolData).filter(([_, data]) => data.time > 0);
  const hasFlowchartData = activeSymbols.length > 0;

  // 1. Gráfica de Tipos de Máquina (Quesito)
  const machineTypeData = {
    labels: [
      t('charts.machineTypes.stopped', { defaultValue: 'Máquina Parada' }),
      t('charts.machineTypes.running', { defaultValue: 'Máquina en Marcha' }),
      t('charts.machineTypes.time', { defaultValue: 'Tiempo de Máquina' })
    ],
    datasets: [
      {
        data: [
          stats.machineStoppedTime,
          stats.machineRunningTime,
          stats.machineTime
        ],
        backgroundColor: [
          machineTypeColors['machine-stopped'],
          machineTypeColors['machine-running'],
          machineTypeColors['machine-time']
        ],
        borderWidth: 2,
        borderColor: '#ffffff'
      }
    ]
  };

  // 2. Gráfica de Tiempos por Elemento (Barras)
  const elementTimeData = {
    labels: filteredElements.map(e => 
      e.description.length > 20 
        ? e.description.substring(0, 20) + '...' 
        : e.description
    ),
    datasets: [
      {
        label: t('charts.elements.finalTime', { defaultValue: 'Tiempo Final' }),
        data: filteredElements.map(e => e.finalTime),
        backgroundColor: filteredElements.map(e => machineTypeColors[e.type as keyof typeof machineTypeColors] || '#6b7280'),
        borderWidth: 1,
        borderColor: '#374151'
      }
    ]
  };

  // 3. Gráfica de Saturación/Rendimiento Normal (Donut)
  const saturationData = {
    labels: [
      t('charts.saturation.used', { defaultValue: 'Tiempo Utilizado' }),
      t('charts.saturation.unused', { defaultValue: 'Tiempo No Utilizado' })
    ],
    datasets: [
      {
        data: [
          stats.normalSaturation || 0,
          100 - (stats.normalSaturation || 0)
        ],
        backgroundColor: [
          '#10b981', // Verde para tiempo utilizado
          '#e5e7eb'  // Gris claro para tiempo no utilizado
        ],
        borderWidth: 2,
        borderColor: '#ffffff',
        cutout: '60%' // Crear el efecto donut
      }
    ]
  };

  // 4. Gráfica de Suplementos por Elemento (Barras Agrupadas)
  const supplementsData = {
    labels: filteredElements.map(e => 
      e.description.length > 15 
        ? e.description.substring(0, 15) + '...' 
        : e.description
    ),
    datasets: [
      {
        label: t('charts.supplements.finalTime', { defaultValue: 'Tiempo Final' }),
        data: filteredElements.map(e => e.finalTime),
        backgroundColor: '#3b82f6', // Azul para tiempo final
        borderWidth: 1,
        borderColor: '#2563eb',
        yAxisID: 'y'
      },
      {
        label: t('charts.supplements.percentage', { defaultValue: 'Porcentaje de Suplementos (%)' }),
        data: filteredElements.map(e => e.supplements),
        backgroundColor: '#8b5cf6', // Púrpura para porcentaje de suplementos
        borderWidth: 1,
        borderColor: '#7c3aed',
        yAxisID: 'y1'
      }
    ]
  };

  // Activity chart removed as requested

  // 6. Gráfica de Distribución del Tiempo de Ciclo (Donut)
  const workTime = stats.normalCycle - stats.totalK; // Tiempo de trabajo
  const restTime = stats.totalK; // Tiempo de descanso (K total)
  
  const cycleDistributionData = {
    labels: [
      t('charts.cycleDistribution.work', { defaultValue: 'Tiempo de Trabajo' }),
      t('charts.cycleDistribution.rest', { defaultValue: 'Tiempo de Descanso' })
    ],
    datasets: [
      {
        data: [workTime, restTime],
        backgroundColor: [
          '#3b82f6', // Azul para tiempo de trabajo
          '#ef4444'  // Rojo para tiempo de descanso
        ],
        borderWidth: 2,
        borderColor: '#ffffff',
        cutout: '60%' // Crear el efecto donut
      }
    ]
  };

  // 7. Gráfica de Símbolos de Diagrama de Flujo (Quesito)
  const flowchartSymbolData = {
    labels: activeSymbols.map(([symbol]) => 
      t(`cronoSeguido:flowchartSymbols.${symbol}.name`, { defaultValue: symbol.toUpperCase() })
    ),
    datasets: [
      {
        data: activeSymbols.map(([_, data]) => data.time),
        backgroundColor: activeSymbols.map(([symbol]) => flowchartSymbolColors[symbol as FlowchartSymbol]),
        borderWidth: 2,
        borderColor: '#ffffff'
      }
    ]
  };

  // 8. Gráfica de Porcentajes por Tipo de Operación (Donut)
  const operationPercentageData = {
    labels: activeSymbols.map(([symbol]) => 
      t(`cronoSeguido:flowchartSymbols.${symbol}.name`, { defaultValue: symbol.toUpperCase() })
    ),
    datasets: [
      {
        data: activeSymbols.map(([_, data]) => ((data.time / totalTime) * 100).toFixed(1)),
        backgroundColor: activeSymbols.map(([symbol]) => flowchartSymbolColors[symbol as FlowchartSymbol]),
        borderWidth: 2,
        borderColor: '#ffffff',
        cutout: '60%' // Crear el efecto donut
      }
    ]
  };

  // 9. Gráfica de Cantidad de Operaciones por Tipo (Barras)
  const operationCountData = {
    labels: activeSymbols.map(([symbol]) => 
      t(`cronoSeguido:flowchartSymbols.${symbol}.name`, { defaultValue: symbol.toUpperCase() })
    ),
    datasets: [
      {
        label: t('charts.flowchartSymbols.count', { defaultValue: 'Cantidad de Operaciones' }),
        data: activeSymbols.map(([_, data]) => data.count),
        backgroundColor: activeSymbols.map(([symbol]) => flowchartSymbolColors[symbol as FlowchartSymbol]),
        borderWidth: 1,
        borderColor: activeSymbols.map(([symbol]) => flowchartSymbolColors[symbol as FlowchartSymbol])
      }
    ]
  };

  // Opciones comunes para gráficas de quesito con etiquetas permanentes
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: {
            size: 12
          },
          // Generate labels with values and percentages for print visibility
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              const dataset = data.datasets[0];
              const total = dataset.data.reduce((a: number, b: number) => a + b, 0);

              return data.labels.map((label: string, i: number) => {
                const value = dataset.data[i];
                // Ensure value is a number and handle edge cases
                const numValue = typeof value === 'number' ? value : 0;
                const percentage = total > 0 ? ((numValue / total) * 100).toFixed(1) : '0.0';
                return {
                  text: `${label}: ${numValue.toFixed(2)} ${t(`units.${timeUnit}`)} (${percentage}%)`,
                  fillStyle: dataset.backgroundColor[i],
                  strokeStyle: dataset.borderColor?.[i] || '#fff',
                  lineWidth: dataset.borderWidth || 1,
                  hidden: false,
                  index: i
                };
              });
            }
            return [];
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value.toFixed(2)} ${t(`units.${timeUnit}`)} (${percentage}%)`;
          }
        }
      },
      // Add permanent data labels for print visibility
      datalabels: {
        display: true,
        color: '#fff',
        font: {
          weight: 'bold' as const,
          size: 11
        },
        formatter: function(value: number, context: any) {
          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
          const percentage = ((value / total) * 100).toFixed(1);
          return percentage >= 5 ? percentage + '%' : ''; // Only show if >= 5%
        }
      }
    }
  };

  // Opciones para gráficas de barras
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const value = context.parsed.y;
            const isPercentage = context.dataset.label?.includes('%');
            return `${context.dataset.label}: ${value.toFixed(2)}${isPercentage ? '%' : ' ' + t(`units.${timeUnit}`)}`;
          }
        }
      },
      // Add permanent data labels for print visibility
      datalabels: {
        display: true,
        anchor: 'end' as const,
        align: 'top' as const,
        color: '#374151',
        font: {
          weight: 'bold' as const,
          size: 10
        },
        formatter: function(value: number, context: any) {
          const isPercentage = context.dataset.label?.includes('%');
          return value.toFixed(1) + (isPercentage ? '%' : '');
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: '#e5e7eb'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 10
          },
          maxRotation: 45
        }
      }
    }
  };

  // Opciones especiales para gráficas de barras agrupadas
  const groupedBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: true,
        position: 'top' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const value = context.parsed.y;
            return `${context.dataset.label}: ${value.toFixed(2)} ${t(`units.${timeUnit}`)}`;
          },
          afterLabel: function(context: any) {
            // Mostrar el porcentaje de suplementos para el elemento
            const elementIndex = context.dataIndex;
            const element = filteredElements[elementIndex];
            if (context.dataset.label?.includes('Suplementos')) {
              return `Suplementos: ${element.supplements}%`;
            }
            return '';
          }
        }
      },
      // Add permanent data labels for print visibility
      datalabels: {
        display: true,
        anchor: 'end' as const,
        align: 'top' as const,
        color: '#374151',
        font: {
          weight: 'bold' as const,
          size: 9
        },
        formatter: function(value: number) {
          return value.toFixed(1);
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 10
          },
          maxRotation: 45
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: '#e5e7eb'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    }
  };

  // Opciones especiales para la gráfica de suplementos con doble eje Y
  const supplementsBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: true,
        position: 'top' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const value = context.parsed.y;
            const isPercentage = context.dataset.yAxisID === 'y1';
            return `${context.dataset.label}: ${value.toFixed(2)}${isPercentage ? '%' : ' ' + t(`units.${timeUnit}`)}`;
          }
        }
      },
      // Add permanent data labels for print visibility
      datalabels: {
        display: true,
        anchor: 'end' as const,
        align: 'top' as const,
        color: '#374151',
        font: {
          weight: 'bold' as const,
          size: 9
        },
        formatter: function(value: number, context: any) {
          const isPercentage = context.dataset.yAxisID === 'y1';
          return value.toFixed(1) + (isPercentage ? '%' : '');
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 10
          },
          maxRotation: 45
        }
      },
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        beginAtZero: true,
        grid: {
          color: '#e5e7eb'
        },
        ticks: {
          font: {
            size: 11
          }
        },
        title: {
          display: true,
          text: `${t('charts.supplements.finalTime', { defaultValue: 'Tiempo Final' })} (${t(`units.${timeUnit}`)})`,
          font: {
            size: 12
          }
        }
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        beginAtZero: true,
        max: Math.max(...filteredElements.map(e => e.supplements)) * 1.2, // Un poco más alto que el máximo
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          font: {
            size: 11
          }
        },
        title: {
          display: true,
          text: t('charts.supplements.percentage', { defaultValue: 'Porcentaje de Suplementos (%)' }),
          font: {
            size: 12
          }
        }
      }
    }
  };

  // Verificar si hay datos para mostrar
  const hasMachineData = stats.machineStoppedTime > 0 || stats.machineRunningTime > 0 || stats.machineTime > 0;
  const hasElementData = filteredElements.length > 0;

  if (!hasElementData) {
    return (
      <div className="bg-white rounded-lg shadow p-6 mt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('charts.title', { defaultValue: 'Análisis Visual del Proceso' })}
        </h3>
        <p className="text-gray-500 text-center py-8">
          {t('charts.noData', { defaultValue: 'No hay datos suficientes para mostrar gráficas' })}
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6 mt-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        {t('charts.title', { defaultValue: 'Análisis Visual del Proceso' })}
      </h3>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Gráfica de Tipos de Máquina */}
        {hasMachineData && chartVisibility.machineTypes && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
              {t('charts.machineTypes.title', { defaultValue: 'Distribución por Tipo de Máquina' })}
            </h4>
            <div className="h-64" id="machine-types-chart">
              <Pie key="machine-types-pie" data={machineTypeData} options={pieOptions} />
            </div>
          </div>
        )}

        {/* Gráfica de Saturación/Rendimiento Normal */}
        {chartVisibility.saturation && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
            {t('charts.saturation.title', { defaultValue: 'Saturación/Rendimiento Normal' })}
          </h4>
          <div className="h-64 relative" id="saturation-chart">
            <Pie key="saturation-pie" data={saturationData} options={{
              ...pieOptions,
              plugins: {
                ...pieOptions.plugins,
                tooltip: {
                  callbacks: {
                    label: function(context: any) {
                      const label = context.label || '';
                      const value = context.parsed;
                      return `${label}: ${value.toFixed(1)}%`;
                    }
                  }
                }
              }
            }} />
            {/* Texto central del donut */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-700">
                  {((stats.normalSaturation || 0)).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-500">
                  {t('charts.saturation.label', { defaultValue: 'Saturación' })}
                </div>
              </div>
            </div>
          </div>
        </div>
        )}

        {/* Gráfica de Tiempos por Elemento */}
        {chartVisibility.elements && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
            {t('charts.elements.title', { defaultValue: 'Tiempos por Elemento' })}
          </h4>
          <div className="h-64" id="elements-chart">
            <Bar key="elements-bar" data={elementTimeData} options={barOptions} />
          </div>
        </div>
        )}

        {/* Gráfica de Suplementos */}
        {chartVisibility.supplements && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
            {t('charts.supplements.title', { defaultValue: 'Suplementos por Elemento' })}
          </h4>
          <div className="h-64" id="supplements-chart">
            <Bar key="supplements-bar" data={supplementsData} options={supplementsBarOptions} />
          </div>
        </div>
        )}

        {/* Activity chart removed as requested */}

        {/* Gráfica de Distribución del Tiempo de Ciclo */}
        {chartVisibility.cycleDistribution && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
            {t('charts.cycleDistribution.title', { defaultValue: 'Distribución del Tiempo de Ciclo' })}
          </h4>
          <div className="h-64 relative" id="cycle-distribution-chart">
            <Pie key="cycle-distribution-pie" data={cycleDistributionData} options={{
              ...pieOptions,
              plugins: {
                ...pieOptions.plugins,
                tooltip: {
                  callbacks: {
                    label: function(context: any) {
                      const label = context.label || '';
                      const value = context.parsed;
                      const percentage = ((value / stats.normalCycle) * 100).toFixed(1);
                      return `${label}: ${value.toFixed(2)} ${t(`units.${timeUnit}`)} (${percentage}%)`;
                    }
                  }
                }
              }
            }} />
            {/* Texto central del donut */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-700">
                  {((workTime / (workTime + restTime)) * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-500">
                  {t('charts.cycleDistribution.work', { defaultValue: 'Tiempo de Trabajo' })}
                </div>
              </div>
            </div>
          </div>
        </div>
        )}

        {/* Nuevas gráficas de símbolos de diagrama de flujo */}
        {hasFlowchartData && (chartVisibility.flowchartSymbols || chartVisibility.operationPercentages || chartVisibility.operationCount) && (
          <>
            {/* Gráfica de Distribución de Símbolos */}
            {chartVisibility.flowchartSymbols && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
                {t('charts.flowchartSymbols.distribution', { defaultValue: 'Distribución por Tipo de Operación' })}
              </h4>
              <div className="h-64" id="flowchart-symbols-chart">
                <Pie key="flowchart-symbols-pie" data={flowchartSymbolData} options={{
                  ...pieOptions,
                  plugins: {
                    ...pieOptions.plugins,
                    tooltip: {
                      callbacks: {
                        label: function(context: any) {
                          const label = context.label || '';
                          const value = context.parsed;
                          const percentage = ((value / totalTime) * 100).toFixed(1);
                          return `${label}: ${value.toFixed(2)} ${t(`units.${timeUnit}`)} (${percentage}%)`;
                        }
                      }
                    }
                  }
                }} />
              </div>
            </div>
            )}

            {/* Gráfica de Porcentajes por Tipo de Operación */}
            {chartVisibility.operationPercentages && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
                {t('charts.flowchartSymbols.percentages', { defaultValue: 'Porcentajes por Tipo de Operación' })}
              </h4>
              <div className="h-64 relative" id="operation-percentages-chart">
                <Pie key="operation-percentages-pie" data={operationPercentageData} options={{
                  ...pieOptions,
                  plugins: {
                    ...pieOptions.plugins,
                    tooltip: {
                      callbacks: {
                        label: function(context: any) {
                          const label = context.label || '';
                          const value = context.parsed;
                          return `${label}: ${value}%`;
                        }
                      }
                    }
                  }
                }} />
                {/* Texto central del donut */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-700">
                      {activeSymbols.length}
                    </div>
                    <div className="text-sm text-gray-500">
                      {t('charts.flowchartSymbols.types', { defaultValue: 'Tipos de Operación' })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            )}

            {/* Gráfica de Cantidad de Operaciones por Tipo */}
            {chartVisibility.operationCount && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-md font-medium text-gray-700 mb-3 text-center">
                {t('charts.flowchartSymbols.countTitle', { defaultValue: 'Cantidad de Operaciones por Tipo' })}
              </h4>
              <div className="h-64" id="operation-count-chart">
                <Bar key="operation-count-bar" data={operationCountData} options={{
                  ...barOptions,
                  plugins: {
                    ...barOptions.plugins,
                    tooltip: {
                      callbacks: {
                        label: function(context: any) {
                          const label = context.dataset.label || '';
                          const value = context.parsed.y;
                          return `${label}: ${value}`;
                        }
                      }
                    }
                  }
                }} />
              </div>
            </div>
            )}
          </>
        )}
      </div>

      {/* Resumen estadístico adicional */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-md font-medium text-blue-900 mb-3">
          {t('charts.summary.title', { defaultValue: 'Resumen Estadístico' })}
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="font-semibold text-blue-700">
              {filteredElements.length}
            </div>
            <div className="text-blue-600">
              {t('charts.summary.totalElements', { defaultValue: 'Elementos Totales' })}
            </div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-blue-700">
              {((stats.optimalSaturation || 0)).toFixed(1)}%
            </div>
            <div className="text-blue-600">
              {t('charts.summary.optimalSaturation', { defaultValue: 'Saturación Óptima' })}
            </div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-blue-700">
              {((stats.normalSaturation || 0)).toFixed(1)}%
            </div>
            <div className="text-blue-600">
              {t('charts.summary.saturation', { defaultValue: 'Saturación Normal' })}
            </div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-blue-700">
              {((stats.maxActivity || 0)).toFixed(1)}%
            </div>
            <div className="text-blue-600">
              {t('charts.summary.maxActivity', { defaultValue: 'Actividad Máxima' })}
            </div>
          </div>
        </div>
      </div>

      {/* Resumen de símbolos de diagrama de flujo */}
      {hasFlowchartData && (
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h4 className="text-md font-medium text-green-900 mb-3">
            {t('charts.flowchartSymbols.summary', { defaultValue: 'Análisis de Tipos de Operación' })}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            {activeSymbols.map(([symbol, data]) => (
              <div key={symbol} className="text-center p-3 bg-white rounded-lg border border-green-200">
                <div className="flex items-center justify-center mb-2">
                  <div 
                    className="w-4 h-4 rounded-full mr-2" 
                    style={{ backgroundColor: flowchartSymbolColors[symbol as FlowchartSymbol] }}
                  ></div>
                  <div className="font-semibold text-green-700">
                    {t(`cronoSeguido:flowchartSymbols.${symbol}.name`, { defaultValue: symbol.toUpperCase() })}
                  </div>
                </div>
                <div className="text-2xl font-bold text-green-800">
                  {((data.time / totalTime) * 100).toFixed(1)}%
                </div>
                <div className="text-green-600 text-xs">
                  {data.time.toFixed(2)} {t(`units.${timeUnit}`)} ({data.count} {data.count === 1 ? t('charts.flowchartSymbols.element', { defaultValue: 'elemento' }) : t('charts.flowchartSymbols.elements', { defaultValue: 'elementos' })})
                </div>
              </div>
            ))}
          </div>
          
          {/* Estadísticas adicionales */}
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm border-t border-green-200 pt-4">
            <div className="text-center">
              <div className="font-semibold text-green-700">
                {activeSymbols.length}
              </div>
              <div className="text-green-600">
                {t('charts.flowchartSymbols.typesUsed', { defaultValue: 'Tipos Utilizados' })}
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-700">
                {activeSymbols.reduce((sum, [_, data]) => sum + data.count, 0)}
              </div>
              <div className="text-green-600">
                {t('charts.flowchartSymbols.totalOperations', { defaultValue: 'Operaciones Totales' })}
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-700">
                {activeSymbols.length > 0 ? 
                  activeSymbols.reduce((max, [_, data]) => Math.max(max, (data.time / totalTime) * 100), 0).toFixed(1) + '%' : 
                  '0%'
                }
              </div>
              <div className="text-green-600">
                {t('charts.flowchartSymbols.mostCommon', { defaultValue: 'Más Común' })}
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-700">
                {(totalTime / activeSymbols.reduce((sum, [_, data]) => sum + data.count, 0)).toFixed(2)}
              </div>
              <div className="text-green-600">
                {t('charts.flowchartSymbols.avgTime', { defaultValue: 'Tiempo Promedio' })} ({t(`units.${timeUnit}`)})
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 