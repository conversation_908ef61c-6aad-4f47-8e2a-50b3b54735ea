-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own studies" ON studies;
DROP POLICY IF EXISTS "Users can update their own studies" ON studies;
DROP POLICY IF EXISTS "Users can delete their own studies" ON studies;

-- Create updated policies with improved organization and sharing support
CREATE POLICY "Users can view their own studies"
ON studies
FOR SELECT
TO authenticated
USING (
    -- User owns the study
    user_id = auth.uid()
    -- OR user is a member of the organization that owns the study
    OR organization_id IN (
        SELECT organization_id
        FROM organization_members
        WHERE user_id = auth.uid()
    )
    -- OR the study is explicitly shared
    OR is_shared = true
);

-- Allow updates for owners, admins, and study creators
CREATE POLICY "Users can update their own studies"
ON studies
FOR UPDATE
TO authenticated
USING (
    user_id = auth.uid()
    OR (
        organization_id IN (
            SELECT organization_id
            FROM organization_members
            WHERE user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    )
);

-- Allow deletion for owners, admins, and study creators
CREATE POLICY "Users can delete their own studies"
ON studies
FOR DELETE
TO authenticated
USING (
    user_id = auth.uid()
    OR (
        organization_id IN (
            SELECT organization_id
            FROM organization_members
            WHERE user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    )
);

-- Ensure proper indexing for performance
CREATE INDEX IF NOT EXISTS idx_studies_is_shared ON studies(is_shared);