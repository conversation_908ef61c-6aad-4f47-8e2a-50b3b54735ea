import React from 'react';
import { Button } from '../ui/button';
import { Study } from '../../types';
import { generatePDF } from '../../utils/reportPDF';

interface ExportToPDFButtonProps {
  study: Study;
  disabled?: boolean;
}

const ExportToPDFButton: React.FC<ExportToPDFButtonProps> = ({ study, disabled }) => {
  const handleExport = async () => {
    try {
      await generatePDF(study);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  return (
    <Button onClick={handleExport} disabled={disabled}>
      Exportar a PDF
    </Button>
  );
};

export default ExportToPDFButton;