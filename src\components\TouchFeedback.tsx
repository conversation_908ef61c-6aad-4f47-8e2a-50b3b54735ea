import React, { useState } from 'react';

interface TouchFeedbackProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  activeClassName?: string;
  disabled?: boolean;
}

export const TouchFeedback: React.FC<TouchFeedbackProps> = ({
  children,
  onClick,
  className = '',
  activeClassName = 'opacity-70',
  disabled = false
}) => {
  const [isActive, setIsActive] = useState(false);

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!disabled) {
      setIsActive(true);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!disabled) {
      setIsActive(false);
      onClick?.();
    }
  };

  const handleTouchCancel = () => {
    setIsActive(false);
  };

  return (
    <div
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchCancel={handleTouchCancel}
      onClick={onClick}
      className={`${className} ${isActive ? activeClassName : ''} transition-opacity duration-150 touch-manipulation`}
      style={{ WebkitTapHighlightColor: 'transparent' }}
    >
      {children}
    </div>
  );
};