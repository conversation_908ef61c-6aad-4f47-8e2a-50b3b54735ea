import React from 'react';
import { <PERSON>r, Trash2, <PERSON>, <PERSON><PERSON>, Book<PERSON>pen, FolderPlus, Folder as FolderIcon } from 'lucide-react';
import { Study, Folder } from '../types/index';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../store/authStore';
import { useStudyStore } from '../store/studyStore';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';
import { toast } from 'react-hot-toast';

export interface StudyCardProps {
  study: Study;
  isSelected: boolean;
  onClick: () => void;
  onDelete: () => void;
  onMoveToFolder?: () => void;
  viewMode: 'grid' | 'list';
  folder?: Folder;
  onFolderClick?: (folder: Folder) => void;
}

export const StudyCard: React.FC<StudyCardProps> = ({
  study,
  isSelected,
  onClick,
  onDelete,
  onMoveToFolder,
  viewMode,
  folder,
  onFolderClick,
}) => {
  const { t } = useTranslation(['study', 'common', 'library']);
  const { user } = useAuthStore();
  const { cloneStudy, setSelectedStudy } = useStudyStore();
  
  const baseName = study.required_info.name;
  const company = study.required_info.company;
  const date = study.required_info.date;
  const isEstimated = study.optional_info?.isEstimated;

  const name = isEstimated ? `${baseName} (${t('estimated', { ns: 'study' })})` : baseName;

  const isOwnedStudy = study.user_id === user?.id;

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  };

  const handleCloneStudy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const clonedStudy = await cloneStudy(study);
      if (clonedStudy) {
        toast.success(t('cloneSuccess', { ns: 'study' }));
        setSelectedStudy(clonedStudy);
      }
    } catch (error) {
      console.error('Error al clonar el estudio:', error);
      toast.error(t('cloneError', { ns: 'study' }));
    }
  };

  const handleMoveToFolder = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMoveToFolder) {
      onMoveToFolder();
    }
  };

  const handleFolderClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (folder && onFolderClick) {
      onFolderClick(folder);
    }
  };

  if (!name || !company || !date) {
    console.warn('Invalid study data', { study_id: study.id, name, company, date });
    return null;
  }

  const StudyIcon = isOwnedStudy ? Timer : Users;
  const iconColor = isOwnedStudy ? 'text-green-600' : 'text-blue-600';

  if (viewMode === 'list') {
    return (
      <div 
        className={`p-3 rounded-lg shadow-md cursor-pointer transition-all duration-200 ease-in-out w-full mb-2 overflow-hidden
          ${isSelected 
            ? 'bg-green-100 border-2 border-green-500 shadow-lg' 
            : isEstimated 
              ? 'bg-purple-100 hover:bg-purple-200 border-2 border-transparent'
              : 'bg-white hover:bg-gray-50 border-2 border-transparent'
          }`}
        onClick={onClick}
      >
        {/* Layout principal para móvil - disposición vertical */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          {/* Contenido principal */}
          <div className="flex items-start space-x-3 flex-grow min-w-0">
            <StudyIcon className={`w-5 h-5 mt-0.5 flex-shrink-0 ${isSelected ? iconColor : 'text-gray-500'}`} />
            <div className="flex-1 min-w-0">
              <p className={`font-semibold break-words text-sm sm:text-base ${isSelected ? 'text-green-700' : 'text-gray-800'}`}>
                {name}
              </p>
              <p className="text-xs text-gray-500 break-words">
                {company}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                {new Date(date).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Acciones - en móvil aparecen debajo, en desktop a la derecha */}
          <div className="flex items-center justify-end space-x-1 flex-shrink-0 ml-8 sm:ml-2">
            {study.optional_info?.visibleEnBiblioteca && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="p-1">
                      <BookOpen className="w-4 h-4 text-purple-600" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent><span>{t('library:shared')}</span></TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleCloneStudy}
                    className="p-1.5 text-blue-500 hover:bg-blue-50 rounded-full transition-colors"
                    aria-label={t('clone', { ns: 'study' })}
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent><p>{t('clone', { ns: 'study' })}</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {onMoveToFolder && isOwnedStudy && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleMoveToFolder}
                      className="p-1.5 text-green-500 hover:bg-green-50 rounded-full transition-colors"
                      aria-label={t('common:moveToFolder')}
                    >
                      <FolderPlus className="w-4 h-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent><p>{t('common:moveToFolder')}</p></TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={handleDelete}
                    className="p-1.5 text-red-500 hover:bg-red-50 rounded-full transition-colors"
                    aria-label={t('common:delete')}
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent><p>{t('common:delete')}</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`rounded-lg shadow-md cursor-pointer transition-all duration-200 ease-in-out max-w-full overflow-hidden flex flex-col justify-between
        ${isSelected 
          ? 'bg-green-100 border-2 border-green-500 shadow-lg' 
          : isEstimated
            ? 'bg-purple-100 hover:bg-purple-200 border-2 border-transparent'
            : 'bg-white hover:bg-gray-50 border-2 border-transparent'
        }`}
      onClick={onClick}
    >
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="flex flex-col items-center">
            <StudyIcon className={`w-6 h-6 ${isSelected ? iconColor : 'text-gray-600'}`} />
              {/* Icono de biblioteca si el estudio es compartido */}
              {study.optional_info?.visibleEnBiblioteca && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span>
                        <BookOpen className="w-5 h-5 mt-1 text-purple-600" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>{t('library:shared')}</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <div className="flex-1">
              <h3 className={`font-semibold text-lg ${isSelected ? 'text-green-700' : 'text-gray-900'}`}>
                {name}
              </h3>
              <div className="text-sm text-gray-600">
                <span>{new Date(date).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end">
            <div className="flex items-center space-x-2 mb-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleCloneStudy}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                      title={t('clone', { ns: 'study' })}
                    >
                      <Copy className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('clone', { ns: 'study' })}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
                           {onMoveToFolder && isOwnedStudy && (
                 <TooltipProvider>
                   <Tooltip>
                     <TooltipTrigger asChild>
                       <button
                         onClick={handleMoveToFolder}
                         className="p-2 text-green-600 hover:bg-green-50 rounded-full"
                         title={t('common:moveToFolder')}
                       >
                         <FolderPlus className="w-5 h-5" />
                       </button>
                     </TooltipTrigger>
                     <TooltipContent>
                       <p>{t('common:moveToFolder')}</p>
                     </TooltipContent>
                   </Tooltip>
                 </TooltipProvider>
               )}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleDelete}
                      className="p-2 text-red-500 hover:bg-red-50 rounded-full"
                      title={t('common:delete')}
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('common:delete')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>
      
      {folder && (
        <div 
          className="bg-gray-50 p-2 border-t border-gray-200 mt-2 hover:bg-gray-100"
          onClick={handleFolderClick}
        >
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FolderIcon className="w-4 h-4" style={{ color: folder.color }} />
            <span className="font-medium truncate">{folder.name}</span>
          </div>
        </div>
      )}
    </div>
  );
};