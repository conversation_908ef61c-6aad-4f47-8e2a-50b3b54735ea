---

# Registro de cambios

## [42.1.37] - 2025-03-28
### Mejoras
- **Sistema de reordenación de elementos**: Implementado un nuevo sistema de navegación para reordenar elementos tanto en la pantalla de método como en la pantalla de cronómetro seguido, utilizando botones en lugar de arrastrar y soltar para mejorar la compatibilidad y rendimiento.
- **Sincronización mejorada**: Optimizamos la sincronización entre la pantalla de método y cronómetro seguido al reordenar elementos, manteniendo la coherencia de los datos.
- **Cambio de contraseña**: Añadida una nueva sección para cambiar la contraseña en la página de perfil, mejorando las opciones de seguridad para los usuarios.
### Corregido
- Eliminadas las advertencias relacionadas con la biblioteca de arrastrar y soltar en la consola de desarrollo.
- Mejorada la accesibilidad de los botones de navegación al añadir etiquetas aria apropiadas.

## [42.1.36] - 2025-03-25
### Correcciones
- Se requiere un mínimo de 10 tomas para elementos con tiempos menores a 30 segundos antes de aplicar el cálculo estadístico de tomas restantes.
- Los elementos con 0% de suplementos ahora se consideran como elementos sin suplementos asignados, lo que afecta a la visibilidad del botón de informe.
- Mejorado el diseño de la tabla de detalle de elementos en el informe:
  - La columna de descripción ahora se ajusta en altura manteniendo su ancho
  - Contenido centrado en todas las columnas excepto la descripción
  - Ajustados los anchos de columna para una mejor visualización

## [42.1.35] - 2025-03-20
### Mejoras
- **Sistema de bloqueo de activación mejorado**:
  - Se añadió un sistema de reintentos automáticos (hasta 3 intentos).
  - Verificación del estado de la batería.
  - Mejor manejo de eventos de visibilidad.
  - Recuperación automática del bloqueo de activación cuando es liberado por el sistema.
  - Mejora en la limpieza de recursos.
  - Solucionado el problema de apagado de pantalla en dispositivos móviles.

## [42.1.34] - 2025-03-15
### Mejoras
- **Advertencia de estudio compartido**: Se implementó una advertencia visual cuando un usuario intenta editar un estudio que no le pertenece. Esta advertencia se muestra en el formulario de estudio, la página de métodos, la página de suplementos, la página de máquinas y la página de frecuencias, informando a los usuarios que los cambios no se guardarán si no son los propietarios del estudio.

## [42.1.33] - 2025-03-10
### Mejoras
- **Advertencia de estudio compartido**: Se implementó una advertencia visual cuando un usuario intenta editar un estudio que no le pertenece. Esta advertencia se muestra en el formulario de estudio, la página de métodos y la página de suplementos, informando a los usuarios que los cambios no se guardarán si no son los propietarios del estudio.

## [42.1.32] - 2025-03-05
### Corregido
- **Preservación de suplementos**: Se corrigió un problema que causaba que los suplementos de los elementos seleccionados no se preservaran correctamente al crear un nuevo estudio. Ahora los valores porcentuales y otras propiedades de los suplementos se mantienen intactos al crear un estudio a partir de elementos existentes.

## [42.1.31] - 2025-03-01
### Corregido
- **Errores de tipo**: Se corrigieron errores de tipo en el componente `StudyForm` relacionados con el manejo de datos del perfil del usuario y la inicialización de nuevos estudios, mejorando la estabilidad de la aplicación.
- **Estructura de datos**: Se actualizó la inicialización de nuevos estudios para asegurar que el campo `elements` se cree correctamente como un arreglo vacío, manteniendo la consistencia con la estructura de datos esperada.

## [42.1.30] - 2025-02-25
### Corregido
- **Referencias a datos de estudio**: Se corrigieron las referencias a la escala de actividad y el nombre del estudio en múltiples componentes (FrequencyPage, MachinePage, CronoSeguidoPage, ReportPage, MethodPage) para usar la estructura correcta `selectedStudy.required_info` en lugar de `selectedStudy.info.required`, mejorando la consistencia y estabilidad de la aplicación.

## [42.1.29] - 2025-02-20
### Mejoras
- **Persistencia de estudios**: Se implementó una funcionalidad para mantener el estudio seleccionado al recargar la página, utilizando la URL como fuente de información. Esto mejora la experiencia del usuario al evitar la pérdida del contexto de trabajo actual.

## [42.1.28] - 2025-02-15
### Corregido
- Se corrigió un problema al crear un nuevo estudio, ahora se selecciona automáticamente y muestra su nombre en el encabezado.
- Se corrigió el cálculo del tiempo base en los suplementos de fatiga para usar la escala de actividad correcta (60-80) en lugar de un valor fijo de 100, mejorando la precisión de los cálculos de fatiga.

## [42.1.27] - 2025-02-10
### Mejoras
- **Traducciones mejoradas**: Se añadieron traducciones para la notificación que informa a los usuarios cuando un estudio se comparte automáticamente con su organización.
- **Correcciones de código**: Se corrigieron errores tipográficos en las referencias de traducción para mejorar la estabilidad de la aplicación.
### Corregido
- **Cálculo de fatiga**: Se corrigió el cálculo de fatiga restante en el CASO 2 (fatiga parcialmente dentro) para aplicar correctamente la fórmula: Fatiga Total - (Inactividad - 30).

## [42.1.26] - 2025-02-05
### Mejoras
- **Eliminación de miembros de la organización**: Cuando se elimina a un miembro de una organización, sus estudios se descomparten automáticamente y ya no tienen acceso a los estudios de otros miembros de la organización.
- **Controles de privacidad mejorados**: Se mejoraron los controles de privacidad para asegurar que los estudios solo sean visibles para los miembros actuales de la organización.
- **Mejoras de seguridad**: Se añadieron medidas de seguridad adicionales para proteger los datos de los estudios cuando cambian los miembros de la organización.
- **Notificaciones de permisos**: Se implementó un sistema de notificaciones que alerta a los usuarios cuando intentan modificar estudios que no les pertenecen, informándoles que sus cambios no se guardarán.

## [42.1.25] - 2025-01-31
### Corregido
- **Traducciones de informes**: Se corrigieron problemas de internacionalización en los componentes de informes asegurando que todas las llamadas a traducciones usen espacios de nombres explícitos.
- **Exportación a PDF y Excel**: Se corrigieron las referencias de traducción en las utilidades de exportación a PDF y Excel para usar correctamente los espacios de nombres.
- **Traducciones al español**: Se actualizó la estructura del archivo de traducciones en español para que coincida con su contraparte en inglés, asegurando claves consistentes entre idiomas.

## [42.1.24] - 2025-01-27
### Corregido
- **Traducciones del menú**: Se corrigió un problema que impedía la visualización correcta de las traducciones para "Cronómetro continuo" y "Tiempos de frecuencia" en el menú de navegación.

## [42.1.23] - 2025-01-23
### Mejoras
- **Compartir estudios automáticamente**: Los nuevos estudios que crees se compartirán automáticamente con tu organización si eres miembro de una, simplificando el proceso de colaboración.

## [42.1.22] - 2025-01-19
### Mejoras
- **Compartir estudios con organizaciones**: Ahora puedes compartir tus estudios con tu organización directamente desde la página de perfil.
- **Interfaz mejorada**: Hemos actualizado la interfaz para hacer más intuitiva la selección de estudios para compartir con tu organización.
- **Notificaciones**: Ahora recibirás notificaciones al compartir estudios con tu organización, manteniéndote informado sobre el estado de tus acciones.

## [42.1.21] - 2025-01-15
### Mejoras
- **Compartir estudios con organizaciones**: Ahora puedes compartir tus estudios con tu organización directamente desde la página de perfil.

## [42.1.20] - 2025-01-11
### Corregido
- **Restricción de acceso a solicitudes**: Hemos mejorado la seguridad de tu organización al restringir el acceso a las solicitudes de unión pendientes solo a administradores y propietarios de la organización.
- **Mejora de seguridad**: Hemos añadido una capa adicional de seguridad para asegurar que solo los usuarios autorizados puedan ver y gestionar las solicitudes de unión.

## [42.1.19] - 2025-01-07
### Corregido
- **Error de clave duplicada**: Hemos corregido un problema que causaba errores al aprobar solicitudes de usuarios previamente expulsados.
- **Manejo de solicitudes previas**: Hemos mejorado la forma en que manejamos las solicitudes previas para evitar conflictos y garantizar una experiencia más fluida.

## [42.1.18] - 2025-01-03
### Añadido
- **Actualizaciones automáticas de solicitudes de unión**: Hemos implementado una función para actualizar automáticamente las solicitudes de unión en la página de perfil, asegurando que veas las últimas actualizaciones sin tener que recargar manualmente.
- **Botón de actualización manual**: Ahora puedes actualizar manualmente las solicitudes de unión pendientes de la organización con un solo clic.
- **Mejoras en la experiencia del usuario**: Hemos añadido indicadores visuales y mensajes de éxito/error para hacer más claro cuando las solicitudes están siendo actualizadas.
### Corregido
- **Problema de notificación**: Hemos corregido un problema donde los administradores no recibían notificaciones para las solicitudes de unión pendientes a sus organizaciones.
- **Caché de solicitudes**: Hemos mejorado la lógica de almacenamiento en caché para permitir la actualización forzada de solicitudes pendientes, ignorando el tiempo de caché configurado.

## [42.1.17] - 2024-12-29
### Añadido
- **Integración de funciones RPC**: Hemos integrado la función `isOrganizationMember` para verificar si un usuario es miembro de una organización.
- **Mejora en la obtención de nombres**: Hemos actualizado la función `getOrganizationName` para usar la nueva función `isOrganizationMember` y simplificar el código.
### Corregido
- **Error de sintaxis**: Hemos corregido un error de sintaxis en la función `fetchOrganizationNames` que impedía la compilación de la aplicación.

## [42.1.16] - 2024-12-25
### Añadido
- **Nombres de organizaciones conocidas**: Hemos implementado un mecanismo para usar nombres de organizaciones conocidos cuando el usuario es miembro de la organización pero no puede acceder directamente al nombre.
- **Caché mejorado**: Hemos mejorado el sistema de caché para verificar primero el caché local antes de realizar consultas a la base de datos.
### Corregido
- **Obtención de nombres de organizaciones**: Hemos simplificado el proceso de obtención de nombres de organizaciones, eliminando consultas innecesarias a tablas que no contienen la información requerida.

## [42.1.15] - 2024-12-21
### Añadido
- **Depuración mejorada**: Hemos añadido código para imprimir la estructura completa de las solicitudes de unión, lo que ayudará a identificar los campos disponibles para obtener el nombre de la organización.
- **Nuevas funciones**: Hemos implementado la función `fetchSentRequests` para recuperar las solicitudes enviadas por el usuario actual.
### Corregido
- **Obtención de nombres de organizaciones**: Hemos mejorado el sistema para obtener nombres de organizaciones usando múltiples métodos alternativos cuando la consulta principal falla.

## [42.1.14] - 2024-12-17
### Corregido
- **Obtención de nombres de organizaciones**: Hemos mejorado el sistema para recuperar nombres de organizaciones usando múltiples métodos alternativos cuando la consulta principal falla.
- **Función RPC inexistente**: Hemos eliminado la llamada a una función RPC inexistente (`get_organization_name`) y la reemplazamos con consultas directas a las tablas.

## [42.1.13] - 2024-12-13
### Corregido
- **Error de consulta SQL**: Hemos corregido un error al intentar acceder a una columna inexistente (`organization_name`) en la tabla `organization_join_requests`.
- **Obtención de nombres de organizaciones**: Hemos mejorado el sistema para recuperar nombres de organizaciones usando funciones RPC cuando sea posible.

## [42.1.12] - 2024-12-09
### Corregido
- **Error de sintaxis**: Hemos corregido un error de sintaxis en la definición de tipo de la función fetchOrganizationNames en organizationStore.ts que impedía que la aplicación se compilara correctamente.

## [42.1.11] - 2024-12-05
### Corregido
- **Nombres de organizaciones**: Hemos corregido un problema donde se mostraban nombres de organizaciones incorrectos almacenados en la caché local en lugar de recuperar el nombre real de la base de datos.
- **Obtención de nombres**: Hemos mejorado la función para obtener nombres de organizaciones para priorizar siempre las consultas directas a la base de datos.

## [42.1.10] - 2024-12-01
### Corregido
- **Visibilidad de solicitudes pendientes**: Hemos corregido un problema donde las solicitudes pendientes no aparecían para los propietarios de la organización debido al sistema de caché.
- **Actualizaciones de solicitudes**: Hemos añadido un botón de actualización manual para forzar la recarga de solicitudes pendientes.
- **Detección de propietarios**: Hemos mejorado la lógica para detectar correctamente a los propietarios de la organización.

## [42.1.9] - 2024-11-27
### Corregido
- **Actualización del conteo de miembros**: Hemos corregido un problema donde el conteo de miembros no se actualizaba instantáneamente al aprobar solicitudes de unión.
- **Actualizaciones de base de datos**: Hemos simplificado el proceso de actualización de solicitudes en la base de datos para evitar problemas al aprobar o rechazar solicitudes.

## [42.1.8] - 2024-11-23
### Corregido
- **Permisos de solicitudes de unión**: Hemos corregido un problema donde los usuarios que no son propietarios de la organización podían ver las solicitudes de unión pendientes.

## [42.1.7] - 2024-11-19
### Mejoras
- **Simplificación de la interfaz**: Hemos integrado la funcionalidad de miembros de la organización directamente en la página de perfil, eliminando la necesidad de una página de organización separada.
- **Experiencia de usuario mejorada**: Hemos añadido un selector de organizaciones en la página de perfil para gestionar miembros de múltiples organizaciones desde un solo lugar.

## [42.1.6] - 2024-11-15
### Corregido
- Hemos mejorado la gestión de miembros de la organización para actualizar la interfaz inmediatamente al expulsar miembros.
- Hemos corregido problemas de solicitudes duplicadas al aprobar o rechazar solicitudes de unión a la organización.
- Hemos implementado lógica para manejar correctamente los casos de re-aprobación de miembros previamente expulsados.

## [42.1.5] - 2024-11-11
### Corregido
- Hemos corregido un problema de recursión infinita en las políticas de seguridad para la relación "organization_members".
- Hemos optimizado la política de seguridad para la tabla "studies" para evitar recursión al consultar miembros de la organización.
- Hemos implementado funciones auxiliares para mejorar el rendimiento y evitar recursión en las políticas de seguridad.
- Hemos añadido índices para optimizar las consultas relacionadas con organizaciones.

## [42.1.4] - 2024-11-07
### Seguridad
- Hemos saneado los datos sensibles antes de registrarlos en la consola para prevenir ataques de inyección de registros.
- Hemos mejorado el manejo de datos potencialmente maliciosos en el registro de depuración.

## [42.1.3] - 2024-11-03
### Añadido
- Hemos implementado el registro exclusivo con Google para nuevos usuarios.
- Hemos añadido traducciones para el registro con Google en ambos idiomas.
### Corregido
- Hemos mejorado el manejo de errores en el proceso de autenticación.
- Hemos corregido un problema con la visualización de mensajes de error en el formulario de inicio de sesión.
- Hemos optimizado el flujo de autenticación para evitar recargas de página que ocultaban los mensajes de error.

## [42.1.2] - 2024-10-30
### Corregido
- Hemos corregido un problema con los mensajes de error en el formulario de inicio de sesión que no se mostraban.
- Hemos mejorado las traducciones para los mensajes de error de autenticación.
- Hemos reorganizado la estructura de traducción para mantener consistencia entre idiomas.

## [42.1.1] - 2024-10-26
### Corregido
- Hemos eliminado claves duplicadas en los archivos de traducción (en/common.ts y es/common.ts).
- Hemos reorganizado la estructura de traducción para mejorar la consistencia.

## [42.1.0] - 2024-10-22
### Añadido
- Hemos restaurado la tarjeta de información de créditos en la página de perfil.
### Corregido
- Hemos corregido el problema de "organización desconocida" al consultar organizaciones.
- Hemos mejorado el manejo de errores en la función `getOrganizationName` para consultar directamente la base de datos.
- Hemos implementado una solución alternativa para consultar organizaciones sin depender de la función `exec_sql`.
- Hemos corregido la estructura de datos para `members` y `joinRequests` en el almacén de organizaciones.
- Hemos actualizado el componente `OrganizationPage` para usar la nueva estructura de datos.
- Hemos actualizado el componente `OrganizationMembers` para usar la nueva estructura de datos.
- Hemos actualizado el componente `PendingJoinRequests` para usar la nueva estructura de datos.
### Mejorado
- Hemos implementado un sistema de caché local para los nombres de organizaciones.
- Hemos mejorado la función `getOrganizationName` para intentar múltiples métodos para obtener nombres.
- Hemos añadido registros detallados para facilitar la depuración.

## [42.0.27] - 2024-10-18
### Añadido
- Hemos añadido funcionalidad para gestionar miembros de la organización.
- Hemos implementado una página de organización para ver y eliminar miembros.
- Hemos mejorado la interfaz de usuario para la gestión de organizaciones.

## [42.0.26] - 2024-10-14
### Corregido
- Hemos corregido un error al cambiar al idioma inglés donde se renderizaba directamente un objeto en el componente CreditsCard.
- Hemos corregido la estructura de las claves de traducción para credits.buy en inglés y español.

## [42.0.25] - 2024-10-10
### Corregido
- Hemos deshabilitado el modo de depuración de i18next para reducir errores en la consola.
- Hemos corregido las referencias de traducción en el componente PendingJoinRequests.

## [42.0.24] - 2024-10-06
### Corregido
- Hemos añadido traducciones faltantes para el encabezado, la suscripción y las solicitudes de unión.
- Hemos corregido problemas de espacios de nombres en el componente PendingJoinRequests.
- Hemos añadido traducciones de mostrar/ocultar en el nivel raíz.

## [42.0.23] - 2024-10-02
### Corregido
- Hemos migrado las traducciones de JSON a archivos TypeScript para mayor seguridad de tipos.
- Hemos añadido traducciones faltantes para las secciones de encabezado y suscripción.
- Hemos mejorado la configuración de i18next con una estructura de espacios de nombres adecuada.
- Hemos mejorado el manejo de errores en la obtención de nombres de organizaciones.
- Hemos añadido una interfaz TypeScript para el tipo JoinRequest.
- Hemos mejorado el manejo de errores asíncronos en los hooks useEffect.

## [42.0.22] - 2024-09-28
### Mejorado
- Hemos reorganizado el orden de las secciones en la página de perfil para una mejor experiencia de usuario.
- Hemos añadido traducciones en español e inglés para la nueva funcionalidad de cambio de contraseña.

## [42.0.21] - 2024-09-24
### Corregido
- Hemos mejorado la integración de i18n en el componente MySentJoinRequests especificando el espacio de nombres.
- Hemos mejorado el manejo de errores en la obtención de nombres de organizaciones.
- Hemos añadido detección de localización para el formato de fechas basado en el idioma actual.

## [42.0.20] - 2024-09-20
### Corregido
- Hemos corregido un error al recuperar nombres de organizaciones usando maybeSingle en lugar de single.
- Hemos añadido traducciones faltantes para los textos de solicitudes de unión en inglés y español.
- Hemos mejorado el manejo de errores cuando los datos de la organización no están disponibles.

## [42.0.19] - 2024-09-16
### Corregido
- Hemos corregido las importaciones de componentes de UI en el componente MySentJoinRequests.
- Hemos reemplazado los componentes personalizados de UI con HTML nativo y clases de Tailwind CSS.
- Hemos mejorado el manejo de errores y los estados de carga con una UI simplificada.

## [42.0.18] - 2024-09-12
### Corregido
- Hemos corregido las importaciones de componentes de UI en el componente MySentJoinRequests.
- Hemos reemplazado los componentes de Ant Design con los componentes de UI propios de la aplicación.
- Hemos mejorado el diseño y estilo de la visualización de solicitudes de unión enviadas.

## [42.0.17] - 2024-09-08
### Corregido
- Hemos corregido problemas con las políticas de seguridad al ver las solicitudes de unión enviadas.
- Hemos mejorado la obtención de nombres de organizaciones para trabajar con las políticas RLS existentes.
- Hemos añadido un mecanismo de caché para los nombres de organizaciones para reducir las consultas a la base de datos.
- Hemos mejorado el manejo de errores para prevenir fallos de la aplicación cuando los permisos son insuficientes.

## [42.0.16] - 2024-09-04
### Añadido
- Hemos añadido una nueva característica: Los usuarios ahora pueden ver sus solicitudes de unión enviadas en la página de perfil.
- Hemos añadido un nuevo componente para mostrar las solicitudes de unión enviadas con su estado actual.
- Hemos mejorado el almacén de organizaciones para obtener y mostrar las solicitudes de unión enviadas con los nombres de las organizaciones.
- Hemos añadido traducciones para las nuevas funciones de solicitudes de unión.

## [42.0.15] - 2024-08-31
### Mejorado
- Hemos mejorado el registro para el proceso de aprobación de solicitudes de unión para diagnosticar mejor los problemas de verificación de membresía.
- Hemos añadido registros detallados de IDs de usuario y estado de membresía para una mejor depuración.
- Hemos reestructurado el código de inserción de miembros para mayor claridad y trazabilidad.

## [42.0.14] - 2024-08-27
### Corregido
- Hemos corregido un error al aprobar solicitudes de unión para usuarios que ya son miembros de la organización.
- Hemos añadido un paso de verificación para comprobar si un usuario ya es miembro antes de intentar añadirlo.
- Hemos mejorado el manejo de errores para proporcionar mensajes de error más descriptivos.

## [42.0.13] - 2024-08-23
### Corregido
- Hemos actualizado las funciones de aprobación/rechazo de solicitudes de unión para usar operaciones UPDATE directas tras implementar la política de seguridad necesaria.
- Hemos simplificado el código eliminando la solución alternativa de DELETE + INSERT.
- Hemos mejorado el registro para una mejor depuración y monitoreo.

## [42.0.12] - 2024-08-19
### Corregido
- Hemos resuelto un problema crítico con la aprobación/rechazo de solicitudes de unión a la organización implementando una estrategia de DELETE + INSERT para evitar restricciones de Seguridad a Nivel de Fila.
- Hemos mejorado el registro para las operaciones de base de datos para mejorar las capacidades de depuración.
- Hemos mantenido la misma funcionalidad mientras trabajamos dentro de las restricciones de seguridad de la base de datos.

## [42.0.11] - 2024-08-15
### Corregido
- Hemos reescrito completamente la lógica de aprobación/rechazo de solicitudes de unión para usar actualizaciones directas en la base de datos.
- Hemos añadido registros detallados para una mejor depuración de las operaciones de base de datos.
- Hemos implementado pasos de verificación para confirmar actualizaciones exitosas.
- Hemos creado funciones SQL para el procesamiento futuro de solicitudes de unión a nivel de base de datos.

## [42.0.10] - 2024-08-11
### Corregido
- Hemos corregido un error crítico en el procesamiento de solicitudes de unión donde las operaciones de actualización fallaban.
- Hemos mejorado la validación de los datos de respuesta de Supabase en operaciones de actualización e inserción.
- Hemos mejorado el manejo de errores para operaciones de base de datos.

## [42.0.9] - 2024-08-07
### Corregido
- Hemos corregido un problema crítico donde las solicitudes de unión a la organización rechazadas no se actualizaban correctamente en la base de datos.
- Hemos mejorado el procesamiento de solicitudes de unión con metadatos adicionales (processed_at y processed_by).
- Hemos mejorado el manejo de errores y el registro para las operaciones de aprobación/rechazo de solicitudes.

## [42.0.8] - 2024-08-03
### Corregido
- Hemos corregido un problema donde las solicitudes de unión a la organización rechazadas seguían apareciendo tras recargar la página.
- Hemos mejorado la gestión del estado para las solicitudes de unión para eliminar inmediatamente las solicitudes aprobadas o rechazadas de la UI.

## [42.0.7] - 2024-07-30
### Añadido
- Hemos implementado un sistema de notificaciones para las solicitudes de unión a la organización pendientes.
- Hemos añadido el componente PendingJoinRequests para mostrar las solicitudes de unión en la página de perfil.
- Hemos actualizado el componente JoinRequestsList para manejar correctamente la aprobación y rechazo de solicitudes.

## [42.0.6] - 2024-07-26
### Corregido
- Hemos implementado la funcionalidad faltante para las solicitudes de unión a la organización.
- Hemos añadido la función sendJoinRequest a organizationStore para manejar códigos de invitación.
- Hemos actualizado el componente JoinOrganization para usar la nueva función.

## [42.0.5] - 2024-07-22
### Añadido
- Hemos restaurado la tarjeta de información de créditos en la página de perfil.
- Hemos añadido funcionalidad para mostrar los créditos disponibles y usados del mes actual.
- Hemos creado un nuevo componente CreditsCard para una mejor organización del código.
### Cambiado
- Hemos actualizado ProfilePage para incluir la sección de información de créditos.
- Hemos mejorado la integración con el servicio de créditos existente.

## [42.0.4] - 2024-07-18
### Añadido
- Hemos añadido una página de Política de Privacidad con traducciones en inglés y español.
- Hemos añadido un enlace a la Política de Cookies en el pie del formulario de inicio de sesión.
- Hemos añadido el campo Nombre a la sección de recolección de datos en la Política de Privacidad.
### Cambiado
- Hemos actualizado la dirección de correo electró<NAME_EMAIL> en toda la aplicación.
- Hemos corregido que las traducciones en inglés no aparecieran en la página de Política de Privacidad.

## [42.0.3] - 2024-07-14
### Cambiado
- Corrección: actualización de la ruta de FAQ para usar el componente FAQ directamente.

## [42.0.2] - 2024-07-10
### Cambiado
- Actualización de App.tsx

## [42.0.1] - 2024-07-06
### Cambiado
- Corrección: actualización de scripts a módulos ES y mejora en la renderización de markdown.

## [42.0.0] - 2024-07-02
### Añadido
- Hemos integrado GitHub Actions para actualizaciones automáticas de versiones.
- Hemos mejorado el sistema de gestión de versiones.
### Corregido
- Hemos corregido el sistema de traducción en SupplementsMachine.
- Hemos eliminado spans redundantes en los textos de explicación.
- Hemos restaurado y organizado los textos de explicación completos en las traducciones.

## [41.0.0] - 2024-06-25
### Añadido
- Hemos mejorado el sistema de suplementos basado en máquinas.
- Hemos añadido una nueva interfaz para la gestión de suplementos.
- Hemos realizado mejoras en la experiencia del usuario.
### Cambiado
- Hemos refactorizado el sistema de cálculo.
- Hemos optimizado el rendimiento general.

## [40.0.3] - 2024-06-18
### Corregido
- Hemos realizado correcciones en el sistema de traducción.
- Hemos realizado mejoras generales en la estabilidad.
- Hemos optimizado el rendimiento.

## [4.2.0] - 2024-06-10
### Nuevas características
- **Restricciones de acceso a estudios**: Cuando se elimina a un miembro de una organización, sus estudios ya no son visibles para la organización, y el miembro eliminado no puede ver los estudios de otros miembros.
- **Limpieza de estudios huérfanos**: Nueva funcionalidad para limpiar estudios de miembros eliminados que aún mantienen referencias a la organización.

## [1.0.0] - 2024-06-01
### Añadido
- Hemos añadido un sistema de traducción multi-idioma.
- Hemos añadido el cálculo de suplementos por máquina.
- Hemos añadido el cálculo de fatiga ponderado.
- Hemos añadido explicaciones detalladas de los cálculos.
- Hemos añadido una interfaz moderna y responsiva.

## [Sin lanzar]
### Añadido
- Mejora en la visualización del cálculo del tiempo base en la sección de suplementos de máquinas.
- Aclaración visual del cálculo del tiempo base como la suma del tiempo de parada de la máquina más el valor máximo entre el tiempo de la máquina y el tiempo de funcionamiento de la máquina.
- Nuevas traducciones para elementos de UI relacionados con el cálculo del tiempo base.
### Corregido
- Corrección en el cálculo del tiempo base para asegurar que se use el valor máximo entre el tiempo de la máquina y el tiempo de funcionamiento de la máquina.
- Consistencia en el cálculo del suplemento de fatiga ponderado, usando la misma lógica de tiempo base en todas las funciones.

## [Próxima versión]

### Correcciones
- Se requiere un mínimo de 10 tomas para elementos con tiempos menores a 30 segundos antes de aplicar el cálculo estadístico de tomas restantes.
- Los elementos con 0% de suplementos ahora se consideran como elementos sin suplementos asignados, lo que afecta a la visibilidad del botón de informe.
- Mejorado el diseño de la tabla de detalle de elementos en el informe:
  - La columna de descripción ahora se ajusta en altura manteniendo su ancho
  - Contenido centrado en todas las columnas excepto la descripción
  - Ajustados los anchos de columna para una mejor visualización

---