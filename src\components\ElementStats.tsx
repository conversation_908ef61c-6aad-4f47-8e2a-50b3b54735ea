import React from 'react';
import { useTranslation } from 'react-i18next';
import { TimeRecord, ElementInstance } from '../types';

interface ElementStatsProps {
  totalTime?: number;
  averageTime?: number;
  recordCount?: number;
  averageActivity?: number;
  timeRecords?: TimeRecord[];
  element?: ElementInstance;
}

function calculateN(nPrime: number, sumX: number, sumX2: number): number {
  if (sumX === 0) return 0;
  const numeratorInsideRoot = nPrime * sumX2 - Math.pow(sumX, 2);
  if (numeratorInsideRoot < 0) return 0;
  const numerator = 40 * Math.sqrt(numeratorInsideRoot);
  const fraction = numerator / sumX;
  return Math.pow(fraction, 2);
}

export const ElementStats: React.FC<ElementStatsProps> = ({
  totalTime = 0,
  averageTime = 0,
  recordCount = 0,
  averageActivity = 0,
  timeRecords = [],
  element
}) => {
  const { t } = useTranslation();

  const sumX = timeRecords.reduce((sum, record) => sum + record.time, 0);
  const sumX2 = timeRecords.reduce((sum, record) => sum + Math.pow(record.time, 2), 0);
  const requiredTakes = Math.ceil(calculateN(timeRecords.length || 1, sumX, sumX2));

  // Calcular el número de tomas restantes usando el método estadístico
  const calculateRemainingTakes = () => {
    if (timeRecords.length < 2) return 0;

    const times = timeRecords.map(record => record.time);
    const mean = times.reduce((a, b) => a + b, 0) / times.length;
    
    // Calcular la desviación estándar
    const squaredDiffs = times.map(time => Math.pow(time - mean, 2));
    const variance = squaredDiffs.reduce((a, b) => a + b, 0) / (times.length - 1); // Usamos n-1 para la varianza muestral
    const stdDev = Math.sqrt(variance);

    // Calcular N' usando la fórmula N' = [(Z * s) / (E * x̄)]²
    const Z = 1.96; // Para 95% de confianza
    const E = 0.05; // 5% de error aceptado
    const totalRequiredTakes = Math.ceil(Math.pow((Z * stdDev) / (E * mean), 2));

    // Si el tiempo promedio es menor a 30 segundos y aún no tenemos 10 tomas
    if (mean < 30 && recordCount < 10) {
      return Math.max(10 - recordCount, totalRequiredTakes);
    }

    // Si ya tenemos más muestras que las requeridas, retornamos 0
    return Math.max(0, totalRequiredTakes - recordCount);
  };

  const remainingTakes = calculateRemainingTakes();

  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6 overflow-x-auto animate-slide-in">
      <div className="flex space-x-6 min-w-max">
        <div>
          <div className="text-sm text-gray-600">{t('takes', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{recordCount}</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('averageTime', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{averageTime.toFixed(2)}s</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('totalTime', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{totalTime.toFixed(2)}s</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('averageActivity', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{averageActivity.toFixed(0)}</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('remainingTakes', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{remainingTakes}</div>
        </div>
      </div>
    </div>
  );
};