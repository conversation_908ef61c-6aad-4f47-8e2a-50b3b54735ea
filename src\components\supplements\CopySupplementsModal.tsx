import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';
import { useStudyStore } from '../../store/studyStore';

interface CopySupplementsModalProps {
  onClose: () => void;
  onCopy: (elementIds: string[]) => void;
  elementId: string;
}

export const CopySupplementsModal: React.FC<CopySupplementsModalProps> = ({
  onClose,
  onCopy,
  elementId
}) => {
  const { t } = useTranslation(['supplements']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const [selectedElements, setSelectedElements] = useState<string[]>([]);

  // Filtrar los elementos excluyendo el elemento actual
  const otherElements = selectedStudy?.elements.filter(e => e.id !== elementId) || [];

  const handleSelectAll = () => {
    const otherElementIds = otherElements.map(e => e.id);
    onCopy(otherElementIds);
  };

  const handleCopySelected = () => {
    if (selectedElements.length > 0) {
      onCopy(selectedElements);
    }
  };

  const toggleElement = (elementId: string) => {
    setSelectedElements(prev =>
      prev.includes(elementId)
        ? prev.filter(id => id !== elementId)
        : [...prev, elementId]
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="p-4 bg-purple-600 text-white flex items-center justify-between rounded-t-lg">
          <h3 className="text-lg font-semibold">
            {t('copySupplements.title')}
          </h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-purple-700 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4">
          <p className="text-gray-600 mb-4">
            {t('copySupplements.description')}
          </p>

          <div className="space-y-2 max-h-[40vh] overflow-y-auto mb-4">
            {otherElements.map(element => (
              <label
                key={element.id}
                className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={selectedElements.includes(element.id)}
                  onChange={() => toggleElement(element.id)}
                  className="mr-3 rounded text-purple-600 focus:ring-purple-500"
                />
                <span>{element.description}</span>
              </label>
            ))}
          </div>

          <div className="flex justify-end space-x-4">
            <button
              onClick={handleSelectAll}
              className="px-4 py-2 text-purple-600 hover:bg-purple-50 rounded-lg"
            >
              {t('copySupplements.copyAll')}
            </button>
            <button
              onClick={handleCopySelected}
              disabled={selectedElements.length === 0}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              {t('copySupplements.copySelected')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};