# useElementNavigation Hook

## Descripción
Hook para gestionar la navegación entre elementos.

## Funcionalidades
```typescript
interface UseElementNavigationReturn {
  currentIndex: number;
  nextElement: () => void;
  previousElement: () => void;
  goToElement: (index: number) => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

function useElementNavigation(
  elements: WorkElement[],
  options?: {
    loop?: boolean;
    initialIndex?: number;
  }
): UseElementNavigationReturn;
```

### Características
- Navegación secuencial
- Navegación directa
- Modo circular opcional
- Validaciones de límites
- Estado de navegación

### Uso
```typescript
const {
  currentIndex,
  nextElement,
  previousElement
} = useElementNavigation(elements, {
  loop: true,
  initialIndex: 0
});
```