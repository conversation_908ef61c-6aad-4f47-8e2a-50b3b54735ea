import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../store/authStore';
import { TermsDialog } from './TermsDialog';
import { useInstallPrompt } from '../hooks/useInstallPrompt';
import { APP_VERSION } from '../config/version';

export const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [pendingAction, setPendingAction] = useState<() => Promise<void>>(() => async () => {});
  const { signIn, signUp, signInWithGoogle, isLoading, error } = useAuthStore();
  const navigate = useNavigate();
  const { t } = useTranslation(['login', 'common']);
  const { promptToInstall } = useInstallPrompt();

  // Estado local para el mensaje de error
  const [localError, setLocalError] = useState('');

  // Sincronizar el error del store con el estado local
  useEffect(() => {
    if (error) {
      console.log('Error del store:', error);
      if (error.includes('Invalid login credentials')) {
        setLocalError(t('login:error.invalidCredentials'));
      } else if (error.includes('email rate limit exceeded')) {
        setLocalError(t('login:error.rateLimit'));
      } else if (error.includes('User already registered')) {
        setLocalError(t('login:error.userAlreadyRegistered'));
      } else {
        setLocalError(t('login:error.generic'));
      }
    }
  }, [error, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault(); // Prevenir el comportamiento por defecto
    setLocalError('');
    
    const action = async () => {
      try {
        if (isSignUp) {
          await signUp(email, password);
          await signIn(email, password);
          navigate('/');
        } else {
          await signIn(email, password);
          navigate('/');
        }
      } catch (err) {
        console.log('Error en login:', err);
        // Los errores se manejan a través del store
      }
    };

    if (isSignUp) {
      setPendingAction(() => action);
      setShowTerms(true);
    } else {
      await action();
    }
  };

  const handleGoogleSignIn = async () => {
    setLocalError('');
    try {
      await signInWithGoogle();
      // No navegamos aquí, la redirección la maneja Supabase
    } catch (err) {
      console.log('Error en login con Google:', err);
      // Los errores se manejan a través del store
    }
  };

  const handleAcceptTerms = async () => {
    setShowTerms(false);
    await pendingAction();
  };

  const toggleSignUp = () => {
    setIsSignUp(!isSignUp);
    setLocalError('');
  };

  return (
    <div className="bg-gradient-to-br from-purple-600 via-purple-700 to-purple-900 min-h-screen w-full flex items-center justify-center p-4 md:p-8">
      <div className="bg-white rounded-2xl shadow-xl p-6 md:p-8 w-full max-w-[90%] md:max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Time Study</h1>
          <p className="text-gray-600">{t('login:subtitle')}</p>
        </div>

        {isSignUp ? (
          // Formulario de registro - Solo con Google
          <div className="space-y-4">
            <div className="text-center text-sm text-gray-600 mb-4">
              {t('login:registerWithGoogleOnly')}
            </div>
            
            <button
              onClick={handleGoogleSignIn}
              className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors"
              disabled={isLoading}
            >
              <img src="/google.svg" alt="Google" className="w-5 h-5 mr-2" />
              {t('login:continueWithGoogle')}
            </button>

            {localError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm mt-2">
                {localError}
              </div>
            )}
          </div>
        ) : (
          // Formulario de inicio de sesión - Original
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                {t('login:email')}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                {t('login:password')}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                required
              />
            </div>

            {localError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm mt-2">
                {localError}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? t('login:loading') : t('login:submit')}
            </button>

            <div className="mt-4 text-center text-sm text-gray-600">
              <button
                type="button"
                onClick={() => navigate('/reset-password')}
                className="text-purple-600 hover:text-purple-700"
              >
                {t('login:forgotPassword')}
              </button>
            </div>

            <div className="mt-4 flex items-center justify-center">
              <div className="border-t border-gray-300 flex-grow"></div>
              <span className="mx-4 text-gray-500 text-sm">{t('login:or')}</span>
              <div className="border-t border-gray-300 flex-grow"></div>
            </div>

            <button
              type="button"
              onClick={handleGoogleSignIn}
              className="w-full flex items-center justify-center bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors"
            >
              <img src="/google.svg" alt="Google" className="w-5 h-5 mr-2" />
              {t('login:continueWithGoogle')}
            </button>
          </form>
        )}

        <div className="mt-4 text-center text-sm text-gray-600">
          {isSignUp ? (
            <button
              type="button"
              onClick={toggleSignUp}
              className="text-purple-600 hover:text-purple-700"
            >
              {t('login:alreadyHaveAccount')}
            </button>
          ) : (
            <button
              type="button"
              onClick={toggleSignUp}
              className="text-purple-600 hover:text-purple-700"
            >
              {t('login:dontHaveAccount')}
            </button>
          )}
        </div>

        <TermsDialog
          open={showTerms}
          onClose={() => setShowTerms(false)}
          onAccept={handleAcceptTerms}
        />

        <div className="mt-8 text-center text-xs text-gray-500 space-y-2">
          <p 
            onClick={() => window.open('https://cronometras.com', '_blank')}
            className="cursor-pointer hover:text-purple-600"
          >
            Cronometras.com 
          </p>
          <p 
            onClick={promptToInstall}
            className="cursor-pointer hover:text-purple-600"
          >
            v. {APP_VERSION}
          </p>
          <div className="flex justify-center gap-4">
            <Link 
              to="/privacy-policy"
              className="text-gray-500 hover:text-purple-600"
            >
              {t('common:privacyPolicy')}
            </Link>
            <a 
              href="https://cronometras.com/cookies/"
              className="text-gray-500 hover:text-purple-600"
              target="_blank"
              rel="noopener noreferrer"
            >
              {t('common:cookiesPolicy')}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};