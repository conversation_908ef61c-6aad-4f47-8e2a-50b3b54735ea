import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';
import { useStudyStore } from '../store/studyStore';

interface AddToMethodModalProps {
  onClose: () => void;
  onSave: (data: { 
    isNewElement: boolean; 
    elementId?: string; 
    type: string; 
    repetitionType: string;
    frequencyRepetitions: number;
    frequencyCycles: number;
  }) => void;
  timeId: string;
}

export const AddToMethodModal: React.FC<AddToMethodModalProps> = ({
  onClose,
  onSave,
  timeId
}) => {
  const { t } = useTranslation(['method', 'common', 'cronoSeguido']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const [isNewElement, setIsNewElement] = useState(true);
  const [selectedElementId, setSelectedElementId] = useState('');
  const [type, setType] = useState('machine-stopped');
  const [repetitionType, setRepetitionType] = useState('repetitive');
  const [frequencyRepetitions, setFrequencyRepetitions] = useState(1);
  const [frequencyCycles, setFrequencyCycles] = useState(1);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleSave = () => {
    onSave({
      isNewElement,
      elementId: isNewElement ? undefined : selectedElementId,
      type,
      repetitionType,
      frequencyRepetitions,
      frequencyCycles
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div ref={modalRef} className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">{t('addSingleRecordToMethod', { ns: 'cronoSeguido' })}</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('elementType', { ns: 'cronoSeguido' })}
            </label>
            <div className="flex space-x-2">
              <button
                onClick={() => setIsNewElement(true)}
                className={`flex-1 px-3 py-2 rounded-lg ${
                  isNewElement
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {t('newElement', { ns: 'cronoSeguido' })}
              </button>
              <button
                onClick={() => setIsNewElement(false)}
                className={`flex-1 px-3 py-2 rounded-lg ${
                  !isNewElement
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {t('existingElement', { ns: 'cronoSeguido' })}
              </button>
            </div>
          </div>

          {isNewElement ? (
            <>
              <div>
                <label className="block text-sm font-medium mb-1">
                  {t('type', { ns: 'method' })}
                </label>
                <select
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                  className="w-full rounded-lg border-gray-300"
                >
                  <option value="machine-stopped">{t('typesLong.machine-stopped', { ns: 'method' })}</option>
                  <option value="machine-running">{t('typesLong.machine-running', { ns: 'method' })}</option>
                  <option value="machine-time">{t('typesLong.machine-time', { ns: 'method' })}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  {t('repetitionType', { ns: 'method' })}
                </label>
                <select
                  value={repetitionType}
                  onChange={(e) => setRepetitionType(e.target.value)}
                  className="w-full rounded-lg border-gray-300"
                >
                  <option value="repetitive">{t('types.repetitive', { ns: 'method' })}</option>
                  <option value="frequency">{t('types.frequency', { ns: 'method' })}</option>
                  <option value="machine">{t('types.machine', { ns: 'method' })}</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {t('repetitions', { ns: 'method' })}
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={frequencyRepetitions}
                    onChange={(e) => setFrequencyRepetitions(e.target.value ? parseInt(e.target.value) : 1)}
                    className="w-full rounded-lg border-gray-300"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    {t('cycles', { ns: 'method' })}
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={frequencyCycles}
                    onChange={(e) => setFrequencyCycles(e.target.value ? parseInt(e.target.value) : 1)}
                    className="w-full rounded-lg border-gray-300"
                  />
                </div>
              </div>
            </>
          ) : (
            <div>
              <label className="block text-sm font-medium mb-1">
                {t('selectElement', { ns: 'cronoSeguido' })}
              </label>
              <select
                value={selectedElementId}
                onChange={(e) => setSelectedElementId(e.target.value)}
                className="w-full rounded-lg border-gray-300"
              >
                <option value="">{t('select', { ns: 'common' })}</option>
                {selectedStudy?.elements.map((element) => (
                  <option key={element.id} value={element.id}>
                    {element.description}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            {t('cancel', { ns: 'common' })}
          </button>
          <button
            onClick={handleSave}
            disabled={!isNewElement && !selectedElementId}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            {t('save', { ns: 'common' })}
          </button>
        </div>
      </div>
    </div>
  );
};