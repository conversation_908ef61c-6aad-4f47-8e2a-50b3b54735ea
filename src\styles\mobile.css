/* ================================
   OPTIMIZACIÓN MÓVIL - CRONOMETRAS
   ================================ */

/* === UTILIDADES DE ESPACIADO MÓVIL === */
.mobile-container {
  @apply px-3 sm:px-4 lg:px-6;
}

.mobile-padding {
  @apply p-3 sm:p-4 lg:p-6;
}

.mobile-padding-lg {
  @apply p-4 sm:p-6 lg:p-8;
}

.mobile-margin {
  @apply m-3 sm:m-4 lg:m-6;
}

.mobile-gap {
  @apply gap-3 sm:gap-4 lg:gap-6;
}

.mobile-gap-lg {
  @apply gap-4 sm:gap-6 lg:gap-8;
}

.mobile-space-y {
  @apply space-y-4 sm:space-y-6 lg:space-y-8;
}

.mobile-space-y-lg {
  @apply space-y-6 sm:space-y-8 lg:space-y-12;
}

/* === TIPOGRAFÍA RESPONSIVE === */
.mobile-text-xs {
  @apply text-xs sm:text-sm;
}

.mobile-text-sm {
  @apply text-sm sm:text-base;
}

.mobile-text-base {
  @apply text-base sm:text-lg;
}

.mobile-text-lg {
  @apply text-lg sm:text-xl;
}

.mobile-text-xl {
  @apply text-xl sm:text-2xl;
}

.mobile-text-2xl {
  @apply text-xl sm:text-2xl lg:text-3xl;
}

/* === TARJETAS MÓVILES === */
.mobile-card {
  @apply rounded-lg sm:rounded-xl shadow-lg border border-gray-100 p-4 sm:p-6 lg:p-8;
  @apply transition-all duration-300 hover:shadow-xl;
}

.mobile-card-compact {
  @apply rounded-lg sm:rounded-xl shadow-md border border-gray-100 p-3 sm:p-4 lg:p-6;
  @apply transition-all duration-200;
}

.mobile-card-header {
  @apply flex items-center mb-4 sm:mb-6;
}

.mobile-card-icon {
  @apply p-2 sm:p-3 mr-3 sm:mr-4 flex-shrink-0 rounded-full;
}

.mobile-card-title {
  @apply min-w-0 flex-1;
}

/* === GESTOS TÁCTILES === */
.mobile-touch-target {
  @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
}

.mobile-button {
  @apply px-4 py-3 sm:px-6 sm:py-3 rounded-lg font-medium transition-all duration-200;
  @apply active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.mobile-button-primary {
  @apply mobile-button bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.mobile-button-secondary {
  @apply mobile-button bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.mobile-button-danger {
  @apply mobile-button bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.mobile-button-full {
  @apply w-full sm:w-auto;
}

/* === INPUTS MÓVILES === */
.mobile-input {
  @apply w-full rounded-lg border border-gray-300 p-3 sm:p-2 text-base sm:text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply transition-colors duration-200;
}

.mobile-select {
  @apply mobile-input appearance-none bg-white bg-no-repeat;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* === NAVEGACIÓN MÓVIL === */
.mobile-nav-item {
  @apply block px-4 py-3 text-base font-medium rounded-lg;
  @apply transition-colors duration-200 mobile-touch-target;
}

/* === GRIDS RESPONSIVOS === */
.mobile-grid-1 {
  @apply grid grid-cols-1;
}

.mobile-grid-2 {
  @apply grid grid-cols-1 sm:grid-cols-2;
}

.mobile-grid-3 {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
}

.mobile-grid-auto {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

/* === FLEXBOX MÓVIL === */
.mobile-flex-col {
  @apply flex flex-col sm:flex-row;
}

.mobile-flex-wrap {
  @apply flex flex-wrap gap-2 sm:gap-3 lg:gap-4;
}

/* === EFECTOS DE HOVER ESPECÍFICOS MÓVIL === */
@media (hover: hover) {
  .mobile-hover-lift:hover {
    @apply transform scale-[1.02] shadow-xl;
  }
  
  .mobile-hover-glow:hover {
    @apply shadow-lg shadow-blue-500/25;
  }
}

/* Desactivar hover en dispositivos táctiles */
@media (hover: none) {
  .mobile-hover-lift:hover {
    @apply transform-none shadow-lg;
  }
  
  .mobile-hover-glow:hover {
    @apply shadow-lg;
  }
}

/* === ESTADOS TÁCTILES === */
.mobile-active:active {
  @apply transform scale-95 transition-transform duration-100;
}

.mobile-ripple {
  @apply relative overflow-hidden;
}

.mobile-ripple::before {
  content: '';
  @apply absolute inset-0 bg-white opacity-0 pointer-events-none;
  @apply transition-opacity duration-300;
  border-radius: inherit;
}

.mobile-ripple:active::before {
  @apply opacity-20;
}

/* === CONTENEDOR DE BOTONES DE NAVEGACIÓN === */
.nav-buttons-container {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
  display: flex;
  justify-content: center;
  width: 100%;
}

.nav-buttons-container::-webkit-scrollbar {
  height: 4px;
}

.nav-buttons-container::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

.nav-buttons-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.nav-buttons-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* === OVERLAY MÓVIL === */
.mobile-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40;
}

/* === MODALES MÓVILES === */
.mobile-modal {
  @apply fixed inset-4 sm:inset-8 lg:inset-16 bg-white rounded-xl shadow-2xl z-50;
  @apply overflow-y-auto max-h-[90vh];
}

.mobile-modal-sm {
  @apply mobile-modal max-w-sm mx-auto;
}

.mobile-modal-lg {
  @apply mobile-modal max-w-4xl mx-auto;
}

/* === ÁREAS DE SCROLL === */
.mobile-scroll-area {
  @apply overflow-y-auto overflow-x-hidden;
  -webkit-overflow-scrolling: touch;
}

.mobile-scroll-hidden {
  @apply overflow-hidden;
}

.mobile-scroll-hidden::-webkit-scrollbar {
  @apply hidden;
}

/* === ANIMACIONES MÓVILES === */
@keyframes mobile-slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes mobile-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes mobile-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.mobile-animate-slide-up {
  animation: mobile-slide-up 0.3s ease-out;
}

.mobile-animate-slide-down {
  animation: mobile-slide-down 0.3s ease-out;
}

.mobile-animate-fade-in {
  animation: mobile-fade-in 0.2s ease-out;
}

/* === ACCESIBILIDAD MÓVIL === */
.mobile-focus-visible {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.mobile-sr-only {
  @apply sr-only;
}

/* === MEDIA QUERIES ESPECÍFICAS === */
@media (max-width: 360px) {
  .mobile-card {
    @apply p-3;
  }
  
  .mobile-text-xl {
    @apply text-lg;
  }
  
  .mobile-text-2xl {
    @apply text-xl;
  }
}

@media (max-width: 480px) {
  .mobile-grid-auto {
    @apply grid-cols-1;
  }
  
  .mobile-modal {
    @apply inset-2;
  }
}

@media (orientation: landscape) and (max-height: 480px) {
  .mobile-modal {
    @apply max-h-[85vh];
  }
  
  .mobile-card {
    @apply p-3;
  }
}

@media (prefers-color-scheme: dark) {
  .mobile-card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .mobile-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }
  
  .mobile-overlay {
    @apply bg-opacity-60;
  }
}