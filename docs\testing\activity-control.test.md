# Pruebas de Control de Actividad

## Descripción
Suite de pruebas para el control de actividad.

## Casos de Prueba

### 1. Control de Rangos
```typescript
describe('Activity Control', () => {
  test('should respect activity limits', () => {
    const { result } = renderHook(() => 
      useActivityControl(100, 133)
    );
    
    act(() => {
      result.current.setActivity(150);
    });
    
    expect(result.current.activity).toBe(133);
  });
});
```

### 2. Incrementos/Decrementos
```typescript
test('should increment/decrement by steps', () => {
  const { result } = renderHook(() => 
    useActivityControl(100, 133)
  );
  
  const initialActivity = result.current.activity;
  
  act(() => {
    result.current.increaseActivity();
  });
  
  expect(result.current.activity).toBe(initialActivity + 5);
});
```

### 3. Validaciones
```typescript
test('should validate activity values', () => {
  const { result } = renderHook(() => 
    useActivityControl(100, 133)
  );
  
  expect(result.current.isValidActivity(90)).toBe(true);
  expect(result.current.isValidActivity(140)).toBe(false);
});
```