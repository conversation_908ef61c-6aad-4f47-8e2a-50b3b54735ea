import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, RotateCcw, Info } from 'lucide-react';
import { useSupplementTablesStore } from '../../store/supplementTablesStore';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";

interface Selection {
  temperature?: number;
  humidity?: number;
}

interface FactorC1ModalProps {
  onClose: () => void;
  onSelect: (value: number, selection?: Selection) => void;
  onReset: () => void;
  currentValue?: number;
  currentSelection?: Selection;
}

export const FactorC1Modal: React.FC<FactorC1ModalProps> = ({
  onClose,
  onSelect,
  onReset,
  currentValue,
  currentSelection
}) => {
  const { t } = useTranslation(['supplements', 'common']);
  const tables = useSupplementTablesStore(state => state.tables);
  const modalRef = useRef<HTMLDivElement>(null);

  const [selectedTemperature, setSelectedTemperature] = useState<string>(
    currentSelection?.temperature?.toString() || ''
  );
  const [selectedHumidity, setSelectedHumidity] = useState<string>(
    currentSelection?.humidity?.toString() || ''
  );

  const humidityLevels = t('supplements:tal.C1.humidityValues', { returnObjects: true }) as string[];
  const temperatures = ["23", "24", "25", "26", "27", "28", "29", "30", "31", "32"];

  // Update selected values when currentSelection changes
  useEffect(() => {
    if (currentSelection?.temperature !== undefined) {
      setSelectedTemperature(currentSelection.temperature.toString());
    }
    if (currentSelection?.humidity !== undefined) {
      setSelectedHumidity(currentSelection.humidity.toString());
    }
  }, [currentSelection]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        if (onClose) {
          onClose();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const getPoints = (temp: string, humidity: string) => {
    // Matriz de puntos para temperatura y humedad
    const temperaturePoints = {
      "23": [0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3],
      "24": [6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8],
      "25": [6, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
      "26": [6, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
      "27": [7, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
      "28": [7, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
      "29": [8, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
      "30": [9, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
      "31": [9, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12],
      "32": [9, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12]
    };

    const humidityIndex = humidityLevels.indexOf(humidity);
    if (humidityIndex === -1) return 0;
    
    const points = temperaturePoints[temp];
    if (!points) return 0;
    
    return points[humidityIndex];
  };

  const handleApply = () => {
    if (selectedTemperature && selectedHumidity) {
      const points = getPoints(selectedTemperature, selectedHumidity);
      onSelect(points, {
        temperature: parseInt(selectedTemperature),
        humidity: parseInt(selectedHumidity)
      });
      if (onClose) {
        onClose();
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div ref={modalRef} className="bg-white rounded-lg w-full max-w-4xl">
        <div className="p-4 bg-red-500 text-white flex items-center justify-between rounded-t-lg">
          <div className="flex items-center gap-2">
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild onClick={(e) => e.preventDefault()}>
                  <button 
                    className="p-1 hover:bg-red-600 rounded-full transition-colors"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <Info className="h-5 w-5 text-white" />
                  </button>
                </TooltipTrigger>
                <TooltipContent 
                  side="top"
                  align="center"
                  className="max-w-sm bg-white text-gray-900 p-3 rounded shadow-lg border border-gray-200"
                  sideOffset={5}
                >
                  <p className="text-sm whitespace-pre-line leading-relaxed">
                    {t('tal.C1.tooltips', { ns: 'supplements' })}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <div className="flex items-center gap-1 truncate">
              <h3 className="text-lg font-semibold truncate">
                {t('supplements:tal.C1.title')}
              </h3>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onReset}
              className="p-2 hover:bg-red-600 rounded-full"
              title={t('reset', { ns: 'common' })}
            >
              <RotateCcw className="w-5 h-5" />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-red-600 rounded-full"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="p-4 grid grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-2">{t('tal.C1.temperatureLabel', { ns: 'supplements' })}</h4>
            <div className="space-y-2 max-h-[60vh] overflow-y-auto pr-2">
              {temperatures.map((temp) => (
                <button
                  key={temp}
                  onClick={() => setSelectedTemperature(temp)}
                  className={`w-full p-4 text-left rounded-lg border ${
                    selectedTemperature === temp
                      ? 'bg-red-100 border-gray-200'
                      : 'hover:bg-red-50/50 border-gray-200'
                  }`}
                >
                  <div className="font-medium">{temp}°C</div>
                </button>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">{t('tal.C1.humidityLabel', { ns: 'supplements' })}</h4>
            <div className="space-y-2 max-h-[60vh] overflow-y-auto pr-2">
              {humidityLevels.map((humidity) => (
                <button
                  key={humidity}
                  onClick={() => setSelectedHumidity(humidity)}
                  className={`w-full p-4 text-left rounded-lg border ${
                    selectedHumidity && parseInt(selectedHumidity) === parseInt(humidity)
                      ? 'bg-red-100 border-gray-200'
                      : 'hover:bg-red-50/50 border-gray-200'
                  }`}
                >
                  <div className="font-medium">{humidity}</div>
                  {selectedTemperature && (
                    <div className="text-sm text-red-500 font-medium">
                      {t('points', { ns: 'supplements' })}: {getPoints(selectedTemperature, humidity)}
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-between items-center">
            <div>
              <div className="text-sm text-gray-600">{t('points', { ns: 'supplements' })}</div>
              <div className="text-lg font-bold text-red-500">
                {selectedTemperature && selectedHumidity
                  ? getPoints(selectedTemperature, selectedHumidity)
                  : 0}
              </div>
            </div>
            <button
              onClick={handleApply}
              disabled={!selectedTemperature || !selectedHumidity}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
            >
              {t('apply', { ns: 'common' })}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};