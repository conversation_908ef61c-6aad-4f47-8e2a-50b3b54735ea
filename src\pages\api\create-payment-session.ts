import { NextApiRequest, NextApiResponse } from 'next';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, credits, price } = req.body;

    if (!userId || !credits || !price) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: {
          userId: !userId ? 'User ID is required' : null,
          credits: !credits ? 'Credits amount is required' : null,
          price: !price ? 'Price is required' : null,
        }
      });
    }

    // Obtener información del usuario
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email, name')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Crear o recuperar el cliente de Stripe
    let customer: Stripe.Customer;
    const { data: existingCustomer } = await stripe.customers.search({
      query: `email:'${userData.email}'`,
    });

    if (existingCustomer && existingCustomer.length > 0) {
      customer = existingCustomer[0];
    } else {
      customer = await stripe.customers.create({
        email: userData.email,
        name: userData.name || undefined,
        metadata: {
          userId,
        },
      });
    }

    // Crear la sesión de pago
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: `${credits} Créditos`,
              description: `Paquete de ${credits} créditos para Cronometras`,
              images: [
                `${process.env.NEXT_PUBLIC_APP_URL}/images/credits-package.png`
              ],
              metadata: {
                credits: credits.toString(),
              },
            },
            unit_amount: price * 100, // Convertir a centavos
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=success`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?payment=cancelled`,
      metadata: {
        userId,
        credits: credits.toString(),
      },
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      customer_email: userData.email,
      locale: 'es',
      payment_intent_data: {
        metadata: {
          userId,
          credits: credits.toString(),
        },
      },
    });

    return res.status(200).json({
      sessionId: session.id,
      url: session.url,
    });
  } catch (error: any) {
    console.error('Error creating payment session:', error);
    return res.status(500).json({
      error: 'Error creating payment session',
      details: error.message
    });
  }
}
