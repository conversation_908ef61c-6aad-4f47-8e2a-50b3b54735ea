-- Add crono_seguido_records column to studies table
ALTER TABLE public.studies
ADD COLUMN IF NOT EXISTS crono_seguido_records JSONB DEFAULT '[]'::jsonb;

-- Add comment to explain the structure of the JSONB column
COMMENT ON COLUMN public.studies.crono_seguido_records IS 'Array of crono seguido records. Each record includes: id, time, activity, description, timestamp, addedToMethod. Example: [{"id": "uuid", "time": 7.554, "activity": 100, "description": "Some action", "timestamp": "2024-01-03T20:10:12.691Z", "addedToMethod": false}]';

-- Update existing RLS policies to include the new column
ALTER POLICY "Users can update their own studies"
    ON public.studies
    USING (auth.uid() = user_id);

-- Ensure the new column is included in the select policy
ALTER POLICY "Users can view their own studies"
    ON public.studies
    USING (auth.uid() = user_id);