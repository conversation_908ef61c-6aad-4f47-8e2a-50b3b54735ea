import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { ElementInstance } from '../types';
import { useStudyStore } from './studyStore';

interface MethodState {
  elements: ElementInstance[];
  isLoading: boolean;
  error: string | null;
  fetchElements: () => Promise<void>;
  createElement: (studyId: string, element: Partial<ElementInstance>) => Promise<void>;
  updateElement: (elementId: string, element: Partial<ElementInstance>) => Promise<void>;
  deleteElement: (elementId: string) => Promise<void>;
  reorderElements: (elements: ElementInstance[]) => Promise<void>;
}

export const useMethodStore = create<MethodState>((set, get) => ({
  elements: [],
  isLoading: false,
  error: null,

  fetchElements: async () => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      set({ 
        elements: selectedStudy.elements || [], 
        isLoading: false 
      });
    } catch (error) {
      console.error('Error fetching elements:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  createElement: async (studyId, element) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      // Generar un nuevo ID para el elemento
      const newElementId = crypto.randomUUID();

      // Crear el nuevo elemento base
      const newElement: ElementInstance = {
        id: newElementId,
        study_id: studyId,
        description: element.description || '',
        type: element.type || 'machine-stopped',
        frequency_repetitions: element.frequency_repetitions || 1,
        frequency_cycles: element.frequency_cycles || 1,
        repetition_type: element.repetition_type || 'repetitive',
        position: selectedStudy.elements.length,
        // Estos campos no se guardarán directamente en el elemento
        time_records: [],
        supplements: {
          points: {},
          percentage: 0,
          is_forced: false,
          factor_selections: {}
        }
      };

      // Crear elemento "limpio" sin time_records y supplements
      const cleanElement = {
        ...newElement,
        time_records: undefined,
        supplements: undefined
      };

      // Añadir a la lista de elementos
      const updatedElements = [...selectedStudy.elements, cleanElement];

      // Preparar los registros de tiempo
      let newTimeRecords = {
        ...selectedStudy.time_records,
        [newElementId]: []
      };

      // Preparar los suplementos
      let newSupplements = {
        ...selectedStudy.supplements,
        [newElementId]: newElement.supplements
      };

      // Si el elemento viene de la biblioteca, integrar los datos originales
      if (element._isFromLibrary) {
        // Procesar registros de tiempo si existen
        if (element._timeRecords && element._timeRecords.length > 0) {
          // Asignar nuevos IDs y vincular al nuevo elemento
          newTimeRecords[newElementId] = element._timeRecords.map((record: any) => ({
            ...record,
            id: crypto.randomUUID(),
            elementId: newElementId
          }));
        }

        // Procesar suplementos si existen
        if (element._supplements && element._supplements.length > 0) {
          const supplData = element._supplements[0];
          newSupplements[newElementId] = {
            percentage: supplData.percentage || 0,
            is_forced: supplData.is_forced || false,
            points: supplData.points || {},
            factor_selections: supplData.factor_selections || {}
          };
        }
      }

      // Actualizar en la base de datos
      const { error } = await supabase
        .from('studies')
        .update({ 
          elements: updatedElements,
          time_records: newTimeRecords,
          supplements: newSupplements
        })
        .eq('id', studyId);

      if (error) throw error;

      // Actualizar ambos stores
      set(state => ({
        elements: updatedElements,
        isLoading: false
      }));

      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          elements: updatedElements,
          time_records: newTimeRecords,
          supplements: newSupplements
        } : null
      }));

    } catch (error) {
      console.error('Error creating element:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  updateElement: async (elementId, element) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      const updatedElements = selectedStudy.elements.map(e => 
        e.id === elementId ? { ...e, ...element } : e
      );

      const { error } = await supabase
        .from('studies')
        .update({ elements: updatedElements })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update both stores
      set({ elements: updatedElements, isLoading: false });

      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          elements: updatedElements
        } : null
      }));

    } catch (error) {
      console.error('Error updating element:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  deleteElement: async (elementId) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      const updatedElements = selectedStudy.elements.filter(e => e.id !== elementId);

      // Remove element from time_records and supplements
      const { [elementId]: _, ...updatedTimeRecords } = selectedStudy.time_records;
      const { [elementId]: __, ...updatedSupplements } = selectedStudy.supplements;

      // Update crono_seguido_records to unmark records associated with this element
      const updatedCronoSeguido = selectedStudy.crono_seguido_records.map(record => 
        record.elementId === elementId 
          ? { ...record, addedToMethod: false, elementId: undefined }
          : record
      );

      const { error } = await supabase
        .from('studies')
        .update({ 
          elements: updatedElements,
          time_records: updatedTimeRecords,
          supplements: updatedSupplements,
          crono_seguido_records: updatedCronoSeguido
        })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update the study in the store
      useStudyStore.setState({
        selectedStudy: {
          ...selectedStudy,
          elements: updatedElements,
          time_records: updatedTimeRecords,
          supplements: updatedSupplements,
          crono_seguido_records: updatedCronoSeguido
        }
      });

      set(state => ({
        elements: state.elements.filter(e => e.id !== elementId),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error deleting element:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  },

  reorderElements: async (elements) => {
    set({ isLoading: true, error: null });
    try {
      const selectedStudy = useStudyStore.getState().selectedStudy;
      if (!selectedStudy) throw new Error('No study selected');

      const { error } = await supabase
        .from('studies')
        .update({ elements })
        .eq('id', selectedStudy.id);

      if (error) throw error;

      // Update both stores
      set({ elements, isLoading: false });

      useStudyStore.setState(state => ({
        selectedStudy: state.selectedStudy ? {
          ...state.selectedStudy,
          elements
        } : null
      }));

    } catch (error) {
      console.error('Error reordering elements:', error);
      set({ error: (error as Error).message, isLoading: false });
      throw error;
    }
  }
}));