# Pruebas del Cronómetro

## Descripción
Suite de pruebas para la funcionalidad del cronómetro.

## Casos de Prueba

### 1. Control Básico
```typescript
describe('Chronometer Controls', () => {
  test('should start counting from zero', () => {
    // Setup
    const { result } = renderHook(() => useChronometer());
    
    // Action
    act(() => {
      result.current.start();
    });
    
    // Assert
    expect(result.current.time).toBeGreaterThan(0);
    expect(result.current.isRunning).toBe(true);
  });
});
```

### 2. Precisión
```typescript
test('should maintain timing precision', () => {
  jest.useFakeTimers();
  
  const { result } = renderHook(() => useChronometer({
    precision: 10
  }));
  
  act(() => {
    result.current.start();
    jest.advanceTimersByTime(1000);
  });
  
  expect(result.current.time).toBe(1000);
});
```

### 3. Pausa/Reanudación
```typescript
test('should pause and resume correctly', () => {
  const { result } = renderHook(() => useChronometer());
  
  act(() => {
    result.current.start();
    result.current.pause();
  });
  
  const pausedTime = result.current.time;
  
  act(() => {
    jest.advanceTimersByTime(1000);
  });
  
  expect(result.current.time).toBe(pausedTime);
});
```