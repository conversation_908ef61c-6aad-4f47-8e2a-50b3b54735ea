# Enable rewrite engine
RewriteEngine On

# Handle MIME types correctly
AddType application/javascript .js
AddType text/css .css
AddType application/json .json

# Enable CORS
Header set Access-Control-Allow-Origin "*"

# Enable compression for better performance
AddOutputFilterByType DEFLATE text/plain
AddOutputFilterByType DEFLATE text/html
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE application/javascript
AddOutputFilterByType DEFLATE application/json

# Cache control
<FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|woff2)$">
    Header set Cache-Control "max-age=********, public"
</FilesMatch>

# SPA routing - redirect all requests to index.html except for actual files and directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^ index.html [L]
