# Documentación de Seguridad de Base de Datos

Este documento describe las políticas de seguridad y funciones implementadas en la base de datos de Cronometras para proteger los datos de los usuarios y garantizar el acceso adecuado a los recursos.

## Políticas de Row Level Security (RLS)

Supabase utiliza las políticas de Row Level Security (RLS) de PostgreSQL para restringir el acceso a los datos a nivel de fila. Estas políticas se aplican automáticamente a todas las consultas realizadas a través de la API de Supabase.

### Políticas para la tabla `studies`

#### 1. Política de visualización de estudios

```sql
CREATE POLICY "Users can view shared organization studies v3"
    ON studies FOR SELECT
    TO authenticated
    USING (
        -- El usuario puede ver sus propios estudios
        auth.uid() = user_id
        OR (
            -- El usuario puede ver estudios compartidos de organizaciones a las que pertenece
            is_shared = true
            AND organization_id IS NOT NULL
            AND is_organization_member(organization_id, auth.uid())
        )
    );
```

**Propósito**: Esta política garantiza que los usuarios solo puedan ver:
- Sus propios estudios (donde `user_id` coincide con su ID de autenticación)
- Estudios compartidos de organizaciones a las que pertenecen actualmente

**Seguridad**: La función `is_organization_member` verifica la pertenencia actual a la organización, lo que impide que los miembros expulsados sigan viendo los estudios compartidos.

#### 2. Política de actualización de estudios

```sql
CREATE POLICY "Users can update their own studies"
    ON studies FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);
```

**Propósito**: Permite a los usuarios actualizar únicamente sus propios estudios.

**Seguridad**: Tanto la cláusula `USING` como `WITH CHECK` verifican que el usuario autenticado sea el propietario del estudio.

#### 3. Política de eliminación de estudios

```sql
CREATE POLICY "Users can delete their own studies"
    ON studies FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);
```

**Propósito**: Permite a los usuarios eliminar únicamente sus propios estudios.

**Seguridad**: La cláusula `USING` verifica que el usuario autenticado sea el propietario del estudio.

#### 4. Política de inserción de estudios

```sql
CREATE POLICY "Users can insert studies"
    ON studies FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);
```

**Propósito**: Permite a los usuarios insertar nuevos estudios, pero solo si se establecen como propietarios.

**Seguridad**: La cláusula `WITH CHECK` verifica que el usuario autenticado sea establecido como el propietario del estudio.

## Funciones de Seguridad

### 1. RPC para limpieza de estudios huérfanos

```sql
CREATE OR REPLACE FUNCTION rpc_clean_orphaned_studies(org_id uuid)
RETURNS int
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
-- Implementación que encuentra y actualiza directamente los estudios huérfanos
$$;
```

**Propósito**: Proporciona un punto de entrada seguro para la API para limpiar estudios huérfanos. Esta función identifica y actualiza los estudios "huérfanos" - estudios que pertenecen a usuarios que ya no son miembros de la organización.

**Seguridad**:
- Verifica que el usuario que llama sea propietario de la organización antes de permitir la limpieza.
- `SECURITY DEFINER`: Se ejecuta con privilegios elevados.
- `SET search_path = public`: Previene ataques de confusión de dependencias.
- Implementa directamente la lógica para encontrar y actualizar estudios huérfanos, asegurando que devuelva un único valor entero.

### 2. Trigger para limpieza automática

```sql
CREATE OR REPLACE FUNCTION clean_member_studies_on_removal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
-- Implementación...
$$;

CREATE TRIGGER clean_studies_on_member_removal
AFTER DELETE ON organization_members
FOR EACH ROW
EXECUTE FUNCTION clean_member_studies_on_removal();
```

**Propósito**: Automatiza la limpieza de estudios cuando un miembro es eliminado de una organización.

**Seguridad**:
- Se ejecuta automáticamente después de cada eliminación en la tabla `organization_members`.
- `SECURITY DEFINER`: Se ejecuta con privilegios elevados para garantizar que pueda actualizar los estudios.
- `SET search_path = public`: Previene ataques de confusión de dependencias.
- Solo actualiza los estudios del miembro específico que fue eliminado.

## Mejores Prácticas Implementadas

1. **Principio de menor privilegio**: Los usuarios solo pueden acceder a los datos que necesitan.
2. **Verificación de propiedad**: Todas las operaciones verifican que el usuario sea el propietario del recurso.
3. **Funciones SECURITY DEFINER**: Utilizadas apropiadamente con search_path explícito.
4. **Automatización de seguridad**: El trigger garantiza que la limpieza ocurra automáticamente sin depender de la aplicación.
5. **Validación de roles**: La función RPC verifica que el usuario tenga el rol adecuado antes de permitir operaciones sensibles.

## Consideraciones Adicionales

- Las políticas RLS se aplican a todas las consultas realizadas a través de la API de Supabase, pero no a las consultas SQL directas realizadas por administradores de la base de datos.
- Es importante mantener actualizadas estas políticas cuando se realizan cambios en el esquema de la base de datos.
- Se recomienda realizar auditorías periódicas para verificar que las políticas funcionen según lo esperado.
- El script de migración está diseñado para ser idempotente, lo que significa que puede ejecutarse múltiples veces sin causar errores. Esto se logra eliminando las políticas y funciones existentes antes de crearlas nuevamente.
