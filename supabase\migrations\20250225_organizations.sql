-- Create organizations table
create table organizations (
    id uuid default uuid_generate_v4() primary key,
    name text not null,
    description text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    created_by uuid references auth.users(id) on delete set null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create organization_members table
create table organization_members (
    organization_id uuid references organizations(id) on delete cascade,
    user_id uuid references auth.users(id) on delete cascade,
    role text not null check (role in ('owner', 'admin', 'member')),
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    primary key (organization_id, user_id)
);

-- Add organization fields to studies table
alter table studies 
    add column organization_id uuid references organizations(id) on delete set null,
    add column is_shared boolean default false;

-- <PERSON>reate updated_at trigger function
create or replace function update_updated_at_column()
returns trigger as $$
begin
    new.updated_at = now();
    return new;
end;
$$ language plpgsql;

-- Add updated_at trigger to organizations
create trigger update_organizations_updated_at
    before update on organizations
    for each row
    execute function update_updated_at_column();

-- RLS Policies for organizations

-- Enable RLS
alter table organizations enable row level security;
alter table organization_members enable row level security;

-- Organizations policies
create policy "Users can create organizations"
    on organizations for insert
    to authenticated
    with check (auth.uid() = created_by);

create policy "Organization members can view their organizations"
    on organizations for select
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organizations.id
            and user_id = auth.uid()
        )
        or auth.uid() = created_by
    );

create policy "Organization owners and admins can update their organizations"
    on organizations for update
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organizations.id
            and user_id = auth.uid()
            and role in ('owner', 'admin')
        )
        or auth.uid() = created_by
    )
    with check (
        exists (
            select 1 from organization_members
            where organization_id = organizations.id
            and user_id = auth.uid()
            and role in ('owner', 'admin')
        )
        or auth.uid() = created_by
    );

create policy "Organization owners can delete their organizations"
    on organizations for delete
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organizations.id
            and user_id = auth.uid()
            and role = 'owner'
        )
        or auth.uid() = created_by
    );

-- Organization members policies
create policy "Organization owners and admins can manage members"
    on organization_members for all
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organization_members.organization_id
            and user_id = auth.uid()
            and role in ('owner', 'admin')
        )
        or exists (
            select 1 from organizations
            where id = organization_members.organization_id
            and created_by = auth.uid()
        )
    );

create policy "Users can view members of their organizations"
    on organization_members for select
    to authenticated
    using (
        exists (
            select 1 from organization_members om
            where om.organization_id = organization_members.organization_id
            and om.user_id = auth.uid()
        )
    );

-- Update studies policies to handle organization sharing
create policy "Users can view shared organization studies"
    on studies for select
    to authenticated
    using (
        auth.uid() = user_id
        or (
            is_shared = true
            and exists (
                select 1 from organization_members
                where organization_id = studies.organization_id
                and user_id = auth.uid()
            )
        )
    );

-- Create indexes for better performance
create index idx_organization_members_user_id on organization_members(user_id);
create index idx_organization_members_org_id on organization_members(organization_id);
create index idx_studies_organization_id on studies(organization_id) where organization_id is not null;
create index idx_studies_is_shared on studies(is_shared) where is_shared = true;

-- Function to add a member to an organization
create or replace function add_organization_member(
    org_id uuid,
    member_email text,
    member_role text
)
returns void
language plpgsql
security definer
as $$
declare
    member_user_id uuid;
begin
    -- Get the user ID from the email
    select id into member_user_id
    from auth.users
    where email = member_email;

    if member_user_id is null then
        raise exception 'User with email % not found', member_email;
    end if;

    -- Insert the member
    insert into organization_members (organization_id, user_id, role)
    values (org_id, member_user_id, member_role)
    on conflict (organization_id, user_id) 
    do update set role = member_role;
end;
$$;
