import { useState, useEffect } from 'react';
import { Factor } from '../types';
import oitFactorsData from '../config/supplements/oit-factors.json';
import talFactorsData from '../config/supplements/tal-factors.json';

export const useSupplementConfig = () => {
  const [oitFactors, setOitFactors] = useState<Factor[]>([]);
  const [talFactors, setTalFactors] = useState<Factor[]>([]);

  useEffect(() => {
    // Aquí podrías hacer una llamada a una API en el futuro
    // Por ahora, cargamos desde los JSON importados
    setOitFactors(oitFactorsData.factors as Factor[]);
    setTalFactors(talFactorsData.factors as Factor[]);
  }, []);

  return { oitFactors, talFactors };
}; 