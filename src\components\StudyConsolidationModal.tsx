import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from './ui/button';
import { X, ArrowUp, ArrowDown, Combine, AlertCircle, ChevronDown, ChevronRight } from 'lucide-react';
import { Study, ElementInstance, StudyWithFolder } from '../types/index';
import { getFolderStudiesRecursive } from '../utils/folderUtils';
import { useStudyStore } from '../store/studyStore';
import { supabase } from '../lib/supabase';

interface StudyConsolidationModalProps {
  isOpen: boolean;
  folderName?: string;
  folderId?: string;
  onClose: () => void;
  onStudyCreated?: () => void;
}

interface StudyForConsolidation extends Study {
  elementCount: number;
  isExpanded?: boolean;
  folder_id?: string;
}

export const StudyConsolidationModal: React.FC<StudyConsolidationModalProps> = ({
  isOpen,
  folderName,
  folderId,
  onClose,
  onStudyCreated
}) => {
  const { t } = useTranslation(['common', 'study']);
  const { createStudy, fetchStudies } = useStudyStore();
  
  const [studies, setStudies] = useState<StudyForConsolidation[]>([]);
  const [loading, setLoading] = useState(true);
  const [consolidating, setConsolidating] = useState(false);
  const [consolidatedName, setConsolidatedName] = useState('');
  const [includeSubfolders, setIncludeSubfolders] = useState(true);

  // Cargar estudios cuando se abre el modal
  useEffect(() => {
    if (isOpen && folderId) {
      loadStudies();
    }
  }, [isOpen, folderId, includeSubfolders]);

  // Establecer nombre por defecto
  useEffect(() => {
    if (folderName && !consolidatedName) {
      setConsolidatedName(`${folderName} - Consolidado`);
    }
  }, [folderName]);

  const loadStudies = async () => {
    if (!folderId) return;
    
    setLoading(true);
    try {
      let studiesData: StudyWithFolder[] = [];
      
      if (includeSubfolders) {
        studiesData = await getFolderStudiesRecursive(folderId);
              } else {
          // Solo estudios de la carpeta actual
          const { data } = await supabase
            .from('studies')
            .select('*')
            .eq('folder_id', folderId);
          studiesData = (data || []) as any[];
        }

      // Procesar estudios para añadir información adicional
      const processedStudies: StudyForConsolidation[] = studiesData.map(study => ({
        ...study,
        elementCount: (study.elements || []).length,
        isExpanded: false
      }));

      setStudies(processedStudies);
    } catch (error) {
      console.error('Error loading studies for consolidation:', error);
    } finally {
      setLoading(false);
    }
  };

  const moveStudyUp = (index: number) => {
    if (index === 0) return;
    const newStudies = [...studies];
    [newStudies[index], newStudies[index - 1]] = [newStudies[index - 1], newStudies[index]];
    setStudies(newStudies);
  };

  const moveStudyDown = (index: number) => {
    if (index === studies.length - 1) return;
    const newStudies = [...studies];
    [newStudies[index], newStudies[index + 1]] = [newStudies[index + 1], newStudies[index]];
    setStudies(newStudies);
  };

  const toggleStudyExpansion = (studyId: string) => {
    setStudies(prev => prev.map(study => 
      study.id === studyId 
        ? { ...study, isExpanded: !study.isExpanded }
        : study
    ));
  };

  const consolidateStudies = async () => {
    if (!consolidatedName.trim() || studies.length === 0) {
      alert(t('consolidation.nameRequired', { ns: 'study' }));
      return;
    }

    setConsolidating(true);
    try {
      // Crear el nuevo estudio consolidado
      const consolidatedElements: ElementInstance[] = [];
      const consolidatedTimeRecords: Record<string, any[]> = {};
      const consolidatedSupplements: Record<string, any> = {};
      let elementIndex = 0;

      // Consolidar elementos de todos los estudios en el orden especificado
      studies.forEach((study, studyIndex) => {
        if (study.elements && study.elements.length > 0) {
          study.elements.forEach((element: ElementInstance) => {
            const newElementId = `consolidated_${elementIndex}`;
            
            const consolidatedElement: ElementInstance = {
              ...element,
              id: newElementId, // Nuevo ID único
              name: `${studyIndex + 1}.${element.name}`, // Prefijo con número de estudio
              description: `Del estudio: ${study.required_info?.name || 'Sin nombre'}\n${element.description || ''}`,
              position: elementIndex
            };
            
            // Consolidar los registros de tiempo del elemento original
            if (study.time_records && study.time_records[element.id]) {
              consolidatedTimeRecords[newElementId] = study.time_records[element.id].map((record: any) => ({
                ...record,
                id: `${newElementId}_record_${record.id}`, // Nuevo ID único para el registro
                elementId: newElementId, // Actualizar referencia al nuevo elemento
                comment: record.comment ? `[${study.required_info?.name}] ${record.comment}` : `Registro del estudio: ${study.required_info?.name}`
              }));
            } else {
              consolidatedTimeRecords[newElementId] = [];
            }

            // Consolidar los suplementos del elemento original
            if (study.supplements && study.supplements[element.id]) {
              consolidatedSupplements[newElementId] = {
                ...study.supplements[element.id],
                // Agregar información del estudio de origen en los suplementos
                source_study: {
                  id: study.id,
                  name: study.required_info?.name,
                  original_element_id: element.id
                }
              };
            }

            consolidatedElements.push(consolidatedElement);
            elementIndex++;
          });
        }
      });

      // Crear la información del estudio consolidado
      const consolidatedStudy = {
        required_info: {
          name: consolidatedName,
          company: studies[0]?.required_info?.company || '',
          date: new Date().toISOString().split('T')[0],
          operator: 'Sistema de Consolidación',
          activity_scale: studies[0]?.required_info?.activity_scale || {
            normal: 100,
            optimal: 100
          },
          process: folderName || 'Proceso Consolidado'
        },
        optional_info: {
          consolidated: true,
          sourceStudies: studies.map(s => ({
            id: s.id,
            name: s.required_info?.name,
            elementCount: s.elementCount,
            company: s.required_info?.company,
            date: s.required_info?.date
          })),
          reportSettings: (studies[0]?.optional_info as any)?.reportSettings || {},
          tools: 'Consolidación automática',
          machine: 'Sistema',
          section: folderName || 'Consolidado',
          operator: 'Sistema de Consolidación',
          reference: `Consolidado de ${studies.length} estudios`,
          technician: 'Sistema',
          study_number: `CONSOL_${Date.now()}`
        },
        elements: consolidatedElements,
        time_records: consolidatedTimeRecords,
        supplements: consolidatedSupplements,
        folder_id: folderId, // Guardar en la misma carpeta
        // Agregar registros de crono seguido si existen
        crono_seguido_records: studies.reduce((acc: any[], study) => {
          if (study.crono_seguido_records) {
            const studyRecords = study.crono_seguido_records.map((record: any) => ({
              ...record,
              id: `consol_${study.id}_${record.id}`,
              description: `[${study.required_info?.name}] ${record.description}`
            }));
            acc.push(...studyRecords);
          }
          return acc;
        }, [])
      };

      console.log('🔄 Creando estudio consolidado:', {
        elements: consolidatedElements.length,
        timeRecords: Object.keys(consolidatedTimeRecords).length,
        supplements: Object.keys(consolidatedSupplements).length,
        consolidatedStudy
      });

      await createStudy(consolidatedStudy);
      
      // Actualizar la lista de estudios para mostrar el nuevo estudio consolidado
      await fetchStudies();
      
      alert(`${t('consolidation.successMessage', { ns: 'study', name: consolidatedName })}
${t('consolidation.elementsCount', { ns: 'study', count: consolidatedElements.length })}
${t('consolidation.timeRecordsCount', { ns: 'study', count: Object.values(consolidatedTimeRecords).flat().length })}
${t('consolidation.supplementsCount', { ns: 'study', count: Object.keys(consolidatedSupplements).length })}`);
      onClose();
      onStudyCreated?.();
      
    } catch (error) {
      console.error('Error creating consolidated study:', error);
      alert(t('consolidation.errorMessage', { ns: 'study', error: (error as Error).message }));
    } finally {
      setConsolidating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            <Combine className="h-5 w-5 inline mr-2" />
            {t('consolidation.title', { ns: 'study' })}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col p-6">
          {/* Folder Info */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <div className="flex items-center gap-2 text-blue-800">
              <Combine className="h-5 w-5" />
              <div className="flex-1">
                <div className="font-medium">
                  {folderName ? `${t('consolidation.folder', { ns: 'study' })} ${folderName}` : t('consolidation.title', { ns: 'study' })}
                </div>
                <div className="text-sm">
                  {studies.length} {studies.length === 1 ? t('consolidation.studyFound', { ns: 'study' }) : t('consolidation.studiesFound', { ns: 'study' })}
                  {includeSubfolders && folderId && (
                    <span className="text-blue-600 ml-1">{t('consolidation.includesSubfolders', { ns: 'study' })}</span>
                  )}
                </div>
                {studies.length > 0 && (
                  <div className="text-xs mt-2 flex gap-4 text-blue-700">
                    {(() => {
                      const totalElements = studies.reduce((sum, study) => sum + study.elementCount, 0);
                      const totalTimeRecords = studies.reduce((sum, study) => {
                        return sum + (study.time_records ? Object.values(study.time_records).flat().length : 0);
                      }, 0);
                      const totalElementsWithSupplements = studies.reduce((sum, study) => {
                        return sum + (study.supplements ? Object.keys(study.supplements).length : 0);
                      }, 0);
                      
                      return (
                        <>
                          <span>📋 {totalElements} {t('consolidation.totalElements', { ns: 'study' })}</span>
                          {totalTimeRecords > 0 && (
                            <span>⏱️ {totalTimeRecords} {t('consolidation.timeRecords', { ns: 'study' })}</span>
                          )}
                          {totalElementsWithSupplements > 0 && (
                            <span>🔧 {totalElementsWithSupplements} {t('consolidation.elementsWithSupplements', { ns: 'study' })}</span>
                          )}
                        </>
                      );
                    })()}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Options */}
          <div className="mb-6 space-y-4">
            <div>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={includeSubfolders}
                  onChange={(e) => setIncludeSubfolders(e.target.checked)}
                  className="rounded text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  {t('includeSubfolders')}
                </span>
              </label>
              <p className="text-xs text-gray-500 mt-1 ml-6">
                {t('consolidateSubfolders')}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('consolidation.consolidatedName', { ns: 'study' })}
              </label>
              <input
                type="text"
                value={consolidatedName}
                onChange={(e) => setConsolidatedName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={t('consolidation.namePlaceholder', { ns: 'study' })}
              />
            </div>
          </div>

          {/* Studies List */}
          <div className="flex-1 overflow-y-auto border border-gray-200 rounded-lg">
            {loading ? (
              <div className="p-8 text-center text-gray-500">
                {t('consolidation.loadingStudies', { ns: 'study' })}
              </div>
            ) : studies.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                {t('consolidation.noStudiesFound', { ns: 'study' })}
              </div>
            ) : (
              <div className="p-4">
                <div className="mb-4 text-sm text-gray-600">
                  <strong>{t('consolidation.consolidationOrder', { ns: 'study' })}</strong> {t('consolidation.orderInstructions', { ns: 'study' })}
                </div>
                
                <div className="space-y-2">
                  {studies.map((study, index) => (
                    <div
                      key={study.id}
                      className="p-3 border rounded-lg bg-white shadow-sm"
                    >
                      <div className="flex items-center gap-3">
                        {/* Order Controls */}
                        <div className="flex flex-col gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => moveStudyUp(index)}
                            disabled={index === 0}
                            className="h-6 w-6 p-0"
                          >
                            <ArrowUp className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => moveStudyDown(index)}
                            disabled={index === studies.length - 1}
                            className="h-6 w-6 p-0"
                          >
                            <ArrowDown className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                          {index + 1}
                        </div>
                        
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {study.required_info?.name || t('consolidation.noName', { ns: 'study' })}
                          </div>
                          <div className="text-sm text-gray-500">
                            {study.elementCount} {t('consolidation.elements', { ns: 'study' })} • {study.required_info?.company || t('consolidation.noCompany', { ns: 'study' })}
                          </div>
                          <div className="text-xs text-gray-400 mt-1 flex gap-3">
                            {(() => {
                              const totalTimeRecords = study.time_records ? 
                                Object.values(study.time_records).flat().length : 0;
                              const elementsWithSupplements = study.supplements ? 
                                Object.keys(study.supplements).length : 0;
                              
                              return (
                                <>
                                  {totalTimeRecords > 0 && (
                                    <span className="text-green-600">⏱️ {totalTimeRecords} {t('consolidation.times', { ns: 'study' })}</span>
                                  )}
                                  {elementsWithSupplements > 0 && (
                                    <span className="text-blue-600">🔧 {elementsWithSupplements} {t('consolidation.withSupplements', { ns: 'study' })}</span>
                                  )}
                                  <span>📅 {study.required_info?.date || t('consolidation.noDate', { ns: 'study' })}</span>
                                </>
                              );
                            })()}
                          </div>
                        </div>
                        
                        {study.elements && study.elements.length > 0 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleStudyExpansion(study.id)}
                            className="h-8 w-8 p-0"
                          >
                            {study.isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                      
                      {/* Expanded Elements */}
                      {study.isExpanded && study.elements && (
                        <div className="mt-3 ml-8 space-y-2">
                          {study.elements.map((element: ElementInstance, elemIndex) => {
                            const timeRecords = study.time_records?.[element.id] || [];
                            const supplements = study.supplements?.[element.id];
                            const hasTimeRecords = timeRecords.length > 0;
                            const hasSupplements = supplements && Object.keys(supplements).length > 0;
                            
                            return (
                              <div key={element.id} className="p-2 bg-gray-50 rounded border">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="bg-gray-100 px-2 py-0.5 rounded text-xs font-medium">
                                    {index + 1}.{elemIndex + 1}
                                  </span>
                                  <span className="font-medium text-gray-800">{element.name}</span>
                                </div>
                                
                                <div className="text-xs text-gray-600 ml-6">
                                  <div className="flex gap-4 flex-wrap">
                                    <span>{t('consolidation.type', { ns: 'study' })} {element.type}</span>
                                    {element.frequency_repetitions && (
                                      <span>{t('consolidation.frequency', { ns: 'study' })} {element.frequency_repetitions}/{element.frequency_cycles}</span>
                                    )}
                                    {hasTimeRecords && (
                                      <span className="text-green-600 font-medium">
                                        ⏱️ {timeRecords.length} {t('consolidation.timeRecords', { ns: 'study' })}
                                      </span>
                                    )}
                                    {hasSupplements && (
                                      <span className="text-blue-600 font-medium">
                                        🔧 {t('consolidation.supplements', { ns: 'study' })} {supplements.percentage?.toFixed(1)}%
                                      </span>
                                    )}
                                  </div>
                                  
                                  {element.description && (
                                    <div className="mt-1 text-gray-500 italic">
                                      {element.description.length > 60 
                                        ? `${element.description.substring(0, 60)}...` 
                                        : element.description
                                      }
                                    </div>
                                  )}
                                  
                                  {/* Detalles de registros de tiempo */}
                                  {hasTimeRecords && (
                                    <div className="mt-1 text-xs">
                                      <span className="text-green-700">
                                        {t('consolidation.times', { ns: 'study' })}: {timeRecords.map((r: any) => `${r.time}s (${r.activity}%)`).join(', ')}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={consolidating}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="button"
            onClick={consolidateStudies}
            disabled={consolidating || studies.length === 0 || !consolidatedName.trim()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {consolidating ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                {t('consolidation.creating', { ns: 'study' })}
              </>
            ) : (
              <>
                <Combine className="h-4 w-4 mr-2" />
                {t('consolidation.createConsolidated', { ns: 'study' })}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}; 