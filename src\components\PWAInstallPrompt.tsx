import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

let deferredPrompt: any = null;

export function PWAInstallPrompt() {
  const [isInstallable, setIsInstallable] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      deferredPrompt = e;
      // Show the install button
      setIsInstallable(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Check if app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstallable(false);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      return;
    }

    // Show the install prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;
    console.log(`User response to the install prompt: ${outcome}`);

    // Clear the deferredPrompt for the next time
    deferredPrompt = null;
    setIsInstallable(false);
  };

  if (!isInstallable) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg p-4 flex items-center justify-between">
      <div>
        <h3 className="font-semibold text-gray-900">{t('pwa.installTitle', 'Install Time Study App')}</h3>
        <p className="text-sm text-gray-600">{t('pwa.installDescription', 'Install our app for a better experience')}</p>
      </div>
      <button
        onClick={handleInstallClick}
        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
      >
        {t('pwa.installButton', 'Install')}
      </button>
    </div>
  );
}
