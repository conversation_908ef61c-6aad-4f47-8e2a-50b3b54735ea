import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Header } from '../components/Header';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DocsPageProps {
  onBack?: () => void;
}

export const DocsPage: React.FC<DocsPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['common', 'docs']);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'docs' | 'changelog'>('docs');

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('docs:title')}
        leftContent={
          <button
            onClick={handleBack}
            className="p-2 hover:bg-gray-200 rounded-full"
          >
            <ArrowLeft className="w-6 h-6" />
          </button>
        }
      />

      <main className="container mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow p-6">
          {/* Tabs */}
          <div className="flex space-x-4 mb-6">
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'docs'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => setActiveTab('docs')}
            >
              {t('docs:documentation')}
            </button>
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'changelog'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => setActiveTab('changelog')}
            >
              {t('docs:changelog')}
            </button>
          </div>

          {/* Content */}
          <div className="prose max-w-none">
            {activeTab === 'docs' ? (
              <div className="space-y-8">
                <section>
                  <h2 className="text-2xl font-bold mb-4">{t('docs:overview')}</h2>
                  <p>{t('docs:overviewText')}</p>
                </section>

                <section>
                  <h2 className="text-2xl font-bold mb-4">{t('docs:features')}</h2>
                  <ul className="list-disc pl-6 space-y-4">
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:studyManagement')}</h3>
                      <p>{t('docs:studyManagementDesc')}</p>
                    </li>
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:timeStudy')}</h3>
                      <p>{t('docs:timeStudyDesc')}</p>
                    </li>
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:cronoSeguido')}</h3>
                      <p>{t('docs:cronoSeguidoDesc')}</p>
                    </li>
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:frequency')}</h3>
                      <p>{t('docs:frequencyDesc')}</p>
                    </li>
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:machineTimes')}</h3>
                      <p>{t('docs:machineTimesDesc')}</p>
                    </li>
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:supplements')}</h3>
                      <p>{t('docs:supplementsDesc')}</p>
                    </li>
                    <li>
                      <h3 className="text-xl font-semibold">{t('docs:reports')}</h3>
                      <p>{t('docs:reportsDesc')}</p>
                    </li>
                  </ul>
                </section>
              </div>
            ) : (
              <div className="space-y-8">
                <h2 className="text-2xl font-bold mb-4">{t('docs:changelog')}</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold">v39.0.3 (2024)</h3>
                    <ul className="list-disc pl-6">
                      <li>Mejora en la clonación de estudios incluyendo todos los datos relacionados</li>
                      <li>Corrección del filtro por proceso en la lista de estudios</li>
                      <li>El estudio permanece seleccionado después de actualizarlo</li>
                      <li>Actualización de iconos para PWA</li>
                      <li>Añadida documentación y changelog</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold">v38.0.0 (2023)</h3>
                    <ul className="list-disc pl-6">
                      <li>Implementación de cronómetro seguido</li>
                      <li>Mejoras en la interfaz de usuario</li>
                      <li>Optimización del rendimiento</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold">v37.0.0 (2023)</h3>
                    <ul className="list-disc pl-6">
                      <li>Nuevo sistema de filtros para estudios</li>
                      <li>Mejoras en la gestión de suplementos</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold">v36.0.0 (2023)</h3>
                    <ul className="list-disc pl-6">
                      <li>Implementación de tiempos de máquina</li>
                      <li>Mejoras en el sistema de reportes</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold">v35.0.0 (2023)</h3>
                    <ul className="list-disc pl-6">
                      <li>Sistema de frecuencias</li>
                      <li>Mejoras en la gestión de elementos</li>
                    </ul>
                  </div>

                  {/* Versiones anteriores... */}
                  <div>
                    <h3 className="text-xl font-semibold">Versiones anteriores</h3>
                    <p>Características principales implementadas:</p>
                    <ul className="list-disc pl-6">
                      <li>Sistema base de estudios de tiempos</li>
                      <li>{t('docs:elementAndMethodManagement')}</li>
                      <li>Sistema de autenticación</li>
                      <li>Cronómetro básico</li>
                      <li>Sistema de reportes básico</li>
                      <li>Interfaz de usuario inicial</li>
                      <li>Gestión de suplementos básica</li>
                      <li>Sistema de biblioteca de elementos</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default DocsPage;
