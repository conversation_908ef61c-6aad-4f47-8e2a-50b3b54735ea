import React, { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';

export const TestFolders = () => {
  const [folders, setFolders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFolders = async () => {
      try {
        console.log('Fetching folders...');
        const { data: session } = await supabase.auth.getSession();
        console.log('Session:', session?.session?.user?.id);

        const { data, error } = await supabase
          .from('folders')
          .select('*')
          .order('name');

        console.log('Query result:', { data, error });

        if (error) throw error;

        setFolders(data || []);
      } catch (err) {
        console.error('Error fetching folders:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchFolders();
  }, []);

  const createTestFolder = async () => {
    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session?.session?.user?.id) {
        alert('No hay sesión activa');
        return;
      }

      const { data, error } = await supabase
        .from('folders')
        .insert({
          name: `Test Folder ${Date.now()}`,
          description: 'Test folder description',
          color: '#3B82F6',
          icon: 'folder',
          user_id: session.session.user.id
        })
        .select()
        .single();

      console.log('Create folder result:', { data, error });

      if (error) throw error;

      // Refresh the list
      setFolders(prev => [...prev, data]);
      alert('Carpeta creada exitosamente');
    } catch (err) {
      console.error('Error creating folder:', err);
      alert(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  if (loading) return <div className="p-4">Cargando...</div>;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Test Carpetas</h1>
      
      <button 
        onClick={createTestFolder}
        className="bg-blue-500 text-white px-4 py-2 rounded mb-4"
      >
        Crear Carpeta de Prueba
      </button>

      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Carpetas encontradas ({folders.length}):</h2>
        {folders.length === 0 ? (
          <p className="text-gray-500">No hay carpetas</p>
        ) : (
          folders.map(folder => (
            <div key={folder.id} className="bg-gray-100 p-3 rounded">
              <div className="font-medium">{folder.name}</div>
              <div className="text-sm text-gray-600">{folder.description}</div>
              <div className="text-xs text-gray-500">
                ID: {folder.id} | Color: {folder.color} | Usuario: {folder.user_id}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}; 