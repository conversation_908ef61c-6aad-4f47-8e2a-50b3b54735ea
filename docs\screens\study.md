# Pantalla de Información del Estudio

## Descripción
Interfaz para la gestión de la información básica y detallada del estudio de tiempos.

## Estructura
```
StudyPage
├── Header
└── StudyForm
```

## Campos del Estudio

### Información Requerida
- **Número de Estudio**: Identificador único opcional
- **Nombre del Proceso**: Descripción del proceso estudiado
- **Empresa**: Compañía donde se realiza el estudio
- **Fecha**: Fecha de realización del estudio
- **Escala de Actividad**:
  - Normal (por defecto 100)
  - Óptima (por defecto 133)

### Información Opcional
- **Operario**: Persona que realiza la operación
- **Sección**: Área o departamento
- **Referencia**: Código o referencia del producto
- **Máquina**: Equipo utilizado
- **Herramientas**: Utillajes necesarios
- **Técnico**: Persona que realiza el estudio

## Funcionalidades

### 1. Gestión de Datos
- Creación de nuevos estudios
- Edición de estudios existentes
- Validación de campos requeridos
- Formateo automático de datos

### 2. Validaciones
- Campos obligatorios:
  - Nombre del proceso
  - Empresa
  - Fecha
- Rangos de actividad:
  - Normal: 1-200
  - Óptima: 1-200
  - Óptima > Normal

### 3. Persistencia
- Almacenamiento en Supabase
- Actualización en tiempo real
- Gestión de errores
- Feedback visual del estado

## Estados
```typescript
{
  isLoading: boolean;
  formData: {
    studyNumber: string;
    name: string;
    company: string;
    date: string;
    operator: string;
    section: string;
    reference: string;
    machine: string;
    tools: string;
    technician: string;
    activityScale: {
      normal: number;
      optimal: number;
    };
  };
}
```

## Flujo de Trabajo
1. Entrada a la pantalla:
   - Como creación nueva
   - Como edición de existente
2. Validación de campos:
   - En tiempo real
   - Al guardar
3. Guardado de datos:
   - Validación final
   - Persistencia
   - Feedback al usuario
4. Retorno a pantalla principal

## Hooks Utilizados
- useStudyStore
- useTranslation
- useState
- useEffect

## Integración
- Conexión con el store global
- Navegación entre pantallas
- Gestión de estado local vs global
- Manejo de errores y feedback