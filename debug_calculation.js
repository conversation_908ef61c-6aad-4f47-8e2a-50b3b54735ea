// Debug calculation for machine stopped time - simplified
console.log("=== VERIFICACIÓN DEL CÁLCULO DEL TIEMPO DE MÁQUINA PARADA ===\n");

// Datos del elemento 1: am9zqy2r0n4maz87ya7
console.log("Elemento 1 (am9zqy2r0n4maz87ya7):");
console.log("Tiempos: 37.031, 7.571, 4.83");
console.log("Actividades: 90, 110, 105");
const el1_avgTime = (37.031 + 7.571 + 4.83) / 3;
const el1_avgActivity = (90 + 110 + 105) / 3;
const el1_normalized = el1_avgTime * (el1_avgActivity / 100) * 1;
console.log(`Promedio tiempo: ${el1_avgTime}`);
console.log(`Promedio actividad: ${el1_avgActivity}`);
console.log(`Normalizado: ${el1_avgTime} × (${el1_avgActivity}/100) × 1 = ${el1_normalized}`);
console.log("");

// Datos del elemento 2: 6cuwtflvzplmaz87ya7
console.log("Elemento 2 (6cuwtflvzplmaz87ya7):");
console.log("Tiempos: 39.97, 7.34, 3.518");
console.log("Actividades: 105, 105, 105");
const el2_avgTime = (39.97 + 7.34 + 3.518) / 3;
const el2_avgActivity = (105 + 105 + 105) / 3;
const el2_normalized = el2_avgTime * (el2_avgActivity / 100) * 1;
console.log(`Promedio tiempo: ${el2_avgTime}`);
console.log(`Promedio actividad: ${el2_avgActivity}`);
console.log(`Normalizado: ${el2_avgTime} × (${el2_avgActivity}/100) × 1 = ${el2_normalized}`);
console.log("");

// Datos del elemento 3: 89c75ac0-ce9b-4ad0-8cb0-c7e97fdfc929
console.log("Elemento 3 (89c75ac0-ce9b-4ad0-8cb0-c7e97fdfc929):");
const el3_times = [46.8, 62.4, 75.83, 23.4, 45.12, 18.31, 54.48, 40.05, 98.87, 44.64, 36, 33.84, 70.55, 67.67];
const el3_activities = [110, 110, 110, 110, 110, 110, 110, 110, 105, 110, 110, 110, 110, 110];
console.log(`Tiempos (${el3_times.length} registros): ${el3_times.join(', ')}`);
console.log(`Actividades: ${el3_activities.join(', ')}`);

const el3_avgTime = el3_times.reduce((a, b) => a + b, 0) / el3_times.length;
const el3_avgActivity = el3_activities.reduce((a, b) => a + b, 0) / el3_activities.length;
const el3_normalized = el3_avgTime * (el3_avgActivity / 100) * (1/8); // frequency 1/8
console.log(`Promedio tiempo: ${el3_avgTime}`);
console.log(`Promedio actividad: ${el3_avgActivity}`);
console.log(`Normalizado: ${el3_avgTime} × (${el3_avgActivity}/100) × (1/8) = ${el3_normalized}`);
console.log("");

const total = el1_normalized + el2_normalized + el3_normalized;
console.log("=== RESULTADO FINAL ===");
console.log(`Elemento 1: ${el1_normalized.toFixed(6)}`);
console.log(`Elemento 2: ${el2_normalized.toFixed(6)}`);
console.log(`Elemento 3: ${el3_normalized.toFixed(6)}`);
console.log(`TOTAL: ${total.toFixed(6)}`);
console.log(`TOTAL REDONDEADO (3 decimales): ${Number(total.toFixed(3))}`);

console.log("\n=== SUMA MANUAL DEL USUARIO ===");
console.log("Según el usuario, la suma manual da: 41.65");
console.log(`Diferencia: ${Math.abs(41.65 - total).toFixed(6)}`);

// Verificar si hay diferencias en el redondeo
console.log("\n=== ANÁLISIS DE REDONDEO ===");
console.log(`Total sin redondear: ${total}`);
console.log(`Total con .toFixed(3): ${total.toFixed(3)}`);
console.log(`Total con Number(toFixed(3)): ${Number(total.toFixed(3))}`);

// Debug script para analizar la situación actual del usuario

console.log('=== ANÁLISIS DE DISCORDANCIA NO DETECTADA ===');
console.log('');

// Datos reportados por el usuario
const currentData = {
  reportNormalCycle: 53.32, // segundos (lo que muestra la página de informe)
  supplementsNormalCycle: 52.99, // segundos (lo que muestra la página de suplementos)
  difference: 53.32 - 52.99 // segundos
};

console.log('DATOS ACTUALES DEL USUARIO:');
console.log(`  ▸ Página de informe - Ciclo Normal: ${currentData.reportNormalCycle} segundos`);
console.log(`  ▸ Página de suplementos - Ciclo Normal Total: ${currentData.supplementsNormalCycle} segundos`);
console.log(`  ▸ Diferencia calculada: ${currentData.difference.toFixed(3)} segundos`);
console.log('');

console.log('=== ANÁLISIS DE UMBRALES ===');
console.log('');

const thresholds = [0.1, 0.5, 1.0, 2.0];
thresholds.forEach(threshold => {
  const shouldDetect = currentData.difference > threshold;
  console.log(`  ▸ Umbral ${threshold} seg: ${shouldDetect ? '✅ SE DETECTARÍA' : '❌ NO SE DETECTA'}`);
});

console.log('');
console.log('=== DIAGNÓSTICO ===');
console.log('');

if (currentData.difference > 0.1) {
  console.log('🔍 PROBLEMA IDENTIFICADO:');
  console.log(`  ▸ La diferencia (${currentData.difference.toFixed(3)} seg) debería detectarse con umbral de 0.1 seg`);
  console.log('  ▸ Si no se detecta, posibles causas:');
  console.log('    1. Los datos en __machine_cycle_data__ no están actualizados');
  console.log('    2. La página de informe no está leyendo los datos correctos');
  console.log('    3. Hay un problema en la lógica de detección');
  console.log('    4. El umbral en el código sigue siendo muy alto');
} else {
  console.log('ℹ️ DIFERENCIA MUY PEQUEÑA:');
  console.log('  ▸ La diferencia es menor a 0.1 segundos');
  console.log('  ▸ Esto podría ser normal debido a redondeos');
}

console.log('');
console.log('=== VALORES ESPERADOS ===');
console.log('');

console.log('Para que se muestre la alerta de discordancia:');
console.log(`  ▸ __machine_cycle_data__.totalCycleTime debe ser: ${currentData.supplementsNormalCycle} seg`);
console.log(`  ▸ Valor actual del informe (normalCycle): ${currentData.reportNormalCycle} seg`);
console.log(`  ▸ Diferencia: ${currentData.difference.toFixed(3)} seg`);
console.log(`  ▸ Con umbral de 0.1 seg: ${currentData.difference > 0.1 ? '✅ DEBERÍA DETECTARSE' : '❌ NO SE DETECTA'}`);

console.log('');
console.log('=== PASOS PARA VERIFICAR ===');
console.log('');
console.log('1. Verificar qué valor está guardado en __machine_cycle_data__.totalCycleTime');
console.log('2. Confirmar que la página de informe está leyendo este valor');
console.log('3. Revisar los logs de consola para ver el debug de detección');
console.log('4. Asegurarse de que el umbral se ha actualizado a 0.1 segundos'); 