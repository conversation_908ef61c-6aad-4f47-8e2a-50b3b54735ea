# Políticas de Seguridad en Supabase

Este documento describe las políticas de seguridad (RLS - Row Level Security) necesarias para el correcto funcionamiento de la aplicación.

## Tabla: organization_join_requests

### Políticas existentes

1. **INSERT** - "Users can create join requests"
   - Permite a usuarios autenticados crear solicitudes de unión a organizaciones.

2. **SELECT** - "Users can view their own join requests"
   - Permite a usuarios ver sus propias solicitudes.

### Política recomendada para añadir

3. **UPDATE** - "Admins can update join requests"
   - Permite a administradores de la organización actualizar el estado de las solicitudes.
   - Permite al creador de la solicitud actualizarla.
   - **Importante**: Esta política es necesaria para que las funciones de aprobación y rechazo de solicitudes funcionen correctamente.

## Cómo implementar la política UPDATE

1. Accede al panel de administración de Supabase
2. Navega a "Authentication" > "Policies"
3. Selecciona la tabla "organization_join_requests"
4. Haz clic en "New Policy"
5. Selecciona "Create a policy from scratch"
6. Completa el formulario:
   - **Name**: "Admins can update join requests"
   - **Operation**: UPDATE
   - **Target roles**: authenticated
   - **USING expression**:
   ```sql
   (auth.uid() = user_id)
   OR
   EXISTS (
     SELECT 1 
     FROM public.organization_members 
     WHERE 
       organization_members.organization_id = organization_join_requests.organization_id
       AND organization_members.user_id = auth.uid()
       AND organization_members.role IN ('admin', 'owner')
   )
   ```
7. Haz clic en "Save Policy"

## Alternativa si no se puede añadir la política

Si no es posible añadir la política UPDATE, la aplicación seguirá funcionando con la solución alternativa implementada en la versión 42.0.12, que utiliza una estrategia DELETE + INSERT para simular la actualización de registros.

## Recomendaciones de seguridad adicionales

- Revisar periódicamente las políticas de seguridad para asegurar que siguen los principios de privilegio mínimo.
- Documentar todas las políticas de seguridad para facilitar el mantenimiento y desarrollo futuro.
- Considerar la implementación de funciones RPC en Supabase para operaciones complejas que requieran múltiples pasos.
