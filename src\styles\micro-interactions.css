/* ========================================
   MICRO-INTERACCIONES Y FEEDBACK VISUAL
   ======================================== */

/* === VARIABLES CSS PARA CONSISTENCIA === */
:root {
  --animation-fast: 150ms;
  --animation-normal: 250ms;
  --animation-slow: 350ms;
  --animation-very-slow: 500ms;
  
  --easing-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
  --easing-in-cubic: cubic-bezier(0.32, 0, 0.67, 0);
  --easing-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-strong: 0 10px 25px rgba(0, 0, 0, 0.2);
  --shadow-colored: 0 4px 14px rgba(59, 130, 246, 0.25);
}

/* === ANIMACIONES DE HOVER SOFISTICADAS === */
.enhanced-hover {
  position: relative;
  overflow: hidden;
  transition: all var(--animation-normal) var(--easing-out-cubic);
}

.enhanced-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left var(--animation-slow) var(--easing-out-cubic);
  z-index: 1;
}

.enhanced-hover:hover::before {
  left: 100%;
}

.enhanced-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-strong);
}

/* === EFECTOS DE BRILLO === */
.glow-effect {
  position: relative;
  transition: all var(--animation-normal) var(--easing-out-cubic);
}

.glow-effect::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6);
  border-radius: inherit;
  opacity: 0;
  z-index: -1;
  transition: opacity var(--animation-normal) var(--easing-out-cubic);
}

.glow-effect:hover::after {
  opacity: 1;
  animation: glow-pulse 2s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* === ANIMACIONES DE BOTONES === */
.btn-micro {
  position: relative;
  overflow: hidden;
  transition: all var(--animation-fast) var(--easing-out-cubic);
  transform-origin: center;
}

.btn-micro:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-medium);
}

.btn-micro:active {
  transform: scale(0.98);
  transition-duration: var(--animation-fast);
}

.btn-micro.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.btn-micro.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: var(--shadow-colored);
}

/* === EFECTOS DE RIPPLE MEJORADOS === */
.ripple-enhanced {
  position: relative;
  overflow: hidden;
}

.ripple-enhanced::before {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation var(--animation-slow) var(--easing-out-cubic);
  pointer-events: none;
}

.ripple-enhanced:active::before {
  animation: ripple-animation var(--animation-slow) var(--easing-out-cubic);
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* === FEEDBACK VISUAL PARA ESTADOS === */
.state-success {
  position: relative;
  animation: success-pulse var(--animation-normal) var(--easing-bounce);
}

.state-success::after {
  content: '✓';
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: success-check var(--animation-slow) var(--easing-bounce);
}

@keyframes success-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes success-check {
  0% { 
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% { 
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
  100% { 
    transform: scale(1) rotate(360deg);
    opacity: 1;
  }
}

/* === LOADING CON MICRO-INTERACCIONES === */
.loading-micro {
  position: relative;
  overflow: hidden;
}

.loading-micro::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* === EFECTOS DE FOCUS MEJORADOS === */
.focus-enhanced {
  transition: all var(--animation-fast) var(--easing-out-cubic);
}

.focus-enhanced:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
}

.focus-enhanced:focus-visible {
  box-shadow: 
    0 0 0 3px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.2);
}

/* === ANIMACIONES DE ENTRADA === */
.enter-from-bottom {
  animation: enter-bottom var(--animation-slow) var(--easing-out-cubic);
}

.enter-from-top {
  animation: enter-top var(--animation-slow) var(--easing-out-cubic);
}

.enter-from-left {
  animation: enter-left var(--animation-slow) var(--easing-out-cubic);
}

.enter-from-right {
  animation: enter-right var(--animation-slow) var(--easing-out-cubic);
}

.enter-fade-in {
  animation: enter-fade var(--animation-normal) var(--easing-out-cubic);
}

.enter-scale {
  animation: enter-scale var(--animation-normal) var(--easing-bounce);
}

@keyframes enter-bottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes enter-top {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes enter-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes enter-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes enter-fade {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes enter-scale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* === MICRO-INTERACCIONES PARA FORMULARIOS === */
.form-field-enhanced {
  transition: all var(--animation-normal) var(--easing-out-cubic);
}

.form-field-enhanced:focus {
  transform: scale(1.02);
  box-shadow: 
    0 0 0 2px rgba(59, 130, 246, 0.2),
    var(--shadow-medium);
}

.form-field-enhanced.field-error {
  animation: shake var(--animation-normal) var(--easing-out-cubic);
  border-color: #ef4444;
}

.form-field-enhanced.field-success {
  border-color: #10b981;
  animation: success-glow var(--animation-normal) var(--easing-out-cubic);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes success-glow {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4); }
  50% { box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.1); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

/* === EFECTOS PARA NOTIFICACIONES === */
.notification-slide {
  animation: notification-enter var(--animation-slow) var(--easing-bounce);
}

.notification-slide.notification-exit {
  animation: notification-leave var(--animation-normal) var(--easing-in-cubic);
}

@keyframes notification-enter {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes notification-leave {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
}

/* === STAGGER ANIMATIONS === */
.stagger-children > * {
  animation: enter-stagger var(--animation-slow) var(--easing-out-cubic) backwards;
}

.stagger-children > *:nth-child(1) { animation-delay: 0ms; }
.stagger-children > *:nth-child(2) { animation-delay: 100ms; }
.stagger-children > *:nth-child(3) { animation-delay: 200ms; }
.stagger-children > *:nth-child(4) { animation-delay: 300ms; }
.stagger-children > *:nth-child(5) { animation-delay: 400ms; }
.stagger-children > *:nth-child(6) { animation-delay: 500ms; }

@keyframes enter-stagger {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === EFECTOS DE HOVER PARA ICONOS === */
.icon-bounce:hover {
  animation: icon-bounce var(--animation-normal) var(--easing-bounce);
}

.icon-rotate:hover {
  animation: icon-rotate var(--animation-normal) var(--easing-out-cubic);
}

.icon-pulse:hover {
  animation: icon-pulse var(--animation-slow) ease-in-out infinite;
}

@keyframes icon-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes icon-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes icon-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* === TRANSICIONES DE ESTADO AVANZADAS === */
.state-transition {
  transition: all var(--animation-normal) var(--easing-out-cubic);
}

.state-loading {
  pointer-events: none;
  opacity: 0.7;
  cursor: not-allowed;
}

.state-disabled {
  opacity: 0.5;
  filter: grayscale(0.5);
  pointer-events: none;
}

.state-processing {
  position: relative;
  overflow: hidden;
}

.state-processing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.5) 50%,
    transparent 70%
  );
  animation: processing-shine 1.5s infinite;
}

@keyframes processing-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* === RESPONSIVE Y ACCESIBILIDAD === */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (max-width: 768px) {
  .enhanced-hover:hover {
    transform: none;
  }
  
  .btn-micro:hover {
    transform: none;
  }
}

/* === DARK MODE ADAPTATIONS === */
@media (prefers-color-scheme: dark) {
  :root {
    --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-strong: 0 10px 25px rgba(0, 0, 0, 0.5);
  }
  
  .enhanced-hover::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
  
  .ripple-enhanced::before {
    background: rgba(255, 255, 255, 0.2);
  }
} 