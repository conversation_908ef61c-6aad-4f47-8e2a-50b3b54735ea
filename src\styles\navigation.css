/* ================================
   ESTILOS DE NAVEGACIÓN - CRONOMETRAS
   ================================ */

/* Prevenir scroll horizontal en el body y html */
html, body {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}

/* Asegurar que no hay scroll horizontal en contenedores principales */
.main-container {
  overflow-x: hidden !important;
  width: 100% !important;
  max-width: 100vw !important;
}

/* Contenedor de botones de navegación con scroll controlado */
.nav-buttons-container {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
  display: flex;
  justify-content: center;
  width: 100%;
  padding-bottom: 1rem;
  /* Asegurar que el scroll llegue completamente al inicio */
  scroll-padding-left: 0;
  scroll-padding-right: 0;
}

/* En pantallas grandes, centrar los botones cuando no hay scroll */
@media (min-width: 1024px) {
  .nav-buttons-container {
    justify-content: center;
  }
  
  .nav-buttons-inner {
    justify-content: center;
    width: auto;
    min-width: auto;
  }
}

/* En pantallas medianas y grandes, centrar los botones cuando es posible */
@media (min-width: 768px) {
  .nav-buttons-container {
    justify-content: center;
  }
  
  .nav-buttons-inner {
    justify-content: center;
    width: auto;
    min-width: auto;
  }
}

/* En pantallas muy grandes, optimizar el centrado y ocultar scroll */
@media (min-width: 1280px) {
  .nav-buttons-container {
    justify-content: center;
    overflow-x: hidden; /* Ocultar scroll cuando no es necesario */
  }
  
  .nav-buttons-inner {
    justify-content: center;
    width: auto;
    min-width: auto;
    padding: 0 0.5rem; /* Reducir padding en pantallas grandes */
  }
}

/* Estilos para la barra de scroll del contenedor de navegación */
.nav-buttons-container::-webkit-scrollbar {
  height: 4px;
}

.nav-buttons-container::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

.nav-buttons-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.nav-buttons-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Contenedor interno de los botones */
.nav-buttons-inner {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* gap-3 equivalent */
  padding: 0 1rem; /* Aumentar padding para mejor scroll */
  width: max-content;
  /* Asegurar que el contenido se extienda completamente */
  min-width: 100%;
}

/* Estilos específicos para los botones de navegación */
.nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem; /* px-4 py-3 equivalent */
  background-color: #fbbf24; /* amber-400 */
  color: #374151; /* gray-700 */
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: background-color 0.2s ease;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 90px;
}

.nav-button:hover {
  background-color: #f59e0b; /* amber-500 */
}

.nav-button-icon {
  width: 1.25rem; /* w-5 h-5 equivalent */
  height: 1.25rem;
  flex-shrink: 0;
}

.nav-button-text {
  font-size: 0.875rem; /* text-sm equivalent */
  text-align: center;
  font-weight: 500;
  line-height: 1.25;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .nav-button {
    min-width: 85px;
    padding: 0.625rem 0.875rem;
  }
  
  .nav-button-text {
    font-size: 0.8125rem;
  }
  
  .nav-button-icon {
    width: 1.125rem;
    height: 1.125rem;
  }
}

@media (max-width: 480px) {
  .nav-buttons-container {
    padding-bottom: 0.5rem;
  }
  
  .nav-buttons-inner {
    gap: 0.5rem;
    padding: 0 0.75rem; /* Mantener padding generoso incluso en móvil */
  }
}

/* Mejorar comportamiento del scroll en diferentes navegadores */
.nav-buttons-container {
  /* Para Firefox */
  scrollbar-width: thin;
  /* Para Safari y Chrome */
  -webkit-overflow-scrolling: touch;
  /* Asegurar que el scroll funcione correctamente */
  scroll-behavior: smooth;
}

/* Asegurar scroll completo en dispositivos móviles */
@media (max-width: 768px) {
  .nav-buttons-container {
    scroll-padding-left: 0;
    scroll-padding-right: 0;
    /* Asegurar que el scroll llegue completamente al inicio */
    scroll-snap-type: x mandatory;
  }
  
  .nav-buttons-inner {
    /* Asegurar que el primer botón sea completamente visible */
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
} 