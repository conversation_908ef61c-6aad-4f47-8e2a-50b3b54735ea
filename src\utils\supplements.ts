export const convertPointsToPercentage = (points: number): number => {
  const conversionTable = {
    "0": [10, 10, 10, 10, 10, 10, 10, 11, 11, 11],
    "10": [11, 11, 11, 11, 11, 12, 12, 12, 12, 12],
    "20": [13, 13, 13, 13, 14, 14, 14, 14, 15, 15],
    "30": [15, 16, 16, 16, 17, 17, 17, 18, 18, 18],
    "40": [19, 19, 20, 20, 21, 21, 22, 22, 23, 23],
    "50": [24, 24, 25, 26, 26, 27, 27, 28, 28, 29],
    "60": [30, 30, 31, 32, 32, 33, 34, 34, 35, 36],
    "70": [37, 37, 38, 39, 40, 40, 41, 42, 43, 44],
    "80": [45, 46, 47, 48, 48, 49, 50, 51, 52, 53],
    "90": [54, 55, 56, 57, 58, 59, 60, 61, 62, 63],
    "100": [64, 65, 66, 68, 69, 70, 71, 72, 73, 74],
    "110": [75, 77, 78, 79, 80, 82, 83, 84, 85, 87],
    "120": [88, 89, 91, 92, 93, 95, 96, 97, 99, 100],
    "130": [101, 103, 105, 106, 107, 109, 110, 112, 113, 115],
    "140": [116, 118, 119, 121, 122, 123, 125, 126, 128, 130]
  };

  // Determinar la fila y la columna en la tabla
  const fila = Math.floor(points / 10) * 10;
  const columna = points % 10;
  
  // Obtener el porcentaje de la tabla
  if (conversionTable[fila] && conversionTable[fila][columna] !== undefined) {
    return conversionTable[fila][columna];
  }
  
  // Si los puntos están fuera de rango, devolver el valor más cercano
  if (points < 0) return conversionTable["0"][0];
  if (points > 140) return conversionTable["140"][9];
  
  // Para puntos intermedios, usar el valor más cercano
  const filaInferior = Math.floor(points / 10) * 10;
  const filaSuperior = Math.ceil(points / 10) * 10;
  
  if (conversionTable[filaInferior]) {
    return conversionTable[filaInferior][0];
  }
  
  return conversionTable[filaSuperior][0];
};