import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Clock, Home, X, Copy, RotateCcw, AlertCircle } from 'lucide-react';
import { Header } from '../components/Header';
import { useStudyStore } from '../store/studyStore';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl'; // Import the hook
import { WorkElement } from '../types';
import { SupplementsTable } from '../components/supplements/SupplementsTable';
import { SupplementsFactors } from '../components/supplements/SupplementsFactors';
import { SupplementsForce } from '../components/supplements/SupplementsForce';
import { SupplementsMachine } from '../components/supplements/SupplementsMachine';
import { CopySupplementsModal } from '../components/supplements/CopySupplementsModal';
import { DeleteConfirmModal } from '../components/DeleteConfirmModal';
import { useAuthStore } from '../store/authStore';

interface SupplementsPageProps {
  onBack: () => void;
}

type TabType = 'points' | 'forzar' | 'calculoFatiga';
type TableType = 'oit' | 'tal' | null;

export const SupplementsPage: React.FC<SupplementsPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['supplements', 'method', 'common', 'study']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const elements = selectedStudy?.elements || [];
  const { updateSupplements } = useStudyStore();
  const { user } = useAuthStore();
  const [isSharedStudy, setIsSharedStudy] = useState(false);

  useStudyFromUrl(); // Add this hook to handle study from URL

  const [selectedTable, setSelectedTable] = useState<TableType>(null);
  const [selectedElement, setSelectedElement] = useState<WorkElement | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('points');
  const [showCopyModal, setShowCopyModal] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [showTableMismatchModal, setShowTableMismatchModal] = useState(false);
  const [copyingElementId, setCopyingElementId] = useState<string | null>(null);
  const [resettingElementId, setResettingElementId] = useState<string | null>(null);
  const [tableMismatchInfo, setTableMismatchInfo] = useState<{ currentTable: TableType; elementId: string } | null>(null);

  const handleBackToTables = () => {
    setSelectedTable(null);
    setSelectedElement(null);
  };

  const handleBackToElements = () => {
    setSelectedElement(null);
  };

  const handleCopy = async (toElementIds: string[]) => {
    if (!selectedStudy || !copyingElementId) return;

    const sourceSupplements = selectedStudy.supplements[copyingElementId];
    const updatedSupplements = { ...selectedStudy.supplements };

    toElementIds.forEach(elementId => {
      updatedSupplements[elementId] = { ...sourceSupplements };
    });

    try {
      await updateSupplements(selectedStudy.id, updatedSupplements);
      setShowCopyModal(false);
      setCopyingElementId(null);
    } catch (error) {
      console.error('Error copying supplements:', error);
    }
  };

  const handleReset = async () => {
    if (!selectedStudy || !resettingElementId) return;

    const updatedSupplements = { ...selectedStudy.supplements };
    updatedSupplements[resettingElementId] = {
      points: {},
      percentage: 0,
      is_forced: false,
      factor_selections: {},
      table_type: null
    };

    try {
      await updateSupplements(selectedStudy.id, updatedSupplements);
      setShowResetModal(false);
      setResettingElementId(null);
    } catch (error) {
      console.error('Error resetting supplement:', error);
    }
  };

  const checkTableMismatch = (element: WorkElement) => {
    if (!selectedStudy || !selectedTable) return false;

    const elementSupplements = selectedStudy.supplements[element.id];
    if (elementSupplements && elementSupplements.table_type && elementSupplements.table_type !== selectedTable) {
      setTableMismatchInfo({
        currentTable: elementSupplements.table_type,
        elementId: element.id
      });
      setShowTableMismatchModal(true);
      return true;
    }
    return false;
  };

  const handleElementSelect = (element: WorkElement) => {
    if (!selectedStudy) return;

    setSelectedElement(element);
    
    // Establecer la pestaña por defecto según el tipo de elemento
    if (element.type === 'machine-time') {
      setActiveTab('forzar');
    } else {
      setActiveTab('points');
    }

    // Si ya hay suplementos asignados, verificar el tipo de tabla
    const elementSupplements = selectedStudy.supplements[element.id];
    if (elementSupplements?.table_type) {
      setSelectedTable(elementSupplements.table_type as TableType);
    }
  };

  // Verificar si el usuario es propietario del estudio
  useEffect(() => {
    if (selectedStudy && user) {
      setIsSharedStudy(selectedStudy.user_id !== user.id);
    }
  }, [selectedStudy, user]);

  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100 p-4 w-full">
        <div className="mt-4">{t('noStudySelected', { ns: 'supplements' })}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 w-full">
      <Header
        title={t('title', { ns: 'supplements' })}
        subtitle={selectedStudy?.required_info?.name || ''}
        showSearch={false}
        showActions={true}
        onBack={onBack}
      />

      <main className="container mx-auto px-4 py-6 w-full">
        {isSharedStudy && (
          <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{t('permission_error.message', { ns: 'study' })}</span>
            <button
              onClick={() => setIsSharedStudy(false)}
              className="ml-auto text-amber-500 hover:text-amber-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {!selectedTable ? (
          <SupplementsTable onSelectTable={(table) => setSelectedTable(table)} />
        ) : !selectedElement ? (
          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow-md p-6 w-full">
              <div className="flex flex-col md:flex-row gap-4 justify-between items-start">
                <div className="flex items-center space-x-4">
                  <img
                    src={`/${selectedTable}-logo.png`}
                    alt={selectedTable.toUpperCase()}
                    className="w-16 h-16 object-contain"
                  />
                  <div className="flex-1">

                    <p className="text-sm text-gray-600">
                   {t(`${selectedTable}Description`, { ns: 'supplements' })}
                    </p>
                  </div>
                </div>
                <div className="w-full md:w-auto">
                  <button
                    onClick={() => setSelectedTable(selectedTable === 'tal' ? 'oit' : 'tal')}
                    className="w-full md:w-auto px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 flex items-center space-x-2"
                  >
                    <span>Cambiar a tablas {selectedTable === 'tal' ? 'OIT' : 'TAL'}</span>
                  </button>
                </div>
              </div>
            </div>

            {elements.map((element, index) => {
              const elementSupplement = selectedStudy.supplements[element.id];
              return (
                <div
                  key={element.id}
                  className="bg-white rounded-lg shadow-md p-6 w-full"
                >
                  <div className="flex items-start justify-between">
                    <div
                      onClick={() => handleElementSelect(element)}
                      className="flex items-center space-x-4 flex-1 cursor-pointer w-full"
                    >
                      <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-lg font-semibold text-purple-600">{element.position + 1}</span>
                      </div>
                      <div className="w-full">
                        <h3 className="font-semibold">{element.description}</h3>
                        <p className="text-sm text-gray-600">
                          {t(`types.${element.repetition_type}`, { ns: 'method' })}
                        </p>
                        {elementSupplement && (
                          <p className="text-sm text-green-600 mt-1">
                            {t('percentage', { ns: 'supplements' })}: {elementSupplement.percentage}%
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {elementSupplement && (
                      <div className="flex flex-col gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setCopyingElementId(element.id);
                            setShowCopyModal(true);
                          }}
                          className="p-2 text-purple-600 hover:bg-purple-50 rounded-lg"
                          title={t('copySupplements.title', { ns: 'supplements' })}
                        >
                          <Copy className="w-5 h-5" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setResettingElementId(element.id);
                            setShowResetModal(true);
                          }}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                          title={t('reset', { ns: 'common' })}
                        >
                          <RotateCcw className="w-5 h-5" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <>
            <div className="flex items-center space-x-4 mb-6 w-full">
              <div className="flex space-x-2">
                <button
                  onClick={handleBackToTables}
                  className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 w-full md:w-auto"
                >
                  <Home className="w-5 h-5" />
                </button>
                <button
                  onClick={handleBackToElements}
                  className="p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 w-full md:w-auto"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <span className="text-lg font-semibold text-gray-800 w-full md:w-auto">
                {selectedElement.description}
              </span>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden w-full">
              <div className="flex border-b">
                <button
                  onClick={() => setActiveTab('points')}
                  className={`flex-1 px-4 py-2 text-sm font-medium ${
                    activeTab === 'points'
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('points', { ns: 'supplements' })}
                </button>
                <button
                  onClick={() => setActiveTab('forzar')}
                  className={`flex-1 px-4 py-2 text-sm font-medium ${
                    activeTab === 'forzar'
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('force', { ns: 'supplements' })}
                </button>
                <button
                  onClick={() => setActiveTab('calculoFatiga')}
                  className={`flex-1 px-4 py-2 text-sm font-medium ${
                    activeTab === 'calculoFatiga'
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('calculoFatiga', { ns: 'supplements' })}
                </button>
              </div>

              <div className="p-4">
                {activeTab === 'points' && (
                  <SupplementsFactors
                    element={selectedElement}
                    tableType={selectedTable}
                    onSaved={handleBackToElements}
                    supplement={selectedStudy.supplements[selectedElement.id]}
                  />
                )}
                {activeTab === 'forzar' && (
                  <SupplementsForce
                    element={selectedElement}
                    onSaved={handleBackToElements}
                    supplement={selectedStudy.supplements[selectedElement.id]}
                  />
                )}
                {activeTab === 'calculoFatiga' && (
                  <SupplementsMachine
                    element={selectedElement}
                    onSaved={handleBackToElements}
                    supplement={selectedStudy.supplements[selectedElement.id]}
                  />
                )}
              </div>
            </div>
          </>
        )}
      </main>

      {showCopyModal && copyingElementId && (
        <CopySupplementsModal
          onClose={() => {
            setShowCopyModal(false);
            setCopyingElementId(null);
          }}
          onCopy={handleCopy}
          elementId={copyingElementId}
        />
      )}

      <DeleteConfirmModal
        isOpen={showResetModal}
        onClose={() => {
          setShowResetModal(false);
          setResettingElementId(null);
        }}
        onConfirm={handleReset}
        title={t('resetSupplements.title', { ns: 'supplements' })}
        message={t('resetSupplements.confirmation', { ns: 'supplements' })}
      />

      {showTableMismatchModal && tableMismatchInfo && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          onClick={() => setShowTableMismatchModal(false)}
        >
          <div 
            className="bg-white rounded-lg w-full max-w-md"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 bg-purple-600 text-white flex items-center justify-between rounded-t-lg">
              <h3 className="text-lg font-semibold">
                {t('tableMismatch.title', { ns: 'supplements' })}
              </h3>
              <button
                onClick={() => setShowTableMismatchModal(false)}
                className="p-2 hover:bg-purple-700 rounded-full"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-4">
              <p className="text-gray-600 mb-4">
                {t('tableMismatch.message', { 
                  ns: 'supplements',
                  table: t(`${tableMismatchInfo.currentTable}Tables`, { ns: 'supplements' })
                })}
              </p>

              <div className="flex justify-end">
                <button
                  onClick={() => setShowTableMismatchModal(false)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                >
                  {t('tableMismatch.close', { ns: 'supplements' })}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};