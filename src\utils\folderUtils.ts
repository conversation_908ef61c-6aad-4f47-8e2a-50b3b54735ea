import { supabase } from '../lib/supabase';
import { Folder, Study, StudyWithFolder } from '../types/index';

/**
 * Obtiene el path completo de una carpeta (equivalente a get_folder_path SQL)
 */
export async function getFolderPath(folderId: string): Promise<string> {
  try {
    const { data: folders, error } = await supabase
      .from('folders')
      .select('id, name, parent_folder_id');

    if (error) throw error;

    const folderMap = new Map(folders.map(f => [f.id, f]));
    const path: string[] = [];
    let currentId: string | null = folderId;

    while (currentId) {
      const folder = folderMap.get(currentId);
      if (!folder) break;
      
      path.unshift(folder.name);
      currentId = folder.parent_folder_id;
    }

    return path.join(' / ');
  } catch (error) {
    console.error('Error getting folder path:', error);
    return '';
  }
}

/**
 * Obtiene todos los estudios de una carpeta de forma recursiva (equivalente a get_folder_studies_recursive SQL)
 */
export async function getFolderStudiesRecursive(folderId: string): Promise<StudyWithFolder[]> {
  try {
    // Primero obtenemos todas las carpetas para construir el árbol
    const { data: folders, error: foldersError } = await supabase
      .from('folders')
      .select('id, parent_folder_id');

    if (foldersError) throw foldersError;

    // Función recursiva para obtener todos los IDs de subcarpetas
    const getAllSubfolderIds = (parentId: string): string[] => {
      const subfolders = folders.filter(f => f.parent_folder_id === parentId);
      let allIds = [parentId];
      
      for (const subfolder of subfolders) {
        allIds = allIds.concat(getAllSubfolderIds(subfolder.id));
      }
      
      return allIds;
    };

    const allFolderIds = getAllSubfolderIds(folderId);

    // Obtenemos todos los estudios de estas carpetas
    const { data: studies, error: studiesError } = await supabase
      .from('studies')
      .select('*')
      .in('folder_id', allFolderIds);

    if (studiesError) throw studiesError;

    return studies.map(study => ({
      ...study,
      folder_id: study.folder_id
    })) as StudyWithFolder[];

  } catch (error) {
    console.error('Error getting folder studies recursively:', error);
    return [];
  }
}

/**
 * Cuenta los estudios en una carpeta (equivalente a count_folder_studies SQL)
 */
export async function countFolderStudies(folderId: string, includeSubfolders = false): Promise<number> {
  try {
    if (includeSubfolders) {
      const studies = await getFolderStudiesRecursive(folderId);
      return studies.length;
    } else {
      const { count, error } = await supabase
        .from('studies')
        .select('*', { count: 'exact', head: true })
        .eq('folder_id', folderId);

      if (error) throw error;
      return count || 0;
    }
  } catch (error) {
    console.error('Error counting folder studies:', error);
    return 0;
  }
}

/**
 * Obtiene todas las carpetas con sus estadísticas
 */
export async function getFoldersWithStats(): Promise<(Folder & { study_count: number; path: string })[]> {
  console.log('📁 getFoldersWithStats: Starting...');
  try {
    const { data: folders, error } = await supabase
      .from('folders')
      .select('*')
      .order('name');

    console.log('📁 getFoldersWithStats: Raw folders query result:', { data: folders, error });

    if (error) throw error;

    // Obtener conteos de estudios para cada carpeta
    const foldersWithStats = await Promise.all((folders || []).map(async (folder: any) => {
      const { count, error: countError } = await supabase
        .from('studies')
        .select('*', { count: 'exact', head: true })
        .eq('folder_id', folder.id);

      if (countError) {
        console.error(`Error counting studies for folder ${folder.id}:`, countError);
      }

      return {
        ...folder,
        study_count: count || 0,
        path: folder.name
      };
    }));

    console.log('📁 getFoldersWithStats: Final result:', foldersWithStats);
    return foldersWithStats;
  } catch (error) {
    console.error('📁 getFoldersWithStats: Error:', error);
    return [];
  }
}

/**
 * Construye el árbol de carpetas con conteos de estudios
 */
export function buildFolderTree(folders: (Folder & { study_count: number })[]): any[] {
  console.log('📁 buildFolderTree (utils): Input folders:', folders);
  
  const buildTree = (parentId?: string | null): any[] => {
    const filtered = folders.filter(folder => 
      (parentId === undefined || parentId === null) 
        ? (folder.parent_folder_id === null || folder.parent_folder_id === undefined)
        : folder.parent_folder_id === parentId
    );
    console.log(`📁 buildFolderTree: Finding children for parent ${parentId}:`, filtered);
    
    return filtered.map(folder => ({
      ...folder,
      children: buildTree(folder.id),
      isExpanded: false,
      hasChildren: folders.some(f => f.parent_folder_id === folder.id)
    }));
  };

  const tree = buildTree();
  console.log('📁 buildFolderTree (utils): Final tree:', tree);
  return tree;
}

/**
 * Valida que una carpeta se pueda mover a otra ubicación (evita ciclos)
 */
export function validateFolderMove(
  folderId: string, 
  targetParentId: string | null, 
  folders: Folder[]
): boolean {
  if (!targetParentId) return true; // Mover a raíz siempre es válido
  if (folderId === targetParentId) return false; // No puede ser padre de sí mismo

  // Verificar que el target no sea descendiente del folder a mover
  const isDescendant = (checkId: string, ancestorId: string): boolean => {
    const folder = folders.find(f => f.id === checkId);
    if (!folder || !folder.parent_folder_id) return false;
    if (folder.parent_folder_id === ancestorId) return true;
    return isDescendant(folder.parent_folder_id, ancestorId);
  };

  return !isDescendant(targetParentId, folderId);
}

/**
 * Obtiene los breadcrumbs de una carpeta
 */
export async function getFolderBreadcrumbs(folderId: string | null): Promise<Array<{ id: string; name: string }>> {
  if (!folderId) return [];

  try {
    const { data: folders, error } = await supabase
      .from('folders')
      .select('id, name, parent_folder_id');

    if (error) throw error;

    const folderMap = new Map((folders as any).map((f: any) => [f.id, f]));
    const breadcrumbs: Array<{ id: string; name: string }> = [];
    let currentId: string | null = folderId;

    while (currentId) {
      const folder = folderMap.get(currentId);
      if (!folder) break;
      
      breadcrumbs.unshift({ id: folder.id, name: folder.name });
      currentId = folder.parent_folder_id;
    }

    return breadcrumbs;
  } catch (error) {
    console.error('Error getting folder breadcrumbs:', error);
    return [];
  }
}

/**
 * Filtra estudios por carpeta actual
 */
export function filterStudiesByFolder(
  studies: Study[], 
  currentFolderId: string | null
): Study[] {
  if (!currentFolderId) {
    // Mostrar solo estudios sin carpeta asignada
    return studies.filter(study => !study.folder_id);
  }
  
  // Mostrar estudios de la carpeta específica
  return studies.filter(study => study.folder_id === currentFolderId);
}

/**
 * Obtiene estudios sin carpeta asignada
 */
export async function getUnorganizedStudies(): Promise<Study[]> {
  try {
    const { data: studies, error } = await supabase
      .from('studies')
      .select('*')
      .is('folder_id', null);

    if (error) throw error;
    return studies || [];
  } catch (error) {
    console.error('Error getting unorganized studies:', error);
    return [];
  }
} 