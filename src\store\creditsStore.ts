import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { UserCredits, SubscriptionPlan, DEFAULT_SUBSCRIPTION_PLANS } from '../types/credits';

interface CreditsState {
  credits: UserCredits | null;
  isLoading: boolean;
  error: string | null;
  subscriptionPlans: SubscriptionPlan[];
  initializeCredits: (userId: string) => Promise<void>;
  useCredit: () => Promise<boolean>;
  refreshCredits: () => Promise<void>;
  buyCredits: (amount: number) => Promise<void>;
  subscribe: (planId: string) => Promise<void>;
  unsubscribe: () => Promise<void>;
}

export const useCreditsStore = create<CreditsState>((set, get) => ({
  credits: null,
  isLoading: false,
  error: null,
  subscriptionPlans: DEFAULT_SUBSCRIPTION_PLANS,

  initializeCredits: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Verificar si el usuario ya tiene créditos
      const { data: existingCredits, error: fetchError } = await supabase
        .from('user_credits')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      if (!existingCredits) {
        // Si no existe, crear un nuevo registro con 1 crédito de prueba
        const { data: newCredits, error: insertError } = await supabase
          .from('user_credits')
          .insert([
            {
              user_id: userId,
              monthly_credits: 0,
              extra_credits: 1, // Crédito de prueba
              credits_used: 0,
              subscription_plan: null,
              subscription_end_date: null
            }
          ])
          .select()
          .single();

        if (insertError) throw insertError;
        set({ credits: newCredits, isLoading: false });
      } else {
        set({ credits: existingCredits, isLoading: false });
      }
    } catch (error) {
      console.error('Error initializing credits:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  useCredit: async () => {
    const { credits } = get();
    if (!credits) return false;

    const availableMonthlyCredits = credits.monthly_credits - credits.credits_used;
    const hasAvailableCredits = availableMonthlyCredits > 0 || credits.extra_credits > 0;

    if (!hasAvailableCredits) return false;

    try {
      const { data, error } = await supabase
        .from('user_credits')
        .update({
          credits_used: availableMonthlyCredits > 0 
            ? credits.credits_used + 1 
            : credits.credits_used,
          extra_credits: availableMonthlyCredits > 0 
            ? credits.extra_credits 
            : credits.extra_credits - 1
        })
        .eq('id', credits.id)
        .select()
        .single();

      if (error) throw error;
      set({ credits: data });
      return true;
    } catch (error) {
      console.error('Error using credit:', error);
      set({ error: (error as Error).message });
      return false;
    }
  },

  refreshCredits: async () => {
    const { credits } = get();
    if (!credits) return;

    set({ isLoading: true, error: null });
    try {
      const { data, error } = await supabase
        .from('user_credits')
        .select('*')
        .eq('id', credits.id)
        .single();

      if (error) throw error;
      set({ credits: data, isLoading: false });
    } catch (error) {
      console.error('Error refreshing credits:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  buyCredits: async (amount: number) => {
    const { credits } = get();
    if (!credits) return;

    set({ isLoading: true, error: null });
    try {
      const { data, error } = await supabase
        .from('user_credits')
        .update({
          extra_credits: (credits.extra_credits || 0) + amount
        })
        .eq('id', credits.id)
        .select()
        .single();

      if (error) throw error;
      set({ credits: data, isLoading: false });
    } catch (error) {
      console.error('Error buying credits:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  subscribe: async (planId: string) => {
    const { credits, subscriptionPlans } = get();
    if (!credits) return;

    const plan = subscriptionPlans.find(p => p.id === planId);
    if (!plan) throw new Error('Plan not found');

    set({ isLoading: true, error: null });
    try {
      // Aquí iría la lógica de pago con Stripe/PayPal

      const { data, error } = await supabase
        .from('user_credits')
        .update({
          subscription_plan: planId,
          monthly_credits: plan.monthly_credits,
          credits_used: 0,
          subscription_end_date: new Date(
            new Date().setMonth(new Date().getMonth() + 1)
          ).toISOString()
        })
        .eq('id', credits.id)
        .select()
        .single();

      if (error) throw error;
      set({ credits: data, isLoading: false });
    } catch (error) {
      console.error('Error subscribing to plan:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  unsubscribe: async () => {
    const { credits } = get();
    if (!credits) return;

    set({ isLoading: true, error: null });
    try {
      // Aquí iría la lógica para cancelar la suscripción en Stripe/PayPal

      const { data, error } = await supabase
        .from('user_credits')
        .update({
          subscription_plan: null,
          monthly_credits: 0,
          subscription_end_date: null
        })
        .eq('id', credits.id)
        .select()
        .single();

      if (error) throw error;
      set({ credits: data, isLoading: false });
    } catch (error) {
      console.error('Error unsubscribing:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  }
}));
