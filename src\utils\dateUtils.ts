import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

export const formatTimeToReset = (resetDate: string | null): string => {
  if (!resetDate) return '';
  
  try {
    const date = new Date(resetDate);
    if (isNaN(date.getTime())) return '';
    
    return formatDistanceToNow(date, { 
      locale: es, 
      addSuffix: true 
    });
  } catch (error) {
    console.error('Error formatting reset date:', error);
    return '';
  }
};
