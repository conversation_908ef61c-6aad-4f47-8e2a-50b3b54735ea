/* Print-specific styles - only apply when printing */
@media print {
  @page {
    size: A4;
    margin: 1cm 0.5cm;
  }

  body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    font-size: 10pt;
    line-height: 1.3;
  }

  button,
  .no-print {
    display: none !important;
  }

  .print-content {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
  }

  .print-header {
    font-size: 14pt;
    font-weight: bold;
    margin-bottom: 0.8cm;
    text-align: center;
  }

  h2 {
    font-size: 12pt;
    margin: 0.5cm 0;
    page-break-after: avoid;
  }

  .print-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.5cm 0;
    page-break-inside: auto;
  }

  .print-table th,
  .print-table td {
    border: 1px solid #000;
    padding: 0.2cm;
    text-align: left;
    font-size: 9pt;
  }

  .print-table th {
    background-color: #f0f0f0;
    font-weight: bold;
  }

  .print-section {
    margin-bottom: 0.8cm;
    page-break-inside: avoid;
  }

  .print-explanation {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: pre-wrap !important;
    word-break: break-word !important;
    font-size: 10pt !important;
    box-sizing: border-box !important;
  }

  pre.print-mono {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: 'Courier New', Courier, monospace !important;
    white-space: pre-wrap !important;
    word-break: break-word !important;
    font-size: 9pt !important;
    box-sizing: border-box !important;
  }

  .explanation-header {
    color: purple !important;
    font-weight: bold !important;
    font-size: 10pt !important;
  }

  /* Ajustar el contenedor principal para maximizar el ancho */
  .supplements-machine {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
}

/* Web display styles - only apply when not printing */
.web-content {
  padding: 1rem;
  max-width: 100%;
  margin: 0 auto;
}

.web-header {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #333;
}

.web-section {
  margin-bottom: 1.5rem;
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.web-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.web-table th,
.web-table td {
  border: 1px solid #e2e8f0;
  padding: 0.75rem;
  text-align: left;
}

.web-table th {
  background-color: #f7fafc;
  font-weight: 600;
  color: #4a5568;
}

.web-table tr:nth-child(even) {
  background-color: #f9fafb;
}

.web-mono {
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  font-size: 0.875rem;
  line-height: 1.5;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
  overflow-x: auto;
}

.explanation-header {
  color: purple;
  font-weight: bold;
}
