/* Enhanced Card Animations */

/* Animación de aparición staggered para grids */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animación de hover suave para tarjetas */
@keyframes cardHover {
  0% {
    transform: translateY(0) scale(1);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
  100% {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

/* Animación de gradient shimmer */
@keyframes gradientShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Efecto de ripple para botones */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Fade in animation para elementos */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Bounce in animation */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Clases utilitarias para animaciones */
.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out forwards;
}

.animate-card-hover:hover {
  animation: cardHover 0.3s ease-out forwards;
}

.animate-gradient-shimmer {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: gradientShimmer 1.5s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out forwards;
}

/* Efecto de flotación para tarjetas */
.card-float {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.card-float:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Efecto de glow para elementos importantes */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  filter: blur(8px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::before {
  opacity: 0.3;
}

/* Staggered animation para listas de tarjetas */
.stagger-animation > * {
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.5s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Responsive grid animations */
@media (max-width: 768px) {
  .card-float:hover {
    transform: translateY(-4px);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .card-float:hover {
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

/* Micro-interactions */
.button-bounce {
  transition: transform 0.1s ease-in-out;
}

.button-bounce:active {
  transform: scale(0.98);
}

/* Loading pulse effect */
.loading-pulse {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: gradientShimmer 1.2s ease-in-out infinite;
  border-radius: 0.5rem;
}

/* Success animation */
@keyframes checkmark {
  0% {
    stroke-dashoffset: 16;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.animate-checkmark {
  stroke-dasharray: 16;
  animation: checkmark 0.3s ease-out forwards;
}

/* Error shake animation */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
} 