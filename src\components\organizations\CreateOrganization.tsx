import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';

export const CreateOrganization: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { createOrganization, error, isLoading } = useOrganizationStore();

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const org = await createOrganization(name, description);
      toast({
        title: t('Organización creada'),
        description: t('La organización se ha creado correctamente. Tu código de invitación es: {{code}}', {
          code: org.invite_code
        }),
        duration: null,
      });
      setName('');
      setDescription('');
    } catch (err) {
      toast({
        title: t('Error'),
        description: error || t('Ha ocurrido un error al crear la organización'),
        variant: 'destructive',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label htmlFor="name" className="text-sm font-medium">
          {t('Nombre de la organización')}
        </label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder={t('Introduce el nombre de la organización')}
          disabled={isLoading}
          required
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">
          {t('Descripción')}
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder={t('Describe brevemente la organización')}
          disabled={isLoading}
          className="w-full min-h-[100px] px-3 py-2 text-sm rounded-md border border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
        />
      </div>

      <Button
        type="submit"
        disabled={isLoading}
        className="w-full"
      >
        {isLoading ? t('Creando...') : t('Crear organización')}
      </Button>
    </form>
  );
};
