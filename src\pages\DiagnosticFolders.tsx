import React, { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';

interface DiagnosticStudy {
  id: string;
  required_info: any;
  folder_id: string | null;
  user_id: string;
}

export const DiagnosticFolders = () => {
  const [studies, setStudies] = useState<DiagnosticStudy[]>([]);
  const [folders, setFolders] = useState<any[]>([]);
  const [selectedStudy, setSelectedStudy] = useState<string>('');
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    try {
      // Obtener usuario actual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('Usuario no autenticado');
        return;
      }

      console.log('📁 Diagnostic - Current user:', user.id);

      // Obtener estudios del usuario actual (sin límite)
      const { data: studiesData, error: studiesError } = await supabase
        .from('studies')
        .select('id, required_info, folder_id, user_id')
        .eq('user_id', user.id);

      if (studiesError) throw studiesError;

      // Obtener carpetas
      const { data: foldersData, error: foldersError } = await supabase
        .from('folders')
        .select('*');

      if (foldersError) throw foldersError;

      setStudies((studiesData as any) || []);
      setFolders((foldersData as any) || []);
      
      console.log('📁 Diagnostic - Studies:', studiesData);
      console.log('📁 Diagnostic - Folders:', foldersData);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const moveStudy = async () => {
    if (!selectedStudy) {
      alert('Selecciona un estudio');
      return;
    }

    setLoading(true);
    try {
      const folderId = selectedFolder === 'null' ? null : selectedFolder;
      
      console.log('📁 Diagnostic - Moving study', selectedStudy, 'to folder', folderId);
      
      const { data, error } = await supabase
        .from('studies')
        .update({ folder_id: folderId } as any)
        .eq('id', selectedStudy)
        .select();

      if (error) throw error;
      
      console.log('📁 Diagnostic - Move result:', data);
      
      // Refrescar datos
      await fetchData();
      
      alert('Estudio movido exitosamente');
    } catch (error) {
      console.error('Error moving study:', error);
      alert('Error: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Diagnóstico de Carpetas</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Estudios */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Estudios</h2>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {studies.map(study => (
              <div 
                key={study.id}
                className={`p-2 border rounded cursor-pointer ${selectedStudy === study.id ? 'bg-blue-100 border-blue-500' : 'border-gray-200'}`}
                onClick={() => setSelectedStudy(study.id)}
              >
                <div className="font-medium">{study.required_info?.name || 'Sin nombre'}</div>
                <div className="text-sm text-gray-500">
                  Carpeta: {study.folder_id || 'Raíz'}
                </div>
                <div className="text-xs text-gray-400">ID: {study.id}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Carpetas */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Carpetas</h2>
          <div className="space-y-2">
            <div 
              className={`p-2 border rounded cursor-pointer ${selectedFolder === 'null' ? 'bg-green-100 border-green-500' : 'border-gray-200'}`}
              onClick={() => setSelectedFolder('null')}
            >
              <div className="font-medium">🏠 Raíz (Sin carpeta)</div>
            </div>
            {folders.map(folder => (
              <div 
                key={folder.id}
                className={`p-2 border rounded cursor-pointer ${selectedFolder === folder.id ? 'bg-green-100 border-green-500' : 'border-gray-200'}`}
                onClick={() => setSelectedFolder(folder.id)}
              >
                <div className="font-medium">📁 {folder.name}</div>
                <div className="text-xs text-gray-400">ID: {folder.id}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Controles */}
      <div className="mt-6 bg-white p-4 rounded-lg shadow">
        <h3 className="font-semibold mb-4">Mover Estudio</h3>
        <div className="flex gap-4 items-center">
          <div>
            <strong>Estudio seleccionado:</strong> {selectedStudy || 'Ninguno'}
          </div>
          <div>
            <strong>Carpeta destino:</strong> {selectedFolder === 'null' ? 'Raíz' : selectedFolder || 'Ninguna'}
          </div>
          <button
            onClick={moveStudy}
            disabled={!selectedStudy || loading}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
          >
            {loading ? 'Moviendo...' : 'Mover Estudio'}
          </button>
        </div>
      </div>

      {/* Refrescar */}
      <div className="mt-4">
        <button
          onClick={fetchData}
          className="px-4 py-2 bg-gray-500 text-white rounded"
        >
          🔄 Refrescar Datos
        </button>
      </div>
    </div>
  );
}; 