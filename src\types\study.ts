import { ElementInstance, TimeRecord, ElementSupplements, TimeUnit } from './index';

export interface StudyInfo {
  required: {
    name: string;
    company: string;
    date: string;
  };
  optional?: {
    study_number?: string;
    operator?: string;
    section?: string;
    reference?: string;
    machine?: string;
    tools?: string;
    technician?: string;
  };
}

export interface Study {
  id: string;
  user_id: string;
  name?: string | number;  // Campo opcional ya que puede estar en info.required.name
  required_info: {
    name: string;
    company: string;
    date: string;
    activity_scale: {
      normal: number;
      optimal: number;
    };
  };
  optional_info?: {
    study_number?: string;
    operator?: string;
    section?: string;
    reference?: string;
    machine?: string;
    tools?: string;
    technician?: string;
    studyContingency?: number;
    visibleEnBiblioteca?: boolean;
    reportSettings?: {
      timeUnit?: TimeUnit;
      shiftMinutes?: number;
      contingency?: number;
    };
    customUnits?: {
      enableSquareMeters: boolean;
      enableLinearMeters: boolean;
      enableCubicMeters: boolean;
      enableKilos: boolean;
      enablePerimeter: boolean;
      squareMeters: {
        length: number;
        width: number;
      };
      linearMeters: {
        length: number;
      };
      cubicMeters: {
        length: number;
        width: number;
        height: number;
      };
      kilos: {
        weight: number;
      };
      perimeter: {
        length: number;
        width: number;
      };
    };
  };
  info?: {
    required: {
      name: string;
      company: string;
      date: string;
      activity_scale?: {
        normal: number;
        optimal: number;
      };
    };
    optional?: {
      study_number?: string;
      operator?: string;
      section?: string;
      reference?: string;
      machine?: string;
      tools?: string;
      technician?: string;
      visibleEnBiblioteca?: boolean;
      customUnits?: {
        enableSquareMeters: boolean;
        enableLinearMeters: boolean;
        enableCubicMeters: boolean;
        enableKilos: boolean;
        enablePerimeter: boolean;
        squareMeters: {
          length: number;
          width: number;
        };
        linearMeters: {
          length: number;
        };
        cubicMeters: {
          length: number;
          width: number;
          height: number;
        };
        kilos: {
          weight: number;
        };
        perimeter: {
          length: number;
          width: number;
        };
      };
    };
  };
  description?: string;
  elements: ElementInstance[];  //
  time_records: Record<string, TimeRecord[]>;  //
  supplements?: Record<string, ElementSupplements>;
  settings?: {
    timeUnit: 'minutes' | 'hours' | 'seconds';
    shiftMinutes: number;
    contingency: number;
  };
  machine_cycle_data?: {
    totalCycleTime: number;
    baseTime: number;
    personalNeedsSupplement: number;
    fatigueSupplement: number;
    remainingFatigue?: number;
    inactivityTime: number;
    calculationCase: string;
    timestamp: string;
  };
  cycle_preference?: 'report' | 'supplements';
  status?: string;
  created_at: string;
  updated_at: string;
  crono_seguido_records?: any[];
  organization_id?: string | null;
  is_shared?: boolean;
}

export interface StudyExport {
  version: string;
  timestamp: string;
  studies: Study[];
}
