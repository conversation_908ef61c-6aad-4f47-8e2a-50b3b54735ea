import { supabase } from '../lib/supabaseClient';
import { UserCredits, SubscriptionPlan } from '../types/credits';

class CreditService {
  private readonly TABLE_NAME = 'user_study_limits';
  private readonly FREE_MONTHLY_CREDITS = 1;

  async getUserCredits(userId: string): Promise<UserCredits | null> {
    try {
      // Intentar obtener los créditos existentes
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      // Si hay un error que no sea "no data found", lanzarlo
      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching credits:', error);
        return null;
      }

      // Si no hay datos, crear nuevos créditos
      if (!data) {
        // Obtener el email del usuario desde la sesión actual
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user?.email) {
          console.error('No session or email available');
          return null;
        }
        return await this.createUserCredits(userId, session.user.email);
      }

      // Verificar si tiene suscripción activa
      if (data.subscription_plan && data.subscription_end_date) {
        const endDate = new Date(data.subscription_end_date);
        if (endDate > new Date()) {
          return {
            ...data,
            monthly_credits: Infinity,
            extra_credits: 0,
            used_monthly_credits: 0,
            used_extra_credits: 0,
            hasUnlimitedCredits: true
          };
        }
      }

      // Verificar si necesitamos renovar los créditos mensuales gratuitos
      if (this.shouldResetCredits(data.reset_date)) {
        const updatedData = await this.resetMonthlyCredits(userId);
        if (updatedData) return updatedData;
      }

      return {
        ...data,
        hasUnlimitedCredits: false
      };
    } catch (error) {
      console.error('Error fetching user credits:', error);
      return null;
    }
  }

  async createUserCredits(userId: string, userEmail: string): Promise<UserCredits | null> {
    try {
      const now = new Date().toISOString();
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .upsert({
          user_id: userId,
          user_email: userEmail,
          monthly_credits: this.FREE_MONTHLY_CREDITS,
          extra_credits: 0,
          used_monthly_credits: 0,
          used_extra_credits: 0,
          reset_date: this.getNextResetDate(),
          subscription_plan: null,
          subscription_start_date: null,
          subscription_end_date: null,
          stripe_subscription_id: null,
          created_at: now,
          updated_at: now
        }, {
          onConflict: 'user_id'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating user credits:', error);
        return null;
      }
      return {
        ...data,
        hasUnlimitedCredits: false
      };
    } catch (error) {
      console.error('Error creating user credits:', error);
      return null;
    }
  }

  private shouldResetCredits(resetDate: string): boolean {
    const resetMonth = new Date(resetDate);
    const now = new Date();
    return (
      now.getFullYear() > resetMonth.getFullYear() ||
      (now.getFullYear() === resetMonth.getFullYear() && 
       now.getMonth() > resetMonth.getMonth())
    );
  }

  private async resetMonthlyCredits(userId: string): Promise<UserCredits | null> {
    try {
      const nextResetDate = this.getNextResetDate();
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .update({
          monthly_credits: this.FREE_MONTHLY_CREDITS,
          used_monthly_credits: 0,
          reset_date: nextResetDate,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error resetting monthly credits:', error);
        return null;
      }

      return {
        ...data,
        hasUnlimitedCredits: false
      };
    } catch (error) {
      console.error('Error resetting monthly credits:', error);
      return null;
    }
  }

  private getNextResetDate(): string {
    const now = new Date();
    // Establecer la fecha al primer día del mes actual
    return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
  }

  async updateSubscriptionStatus(userId: string, plan: SubscriptionPlan | null): Promise<boolean> {
    try {
      const { error } = await supabase
        .from(this.TABLE_NAME)
        .update({
          subscription_plan: plan,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        console.error('Error updating subscription status:', error);
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error updating subscription status:', error);
      return false;
    }
  }
}

export const creditService = new CreditService();
