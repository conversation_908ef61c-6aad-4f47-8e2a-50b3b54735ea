-- ================================================
-- ESTRUCTURA DE BASE DE DATOS PARA CRONOMETRAS
-- Migración completa con RLS para organizaciones
-- ================================================

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ================================================
-- TABLAS PRINCIPALES
-- ================================================

-- Tabla de perfiles de usuario
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    avatar_url TEXT,
    logo_url TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de organizaciones
CREATE TABLE IF NOT EXISTS organizations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES profiles(id) NOT NULL,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de miembros de organizaciones
CREATE TABLE IF NOT EXISTS organization_members (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    permissions JSONB DEFAULT '{}',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);

-- Tabla de solicitudes de unión a organizaciones
CREATE TABLE IF NOT EXISTS organization_join_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    message TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES profiles(id),
    UNIQUE(organization_id, user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- Tabla de carpetas
CREATE TABLE IF NOT EXISTS folders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
    color VARCHAR(7) DEFAULT '#8B5CF6',
    icon VARCHAR(50) DEFAULT 'folder',
    is_shared BOOLEAN DEFAULT false,
    study_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_name_length CHECK (length(name) >= 1 AND length(name) <= 255),
    CONSTRAINT check_description_length CHECK (description IS NULL OR length(description) <= 500),
    CONSTRAINT check_color_format CHECK (color ~ '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT prevent_self_reference CHECK (id != parent_folder_id)
);

-- Tabla de estudios
CREATE TABLE IF NOT EXISTS studies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
    name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'draft',
    
    -- Información del estudio
    required_info JSONB NOT NULL DEFAULT '{}',
    optional_info JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    
    -- Datos del estudio
    elements JSONB DEFAULT '[]',
    time_records JSONB DEFAULT '{}',
    supplements JSONB DEFAULT '{}',
    crono_seguido_records JSONB DEFAULT '[]',
    
    -- Configuración de compartición
    is_shared BOOLEAN DEFAULT false,
    share_settings JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_required_info_not_empty CHECK (jsonb_typeof(required_info) = 'object' AND required_info != '{}'),
    CONSTRAINT check_elements_is_array CHECK (jsonb_typeof(elements) = 'array'),
    CONSTRAINT check_time_records_is_object CHECK (jsonb_typeof(time_records) = 'object'),
    CONSTRAINT check_supplements_is_object CHECK (jsonb_typeof(supplements) = 'object'),
    CONSTRAINT check_crono_seguido_is_array CHECK (jsonb_typeof(crono_seguido_records) = 'array')
);

-- Tabla de créditos (si se usa sistema de créditos)
CREATE TABLE IF NOT EXISTS user_credits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    credits INTEGER DEFAULT 0,
    max_credits INTEGER DEFAULT 100,
    reset_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, organization_id)
);

-- ================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- ================================================

-- Índices para profiles
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_company ON profiles(company_name);

-- Índices para organizations
CREATE INDEX IF NOT EXISTS idx_organizations_owner ON organizations(owner_id);
CREATE INDEX IF NOT EXISTS idx_organizations_active ON organizations(is_active);

-- Índices para organization_members
CREATE INDEX IF NOT EXISTS idx_org_members_org_id ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_org_members_user_id ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_members_role ON organization_members(role);

-- Índices para organization_join_requests
CREATE INDEX IF NOT EXISTS idx_join_requests_org_id ON organization_join_requests(organization_id);
CREATE INDEX IF NOT EXISTS idx_join_requests_user_id ON organization_join_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_join_requests_status ON organization_join_requests(status);

-- Índices para folders
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON folders(user_id);
CREATE INDEX IF NOT EXISTS idx_folders_org_id ON folders(organization_id);
CREATE INDEX IF NOT EXISTS idx_folders_parent ON folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_folders_shared ON folders(is_shared);

-- Índices para studies
CREATE INDEX IF NOT EXISTS idx_studies_user_id ON studies(user_id);
CREATE INDEX IF NOT EXISTS idx_studies_org_id ON studies(organization_id);
CREATE INDEX IF NOT EXISTS idx_studies_folder_id ON studies(folder_id);
CREATE INDEX IF NOT EXISTS idx_studies_shared ON studies(is_shared);
CREATE INDEX IF NOT EXISTS idx_studies_created_at ON studies(created_at);
CREATE INDEX IF NOT EXISTS idx_studies_updated_at ON studies(updated_at);

-- Índices para user_credits
CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON user_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_user_credits_org_id ON user_credits(organization_id);

-- ================================================
-- FUNCIONES AUXILIARES
-- ================================================

-- Función para verificar si un usuario pertenece a una organización
CREATE OR REPLACE FUNCTION user_belongs_to_organization(user_uuid UUID, org_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members 
        WHERE user_id = user_uuid 
        AND organization_id = org_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para obtener rol de usuario en organización
CREATE OR REPLACE FUNCTION get_user_role_in_organization(user_uuid UUID, org_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role 
    FROM organization_members 
    WHERE user_id = user_uuid 
    AND organization_id = org_uuid;
    
    RETURN COALESCE(user_role, 'none');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ================================================
-- TRIGGERS PARA UPDATED_AT
-- ================================================

CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_organizations_updated_at
    BEFORE UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_folders_updated_at
    BEFORE UPDATE ON folders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_studies_updated_at
    BEFORE UPDATE ON studies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_credits_updated_at
    BEFORE UPDATE ON user_credits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ================================================
-- HABILITAR ROW LEVEL SECURITY (RLS)
-- ================================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_join_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE studies ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;

-- ================================================
-- POLÍTICAS RLS PARA PROFILES
-- ================================================

-- Los usuarios pueden ver y editar su propio perfil
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Los miembros de organización pueden ver perfiles básicos de otros miembros
CREATE POLICY "Organization members can view member profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organization_members om1
            JOIN organization_members om2 ON om1.organization_id = om2.organization_id
            WHERE om1.user_id = auth.uid()
            AND om2.user_id = profiles.id
        )
    );

-- ================================================
-- POLÍTICAS RLS PARA ORGANIZATIONS
-- ================================================

-- Ver organizaciones propias o donde soy miembro
CREATE POLICY "Users can view own organizations" ON organizations
    FOR SELECT USING (
        owner_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM organization_members
            WHERE organization_id = organizations.id
            AND user_id = auth.uid()
        )
    );

-- Solo el propietario puede actualizar la organización
CREATE POLICY "Organization owners can update" ON organizations
    FOR UPDATE USING (owner_id = auth.uid());

-- Cualquier usuario autenticado puede crear organizaciones
CREATE POLICY "Authenticated users can create organizations" ON organizations
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- Solo el propietario puede eliminar la organización
CREATE POLICY "Organization owners can delete" ON organizations
    FOR DELETE USING (owner_id = auth.uid());

-- ================================================
-- POLÍTICAS RLS PARA ORGANIZATION_MEMBERS
-- ================================================

-- Ver miembros de organizaciones donde soy miembro
CREATE POLICY "Organization members can view members" ON organization_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organization_members.organization_id
            AND om.user_id = auth.uid()
        )
    );

-- Solo propietarios y admins pueden agregar miembros
CREATE POLICY "Organization admins can insert members" ON organization_members
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id AND om.user_id = auth.uid()
            WHERE o.id = organization_members.organization_id
            AND (o.owner_id = auth.uid() OR om.role IN ('owner', 'admin'))
        )
    );

-- Solo propietarios y admins pueden actualizar miembros (excepto cambiar el rol del propietario)
CREATE POLICY "Organization admins can update members" ON organization_members
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id AND om.user_id = auth.uid()
            WHERE o.id = organization_members.organization_id
            AND (o.owner_id = auth.uid() OR om.role IN ('owner', 'admin'))
        )
    );

-- Los usuarios pueden salir de la organización (eliminar su propia membresía)
CREATE POLICY "Users can leave organizations" ON organization_members
    FOR DELETE USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id AND om.user_id = auth.uid()
            WHERE o.id = organization_members.organization_id
            AND (o.owner_id = auth.uid() OR om.role IN ('owner', 'admin'))
        )
    );

-- ================================================
-- POLÍTICAS RLS PARA ORGANIZATION_JOIN_REQUESTS
-- ================================================

-- Los usuarios pueden ver sus propias solicitudes
CREATE POLICY "Users can view own join requests" ON organization_join_requests
    FOR SELECT USING (user_id = auth.uid());

-- Los admins pueden ver solicitudes de su organización
CREATE POLICY "Organization admins can view join requests" ON organization_join_requests
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id AND om.user_id = auth.uid()
            WHERE o.id = organization_join_requests.organization_id
            AND (o.owner_id = auth.uid() OR om.role IN ('owner', 'admin'))
        )
    );

-- Los usuarios pueden crear solicitudes para unirse
CREATE POLICY "Users can create join requests" ON organization_join_requests
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Solo admins pueden procesar solicitudes
CREATE POLICY "Organization admins can update join requests" ON organization_join_requests
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id AND om.user_id = auth.uid()
            WHERE o.id = organization_join_requests.organization_id
            AND (o.owner_id = auth.uid() OR om.role IN ('owner', 'admin'))
        )
    );

-- ================================================
-- POLÍTICAS RLS PARA FOLDERS
-- ================================================

-- Ver carpetas propias o de miembros de la organización
CREATE POLICY "Users can view accessible folders" ON folders
    FOR SELECT USING (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        is_shared = true
    );

-- Solo el propietario puede crear carpetas
CREATE POLICY "Users can create own folders" ON folders
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Solo el propietario puede actualizar sus carpetas
CREATE POLICY "Users can update own folders" ON folders
    FOR UPDATE USING (user_id = auth.uid());

-- Solo el propietario puede eliminar sus carpetas
CREATE POLICY "Users can delete own folders" ON folders
    FOR DELETE USING (user_id = auth.uid());

-- ================================================
-- POLÍTICAS RLS PARA STUDIES
-- ================================================

-- Ver estudios propios, de miembros de organización, o compartidos
CREATE POLICY "Users can view accessible studies" ON studies
    FOR SELECT USING (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        is_shared = true
    );

-- Solo el propietario puede crear estudios
CREATE POLICY "Users can create own studies" ON studies
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Solo el propietario puede actualizar sus estudios
CREATE POLICY "Users can update own studies" ON studies
    FOR UPDATE USING (user_id = auth.uid());

-- Solo el propietario puede eliminar sus estudios
CREATE POLICY "Users can delete own studies" ON studies
    FOR DELETE USING (user_id = auth.uid());

-- ================================================
-- POLÍTICAS RLS PARA USER_CREDITS
-- ================================================

-- Ver créditos propios o de miembros de organización (solo admins)
CREATE POLICY "Users can view accessible credits" ON user_credits
    FOR SELECT USING (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND 
         get_user_role_in_organization(auth.uid(), organization_id) IN ('owner', 'admin'))
    );

-- Solo el propietario puede actualizar sus créditos
CREATE POLICY "Users can update own credits" ON user_credits
    FOR UPDATE USING (user_id = auth.uid());

-- Los usuarios y admins de organización pueden crear créditos
CREATE POLICY "Users can create credits" ON user_credits
    FOR INSERT WITH CHECK (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND 
         get_user_role_in_organization(auth.uid(), organization_id) IN ('owner', 'admin'))
    );

-- ================================================
-- FUNCIONES PARA GESTIÓN DE DATOS
-- ================================================

-- Función para obtener estudios de una carpeta recursivamente
CREATE OR REPLACE FUNCTION get_folder_studies_recursive(folder_uuid UUID)
RETURNS TABLE(study_id UUID) AS $$
WITH RECURSIVE folder_tree AS (
    -- Caso base: la carpeta especificada
    SELECT id, parent_folder_id
    FROM folders
    WHERE id = folder_uuid
    
    UNION ALL
    
    -- Caso recursivo: subcarpetas
    SELECT f.id, f.parent_folder_id
    FROM folders f
    INNER JOIN folder_tree ft ON f.parent_folder_id = ft.id
)
SELECT s.id as study_id
FROM studies s
WHERE s.folder_id IN (SELECT id FROM folder_tree);
$$ LANGUAGE SQL SECURITY DEFINER;

-- Función para contar estudios en una carpeta
CREATE OR REPLACE FUNCTION count_folder_studies(folder_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    study_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO study_count
    FROM get_folder_studies_recursive(folder_uuid);
    
    RETURN COALESCE(study_count, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================
-- DATOS INICIALES (OPCIONAL)
-- ================================================

-- Insertar organización por defecto si es necesario
-- INSERT INTO organizations (id, name, description, owner_id)
-- VALUES ('00000000-0000-0000-0000-000000000000', 'Organización General', 'Organización por defecto del sistema', '00000000-0000-0000-0000-000000000000')
-- ON CONFLICT (id) DO NOTHING;

-- ================================================
-- COMENTARIOS FINALES
-- ================================================

COMMENT ON TABLE profiles IS 'Perfiles de usuario con información básica y configuraciones';
COMMENT ON TABLE organizations IS 'Organizaciones que agrupan usuarios y contenido';
COMMENT ON TABLE organization_members IS 'Relación de membresía entre usuarios y organizaciones';
COMMENT ON TABLE organization_join_requests IS 'Solicitudes de unión a organizaciones';
COMMENT ON TABLE folders IS 'Carpetas para organizar estudios jerárquicamente';
COMMENT ON TABLE studies IS 'Estudios de tiempos y movimientos';
COMMENT ON TABLE user_credits IS 'Sistema de créditos por usuario/organización';

-- ================================================
-- FIN DEL SCRIPT
-- ================================================

-- Para verificar que todo se creó correctamente:
-- SELECT schemaname, tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename; 