import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock } from 'lucide-react';
import { WorkElement } from '../types';

interface FrequencyElementListProps {
  elements: WorkElement[];
  allElements: WorkElement[]; // Lista completa de elementos para calcular índices
  records: Record<string, any[]>;
  isLoading: boolean;
  onElementClick: (element: WorkElement) => void;
}

export const FrequencyElementList: React.FC<FrequencyElementListProps> = ({
  elements,
  allElements,
  records,
  isLoading,
  onElementClick
}) => {
  const { t } = useTranslation(['frequency', 'common']);

  const getElementStats = (elementId: string) => {
    const elementRecords = records[elementId] || [];
    if (elementRecords.length === 0) return null;
    
    const times = elementRecords.map(r => r.time);
    const activities = elementRecords.map(r => r.activity);
    
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const avgActivity = activities.reduce((sum, activity) => sum + activity, 0) / activities.length;
    
    return {
      averageTime: avgTime.toFixed(2),
      averageActivity: avgActivity.toFixed(0)
    };
  };

  const formatFrequency = (element: WorkElement) => {
    return `${element.frequency_repetitions} ${t('repetitions', { ns: 'frequency' })} ${t('each', { ns: 'common' })} ${element.frequency_cycles} ${t('cycles', { ns: 'frequency' })}`;
  };

  const getElementIndex = (element: WorkElement) => {
    // Encontrar el índice del elemento en la lista completa de elementos
    return allElements.findIndex(e => e.id === element.id) + 1;
  };

  if (isLoading) {
    return (
      <div className="text-center py-8 text-gray-600">
        {t('loading', { ns: 'common' })}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {elements.map((element) => {
        const stats = getElementStats(element.id);
        const elementIndex = getElementIndex(element);
        return (
          <div
            key={element.id}
            onClick={() => onElementClick(element)}
            className="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:bg-gray-50"
          >
            <div className="flex flex-col sm:flex-row sm:items-start space-y-3 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center sm:block">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 text-lg sm:text-xl font-bold">
                    {String(elementIndex).padStart(2, '0')}
                  </span>
                </div>
              </div>
              <div className="flex-1 space-y-2">
                <h3 className="font-semibold text-lg">{element.description}</h3>
                <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded-md">
                  {formatFrequency(element)}
                </div>
                {stats && (
                  <div className="flex flex-wrap gap-4 mt-2">
                    <div className="text-sm text-gray-600">
                      {t('averageActivity', { ns: 'frequency' })}: {stats.averageActivity}
                    </div>
                    <div className="flex items-center text-sm font-medium text-gray-800">
                      <Clock className="w-4 h-4 mr-1" />
                      {stats.averageTime}s
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};