# Time Record List Component

## Descripción
Lista de registros de tiempo con funcionalidades de edición.

## Props
```typescript
interface TimeRecordListProps {
  element: WorkElement;
  records: TimeRecord[];
  onDeleteRecord: (recordId: string) => void;
  onEditRecord: (record: TimeRecord) => void;
  onAddComment: (record: TimeRecord) => void;
  onDeleteAll: () => void;
}
```

## Características
- Lista de tiempos registrados
- Acciones por registro:
  - Edición de tiempo
  - Edición de actividad
  - Añadir/editar comentario
  - Eliminar registro
- Eliminación masiva
- Ordenación por timestamp
- Estadísticas por registro

## Uso
```tsx
<TimeRecordList
  element={currentElement}
  records={elementRecords}
  onDeleteRecord={handleDelete}
  onEditRecord={handleEdit}
  onAddComment={handleComment}
  onDeleteAll={handleDeleteAll}
/>
```