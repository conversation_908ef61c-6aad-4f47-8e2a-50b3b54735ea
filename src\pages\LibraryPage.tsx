import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>Lef<PERSON>, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useElementSearchStore } from '../store/elementSearchStore';
import { StudyLibraryList } from '../components/library/StudyLibraryList';
import { SelectedElementsPanel } from '../components/library/SelectedElementsPanel';
import { LibrarySearchBar } from '../components/library/LibrarySearchBar';
import { useLibraryData } from '../hooks/useLibraryData';
import { ElementInstance, Study } from '../types';
import { supabase } from '../lib/supabase';

export const LibraryPage: React.FC = () => {
  const { t } = useTranslation(['library', 'common']);
  const navigate = useNavigate();
  const {
    searchResults,
    isLoading,
    searchElements
  } = useElementSearchStore();

  const [showSelectedPanel, setShowSelectedPanel] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedElements, setSelectedElements] = useState<Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>>([]);
  const [filters, setFilters] = useState<{
    company?: string;
    dateFrom?: string;
    dateTo?: string;
    hasElements?: boolean;
  }>({});
  const [isCreatingStudy, setIsCreatingStudy] = useState(false);

  // Usar el hook personalizado para manejar los datos de la biblioteca
  const { studiesWithElements, availableCompanies, stats } = useLibraryData({
    searchResults,
    searchTerm,
    filters
  });

  useEffect(() => {
    searchElements('');
  }, [searchElements]);

  // Realizar búsqueda cuando cambie el término
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchElements(searchTerm);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, searchElements]);

  const handleBack = () => {
    navigate('/');
  };

  const handleElementToggle = (element: ElementInstance, studyName: string, studyId: string) => {
    setSelectedElements(prev => {
      const isSelected = prev.some(sel => sel.element.id === element.id && sel.studyId === studyId);

      if (isSelected) {
        return prev.filter(sel => !(sel.element.id === element.id && sel.studyId === studyId));
      } else {
        return [...prev, { element, studyName, studyId }];
      }
    });
  };

  const handleRemoveElement = (elementId: string, studyId: string) => {
    setSelectedElements(prev =>
      prev.filter(sel => !(sel.element.id === elementId && sel.studyId === studyId))
    );
  };

  const handleClearAll = () => {
    setSelectedElements([]);
  };

  const handleCreateStudy = async (studyData: {
    name: string;
    company: string;
    date: string;
    normalActivity: number;
    optimalActivity: number;
  }) => {
    setIsCreatingStudy(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Crear suplementos para cada elemento seleccionado
      const supplements: Record<string, any> = {
        elements: {}
      };

      // Mapear elementos para el nuevo estudio
      const elements = selectedElements.map((sel, index) => {
        const elementObj: any = {
          id: sel.element.id,
          name: sel.element.name || sel.element.description || `Elemento ${index + 1}`,
          description: sel.element.description || '',
          time: sel.element.time || 0,
          type: sel.element.type || 'machine-stopped',
          position: index + 1,
          repetition_type: sel.element.repetition_type || 'repetitive',
          frequency_cycles: sel.element.frequency_cycles || 1,
          frequency_repetitions: sel.element.frequency_repetitions || 1,
          activity: sel.element.activity || 100
        };

        // Añadir los suplementos al elemento
        if (sel.element.supplements && sel.element.supplements.length > 0) {
          const existingSupplement = sel.element.supplements[0];

          supplements[sel.element.id] = {
            points: existingSupplement.points || {},
            is_forced: existingSupplement.is_forced || false,
            percentage: existingSupplement.percentage || 0,
            factor_selections: existingSupplement.factor_selections || {}
          };

          elementObj.supplements = [{
            points: existingSupplement.points || {},
            is_forced: existingSupplement.is_forced || false,
            percentage: existingSupplement.percentage || 0,
            factor_selections: existingSupplement.factor_selections || {}
          }];
        } else {
          supplements[sel.element.id] = {
            points: {},
            is_forced: false,
            percentage: 0,
            factor_selections: {}
          };

          elementObj.supplements = [{
            points: {},
            is_forced: false,
            percentage: 0,
            factor_selections: {}
          }];
        }

        return elementObj;
      }).filter(el => el.id) as ElementInstance[];

      // Crear registros de tiempo
      const time_records: Record<string, any> = {};
      selectedElements.forEach(sel => {
        if (sel.element.id) {
          if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
            time_records[sel.element.id] = sel.element.timeRecords.map(record => ({
              id: crypto.randomUUID(),
              time: record.time,
              activity: record.activity,
              elementId: sel.element.id,
              timestamp: record.timestamp || new Date().toISOString(),
              comment: record.comment || sel.element.description || ''
            }));
          } else if (sel.element.time) {
            time_records[sel.element.id] = [{
              id: crypto.randomUUID(),
              time: sel.element.time,
              activity: sel.element.activity || 100,
              elementId: sel.element.id,
              timestamp: new Date().toISOString(),
              comment: sel.element.description || ''
            }];
          }
        }
      });

      // Crear el objeto de estudio completo
      const study: Study = {
        id: crypto.randomUUID(),
        user_id: user.id,
        required_info: {
          name: studyData.name,
          company: studyData.company,
          date: studyData.date,
          activity_scale: {
            normal: studyData.normalActivity,
            optimal: studyData.optimalActivity
          }
        },
        optional_info: {
          tools: '',
          machine: '',
          section: '',
          operator: '',
          reference: '',
          technician: '',
          study_number: '',
          isEstimated: true
        },
        elements,
        time_records,
        supplements,
        crono_seguido_records: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Crear el estudio en la base de datos
      const { error } = await supabase
        .from('studies')
        .insert([study]);

      if (error) throw error;

      // Limpiar selección y cerrar panel
      setSelectedElements([]);
      setShowSelectedPanel(false);

      // Navegar a la página de estudios
      navigate('/');
    } catch (error) {
      console.error('Error creating study:', error);
      throw error;
    } finally {
      setIsCreatingStudy(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title={t('back', { ns: 'common' })}
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                {t('title', { ns: 'library' })}
              </h1>
            </div>

            {selectedElements.length > 0 && (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-600">
                  {selectedElements.length} {t('selected', { ns: 'common' })}
                </span>
                <button
                  type="button"
                  onClick={() => setShowSelectedPanel(true)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
                >
                  {t('viewSelection', { ns: 'library' })}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Search Bar */}
          <LibrarySearchBar
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            filters={filters}
            onFiltersChange={setFilters}
            availableCompanies={availableCompanies}
          />

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
              <span className="ml-2 text-gray-600">{t('loading', { ns: 'common' })}</span>
            </div>
          )}

          {/* Studies List */}
          {!isLoading && (
            <StudyLibraryList
              studies={studiesWithElements}
              selectedElements={selectedElements}
              onElementToggle={handleElementToggle}
              isLoading={isLoading}
            />
          )}
        </div>
      </main>

      {/* Selected Elements Panel */}
      <SelectedElementsPanel
        selectedElements={selectedElements}
        onRemoveElement={handleRemoveElement}
        onClearAll={handleClearAll}
        onCreateStudy={handleCreateStudy}
        isVisible={showSelectedPanel}
        onToggleVisibility={() => setShowSelectedPanel(!showSelectedPanel)}
        isCreatingStudy={isCreatingStudy}
      />
    </div>
  );
};