import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Calculator, Search } from 'lucide-react';
import { Header } from '../components/Header';
import { useElementSearchStore } from '../store/elementSearchStore';
import { ElementInstance, Study } from '../types';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import { NoCreditsModal } from '../components/modals/NoCreditsModal';

export const LibraryPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    searchResults,
    selectedElements,
    isLoading,
    error: searchError,
    searchElements,
    toggleElementSelection,
    createNewStudyFromSelection,
    addAveragedElement,
    clearSelection
  } = useElementSearchStore();

  // Estado local solo para UI
  const [activeTab, setActiveTab] = useState<'library' | 'average'>('library');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNoCreditsModal, setShowNoCreditsModal] = useState(false);
  const [newStudyData, setNewStudyData] = useState({
    name: '',
    company: '',
    date: new Date().toISOString().split('T')[0],
    normalActivity: 100,
    optimalActivity: 133
  });
  const [averagedElement, setAveragedElement] = useState<ElementInstance | null>(null);

  // Después de tus declaraciones de estado existentes, añade estos dos estados nuevos:
  const [librarySelectedElements, setLibrarySelectedElements] = useState<Array<{
    element: any; 
    studyName: string; 
    studyId: string;
  }>>([]);

  const [averageSelectedElements, setAverageSelectedElements] = useState<Array<{
    element: any; 
    studyName: string; 
    studyId: string;
  }>>([]);

  // En su lugar, usar el estado correcto según la pestaña activa:
  const currentSelectedElements = activeTab === 'library' 
    ? librarySelectedElements 
    : averageSelectedElements;

  // Cargar elementos al inicio
  useEffect(() => {
    searchElements('');
  }, []);

  // Realizar búsqueda cuando cambie el query
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchElements(searchQuery);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery, searchElements]);

  // Añadir este efecto para limpiar selecciones al cambiar de pestaña
  useEffect(() => {
    // No limpiar automáticamente, solo usar los estados separados
  }, [activeTab]);

  const handleCreateAverage = () => {
    if (averageSelectedElements.length === 0) return;

    // Calcular el tiempo promedio usando los registros de tiempo
    let totalTime = 0;
    let validElements = 0;
    
    averageSelectedElements.forEach(sel => {
      // Verificar si el elemento tiene registros de tiempo
      if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
        // Calcular el promedio de los tiempos de este elemento
        const elementTime = sel.element.timeRecords.reduce(
          (sum: number, record: any) => sum + (record.time || 0), 
          0
        ) / sel.element.timeRecords.length;
        
        totalTime += elementTime;
        validElements++;
      }
    });
    
    // Usar el tiempo del elemento directamente si no hay registros de tiempo válidos
    if (validElements === 0) {
      totalTime = averageSelectedElements.reduce(
        (sum, sel) => sum + (sel.element.time || 0), 
        0
      );
      validElements = averageSelectedElements.length || 1;
    }
    
    const averageTime = validElements > 0 ? totalTime / validElements : 0;

    // Calcular el promedio de suplementos
    const totalSupplements = averageSelectedElements.reduce((sum, sel) => 
      sum + (sel.element.supplements?.[0]?.percentage || 0), 0);
    const averageSupplements = totalSupplements / averageSelectedElements.length;

    // Obtener todos los registros de tiempo de los elementos seleccionados
    const allTimeRecords = averageSelectedElements.flatMap(sel => 
      sel.element.timeRecords || []
    );

    // Crear descripción con los elementos de origen
    const descriptions = averageSelectedElements.map(sel => 
      `${t('elementFrom', { ns: 'library' })}: ${sel.element.name || 'Elemento'}: ${sel.element.description || ''}`
    );
    const combinedDescription = descriptions.join('\n');

    // Crear elemento promediado con el tiempo correcto
    const averagedElement: any = {
      id: crypto.randomUUID(),
      name: t('averageOfElements', { ns: 'library' }),
      description: combinedDescription,
      type: 'machine-stopped',
      time: averageTime,
      position: 0,
      repetition_type: 'repetitive',
      frequency_cycles: 1,
      frequency_repetitions: 1,
      supplements: [{
        id: crypto.randomUUID(),
        name: t('averageSupplement', { ns: 'library' }),
        description: t('automaticallyGeneratedSupplement', { ns: 'library' }),
        percentage: averageSupplements,
        is_forced: true,
        points: {},
        factor_selections: {}
      }],
      timeRecords: [{
        id: crypto.randomUUID(),
        time: averageTime,
        activity: 100,
        elementId: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        comment: t('averagedTime', { ns: 'library' })
      }],
      sourceElements: averageSelectedElements.map(sel => ({
        id: sel.element.id,
        name: sel.element.name || '',
        description: sel.element.description || '',
        time: sel.element.time || 0
      }))
    };

    console.log('Cálculo del promedio en biblioteca:', {
      elementosTotales: averageSelectedElements.length,
      elementosConRegistros: validElements,
      tiempoTotal: totalTime,
      tiempoPromedio: averageTime,
      registrosTiempo: averageSelectedElements.map(sel => ({
        nombre: sel.element.name,
        tiempos: sel.element.timeRecords?.map((r: any) => r.time) || [],
        tiempoElemento: sel.element.time || 0
      })),
      suplementosPromedio: averageSupplements
    });

    setAveragedElement(averagedElement);
  };

  const handleSendToLibrary = () => {
    if (!averagedElement) return;
    
    // Generar un ID único para este elemento promediado
    const studyId = 'averaged-' + crypto.randomUUID();
    const studyName = t('averagedElement', { ns: 'library' });
    
    // Añadir a los resultados de búsqueda
    addAveragedElement(averagedElement);
    
    // Añadir el elemento promediado a los seleccionados en la biblioteca
    setLibrarySelectedElements(prev => [
      ...prev,
      {
        element: averagedElement,
        studyName,
        studyId
      }
    ]);
    
    // Limpiar el elemento promediado
    setAveragedElement(null);
    
    // Limpiar selecciones en la pestaña de promedio
    setAverageSelectedElements([]);
    
    // Cambiar a la pestaña de biblioteca
    setActiveTab('library');
  };

  const handleCreateNewStudy = async () => {
    try {
      if (!newStudyData.name || !newStudyData.company) {
        setError(t('fillRequiredFields', { ns: 'library' }));
        return;
      }

      // Verificar que hay elementos seleccionados
      if (librarySelectedElements.length === 0) {
        setError(t('selectAtLeastOneElement', { ns: 'library' }));
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Crear suplementos para cada elemento seleccionado
      // Estructura de supplements en la base de datos: { "elements": {}, "elementId1": {...}, "elementId2": {...} }
      // Según el ejemplo SQL, necesitamos este formato exacto
      const supplements: Record<string, any> = {
        elements: {}
      };

      // Mapear elementos para el nuevo estudio usando librarySelectedElements
      const elements = librarySelectedElements.map(sel => {
        const elementObj: any = {
          id: sel.element.id,
          name: sel.element.name,
          description: sel.element.description,
          time: sel.element.time || 0,
          type: sel.element.type || 'machine-stopped',
          position: 0, // Se asignará una posición secuencial después
          repetition_type: sel.element.repetition_type || 'repetitive',
          frequency_cycles: sel.element.frequency_cycles || 1,
          frequency_repetitions: sel.element.frequency_repetitions || 1,
          activity: sel.element.activity || 100
        };

        // Añadir los suplementos al elemento
        if (sel.element.supplements && sel.element.supplements.length > 0) {
          const existingSupplement = sel.element.supplements[0];
          console.log('Suplemento existente encontrado:', existingSupplement);
          
          // Añadir el suplemento al objeto supplements para la base de datos
          if (sel.element.id) {
            supplements[sel.element.id] = {
              points: existingSupplement.points || {},
              is_forced: existingSupplement.is_forced || false,
              percentage: existingSupplement.percentage || 0,
              factor_selections: existingSupplement.factor_selections || {}
            };
          }
          
          // Añadir el suplemento al elemento para la visualización
          elementObj.supplements = [{
            points: existingSupplement.points || {},
            is_forced: existingSupplement.is_forced || false,
            percentage: existingSupplement.percentage || 0,
            factor_selections: existingSupplement.factor_selections || {}
          }];
        } else {
          // Crear un suplemento básico si no existe
          if (sel.element.id) {
            supplements[sel.element.id] = {
              points: {},
              is_forced: false,
              percentage: 0,
              factor_selections: {}
            };
          }
          
          elementObj.supplements = [{
            points: {},
            is_forced: false,
            percentage: 0,
            factor_selections: {}
          }];
        }

        return elementObj;
      }).filter(el => el.id) as ElementInstance[];
      
      // Asignar posiciones secuenciales a los elementos
      elements.forEach((element, index) => {
        element.position = index + 1;
      });

      // Crear registros de tiempo
      const time_records: Record<string, any> = {};
      librarySelectedElements.forEach(sel => {
        if (sel.element.id) {
          // Verificar si el elemento tiene registros de tiempo guardados
          if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
            // Copiar todos los registros de tiempo existentes, generando nuevos IDs
            time_records[sel.element.id] = sel.element.timeRecords.map(record => ({
              id: crypto.randomUUID(),
              time: record.time,
              activity: record.activity,
              elementId: sel.element.id,
              timestamp: record.timestamp || new Date().toISOString(),
              comment: record.comment || sel.element.description || ''
            }));
          } else if (sel.element.time) {
            // Si no hay registros guardados pero hay un tiempo, crear un registro
            time_records[sel.element.id] = [{
              id: crypto.randomUUID(),
              time: sel.element.time,
              activity: sel.element.activity || 100,
              elementId: sel.element.id,
              timestamp: new Date().toISOString(),
              comment: sel.element.description || ''
            }];
          }
        }
      });

      // Crear registros de cronómetro seguido
      const crono_seguido_records = librarySelectedElements
        .filter(sel => sel.element.id)
        .flatMap(sel => {
          // Si hay registros de tiempo, crear un registro de cronómetro para cada uno
          if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
            return sel.element.timeRecords.map(record => ({
              id: crypto.randomUUID(),
              time: record.time,
              activity: record.activity,
              elementId: sel.element.id,
              timestamp: record.timestamp || new Date().toISOString(),
              description: sel.element.description || '',
              addedToMethod: true
            }));
          } else if (sel.element.time) {
            // Si no hay registros pero hay un tiempo, crear un registro
            return [{
              id: crypto.randomUUID(),
              time: sel.element.time || 0,
              activity: sel.element.activity || 100,
              elementId: sel.element.id,
              timestamp: new Date().toISOString(),
              description: sel.element.description || '',
              addedToMethod: true
            }];
          }
          return [];
        });

      // Crear el objeto de estudio completo
      const study: Study = {
        id: crypto.randomUUID(),
        user_id: user.id,
        required_info: {
          name: newStudyData.name,
          company: newStudyData.company,
          date: newStudyData.date || new Date().toISOString().split('T')[0],
          activity_scale: {
            normal: newStudyData.normalActivity || 100,
            optimal: newStudyData.optimalActivity || 133
          }
        },
        optional_info: {
          tools: '',
          machine: '',
          section: '',
          operator: '',
          reference: '',
          technician: '',
          study_number: '',
          isEstimated: true
        },
        elements,
        time_records,
        supplements,
        crono_seguido_records,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      await createNewStudyFromSelection(study);
      setShowCreateModal(false);
      clearSelection();
      setNewStudyData({
        name: '',
        company: '',
        date: new Date().toISOString().split('T')[0],
        normalActivity: 100,
        optimalActivity: 133
      });
      setError(null);
      navigate('/studies');
    } catch (err) {
      console.error('Error creating study:', err);
      // Mejorar el manejo de errores para detectar problemas de créditos
      if (err instanceof Error && err.message.includes('créditos')) {
        setShowNoCreditsModal(true);
      } else {
        setError(t('errorCreatingStudy', { ns: 'library' }));
      }
    }
  };

  // Añadir esta función para manejar las selecciones por separado
  const handleToggleSelection = (element: any, studyName: string, studyId: string) => {
    if (activeTab === 'library') {
      // Manejar selección para la pestaña de biblioteca
      setLibrarySelectedElements(prev => {
        const isSelected = prev.some(
          item => item.element.id === element.id && item.studyId === studyId
        );
        
        if (isSelected) {
          // Eliminar si ya está seleccionado
          return prev.filter(
            item => !(item.element.id === element.id && item.studyId === studyId)
          );
        } else {
          // Añadir si no está seleccionado
          return [...prev, { element, studyName, studyId }];
        }
      });
    } else {
      // Manejar selección para la pestaña de promedio
      setAverageSelectedElements(prev => {
        const isSelected = prev.some(
          item => item.element.id === element.id && item.studyId === studyId
        );
        
        if (isSelected) {
          // Eliminar si ya está seleccionado
          return prev.filter(
            item => !(item.element.id === element.id && item.studyId === studyId)
          );
        } else {
          // Añadir si no está seleccionado
          return [...prev, { element, studyName, studyId }];
        }
      });
    }
  };

  // Función para limpiar selección según la pestaña activa
  const handleClearSelection = () => {
    if (activeTab === 'library') {
      setLibrarySelectedElements([]);
    } else {
      setAverageSelectedElements([]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('library', { ns: 'library' })}
        subtitle=""
        onBack={() => navigate('/')}
      />

      {/* Pestañas */}
      <div className="container mx-auto px-4">
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => setActiveTab('library')}
            className={`px-4 py-2 rounded-lg font-medium ${
              activeTab === 'library'
                ? 'bg-amber-500 text-white'
                : 'bg-white text-gray-600 hover:bg-gray-50'
            }`}
          >
            {t('libraryTab', { ns: 'library' })}
          </button>
          <button
            onClick={() => setActiveTab('average')}
            className={`px-4 py-2 rounded-lg font-medium ${
              activeTab === 'average'
                ? 'bg-amber-500 text-white'
                : 'bg-white text-gray-600 hover:bg-gray-50'
            }`}
          >
            {t('averageTab', { ns: 'library' })}
          </button>
        </div>

        {/* Campo de búsqueda */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('searchPlaceholder', { ns: 'library' })}
              className="w-full px-4 py-2 pl-10 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-amber-500"
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>

        {/* Panel de elementos seleccionados */}
        {currentSelectedElements.length > 0 && (
          <div className="mb-6 p-4 bg-white rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {t('selectedElements', { count: currentSelectedElements.length, ns: 'library' })}
              </h3>
              {activeTab === 'library' ? (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                >
                  {t('createNewStudy', { ns: 'library' })}
                </button>
              ) : (
                <button
                  onClick={handleCreateAverage}
                  className="bg-amber-500 text-white px-4 py-2 rounded hover:bg-amber-600 flex items-center"
                >
                  <Calculator className="w-5 h-5 mr-2" />
                  {t('calculateAverage', { ns: 'library' })}
                </button>
              )}
            </div>
            <div className="space-y-2">
              {currentSelectedElements.map(({ element, studyName, studyId }) => (
                <div
                  key={`${element.id}-${studyId}`}
                  className="flex items-center justify-between bg-white p-3 rounded-lg shadow"
                >
                  <div>
                    <p className="font-medium">{element.name}</p>
                    <p className="text-sm text-gray-600">
                      {t('studyName', { ns: 'library' })}: {studyName}
                    </p>
                  </div>
                  <button
                    onClick={() => handleToggleSelection(element, studyName, studyId)}
                    className={`px-3 py-1 rounded ${
                      currentSelectedElements.some(
                        sel => sel.element.id === element.id && sel.studyId === studyId
                      )
                        ? 'bg-amber-100 text-amber-600 hover:bg-amber-200'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {currentSelectedElements.some(
                      sel => sel.element.id === element.id && sel.studyId === studyId
                    )
                      ? t('selected', { ns: 'common' })
                      : t('select', { ns: 'common' })}
                  </button>
                </div>
              ))}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleClearSelection}
                className="bg-gray-200 text-gray-700 px-3 py-1 rounded hover:bg-gray-300"
              >
                {t('clearSelection', { ns: 'common' })}
              </button>
            </div>
          </div>
        )}

        {/* Elemento promediado */}
        {activeTab === 'average' && averagedElement && (
          <div className="mb-6 p-4 bg-white rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">{t('averagedElement', { ns: 'library' })}</h3>
              <button
                onClick={handleSendToLibrary}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
              >
                {t('sendToLibrary', { ns: 'library' })}
              </button>
            </div>

            <div className="space-y-2">
              <p><strong>{t('name', { ns: 'common' })}:</strong> {averagedElement.name}</p>
              <p className="whitespace-pre-line"><strong>{t('description', { ns: 'common' })}:</strong> {averagedElement.description}</p>
              <p><strong>{t('baseTime', { ns: 'library' })}:</strong> {(averagedElement.time || 0).toFixed(2)}</p>
              <p><strong>{t('totalTakes', { ns: 'library' })}:</strong> {currentSelectedElements.length}</p>
              {averagedElement.supplements && averagedElement.supplements.length > 0 && (
                <div>
                  <strong>{t('supplements', { ns: 'library' })}:</strong>
                  <ul className="ml-4">
                    {averagedElement.supplements.map((supplement, index) => (
                      <li key={index}>
                        {(supplement.percentage || 0).toFixed(2)}%
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {averagedElement.timeRecords && averagedElement.timeRecords.length > 0 && (
                <div>
                  <strong>{t('timeRecords', { ns: 'library' })}:</strong>
                  <ul className="ml-4">
                    {averagedElement.timeRecords.map((record, index) => (
                      <li key={index}>
                        {(record.time || 0).toFixed(2)}s - {new Date(record.timestamp).toLocaleTimeString()} - {record.comment}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Resultados de búsqueda */}
        {!isLoading && searchResults.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {t('noResults', { ns: 'library' })}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {searchResults.map(({ element, studyName, studyId }) => {
              const isSelected = currentSelectedElements.some(
                sel => sel.element.id === element.id && sel.studyId === studyId
              );
              return (
                <div
                  key={`${element.id}-${studyId}`}
                  className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold">{element.name}</h3>
                    <button
                      onClick={() => handleToggleSelection(element, studyName, studyId)}
                      className={`px-3 py-1 rounded ${
                        isSelected
                          ? 'bg-amber-100 text-amber-600 hover:bg-amber-200'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {isSelected
                        ? t('selected', { ns: 'common' })
                        : t('select', { ns: 'common' })}
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{element.description}</p>
                  <p className="text-sm text-gray-500">{studyName}</p>
                </div>
              );
            })}
          </div>
        )}

        {/* Estado de carga y error */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-amber-500"></div>
          </div>
        )}

        {searchError && (
          <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-6">
            {searchError}
          </div>
        )}

        {/* Modal para crear nuevo estudio */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full">
              <h2 className="text-xl font-semibold mb-4">{t('createNewStudy', { ns: 'library' })}</h2>
              
              {error && (
                <div className="text-red-500 text-sm mb-4">{error}</div>
              )}

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('studyName', { ns: 'library' })} *
                  </label>
                  <input
                    type="text"
                    value={newStudyData.name}
                    onChange={(e) => setNewStudyData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-md"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('studyCompany', { ns: 'library' })} *
                  </label>
                  <input
                    type="text"
                    value={newStudyData.company}
                    onChange={(e) => setNewStudyData(prev => ({ ...prev, company: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-md"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('studyDate', { ns: 'library' })} *
                  </label>
                  <input
                    type="date"
                    value={newStudyData.date}
                    onChange={(e) => setNewStudyData(prev => ({ ...prev, date: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-md"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('normalActivity', { ns: 'library' })} *
                    </label>
                    <input
                      type="number"
                      value={newStudyData.normalActivity}
                      onChange={(e) => setNewStudyData(prev => ({ ...prev, normalActivity: parseInt(e.target.value) || 100 }))}
                      className="w-full px-3 py-2 border rounded-md"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('optimalActivity', { ns: 'library' })} *
                    </label>
                    <input
                      type="number"
                      value={newStudyData.optimalActivity}
                      onChange={(e) => setNewStudyData(prev => ({ ...prev, optimalActivity: parseInt(e.target.value) || 133 }))}
                      className="w-full px-3 py-2 border rounded-md"
                      min="1"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    setError(null);
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  {t('cancel', { ns: 'common' })}
                </button>
                <button
                  onClick={handleCreateNewStudy}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  disabled={
                    !newStudyData.name.trim() || 
                    !newStudyData.company.trim() || 
                    !newStudyData.date ||
                    !newStudyData.normalActivity ||
                    !newStudyData.optimalActivity
                  }
                >
                  {t('create', { ns: 'common' })}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modal de No Créditos */}
      <NoCreditsModal 
        isOpen={showNoCreditsModal} 
        onClose={() => setShowNoCreditsModal(false)} 
      />
    </div>
  );
};