import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>Left, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useElementSearchStore } from '../store/elementSearchStore';
import { StudyLibraryList } from '../components/library/StudyLibraryList';
import { SelectedElementsPanel } from '../components/library/SelectedElementsPanel';
import { LibrarySearchBar } from '../components/library/LibrarySearchBar';
import { ElementInstance, Study } from '../types';
import { supabase } from '../lib/supabase';

export const LibraryPage: React.FC = () => {
  const { t } = useTranslation(['library', 'common']);
  const navigate = useNavigate();
  const {
    searchResults,
    isLoading,
    searchElements
  } = useElementSearchStore();

  const [showSelectedPanel, setShowSelectedPanel] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedElements, setSelectedElements] = useState<Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>>([]);
  const [filters, setFilters] = useState<{
    company?: string;
    dateFrom?: string;
    dateTo?: string;
    hasElements?: boolean;
  }>({});

  // Agrupar resultados por estudio
  const studiesWithElements = React.useMemo(() => {
    const studyMap = new Map<string, { study: Study; elements: ElementInstance[] }>();

    searchResults.forEach(result => {
      const studyId = result.studyId;
      if (!studyMap.has(studyId)) {
        // Crear un objeto Study a partir de la información disponible
        const study: Study = {
          id: studyId,
          user_id: '', // Se llenará desde la base de datos
          required_info: {
            name: result.studyName,
            company: '', // Se puede extraer de los elementos si está disponible
            date: new Date().toISOString().split('T')[0],
            activity_scale: { normal: 100, optimal: 133 }
          },
          optional_info: {},
          elements: [],
          time_records: {},
          supplements: {},
          crono_seguido_records: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        studyMap.set(studyId, {
          study,
          elements: []
        });
      }
      studyMap.get(studyId)!.elements.push(result.element);
    });

    return Array.from(studyMap.values());
  }, [searchResults]);

  // Obtener empresas disponibles para filtros
  const availableCompanies = React.useMemo(() => {
    const companies = new Set<string>();
    studiesWithElements.forEach(({ study }) => {
      if (study.required_info?.company) {
        companies.add(study.required_info.company);
      }
    });
    return Array.from(companies).sort();
  }, [studiesWithElements]);

  useEffect(() => {
    searchElements('');
  }, [searchElements]);

  // Realizar búsqueda cuando cambie el término
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      searchElements(searchTerm);
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, searchElements]);

  const handleBack = () => {
    navigate('/');
  };

  const handleElementToggle = (element: ElementInstance, studyName: string, studyId: string) => {
    setSelectedElements(prev => {
      const isSelected = prev.some(sel => sel.element.id === element.id && sel.studyId === studyId);

      if (isSelected) {
        return prev.filter(sel => !(sel.element.id === element.id && sel.studyId === studyId));
      } else {
        return [...prev, { element, studyName, studyId }];
      }
    });
  };

  const handleRemoveElement = (elementId: string, studyId: string) => {
    setSelectedElements(prev =>
      prev.filter(sel => !(sel.element.id === elementId && sel.studyId === studyId))
    );
  };

  const handleClearAll = () => {
    setSelectedElements([]);
  };

  const handleCreateStudy = () => {
    // TODO: Implementar creación de estudio con elementos seleccionados
    console.log('Creating study with selected elements:', selectedElements);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title={t('back', { ns: 'common' })}
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                {t('title', { ns: 'library' })}
              </h1>
            </div>

            {selectedElements.length > 0 && (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-600">
                  {selectedElements.length} {t('selected', { ns: 'common' })}
                </span>
                <button
                  type="button"
                  onClick={() => setShowSelectedPanel(true)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
                >
                  {t('viewSelection', { ns: 'library' })}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Search Bar */}
          <LibrarySearchBar
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            filters={filters}
            onFiltersChange={setFilters}
            availableCompanies={availableCompanies}
          />

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
              <span className="ml-2 text-gray-600">{t('loading', { ns: 'common' })}</span>
            </div>
          )}

          {/* Studies List */}
          {!isLoading && (
            <StudyLibraryList
              studies={studiesWithElements}
              selectedElements={selectedElements}
              onElementToggle={handleElementToggle}
              isLoading={isLoading}
            />
          )}
        </div>
      </main>

      {/* Selected Elements Panel */}
      <SelectedElementsPanel
        selectedElements={selectedElements}
        onRemoveElement={handleRemoveElement}
        onClearAll={handleClearAll}
        onCreateStudy={handleCreateStudy}
        isVisible={showSelectedPanel}
        onToggleVisibility={() => setShowSelectedPanel(!showSelectedPanel)}
      />
    </div>
  );
};