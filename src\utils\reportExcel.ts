import type { Study } from '../types/study';
import type { ElementInstance, ElementStats, ReportStats, TimeUnit } from '../types/index';
import type { TFunction } from 'i18next';
import { Workbook, Worksheet, Cell, Row, Borders, Fill, Font, Alignment } from 'exceljs';
import i18next from 'i18next';
import { supabase } from '../lib/supabase';
import { ChartExportData } from './chartExport';
import { formatNumberForExcel, formatPercentageForExcel, formatNumberAsString } from './numberFormat';

type FlowchartSymbol = 'operation' | 'inspection' | 'transport' | 'delay' | 'storage' | 'combined';

interface StudyExcelRow {
  id?: string;
  name: string;
  date: string;
  company: string;
  section?: string;
  reference?: string;
  studyNumber?: string;
  timeUnit: string;
  studyType: string;
  tools?: string;
}

function validateStudyRow(row: StudyExcelRow): boolean {
  if (!row.id) {
    row.id = crypto.randomUUID();
  }
  
  const requiredFields: (keyof StudyExcelRow)[] = ['name', 'date', 'company'];
  
  return requiredFields.every(field => row[field] !== undefined && row[field] !== '');
}

function sanitizeExcelValue(value: string | number | undefined | null): string {
  if (value === undefined || value === null) return '';
  return String(value).replace(/[\\/:*?"<>|]/g, '_');
}

function validateReportData(data: ElementStats[]): void {
  if (!Array.isArray(data)) {
    throw new Error('Datos del reporte no son un array');
  }
}

// Constantes de estilo
const PURPLE_COLOR = { argb: '9333EA' }; // RGB: 147, 51, 234
const GRAY_COLOR = { argb: '4B5563' }; // RGB: 75, 85, 99
const RED_COLOR = { argb: 'EF4444' }; // RGB: 239, 68, 68
const WHITE_COLOR = { argb: 'FFFFFF' };

// Función para obtener descripciones cortas para Excel usando traducciones
const getShortResultsLabels = (t: TFunction) => ({
  normalCycle: t('resultShort.normalCycle', { ns: 'report' }),
  optimalCycle: t('resultShort.optimalCycle', { ns: 'report' }),
  contingencyTime: t('resultShort.contingencyTime', { ns: 'report' }),
  totalK: t('resultShort.totalK', { ns: 'report' }),
  normalProduction: t('resultShort.normalProduction', { ns: 'report' }),
  optimalProduction: t('resultShort.optimalProduction', { ns: 'report' }),
  normalProductionPerHour: t('resultShort.normalProductionPerHour', { ns: 'report' }),
  optimalProductionPerHour: t('resultShort.optimalProductionPerHour', { ns: 'report' }),
  normalSaturation: t('resultShort.normalSaturation', { ns: 'report' }),
  optimalSaturation: t('resultShort.optimalSaturation', { ns: 'report' }),
  maxActivity: t('resultShort.maxActivity', { ns: 'report' }),
  valueHour: t('resultShort.valueHour', { ns: 'report' }),
  valueMinute: t('resultShort.valueMinute', { ns: 'report' }),
  valuePoint: t('resultShort.valuePoint', { ns: 'report' })
});

// Definir estilos de bordes
const thinBorder = {
  top: { style: 'thin' as const, color: { argb: 'FF000000' } },
  left: { style: 'thin' as const, color: { argb: 'FF000000' } },
  bottom: { style: 'thin' as const, color: { argb: 'FF000000' } },
  right: { style: 'thin' as const, color: { argb: 'FF000000' } }
};

// Estilos comunes
const headerStyle = {
  font: { bold: true, color: WHITE_COLOR, size: 12 },
  fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: PURPLE_COLOR },
  alignment: { vertical: 'middle' as const, horizontal: 'left' as const },
  border: thinBorder
};

const labelStyle = {
  font: { bold: true, color: GRAY_COLOR, size: 9 },
  alignment: { vertical: 'middle' as const },
  border: thinBorder
};

const valueStyle = {
  font: { color: GRAY_COLOR, size: 9 },
  alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
  border: thinBorder
};

// Extender la interfaz ElementSupplements para incluir table_type
interface ExtendedElementSupplements {
  points: Record<string, number>;
  is_forced: boolean;
  percentage: number;
  factor_selections: Record<string, any>;
  table_type?: 'oit' | 'tal' | '';
}

interface CustomUnitValues {
  unitValue: number;
  unitLabel: string;
  normalCycle: number;
  optimalCycle: number;
  normalProduction: number;
  optimalProduction: number;
  normalProductionPerHour: number;
  optimalProductionPerHour: number;
  valueHour: number;
  valueMinute: number;
  valuePoint: number;
}

// Función para calcular valores por unidad personalizada
const calculateCustomUnitValues = (
  unitType: 'squareMeters' | 'linearMeters' | 'cubicMeters' | 'kilos' | 'perimeter',
  customUnits: any,
  reportStats: ReportStats,
  shiftMinutes: number,
  timeUnit: TimeUnit
): CustomUnitValues | null => {
  if (!customUnits) return null;

  let unitValue = 0;
  let unitLabel = '';

  switch (unitType) {
    case 'squareMeters':
      unitValue = customUnits.squareMeters.length * customUnits.squareMeters.width;
      unitLabel = 'm²';
      break;
    case 'linearMeters':
      unitValue = customUnits.linearMeters.length;
      unitLabel = 'm';
      break;
    case 'cubicMeters':
      unitValue = customUnits.cubicMeters.length * customUnits.cubicMeters.width * customUnits.cubicMeters.height;
      unitLabel = 'm³';
      break;
    case 'kilos':
      unitValue = customUnits.kilos.weight;
      unitLabel = 'kg';
      break;
    case 'perimeter':
      unitValue = 2 * (customUnits.perimeter.length + customUnits.perimeter.width);
      unitLabel = 'm (perímetro)';
      break;
  }

  if (unitValue <= 0) return null;

  // Conversiones de tiempo para cálculos de producción
  const SHIFT_CONVERSIONS: Record<TimeUnit, number> = {
    seconds: 60,    // 60 seconds per minute
    minutes: 1,     // direct value in minutes
    hours: 1/60,
    mmm: 1000,      // 1000 milésimas per minute
    cmm: 100,       // 100 centésimas per minute
    tmu: 100000/60, // Valor exacto: 1666.6666666666667
    dmh: 10000/60   // Valor exacto: 166.66666666666666
  };

  // Calcular todos los valores divididos por la unidad personalizada
  return {
    unitValue,
    unitLabel,
    normalCycle: +(reportStats.normalCycle / unitValue).toFixed(4),
    optimalCycle: +(reportStats.optimalCycle / unitValue).toFixed(4),
    normalProduction: reportStats.normalProduction * unitValue >= 1 ? Math.floor(reportStats.normalProduction * unitValue) : +(reportStats.normalProduction * unitValue).toFixed(3),
    optimalProduction: reportStats.optimalProduction * unitValue >= 1 ? Math.floor(reportStats.optimalProduction * unitValue) : +(reportStats.optimalProduction * unitValue).toFixed(3),
    normalProductionPerHour: +(reportStats.normalProductionPerHour * unitValue).toFixed(2),
    optimalProductionPerHour: +(reportStats.optimalProductionPerHour * unitValue).toFixed(2),
    valueHour: +(reportStats.valueHour / unitValue).toFixed(4),
    valueMinute: +(reportStats.valueMinute / unitValue).toFixed(4),
    valuePoint: +(reportStats.valuePoint / unitValue).toFixed(4)
  };
};

/**
 * Agrega una hoja de trabajo para el diagrama de flujo de operación
 */
async function addFlowchartWorksheet(
  workbook: Workbook, 
  elementStats: ElementStats[], 
  timeUnit: TimeUnit, 
  t: TFunction,
  study?: Study // Agregamos el estudio para detectar si es consolidado
): Promise<void> {
  // Filtrar elementos concurrentes pero incluir TODOS los demás elementos
  const allElements = elementStats.filter(element => !element.concurrent_machine_time);

  // Si no hay elementos, no crear la hoja
  if (allElements.length === 0) {
    return;
  }

  const wsFlowchart = workbook.addWorksheet('Diagrama de Flujo de Operación');

  // Configurar columnas con anchos pequeños como en el informe completo
  wsFlowchart.columns = [
    { width: 3 }, // A
    { width: 3 }, // B
    { width: 3 }, // C
    { width: 3 }, // D
    { width: 3 }, // E
    { width: 3 }, // F
    { width: 3 }, // G
    { width: 3 }, // H
    { width: 3 }, // I
    { width: 3 }, // J
    { width: 3 }, // K
    { width: 3 }, // L
    { width: 3 }, // M
    { width: 3 }, // N
    { width: 3 }, // O
    { width: 3 }, // P
    { width: 3 }, // Q
    { width: 3 }, // R
    { width: 3 }, // S
    { width: 3 }, // T
    { width: 3 }, // U
    { width: 3 }, // V
    { width: 3 }, // W
    { width: 3 }  // X
  ];

  let currentRow = 1;

  // Título principal
  const titleRow = wsFlowchart.addRow(['DIAGRAMA DE FLUJO DE OPERACIÓN']);
  titleRow.getCell(1).style = {
    font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 18 },
    fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF10B981' } },
    alignment: { vertical: 'middle', horizontal: 'center' }
  };
  wsFlowchart.mergeCells(`A${currentRow}:X${currentRow}`);
  wsFlowchart.getRow(currentRow).height = 45;
  currentRow++;

  // Obtener nombres de símbolos en español
  const getSymbolName = (symbol: FlowchartSymbol | 'none'): string => {
    const names = {
      'operation': 'OPERACIÓN',
      'inspection': 'INSPECCIÓN',
      'transport': 'TRANSPORTE',
      'delay': 'ESPERA',
      'storage': 'ALMACENAMIENTO',
      'combined': 'COMBINADA',
      'none': 'SIN SÍMBOLO ASIGNADO'
    };
    return names[symbol];
  };

  // Obtener representación del símbolo
  const getSymbolRepresentation = (symbol: FlowchartSymbol | 'none'): string => {
    const symbols = {
      'operation': '○',
      'inspection': '□',
      'transport': '→',
      'delay': 'D',
      'storage': '▽',
      'combined': '○□',
      'none': '—'
    };
    return symbols[symbol];
  };

  // Verificar si es un estudio consolidado y agrupar elementos por estudio
  const isConsolidated = (study?.optional_info as any)?.consolidated;
  const sourceStudies = (study?.optional_info as any)?.sourceStudies || [];
  
  // Agrupar elementos por estudio de origen
  const studyGroups: { [key: number]: ElementStats[] } = {};
  
  if (isConsolidated && sourceStudies.length > 0) {
    // Para estudios consolidados, agrupar por el patrón en la descripción
    allElements.forEach(element => {
      // Intentar detectar el estudio de origen desde el elemento original
      let studyIndex = 0;
      
      if (study?.elements) {
        const originalElement = study.elements.find(elem => elem.id === element.elementId);
        if (originalElement && originalElement.name) {
          const match = originalElement.name.match(/^(\d+)\./);
          if (match) {
            studyIndex = parseInt(match[1]) - 1;
          }
        }
      }
      
      if (!studyGroups[studyIndex]) {
        studyGroups[studyIndex] = [];
      }
      studyGroups[studyIndex].push(element);
    });
  } else {
    // Para estudios únicos, todos los elementos van al grupo 0
    studyGroups[0] = allElements;
  }

  // Generar una tabla para cada grupo de estudio
  Object.keys(studyGroups).forEach((studyIndexStr, groupIndex) => {
    const studyIndex = parseInt(studyIndexStr);
    const studyElements = studyGroups[studyIndex];
    
    if (studyElements.length === 0) return;

    // Agregar 2 filas vacías de separación entre tablas (excepto antes de la primera)
    if (groupIndex > 0) {
      wsFlowchart.addRow([]);
      wsFlowchart.addRow([]);
      currentRow += 2;
    }

    // Calcular datos de símbolos para este grupo de estudio
    const symbolData: Record<FlowchartSymbol | 'none', { time: number, count: number, elements: ElementStats[] }> = {
      'operation': { time: 0, count: 0, elements: [] },
      'inspection': { time: 0, count: 0, elements: [] },
      'transport': { time: 0, count: 0, elements: [] },
      'delay': { time: 0, count: 0, elements: [] },
      'storage': { time: 0, count: 0, elements: [] },
      'combined': { time: 0, count: 0, elements: [] },
      'none': { time: 0, count: 0, elements: [] }
    };
    
    let totalTime = 0;

    studyElements.forEach(element => {
      const symbol = element.flowchartSymbol || 'none';
      if (element.finalTime > 0) {
        symbolData[symbol].time += element.finalTime;
        symbolData[symbol].count += 1;
        symbolData[symbol].elements.push(element);
        totalTime += element.finalTime;
      }
    });

    // Título del estudio (si es consolidado)
    if (isConsolidated && sourceStudies[studyIndex]) {
      const studyTitle = `ESTUDIO ${studyIndex + 1}: ${sourceStudies[studyIndex].name}`;
      const studyTitleRow = wsFlowchart.addRow([studyTitle]);
      studyTitleRow.getCell(1).style = {
        font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF3B82F6' } },
        alignment: { vertical: 'middle', horizontal: 'center' }
      };
      wsFlowchart.mergeCells(`A${currentRow}:X${currentRow}`);
      wsFlowchart.getRow(currentRow).height = 35;
      currentRow++;
    }

    // Encabezado de tabla de elementos (ocupa todo el ancho A:X)
    const elementHeaderRow = wsFlowchart.addRow([
      'N°', '', 'Descripción del Elemento', '', '', '', '', '', '', '', '', '',
      'Tipo', '', '', '', 'Símbolo', '', `Tiempo (${timeUnit})`, '', '', 'Porcentaje (%)', '', ''
    ]);
    
    // Aplicar estilo y combinar celdas para el encabezado
    const headerStyle = {
      font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 11 },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FF10B981' } },
      alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    };
    
    // Aplicar estilo a todas las celdas del encabezado A:X
    for (let col = 1; col <= 24; col++) {
      elementHeaderRow.getCell(col).style = headerStyle;
    }
    
    // Combinar celdas para cada columna
    wsFlowchart.mergeCells(`A${currentRow}:B${currentRow}`); // N°
    wsFlowchart.mergeCells(`C${currentRow}:L${currentRow}`); // Descripción del Elemento
    wsFlowchart.mergeCells(`M${currentRow}:P${currentRow}`); // Tipo
    wsFlowchart.mergeCells(`Q${currentRow}:R${currentRow}`); // Símbolo
    wsFlowchart.mergeCells(`S${currentRow}:U${currentRow}`); // Tiempo
    wsFlowchart.mergeCells(`V${currentRow}:X${currentRow}`); // Porcentaje
    
    wsFlowchart.getRow(currentRow).height = 35;
    currentRow++;

    // Datos de elementos de este estudio
    studyElements.forEach((element, elementIndex) => {
      const percentage = totalTime > 0 ? ((element.finalTime / totalTime) * 100) : 0;
      const symbol = element.flowchartSymbol || 'none';
      
      const row = wsFlowchart.addRow([
        elementIndex + 1, '', element.description, '', '', '', '', '', '', '', '', '',
        getSymbolName(symbol), '', '', '', getSymbolRepresentation(symbol), '', element.finalTime, '', '', percentage, '', ''
      ]);

      // Aplicar bordes a todas las celdas
      const thinBorder = {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      };
      
      for (let col = 1; col <= 24; col++) {
        row.getCell(col).border = thinBorder;
      }
      
      // Combinar celdas para cada columna
      wsFlowchart.mergeCells(`A${currentRow}:B${currentRow}`); // N°
      wsFlowchart.mergeCells(`C${currentRow}:L${currentRow}`); // Descripción
      wsFlowchart.mergeCells(`M${currentRow}:P${currentRow}`); // Tipo
      wsFlowchart.mergeCells(`Q${currentRow}:R${currentRow}`); // Símbolo
      wsFlowchart.mergeCells(`S${currentRow}:U${currentRow}`); // Tiempo
      wsFlowchart.mergeCells(`V${currentRow}:X${currentRow}`); // Porcentaje
      
      // Configurar alineación y formato
      row.getCell(1).alignment = { vertical: 'middle' as const, horizontal: 'center' as const }; // N°
      row.getCell(3).alignment = { vertical: 'middle' as const, horizontal: 'left' as const }; // Descripción
      row.getCell(13).alignment = { vertical: 'middle' as const, horizontal: 'center' as const }; // Tipo
      row.getCell(17).alignment = { vertical: 'middle' as const, horizontal: 'center' as const }; // Símbolo
      row.getCell(19).alignment = { vertical: 'middle' as const, horizontal: 'right' as const }; // Tiempo
      row.getCell(19).numFmt = '0.000';
      row.getCell(22).alignment = { vertical: 'middle' as const, horizontal: 'right' as const }; // Porcentaje
      row.getCell(22).numFmt = '0.0%';
      row.getCell(22).value = percentage / 100;
      
      currentRow++;
    });

    // Agregar estadísticas para este estudio
    // Saltar una fila
    wsFlowchart.addRow([]);
    currentRow++;

    // Sección de estadísticas por tipo de operación
    const statsHeaderRow = wsFlowchart.addRow(['ESTADÍSTICAS POR TIPO DE OPERACIÓN']);
    statsHeaderRow.getCell(1).style = {
      font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FF10B981' } },
      alignment: { vertical: 'middle' as const, horizontal: 'center' as const }
    };
    wsFlowchart.mergeCells(`A${currentRow}:X${currentRow}`);
    wsFlowchart.getRow(currentRow).height = 35;
    currentRow++;

    // Encabezado de estadísticas (ocupa A:X)
    const statsHeaderColumns = wsFlowchart.addRow([
      'Tipo de Operación', '', '', '', '', '', '', '',
      `Tiempo Total (${timeUnit})`, '', '', '', '', '', '', 'Cantidad', '', '', 'Porcentaje (%)', '', '', '', '', ''
    ]);
    
    // Aplicar estilo a todas las celdas del encabezado de estadísticas A:X
    for (let col = 1; col <= 24; col++) {
      statsHeaderColumns.getCell(col).style = {
        font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 11 },
        fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FF10B981' } },
        alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
        border: {
          top: { style: 'thin' as const },
          bottom: { style: 'thin' as const },
          left: { style: 'thin' as const },
          right: { style: 'thin' as const }
        }
      };
    }
    
    // Combinar celdas para encabezados de estadísticas
    wsFlowchart.mergeCells(`A${currentRow}:H${currentRow}`); // Tipo de Operación
    wsFlowchart.mergeCells(`I${currentRow}:O${currentRow}`); // Tiempo Total
    wsFlowchart.mergeCells(`P${currentRow}:R${currentRow}`); // Cantidad
    wsFlowchart.mergeCells(`S${currentRow}:X${currentRow}`); // Porcentaje
    
    currentRow++;

    // Filtrar solo símbolos activos para este estudio
    const activeSymbols = Object.entries(symbolData).filter(([_, data]) => data.count > 0);

    // Datos de estadísticas por tipo para este estudio
    activeSymbols.forEach(([symbol, data]) => {
      const percentage = totalTime > 0 ? ((data.time / totalTime) * 100) : 0;
      
      const row = wsFlowchart.addRow([
        `${getSymbolRepresentation(symbol as FlowchartSymbol | 'none')} ${getSymbolName(symbol as FlowchartSymbol | 'none')}`, '', '', '', '', '', '', '',
        data.time, '', '', '', '', '', '', data.count, '', '', percentage, '', '', '', '', ''
      ]);

      // Aplicar bordes a todas las celdas
      const thinBorder = {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      };
      
      for (let col = 1; col <= 24; col++) {
        row.getCell(col).border = thinBorder;
      }
      
      // Combinar celdas para estadísticas
      wsFlowchart.mergeCells(`A${currentRow}:H${currentRow}`); // Tipo de Operación
      wsFlowchart.mergeCells(`I${currentRow}:O${currentRow}`); // Tiempo Total
      wsFlowchart.mergeCells(`P${currentRow}:R${currentRow}`); // Cantidad
      wsFlowchart.mergeCells(`S${currentRow}:X${currentRow}`); // Porcentaje
      
      // Configurar alineación y formato
      row.getCell(1).alignment = { vertical: 'middle' as const, horizontal: 'left' as const }; // Tipo
      row.getCell(9).alignment = { vertical: 'middle' as const, horizontal: 'right' as const }; // Tiempo
      row.getCell(9).value = data.time;
      row.getCell(9).numFmt = '0.000';
      row.getCell(16).alignment = { vertical: 'middle' as const, horizontal: 'center' as const }; // Cantidad
      row.getCell(16).value = data.count;
      row.getCell(16).numFmt = '0';
      row.getCell(19).alignment = { vertical: 'middle' as const, horizontal: 'right' as const };
      row.getCell(19).value = percentage / 100;
      row.getCell(19).numFmt = '0.0%';
      
      currentRow++;
    });

    // Fila de totales para este estudio
    const totalRow = wsFlowchart.addRow([
      'TOTAL', '', '', '', '', '', '', '',
      totalTime, '', '', '', '', '', '', studyElements.length, '', '', 100.0, '', '', '', '', ''
    ]);

    // Aplicar estilo a todas las celdas de totales
    const totalStyle = {
      font: { bold: true, size: 11 },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FFF3F4F6' } },
      border: {
        top: { style: 'thick' as const },
        bottom: { style: 'thick' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    };
    
    for (let col = 1; col <= 24; col++) {
      totalRow.getCell(col).style = totalStyle;
    }
    
    // Combinar celdas para totales
    wsFlowchart.mergeCells(`A${currentRow}:H${currentRow}`); // TOTAL
    wsFlowchart.mergeCells(`I${currentRow}:O${currentRow}`); // Tiempo Total
    wsFlowchart.mergeCells(`P${currentRow}:R${currentRow}`); // Cantidad
    wsFlowchart.mergeCells(`S${currentRow}:X${currentRow}`); // Porcentaje
    
    // Configurar alineación y formato para totales
    totalRow.getCell(1).alignment = { vertical: 'middle' as const, horizontal: 'center' as const }; // TOTAL
    totalRow.getCell(9).alignment = { vertical: 'middle' as const, horizontal: 'right' as const }; // Tiempo
    totalRow.getCell(9).value = totalTime;
    totalRow.getCell(9).numFmt = '0.000';
    totalRow.getCell(16).alignment = { vertical: 'middle' as const, horizontal: 'center' as const }; // Cantidad
    totalRow.getCell(16).value = studyElements.length;
    totalRow.getCell(16).numFmt = '0';
    totalRow.getCell(19).alignment = { vertical: 'middle' as const, horizontal: 'right' as const }; // Porcentaje
    totalRow.getCell(19).value = 1.0;
    totalRow.getCell(19).numFmt = '0.0%';
    
    currentRow++;
  });

  // Agregar leyenda al final de todas las tablas
  // Agregar 2 filas de separación antes de la leyenda
  wsFlowchart.addRow([]);
  wsFlowchart.addRow([]);
  currentRow += 2;

  // Leyenda de símbolos
  const legendRow = wsFlowchart.addRow(['LEYENDA DE SÍMBOLOS DE DIAGRAMA DE FLUJO']);
  legendRow.getCell(1).style = {
    font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 },
    fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FF10B981' } },
    alignment: { vertical: 'middle' as const, horizontal: 'center' as const }
  };
  wsFlowchart.mergeCells(`A${currentRow}:X${currentRow}`);
  wsFlowchart.getRow(currentRow).height = 35;
  currentRow++;

  // Encabezado de la tabla de leyenda - ocupa desde A-X
  const legendHeaderRow = wsFlowchart.addRow(['Símbolo | Nombre | Descripción']);
  
  // Aplicar estilo al encabezado completo
  legendHeaderRow.getCell(1).style = {
    font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 11 },
    fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FF10B981' } },
    alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
    border: {
      top: { style: 'thin' as const },
      bottom: { style: 'thin' as const },
      left: { style: 'thin' as const },
      right: { style: 'thin' as const }
    }
  };
  
  // Combinar todas las celdas del encabezado A-X
  wsFlowchart.mergeCells(`A${currentRow}:X${currentRow}`);
  
  currentRow++;

  // Subencabezado con columnas específicas
  const legendSubHeaderRow = wsFlowchart.addRow([
    'Símbolo', '', 'Nombre', '', '', '', '', '', '', '', 'Descripción', '', '', '', '', '', '', '', '', '', '', '', '', ''
  ]);
  
  // Aplicar estilo a todas las celdas del subencabezado
  for (let col = 1; col <= 24; col++) {
    legendSubHeaderRow.getCell(col).style = {
      font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 10 },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: { argb: 'FF10B981' } },
      alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    };
  }
  
  // Combinar celdas para subencabezados de leyenda
  wsFlowchart.mergeCells(`A${currentRow}:B${currentRow}`); // Símbolo
  wsFlowchart.mergeCells(`C${currentRow}:J${currentRow}`); // Nombre
  wsFlowchart.mergeCells(`K${currentRow}:X${currentRow}`); // Descripción
  
  currentRow++;

  // Descripción de símbolos
  const symbolDescriptions = [
    { symbol: '○', name: 'OPERACIÓN', desc: 'Indica las principales fases del proceso. Agrega, modifica, montaje, etc.' },
    { symbol: '□', name: 'INSPECCIÓN', desc: 'Verifica la calidad o cantidad. En general no agrega valor.' },
    { symbol: '→', name: 'TRANSPORTE', desc: 'Indica el movimiento de materiales. Traslado de un lugar a otro.' },
    { symbol: 'D', name: 'ESPERA', desc: 'Indica demora entre dos operaciones o abandono momentáneo.' },
    { symbol: '▽', name: 'ALMACENAMIENTO', desc: 'Indica depósito de un objeto bajo vigilancia.' },
    { symbol: '○□', name: 'COMBINADA', desc: 'Indica actividades de operación e inspección simultáneas.' },
    { symbol: '—', name: 'SIN SÍMBOLO ASIGNADO', desc: 'Elementos que aún no tienen un tipo de operación definido.' }
  ];

  symbolDescriptions.forEach(item => {
    const row = wsFlowchart.addRow([
      item.symbol, '', item.name, '', '', '', '', '', '', '', item.desc, '', '', '', '', '', '', '', '', '', '', '', '', ''
    ]);

    // Aplicar bordes a todas las celdas de la leyenda
    const thinBorder = {
      top: { style: 'thin' as const },
      bottom: { style: 'thin' as const },
      left: { style: 'thin' as const },
      right: { style: 'thin' as const }
    };
    
    for (let col = 1; col <= 24; col++) {
      row.getCell(col).border = thinBorder;
    }
    
    // Combinar celdas para la leyenda según nueva agrupación
    wsFlowchart.mergeCells(`A${currentRow}:B${currentRow}`); // Símbolo
    wsFlowchart.mergeCells(`C${currentRow}:J${currentRow}`); // Nombre
    wsFlowchart.mergeCells(`K${currentRow}:X${currentRow}`); // Descripción
    
    // Aplicar estilos uniformes a todas las filas
    row.getCell(1).style = {
      font: { bold: true, size: 14 },
      alignment: { vertical: 'middle' as const, horizontal: 'center' as const },
      border: thinBorder
    };
    
    row.getCell(3).style = {
      font: { bold: true, size: 11 },
      alignment: { vertical: 'middle' as const, horizontal: 'left' as const },
      border: thinBorder
    };
    
    row.getCell(11).style = {
      font: { size: 10 },
      alignment: { vertical: 'middle' as const, horizontal: 'left' as const, wrapText: true },
      border: thinBorder
    };
    
    // Asegurar altura mínima para contenido
    row.height = 30;
    currentRow++;
  });

  // Configurar impresión
  wsFlowchart.pageSetup = {
    orientation: 'landscape',
    printArea: 'A:X',
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0,
    margins: {
      left: 0.7,
      right: 0.7,
      top: 0.75,
      bottom: 0.75,
      header: 0.3,
      footer: 0.3
    }
  };
}

export async function generateExcel(
  study: Study,
  elementStats: ElementStats[],
  reportStats: ReportStats,
  timeUnit: TimeUnit,
  shiftMinutes: number,
  contingency: number,
  t: TFunction,
  chartData?: ChartExportData,
  pointsPerHour: number = 100
): Promise<void> {
  if (!study) {
    throw new Error('Estudio no proporcionado');
  }

  // Determinar el nombre del estudio para usar en el informe y nombre de archivo
  const studyName = study.required_info.name;

  console.log('Generating Excel for study:', studyName);
  
  // Obtener el logo de la empresa del usuario actual
  let profile;
  try {
    console.log('Intentando obtener logo del perfil...');
    
    // Obtener el usuario actual primero
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.warn('No hay usuario autenticado');
      profile = null;
    } else {
      console.log('Usuario autenticado, obteniendo perfil para:', user.id);
      
      const profileResult = await supabase
        .from('profiles')
        .select('logo_url')
        .eq('id', user.id)
        .single();
      
      if (profileResult.error) {
        console.warn('Error obteniendo perfil del usuario:', profileResult.error);
        profile = null;
      } else {
        console.log('Perfil obtenido exitosamente:', profileResult.data);
        profile = profileResult;
      }
    }
  } catch (error) {
    console.error('Error al obtener perfil:', error);
    profile = null;
  }
  
  const studyRow: StudyExcelRow = {
    id: study.id,
    name: study.required_info.name,
    date: study.required_info.date,
    company: study.required_info.company,
    section: study.optional_info?.section || '',
    reference: study.optional_info?.reference || '',
    studyNumber: study.optional_info?.study_number || '',
    timeUnit: study.settings?.timeUnit || 'seconds',
    studyType: study.status || 'standard',
    tools: study.optional_info?.tools || '-'
  };

  if (!validateStudyRow(studyRow)) {
    throw new Error('Invalid study row');
  }
  validateReportData(elementStats);

  const workbook = new Workbook();
  workbook.creator = 'Cronometras';
  workbook.lastModifiedBy = 'Cronometras';
  workbook.created = new Date();
  workbook.modified = new Date();

  // Añadir la nueva hoja con el formato del PDF
  const wsPDFFormat = workbook.addWorksheet('Informe Completo');
  
  // Configurar columnas con anchos más pequeños
  wsPDFFormat.columns = [
    { width: 3 }, // A - Descripción (parte 1)
    { width: 3 }, // B - Descripción (parte 2)
    { width: 3 }, // C - Descripción (parte 3)
    { width: 3 }, // D - Descripción (parte 4)
    { width: 3 }, // E - Descripción (parte 5)
    { width: 3 }, // F - Descripción (parte 6)
    { width: 3 }, // G - Descripción (parte 7)
    { width: 3 }, // H - Descripción (parte 8)
    { width: 3 }, // I - Descripción (parte 9)
    { width: 3 }, // J - Descripción (parte 10)
    { width: 3 }, // K - Descripción (parte 11)
    { width: 3 }, // L - Descripción (parte 12)
    { width: 5 }, // M - Tipo (parte 1)
    { width: 5 }, // N - Tipo (parte 2)
    { width: 5 }, // O - Frecuencia (parte 1)
    { width: 5 }, // P - Frecuencia (parte 2)
    { width: 5 }, // Q - Tiempo observado (parte 1)
    { width: 5 }, // R - Tiempo observado (parte 2)
    { width: 5 }, // S - Actividad (parte 1)
    { width: 5 }, // T - Actividad (parte 2)
    { width: 5 }, // U - Suplementos (parte 1)
    { width: 5 }, // V - Suplementos (parte 2)
    { width: 5 }, // W - Total (parte 1)
    { width: 5 }  // X - Total (parte 2)
  ];

  // Título del estudio
  const titleRow = wsPDFFormat.addRow([studyName]);
  titleRow.font = { bold: true, color: PURPLE_COLOR, size: 14 };
  titleRow.alignment = { vertical: 'middle', horizontal: 'center' };
  wsPDFFormat.mergeCells('A1:X1');
  wsPDFFormat.getRow(1).height = 40;

  // Añadir el logo si existe
  if (profile?.data && 'logo_url' in profile.data && profile.data.logo_url) {
    try {
      console.log('Intentando cargar logo:', profile.data.logo_url);
      const logoUrl = profile.data.logo_url as string;
      
      let imageData: Blob;
      
      // Verificar si es una URL pública o privada
      if (logoUrl.includes('/object/public/')) {
        console.log('Logo es público, descargando directamente con fetch...');
        
        // Para buckets públicos, usar fetch directamente
        const response = await fetch(logoUrl);
        if (!response.ok) {
          throw new Error(`Error HTTP ${response.status}: ${response.statusText}`);
        }
        imageData = await response.blob();
        console.log('Logo público descargado exitosamente, tamaño:', imageData.size);
        
      } else {
        console.log('Logo es privado, usando Supabase Storage...');
        
        // Para buckets privados, usar Supabase Storage
        const fileName = logoUrl.split('/').pop() || '';
        console.log('Descargando archivo de logo privado:', fileName);
        
        const { data: privateImageData, error } = await supabase
          .storage
          .from('logos')
          .download(fileName);

        if (error) {
          console.error('Error descargando logo privado:', error);
          throw error;
        }
        
        imageData = privateImageData;
        console.log('Logo privado descargado exitosamente, tamaño:', imageData.size);
      }

      // Convertir el blob a base64
      const base64 = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64String = reader.result as string;
          resolve(base64String.split(',')[1]); // Obtener solo la parte base64
        };
        reader.readAsDataURL(imageData);
      });

      // Crear una imagen temporal para obtener las dimensiones
      const img = new Image();
      img.src = URL.createObjectURL(imageData);
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
      });
      
      const fixedHeight = 40;
      const aspectRatio = img.width / img.height;
      const width = fixedHeight * aspectRatio;

      const imageId = workbook.addImage({
        base64,
        extension: 'jpeg',
      });

      // Añadir la imagen superpuesta en la esquina superior izquierda
      wsPDFFormat.addImage(imageId, {
        tl: { col: 0, row: 0 },
        ext: { width: width, height: fixedHeight },
        editAs: 'oneCell'
      });
      
      console.log('Logo añadido exitosamente al Excel');
    } catch (error) {
      console.error('Error loading logo:', error);
      console.error('Detalles del error:', {
        message: error instanceof Error ? error.message : 'Mensaje no disponible',
        name: error instanceof Error ? error.name : 'Error desconocido',
        code: (error as any)?.code || 'N/A'
      });
      
      // Agregar mensaje informativo en el Excel si no se pudo cargar el logo
      const logoErrorNote = wsPDFFormat.addRow(['Logo no disponible - verifique permisos RLS']);
      logoErrorNote.getCell(1).style = {
        font: { italic: true, color: { argb: 'FF666666' }, size: 8 },
        alignment: { vertical: 'middle', horizontal: 'left' }
      };
      wsPDFFormat.mergeCells(`A${logoErrorNote.number}:D${logoErrorNote.number}`);
    }
  } else {
    console.log('No se encontró logo en el perfil o perfil no disponible');
  }

  // Técnico en fila 2
  if (study.optional_info?.technician) {
    const technicianRow = wsPDFFormat.addRow([`${t('technician', { ns: 'study' })}: ${study.optional_info.technician}`]);
    technicianRow.getCell(1).style = {
      font: { italic: true, color: GRAY_COLOR, size: 10 },
      alignment: { vertical: 'middle', horizontal: 'left' }
    };
    wsPDFFormat.mergeCells('A2:X2');
  } else {
    // Espacio en blanco si no hay técnico
    wsPDFFormat.addRow([]);
  }

  // Datos de identificación en fila 3
  const identifyingDataRow = wsPDFFormat.addRow([t('identifyingData', { ns: 'report' }).toUpperCase()]);
  identifyingDataRow.getCell(1).style = headerStyle;
  identifyingDataRow.height = 30;
  wsPDFFormat.mergeCells('A3:X3');

  // Información del estudio con celdas unidas de cuatro en cuatro
  const studyInfo = [
    [
      t('company', { ns: 'study' }), '', '', '', 
      study.required_info.company, '', '', '',
      t('studyNumber', { ns: 'study' }), '', '', '', 
      study.optional_info?.study_number || '-', '', '', '',
      t('date', { ns: 'study' }), '', '', '', 
      study.required_info.date, '', '', ''
    ],
    [
      t('operator', { ns: 'study' }), '', '', '', 
      study.optional_info?.operator || '-', '', '', '',
      t('section', { ns: 'study' }), '', '', '', 
      study.optional_info?.section || '-', '', '', '',
      t('reference', { ns: 'study' }), '', '', '', 
      study.optional_info?.reference || '-', '', '', ''
    ],
    [
      t('machine', { ns: 'study' }), '', '', '', 
      study.optional_info?.machine || '-', '', '', '',
      t('tools', { ns: 'study' }), '', '', '', 
      study.optional_info?.tools || '-', '', '', '',
      t('timeUnit', { ns: 'report' }), '', '', '', 
      t(`units.${timeUnit}`, { ns: 'report' }), '', '', ''
    ],
    [
      t('shiftMinutes', { ns: 'report' }), '', '', '', 
      shiftMinutes.toString(), '', '', '',
      t('contingency', { ns: 'report' }), '', '', '', 
      `${contingency}%`, '', '', '',
      t('activityScale', { ns: 'report' }), '', '', '', 
      `${study.required_info.activity_scale.normal}-${study.required_info.activity_scale.optimal}`, '', '', ''
    ]
  ];

  studyInfo.forEach((rowData, index) => {
    const row = wsPDFFormat.addRow(rowData);
    row.eachCell((cell: Cell, colNumber: number) => {
      // Agregar bordes a todas las celdas
      cell.border = thinBorder;
      
      if ((colNumber === 5 || colNumber === 13 || colNumber === 21) && (row.number >= 4 && row.number <= 7)) {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { ...cell.font, size: 9 };
      } else if (colNumber % 8 === 1) {
        cell.style = { ...labelStyle, border: thinBorder };
        cell.alignment = { vertical: 'middle', horizontal: 'left' };
      } else if (colNumber % 8 === 5) {
        cell.style = { ...valueStyle, border: thinBorder };
        cell.alignment = { vertical: 'middle', horizontal: 'left' };
      }
    });
    // Unir celdas para cada grupo de cuatro columnas en la fila
    for (let i = 0; i < 24; i += 4) {
      wsPDFFormat.mergeCells(row.number, i + 1, row.number, i + 4);
    }
  });

  // Espacio entre secciones
  wsPDFFormat.addRow([]);

  // Resultados
  const resultsHeaderRow = wsPDFFormat.addRow([t('results.title', { ns: 'report' }).toUpperCase()]);
  resultsHeaderRow.getCell(1).style = headerStyle;
  wsPDFFormat.mergeCells(`A${resultsHeaderRow.number}:X${resultsHeaderRow.number}`);

  // Usar descripciones cortas para Excel
  const shortLabels = getShortResultsLabels(t);

  const resultsData = [
    [
      shortLabels.normalCycle, '', '', '', 
      reportStats.normalCycle, '', '', '',
      shortLabels.optimalCycle, '', '', '', 
      reportStats.optimalCycle, '', '', '',
      shortLabels.contingencyTime, '', '', '', 
      reportStats.contingencyTime, '', '', ''
    ],
    [
      shortLabels.totalK, '', '', '', 
      reportStats.totalK, '', '', '',
      shortLabels.normalProduction, '', '', '', 
      reportStats.normalProduction, '', '', '',
      shortLabels.optimalProduction, '', '', '', 
      reportStats.optimalProduction, '', '', ''
    ],
    [
      shortLabels.normalProductionPerHour, '', '', '', 
      reportStats.normalProductionPerHour, '', '', '',
      shortLabels.optimalProductionPerHour, '', '', '', 
      reportStats.optimalProductionPerHour, '', '', '',
      shortLabels.normalSaturation, '', '', '', 
      reportStats.normalSaturation / 100, '', '', ''
    ],
    [
      shortLabels.optimalSaturation, '', '', '', 
      reportStats.optimalSaturation / 100, '', '', '',
      shortLabels.maxActivity, '', '', '', 
      reportStats.maxActivity / 100, '', '', '',
      shortLabels.valueHour, '', '', '', 
      reportStats.valueHour, '', '', ''
    ],
    [
      shortLabels.valueMinute, '', '', '', 
      reportStats.valueMinute, '', '', '',
      shortLabels.valuePoint, '', '', '', 
      reportStats.valuePoint, '', '', '',
      '', '', '', '', '', '', '', ''
    ]
  ];

  resultsData.forEach((rowData, rowIndex) => {
    const row = wsPDFFormat.addRow(rowData);
    row.eachCell((cell: Cell, colNumber: number) => {
      // Agregar bordes a todas las celdas
      cell.border = thinBorder;
      
      if ((colNumber === 5 || colNumber === 13 || colNumber === 21) && row.number >= 10 && row.number <= 14) {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { ...cell.font, size: 9 };
        
        // Aplicar formato según el tipo de valor
        if (typeof cell.value === 'number') {
          // Determinar el tipo de formato basado en la posición y contenido
          if (rowIndex === 2 && (colNumber === 21)) { // normalSaturation
            cell.numFmt = '0.00%';
            cell.value = reportStats.normalSaturation / 100;
          } else if (rowIndex === 3 && (colNumber === 5)) { // optimalSaturation
            cell.numFmt = '0.00%';
            cell.value = reportStats.optimalSaturation / 100;
          } else if (rowIndex === 3 && (colNumber === 13)) { // maxActivity
            cell.numFmt = '0.0%';
            cell.value = reportStats.maxActivity / 100;
          } else if (rowIndex === 1 && (colNumber === 13 || colNumber === 21)) { // normalProduction, optimalProduction
            cell.numFmt = '0.00';
          } else if (rowIndex === 2 && (colNumber === 5 || colNumber === 13)) { // normalProductionPerHour, optimalProductionPerHour
            cell.numFmt = '0.00';
          } else if (rowIndex === 3 && (colNumber === 21) || rowIndex === 4 && (colNumber === 5 || colNumber === 13)) { // valueHour, valueMinute, valuePoint
            cell.numFmt = '0.000';
          } else {
            cell.numFmt = '0.000';
          }
        }
      } else if (colNumber % 8 === 1) {
        cell.style = { ...labelStyle, border: thinBorder };
        cell.alignment = { vertical: 'middle', horizontal: 'left' };
      } else if (colNumber % 8 === 5) {
        cell.style = { ...valueStyle, border: thinBorder };
        cell.alignment = { vertical: 'middle', horizontal: 'right' };
      }
    });
    // Unir celdas para cada grupo de cuatro columnas en la fila
    for (let i = 0; i < 24; i += 4) {
      wsPDFFormat.mergeCells(row.number, i + 1, row.number, i + 4);
    }
  });

  // Espacio entre secciones
  wsPDFFormat.addRow([]);

  // Detalle de elementos
  const elementsHeaderRow = wsPDFFormat.addRow([t('elementDetails.title', { ns: 'report' }).toUpperCase()]);
  elementsHeaderRow.getCell(1).style = headerStyle;
  wsPDFFormat.mergeCells(`A${elementsHeaderRow.number}:X${elementsHeaderRow.number}`);

  // Cabeceras de la tabla de elementos
  const elementHeaderRow = wsPDFFormat.addRow([
    t('description', { ns: 'study' }), '', '', '', '', '', '', '', '', '', '', '', '',  // A-M (descripción)
    t('type', { ns: 'study' }),                             // N
    t('freq', { ns: 'study' }), '',                             // O-P
    t('time', { ns: 'study' }), '',                             // Q-R
    t('act', { ns: 'study' }), '',                              // S-T
    t('supl', { ns: 'study' }), '',                             // U-V
    t('total', { ns: 'study' }), ''                             // W-X
  ]);

  // Aplicar estilos a las cabeceras
  elementHeaderRow.eachCell((cell: Cell, colNumber: number) => {
    cell.style = headerStyle;
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
  });
  wsPDFFormat.mergeCells(`A${elementHeaderRow.number}:M${elementHeaderRow.number}`);
  wsPDFFormat.mergeCells(`O${elementHeaderRow.number}:P${elementHeaderRow.number}`);
  wsPDFFormat.mergeCells(`Q${elementHeaderRow.number}:R${elementHeaderRow.number}`);
  wsPDFFormat.mergeCells(`S${elementHeaderRow.number}:T${elementHeaderRow.number}`);
  wsPDFFormat.mergeCells(`U${elementHeaderRow.number}:V${elementHeaderRow.number}`);
  wsPDFFormat.mergeCells(`W${elementHeaderRow.number}:X${elementHeaderRow.number}`);

  // Datos de elementos
  elementStats.forEach((stat, index) => {
    console.log('Element stat:', stat);

    const elementNumber = (index + 1) * 10;
    const description = stat.concurrent_machine_time ? 
      `${elementNumber}- ${stat.description || ''} [${t('concurrent', { ns: 'machine' })}]` : 
      `${elementNumber}- ${stat.description || ''}`;

    const rowNumber = elementHeaderRow.number + index + 1;

    // Crear la fila con todas las celdas explícitamente
    const row = wsPDFFormat.addRow([
      description, '', '', '', '', '', '', '', '', '', '', '', '',  // A-M (descripción)
      t(`types.${stat.type}`, { ns: 'report' }),           // N
      stat.frequency || '1/1', '',                             // O-P
      Number(stat.observedTime || 0), '',           // Q-R
      Number(stat.averageActivity || 100) / 100, '',  // S-T
      Number(stat.supplements || 0) / 100, '',         // U-V
      Number(stat.finalTime || 0), ''               // W-X
    ]);

    // Calcular altura automática basada en la longitud de la descripción y saltos de línea
    const descriptionText = description || '';
    const lineBreaks = (descriptionText.match(/\n/g) || []).length;
    const estimatedLines = Math.max(1, Math.ceil(descriptionText.length / 60) + lineBreaks);
    const calculatedHeight = Math.max(25, estimatedLines * 20);
    
    row.height = calculatedHeight;
    
    // Detectar el estudio de origen para estudios consolidados
    let studyIndex = 0;
    console.log('=== DEBUG DETECCIÓN ESTUDIO ===');
    console.log('¿Es consolidado?', !!(study.optional_info as any)?.consolidated);
    console.log('¿Tiene sourceStudies?', !!(study.optional_info as any)?.sourceStudies);
    console.log('ElementStats description:', stat.description);
    console.log('ElementStats elementId:', stat.elementId);
    
    if ((study.optional_info as any)?.consolidated && (study.optional_info as any)?.sourceStudies) {
      // Buscar el elemento original en el estudio para obtener el name
      const originalElement = study.elements?.find(elem => elem.id === stat.elementId);
      console.log('Elemento original encontrado:', !!originalElement);
      
      if (originalElement) {
        // El patrón se encuentra en el name del elemento original
        const elementName = originalElement.name || '';
        console.log('Name del elemento original:', elementName);
        const match = elementName.match(/^(\d+)\./);
        
        if (match) {
          studyIndex = parseInt(match[1]) - 1; // Convertir a índice (0-based)
          console.log('✅ Estudio detectado:', studyIndex + 1, 'para elemento:', elementName);
        } else {
          console.log('❌ No se encontró patrón de estudio en name:', elementName);
        }
      } else {
        console.log('❌ No se encontró elemento original para elementId:', stat.elementId);
        console.log('Elementos disponibles:', study.elements?.map(e => ({id: e.id, name: e.name})));
      }
    }
    console.log('=== FIN DEBUG ===');
    
    // Aplicar alineación a todas las celdas
    row.eachCell((cell: Cell, colNumber: number) => {
      cell.font = { ...cell.font, size: 9 };  // Asegurar tamaño 9 para todas las celdas
      if (colNumber <= 13) { // Columnas A-M (descripción)
        cell.alignment = { 
          vertical: 'middle', // Cambiar a 'top' para mejor lectura con múltiples líneas
          horizontal: 'left',
          wrapText: true
        };
      } else {
        cell.alignment = { 
          vertical: 'middle', 
          horizontal: 'center'
        };
        // Aplicar formato de porcentaje sin decimales a las columnas de actividad y suplementos
        if (colNumber === 19 || colNumber === 21) { // Columnas S y U
          cell.numFmt = '0%';
        }
      }
    });

    if (stat.concurrent_machine_time) {
      row.eachCell((cell: Cell) => {
        cell.font = { color: RED_COLOR, italic: true, size: 9 };
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6' } };
      });
    } else {
      // Aplicar colores de fondo para estudios consolidados SOLO si no es tiempo concurrente
      if ((study.optional_info as any)?.consolidated) {
        const colors = [
          { argb: 'FFFFEEE6' }, // Naranja muy claro
          { argb: 'FFF0FDF4' }, // Verde muy claro
          { argb: 'FFFEF7CD' }, // Amarillo muy claro
          { argb: 'FFFDF2F8' }, // Rosa muy claro
          { argb: 'FFF3F4F6' }, // Gris muy claro
          { argb: 'FFEEF2FF' }, // Índigo muy claro
          { argb: 'FFF0FDFA' }, // Teal muy claro
          { argb: 'FFFEFCE8' }  // Lima muy claro
        ];
        
        const colorIndex = studyIndex % colors.length;
        console.log('Aplicando color', colorIndex, 'a elemento índice', index, 'studyIndex:', studyIndex);
        row.eachCell((cell: Cell) => {
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: colors[colorIndex] };
        });
      }
    }

    // Aplicar merge a todas las celdas
    wsPDFFormat.mergeCells(`A${row.number}:M${row.number}`); // Descripción
    wsPDFFormat.mergeCells(`O${row.number}:P${row.number}`); // Frecuencia
    wsPDFFormat.mergeCells(`Q${row.number}:R${row.number}`); // Tiempo
    wsPDFFormat.mergeCells(`S${row.number}:T${row.number}`); // Actividad
    wsPDFFormat.mergeCells(`U${row.number}:V${row.number}`); // Suplementos
    wsPDFFormat.mergeCells(`W${row.number}:X${row.number}`); // Total
  });

  // Agregar nota explicativa para estudios consolidados
  if ((study.optional_info as any)?.consolidated && (study.optional_info as any)?.sourceStudies) {
    // Espacio en blanco
    wsPDFFormat.addRow([]);
    
    // Nota explicativa
    const noteRow = wsPDFFormat.addRow(['NOTA: Los elementos están agrupados visualmente por colores de fondo según el estudio de origen:']);
    noteRow.getCell(1).style = {
      font: { bold: true, color: { argb: 'FF4B5563' }, size: 9 },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFEF3C7' } },
      alignment: { vertical: 'middle', horizontal: 'left', wrapText: true },
      border: {
        top: { style: 'thin', color: { argb: 'FFD97706' } },
        bottom: { style: 'thin', color: { argb: 'FFD97706' } },
        left: { style: 'thin', color: { argb: 'FFD97706' } },
        right: { style: 'thin', color: { argb: 'FFD97706' } }
      }
    };
    wsPDFFormat.mergeCells(`A${noteRow.number}:X${noteRow.number}`);
    noteRow.height = 25;

    // Lista de estudios con sus colores
    const sourceStudies = (study.optional_info as any).sourceStudies || [];
    const colors = [
      { argb: 'FFFFEEE6', name: 'Naranja claro' }, 
      { argb: 'FFF0FDF4', name: 'Verde claro' }, 
      { argb: 'FFFEF7CD', name: 'Amarillo claro' }, 
      { argb: 'FFFDF2F8', name: 'Rosa claro' }, 
      { argb: 'FFF3F4F6', name: 'Gris claro' }, 
      { argb: 'FFEEF2FF', name: 'Índigo claro' }, 
      { argb: 'FFF0FDFA', name: 'Teal claro' }, 
      { argb: 'FFFEFCE8', name: 'Lima claro' }
    ];

    sourceStudies.forEach((sourceStudy: any, index: number) => {
      const colorIndex = index % colors.length;
      const legendRow = wsPDFFormat.addRow([`• Estudio ${index + 1}: ${sourceStudy.name} (${sourceStudy.elementCount} elementos)`]);
      legendRow.getCell(1).style = {
        font: { size: 8, color: { argb: 'FF374151' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: colors[colorIndex] },
        alignment: { vertical: 'middle', horizontal: 'left', wrapText: true },
        border: {
          top: { style: 'thin', color: { argb: 'FFE5E7EB' } },
          bottom: { style: 'thin', color: { argb: 'FFE5E7EB' } },
          left: { style: 'thin', color: { argb: 'FFE5E7EB' } },
          right: { style: 'thin', color: { argb: 'FFE5E7EB' } }
        }
      };
      wsPDFFormat.mergeCells(`A${legendRow.number}:X${legendRow.number}`);
      legendRow.height = 20;
    });
  }

  // Ajustar el ancho de las columnas
  wsPDFFormat.columns = [
    { width: 4 }, // A - Descripción
    { width: 4 }, // B - Descripción
    { width: 4 }, // C - Descripción
    { width: 4 }, // D - Descripción
    { width: 4 }, // E - Descripción
    { width: 4 }, // F - Descripción
    { width: 4 }, // G - Descripción
    { width: 4 }, // H - Descripción
    { width: 4 }, // I - Descripción
    { width: 4 }, // J - Descripción
    { width: 4 }, // K - Descripción
    { width: 4 }, // L - Descripción
    { width: 4 }, // M - Tipo (parte 1)
    { width: 4 }, // N - Tipo (parte 2)
    { width: 4 }, // O - Frecuencia (parte 1)
    { width: 4 }, // P - Frecuencia (parte 2)
    { width: 4 }, // Q - Tiempo (parte 1)
    { width: 4 }, // R - Tiempo (parte 2)
    { width: 3 }, // S - Actividad (parte 1)
    { width: 3 }, // T - Actividad (parte 2)
    { width: 3 }, // U - Suplementos (parte 1)
    { width: 3 }, // V - Suplementos (parte 2)
    { width: 3 }, // W - Total (parte 1)
    { width: 3 }  // X - Total (parte 2)
  ];

  // Configurar impresión para Informe Completo
  wsPDFFormat.pageSetup = {
    orientation: 'portrait',
    printArea: 'A:X',
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0,
    margins: {
      left: 0.7,
      right: 0.7,
      top: 0.75,
      bottom: 0.75,
      header: 0.3,
      footer: 0.3
    }
  };

  // Definir los factores
  const factors = [
    { id: 'A1', description: t('factors.A1', { ns: 'supplements' }) },
    { id: 'A2', description: t('factors.A2', { ns: 'supplements' }) },
    { id: 'A3', description: t('factors.A3', { ns: 'supplements' }) },
    { id: 'A4', description: t('factors.A4', { ns: 'supplements' }) },
    { id: 'A5', description: t('factors.A5', { ns: 'supplements' }) },
    { id: 'B1', description: t('factors.B1', { ns: 'supplements' }) },
    { id: 'B2', description: t('factors.B2', { ns: 'supplements' }) },
    { id: 'B3', description: t('factors.B3', { ns: 'supplements' }) },
    { id: 'B4', description: t('factors.B4', { ns: 'supplements' }) },
    { id: 'C1', description: t('factors.C1', { ns: 'supplements' }) },
    { id: 'C2', description: t('factors.C2', { ns: 'supplements' }) },
    { id: 'C3', description: t('factors.C3', { ns: 'supplements' }) },
    { id: 'C4', description: t('factors.C4', { ns: 'supplements' }) },
    { id: 'C5', description: t('factors.C5', { ns: 'supplements' }) },
    { id: 'C6', description: t('factors.C6', { ns: 'supplements' }) }
  ];

  // Hoja 3: Detalle de Suplementos
  const wsSupplements = workbook.addWorksheet(t('title', { ns: 'supplements' }));
  
  wsSupplements.columns = [
    { header: t('elementDetails.description', { ns: 'report' }), width: 40 },
    { header: t('elementDetails.type', { ns: 'report' }), width: 6 },
    { header: t('tableUsed', { ns: 'supplements' }), width: 10 },
    ...factors.map(f => ({ header: f.id, width: 4 })),
    { header: t('elementDetails.totalPoints', { ns: 'report' }), width: 8 },
    { header: t('elementDetails.conversionPercentage', { ns: 'report' }), width: 8 },
    { header: t('elementDetails.forcedSupplements', { ns: 'report' }), width: 8 }
  ];

  // Dar formato al encabezado de la tabla
  const supplementsTableHeader = wsSupplements.getRow(1);
  supplementsTableHeader.font = { bold: true, color: WHITE_COLOR, size: 9 };
  supplementsTableHeader.fill = { type: 'pattern', pattern: 'solid', fgColor: PURPLE_COLOR };
  supplementsTableHeader.alignment = { vertical: 'middle', horizontal: 'center' };
  supplementsTableHeader.height = 30;

  elementStats.forEach((element, index) => {
    const elementNumber = (index + 1) * 10;
    const elementSupplements = study.supplements?.[element.elementId] || {
      points: {} as Record<string, number>,
      is_forced: false,
      percentage: 0,
      factor_selections: {} as Record<string, any>
    } as ExtendedElementSupplements;

    const points = elementSupplements.points || {};
    const totalPoints = Object.values(points).reduce<number>((sum, value) => sum + (typeof value === 'number' ? value : 0), 0);

    // Detectar el estudio de origen para estudios consolidados
    let studyIndex = 0;
    console.log('=== SUPLEMENTOS DEBUG DETECCIÓN ESTUDIO ===');
    console.log('¿Es consolidado?', !!(study.optional_info as any)?.consolidated);
    console.log('¿Tiene sourceStudies?', !!(study.optional_info as any)?.sourceStudies);
    console.log('Element description:', element.description);
    console.log('Element elementId:', element.elementId);
    
    if ((study.optional_info as any)?.consolidated && (study.optional_info as any)?.sourceStudies) {
      // Buscar el elemento original en el estudio para obtener el name
      const originalElement = study.elements?.find(elem => elem.id === element.elementId);
      console.log('Suplementos - Elemento original encontrado:', !!originalElement);
      
      if (originalElement) {
        // El patrón se encuentra en el name del elemento original
        const elementName = originalElement.name || '';
        console.log('Suplementos - Name del elemento original:', elementName);
        const match = elementName.match(/^(\d+)\./);
        
        if (match) {
          studyIndex = parseInt(match[1]) - 1; // Convertir a índice (0-based)
          console.log('✅ Suplementos - Estudio detectado:', studyIndex + 1, 'para elemento:', elementName);
        } else {
          console.log('❌ Suplementos - No se encontró patrón de estudio en name:', elementName);
        }
      } else {
        console.log('❌ Suplementos - No se encontró elemento original para elementId:', element.elementId);
        console.log('Suplementos - Elementos disponibles:', study.elements?.map(e => ({id: e.id, name: e.name})));
      }
    }
    console.log('=== SUPLEMENTOS FIN DEBUG ===');

    const row = wsSupplements.addRow([
      sanitizeExcelValue(`${elementNumber}- ${element.description}`),
      t(`types.${element.type}`, { ns: 'report' }),
      (elementSupplements as ExtendedElementSupplements).table_type?.toUpperCase() || '-',
      ...factors.map(f => points[f.id] || 0),
      totalPoints,
      elementSupplements.percentage || 0,
      elementSupplements.is_forced ? t('yes', { ns: 'common' }) : t('no', { ns: 'common' })
    ]);

    // Centrar el texto desde la columna B hasta la U y aplicar formato
    row.eachCell((cell: Cell, colNumber: number) => {
      if (colNumber >= 2) { // Columna B en adelante
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
      }
      
      // Aplicar formato de 2 decimales a la columna de porcentaje de suplementos
      if (colNumber === factors.length + 5) { // Columna de porcentaje
        cell.numFmt = '0%';
        cell.value = (elementSupplements.percentage || 0) / 100; // Convertir a decimal para formato porcentaje
      }
    });

    // Aplicar colores de fondo para estudios consolidados
    if ((study.optional_info as any)?.consolidated) {
      const colors = [
        { argb: 'FFFFEEE6' }, // Naranja muy claro
        { argb: 'FFF0FDF4' }, // Verde muy claro
        { argb: 'FFFEF7CD' }, // Amarillo muy claro
        { argb: 'FFFDF2F8' }, // Rosa muy claro
        { argb: 'FFF3F4F6' }, // Gris muy claro
        { argb: 'FFEEF2FF' }, // Índigo muy claro
        { argb: 'FFF0FDFA' }, // Teal muy claro
        { argb: 'FFFEFCE8' }  // Lima muy claro
      ];
      
      const colorIndex = studyIndex % colors.length;
      console.log('Suplementos - Aplicando color', colorIndex, 'a elemento índice', index);
      row.eachCell((cell: Cell) => {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: colors[colorIndex] };
      });
    }
  });

  // Configurar impresión para Suplementos
  wsSupplements.pageSetup = {
    orientation: 'landscape',
    printArea: 'A:U',
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0,
    margins: {
      left: 0.7,
      right: 0.7,
      top: 0.75,
      bottom: 0.75,
      header: 0.3,
      footer: 0.3
    }
  };

  // Hoja 4: Detalle de Tomas de Tiempo
  const wsTimeRecords = workbook.addWorksheet(t('timeRecords', { ns: 'report' }));

  wsTimeRecords.columns = [
    { header: t('elementDetails.description', { ns: 'report' }), width: 40 },
    { header: t('elementDetails.recordNumber', { ns: 'report' }), width: 15 },
    { header: t('elementDetails.time', { ns: 'report' }), width: 15 },
    { header: t('elementDetails.activity', { ns: 'report' }), width: 15 },
    { header: t('elementDetails.comment', { ns: 'report', defaultValue: 'Comentario' }), width: 30 }
  ];

  // Dar formato al encabezado de la tabla
  const timeRecordsTableHeader = wsTimeRecords.getRow(1);
  timeRecordsTableHeader.font = { bold: true, color: WHITE_COLOR, size: 9 };
  timeRecordsTableHeader.fill = { type: 'pattern', pattern: 'solid', fgColor: PURPLE_COLOR };
  timeRecordsTableHeader.alignment = { vertical: 'middle', horizontal: 'center' };
  timeRecordsTableHeader.height = 30;

  elementStats.forEach(element => {
    (study.time_records[element.elementId] || []).forEach((record: { time: number; activity: number; comment?: string }, index: number) => {
      const row = wsTimeRecords.addRow([
        sanitizeExcelValue(element.description),
        index + 1,
        record.time,
        record.activity,
        sanitizeExcelValue(record.comment || '')
      ]);

      // Centrar el texto desde la columna B hasta la D
      row.eachCell((cell: Cell, colNumber: number) => {
        if (colNumber >= 2 && colNumber <= 4) { // Columnas B, C, D
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
        } else if (colNumber === 5) { // Columna E (comentario)
          cell.alignment = { vertical: 'middle', horizontal: 'left' };
        }
      });
    });
  });

  // Configurar impresión para Registros de Tiempo
  wsTimeRecords.pageSetup = {
    orientation: 'portrait',
    printArea: 'A:E',
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0,
    margins: {
      left: 0.7,
      right: 0.7,
      top: 0.75,
      bottom: 0.75,
      header: 0.3,
      footer: 0.3
    }
  };

  // Hoja 5: Registros de CronoSeguido
  if (study.crono_seguido_records && study.crono_seguido_records.length > 0) {
    const wsCronoSeguido = workbook.addWorksheet(t('cronoSeguido.title', { ns: 'cronoSeguido', defaultValue: 'CronoSeguido' }));

    wsCronoSeguido.columns = [
      { header: t('cronoSeguido.recordNumber', { ns: 'cronoSeguido', defaultValue: 'N° Registro' }), width: 15 },
      { header: t('elementDetails.time', { ns: 'report' }), width: 15 },
      { header: t('elementDetails.activity', { ns: 'report' }), width: 15 },
      { header: t('elementDetails.description', { ns: 'report' }), width: 40 },
      { header: t('elementDetails.comment', { ns: 'report', defaultValue: 'Comentario' }), width: 30 },
      { header: t('cronoSeguido.timestamp', { ns: 'cronoSeguido', defaultValue: 'Fecha/Hora' }), width: 20 },
      { header: t('cronoSeguido.addedToMethod', { ns: 'cronoSeguido', defaultValue: 'Añadido al Método' }), width: 15 }
    ];

    // Dar formato al encabezado de la tabla
    const cronoSeguidoTableHeader = wsCronoSeguido.getRow(1);
    cronoSeguidoTableHeader.font = { bold: true, color: WHITE_COLOR, size: 9 };
    cronoSeguidoTableHeader.fill = { type: 'pattern', pattern: 'solid', fgColor: PURPLE_COLOR };
    cronoSeguidoTableHeader.alignment = { vertical: 'middle', horizontal: 'center' };
    cronoSeguidoTableHeader.height = 30;

    study.crono_seguido_records.forEach((record: any, index: number) => {
      const row = wsCronoSeguido.addRow([
        index + 1,
        record.time,
        record.activity,
        sanitizeExcelValue(record.description || ''),
        sanitizeExcelValue(record.comment || ''),
        new Date(record.timestamp).toLocaleString(),
        record.addedToMethod ? t('yes', { ns: 'common' }) : t('no', { ns: 'common' })
      ]);

      // Aplicar formatos
      row.eachCell((cell: Cell, colNumber: number) => {
        if (colNumber === 1 || colNumber === 2 || colNumber === 3 || colNumber === 7) { // Números y booleanos
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
        } else if (colNumber === 4 || colNumber === 5) { // Descripción y comentario
          cell.alignment = { vertical: 'middle', horizontal: 'left' };
        } else if (colNumber === 6) { // Timestamp
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
        }
      });
    });

    // Configurar impresión para CronoSeguido
    wsCronoSeguido.pageSetup = {
      orientation: 'landscape',
      printArea: 'A:G',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      }
    };
  }

  // Agregar hoja de análisis visual si hay datos de gráficas
  if (chartData && Object.keys(chartData).length > 0) {
    const wsCharts = workbook.addWorksheet('Análisis Visual del Proceso');
    
    // Configurar columnas con anchos optimizados
    wsCharts.columns = [
      { width: 25 }, // A - Descripción/etiquetas
      { width: 15 }, // B - Valores
      { width: 8 },  // C - Separador
      { width: 25 }, // D - Descripción/etiquetas
      { width: 15 }, // E - Valores  
      { width: 8 },  // F - Separador
      { width: 25 }, // G - Descripción/etiquetas
      { width: 15 }  // H - Valores
    ];

    let currentRow = 1;

    // Título principal con estilo mejorado
    const titleRow = wsCharts.addRow(['ANÁLISIS VISUAL DEL PROCESO']);
    titleRow.getCell(1).style = {
      font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 18 },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF9333EA' } },
      alignment: { vertical: 'middle', horizontal: 'center' },
      border: {
        top: { style: 'thin', color: { argb: 'FF9333EA' } },
        bottom: { style: 'thin', color: { argb: 'FF9333EA' } },
        left: { style: 'thin', color: { argb: 'FF9333EA' } },
        right: { style: 'thin', color: { argb: 'FF9333EA' } }
      }
    };
    wsCharts.mergeCells(`A${currentRow}:E${currentRow}`);
    wsCharts.getRow(currentRow).height = 45;
    currentRow += 2;

    // Resumen estadístico con diseño mejorado
    if (chartData.summary) {
      const summaryHeaderRow = wsCharts.addRow(['RESUMEN ESTADÍSTICO']);
      summaryHeaderRow.getCell(1).style = {
        font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF3B82F6' } },
        alignment: { vertical: 'middle', horizontal: 'center' },
        border: {
          top: { style: 'thin', color: { argb: 'FF3B82F6' } },
          bottom: { style: 'thin', color: { argb: 'FF3B82F6' } },
          left: { style: 'thin', color: { argb: 'FF3B82F6' } },
          right: { style: 'thin', color: { argb: 'FF3B82F6' } }
        }
      };
      wsCharts.mergeCells(`A${currentRow}:H${currentRow}`);
      wsCharts.getRow(currentRow).height = 35;
      currentRow++;

      // Datos del resumen reorganizados con Actividad Máxima debajo de Elementos Totales
      const summaryData = [
        [
          'Elementos Totales', chartData.summary.totalElements.toString(), '',
          'Saturación Óptima', chartData.summary.optimalSaturation / 100, '',
          '', ''
        ],
        [
          'Actividad Máxima', chartData.summary.maxActivity / 100, '',
          'Saturación Normal', chartData.summary.normalSaturation / 100, '',
          '', ''
        ]
      ];

      summaryData.forEach((rowData, rowIndex) => {
        const row = wsCharts.addRow(rowData);
        row.eachCell((cell: Cell, colNumber: number) => {
          if (colNumber === 1 || colNumber === 4 || colNumber === 7) {
            // Etiquetas
            cell.style = {
              font: { bold: true, color: { argb: 'FF1F2937' }, size: 11 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3F4F6' } },
              alignment: { vertical: 'middle', horizontal: 'left' },
              border: {
                top: { style: 'thin', color: { argb: 'FFD1D5DB' } },
                bottom: { style: 'thin', color: { argb: 'FFD1D5DB' } },
                left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
                right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
              }
            };
          } else if (colNumber === 2 || colNumber === 5 || colNumber === 8) {
            // Valores
            cell.style = {
              font: { bold: true, color: { argb: 'FF3B82F6' }, size: 12 },
              fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
              alignment: { vertical: 'middle', horizontal: 'center' },
              border: {
                top: { style: 'thin', color: { argb: 'FFD1D5DB' } },
                bottom: { style: 'thin', color: { argb: 'FFD1D5DB' } },
                left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
                right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
              }
            };
            
            // Aplicar formato de porcentaje si es un número decimal menor a 1
            if (typeof cell.value === 'number' && cell.value < 1 && cell.value > 0) {
              const percentFormat = formatPercentageForExcel(cell.value * 100);
              cell.numFmt = percentFormat.format;
            }
          }
        });
        wsCharts.getRow(currentRow).height = 25;
        currentRow++;
      });

      currentRow += 2;
    }

    // Sección de gráficas incluidas con mejor diseño
    const chartsHeaderRow = wsCharts.addRow(['GRÁFICAS INCLUIDAS']);
    chartsHeaderRow.getCell(1).style = {
      font: { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF10B981' } },
      alignment: { vertical: 'middle', horizontal: 'center' },
      border: {
        top: { style: 'thin', color: { argb: 'FF10B981' } },
        bottom: { style: 'thin', color: { argb: 'FF10B981' } },
        left: { style: 'thin', color: { argb: 'FF10B981' } },
        right: { style: 'thin', color: { argb: 'FF10B981' } }
      }
    };
    wsCharts.mergeCells(`A${currentRow}:H${currentRow}`);
    wsCharts.getRow(currentRow).height = 35;
    currentRow++;

    // Agregar información sobre las gráficas en formato de lista numerada mejorada
    const chartsInfo = [
      { data: chartData.machineTypes, title: 'Distribución por Tipo de Máquina', status: 'Incluida' },
      { data: chartData.saturation, title: 'Saturación/Rendimiento Normal', status: 'Incluida' },
      { data: chartData.elements, title: 'Tiempos por Elemento', status: 'Incluida' },
      { data: chartData.supplements, title: 'Suplementos por Elemento', status: 'Incluida' },
      { data: chartData.activity, title: 'Actividad por Elemento', status: 'Incluida' },
      { data: chartData.cycleDistribution, title: 'Distribución del Tiempo de Ciclo', status: 'Incluida' }
    ];

    chartsInfo.forEach((chart, index) => {
      if (chart.data) {
        const row = wsCharts.addRow([
          `${index + 1}. ${chart.data.title}`, 
          'Gráfica incluida',
          '', '', '', '', '', ''
        ]);
        
        // Estilo para el número y título de la gráfica
        row.getCell(1).style = {
          font: { bold: true, color: { argb: 'FF1F2937' }, size: 11 },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFEEF2FF' } },
          alignment: { vertical: 'middle', horizontal: 'left', wrapText: true },
          border: {
            top: { style: 'thin', color: { argb: 'FFD1D5DB' } },
            bottom: { style: 'thin', color: { argb: 'FFD1D5DB' } },
            left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
            right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
          }
        };

        // Estilo para el estado de la gráfica
        row.getCell(2).style = {
          font: { bold: true, color: { argb: 'FF10B981' }, size: 10 },
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFFF' } },
          alignment: { vertical: 'middle', horizontal: 'center' },
          border: {
            top: { style: 'thin', color: { argb: 'FFD1D5DB' } },
            bottom: { style: 'thin', color: { argb: 'FFD1D5DB' } },
            left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
            right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
          }
        };

        // Ajustar altura de la fila del título para acomodar títulos largos
        wsCharts.getRow(currentRow).height = 45;
        const titleRowNumber = currentRow;
        currentRow++;

        // Intentar agregar la imagen de la gráfica ALINEADA con el título
        try {
          if (chart.data.imageData) {
            // Convertir base64 a buffer
            const base64Data = chart.data.imageData.split(',')[1];
            const imageId = workbook.addImage({
              base64: base64Data,
              extension: 'png',
            });

            // Agregar la imagen alineada con el título (en la misma fila que el título)
            wsCharts.addImage(imageId, {
              tl: { col: 2, row: titleRowNumber - 1 }, // Usar titleRowNumber - 1 porque row está basado en 0
              ext: { width: 280, height: 180 }, // Tamaño más compacto
              editAs: 'oneCell'
            });

            // Ajustar altura adicional para la imagen
            wsCharts.getRow(titleRowNumber).height = Math.max(45, 140); // Asegurar altura mínima para imagen y título
            currentRow++; // Espacio adicional después de la imagen
          }
        } catch (error) {
          console.error(`Error agregando imagen de gráfica ${chart.title}:`, error);
          // Agregar texto de error con estilo
          const errorRow = wsCharts.addRow(['', 'Error al cargar gráfica', '', '', '', '', '', '']);
          errorRow.getCell(2).style = {
            font: { italic: true, color: { argb: 'FFEF4444' }, size: 10 },
            alignment: { vertical: 'middle', horizontal: 'center' }
          };
          currentRow++;
        }

        // Separador entre gráficas (más pequeño)
        const separatorRow = wsCharts.addRow(['', '', '', '', '', '', '', '']);
        separatorRow.getCell(1).style = {
          fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF9FAFB' } }
        };
        wsCharts.mergeCells(`A${currentRow}:H${currentRow}`);
        wsCharts.getRow(currentRow).height = 8;
        currentRow++;
      }
    });

    // Nota final con estilo mejorado
    currentRow += 1;
    const noteRow = wsCharts.addRow(['💡 Nota: Las gráficas completas también están disponibles en la exportación PDF con mejor resolución y formato optimizado para impresión.']);
    noteRow.getCell(1).style = {
      font: { italic: true, color: { argb: 'FF6B7280' }, size: 11 },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFEF3C7' } },
      alignment: { vertical: 'middle', horizontal: 'left', wrapText: true },
      border: {
        top: { style: 'thin', color: { argb: 'FFD97706' } },
        bottom: { style: 'thin', color: { argb: 'FFD97706' } },
        left: { style: 'thin', color: { argb: 'FFD97706' } },
        right: { style: 'thin', color: { argb: 'FFD97706' } }
      }
    };
    wsCharts.mergeCells(`A${currentRow}:H${currentRow}`);
    wsCharts.getRow(currentRow).height = 40;

    // Agregar filtros automáticos a la hoja
    wsCharts.autoFilter = 'A1:H1';

    // Proteger las celdas con fórmulas (opcional)
    wsCharts.protect('', {
      selectLockedCells: true,
      selectUnlockedCells: true,
      formatCells: false,
      formatColumns: false,
      formatRows: false,
      insertRows: false,
      insertColumns: false,
      deleteRows: false,
      deleteColumns: false
    });

    // Configurar impresión para Análisis Visual del Proceso
    wsCharts.pageSetup = {
      orientation: 'portrait',
      printArea: 'A:E',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      }
    };
  }

  // Agregar hojas de unidades personalizadas si están habilitadas
  const customUnits = study.optional_info?.customUnits;
  if (customUnits) {
    const enabledUnits = [
      { type: 'squareMeters' as const, enabled: customUnits.enableSquareMeters, name: 'Metro Cuadrado', symbol: 'm²' },
      { type: 'linearMeters' as const, enabled: customUnits.enableLinearMeters, name: 'Metro Lineal', symbol: 'm' },
      { type: 'cubicMeters' as const, enabled: customUnits.enableCubicMeters, name: 'Metro Cúbico', symbol: 'm³' },
      { type: 'kilos' as const, enabled: customUnits.enableKilos, name: 'Kilogramo', symbol: 'kg' },
      { type: 'perimeter' as const, enabled: customUnits.enablePerimeter, name: 'Perímetro', symbol: 'm' }
    ].filter(unit => unit.enabled);

    for (const unitConfig of enabledUnits) {
      const customValues = calculateCustomUnitValues(unitConfig.type, customUnits, reportStats, shiftMinutes, timeUnit);
      
      if (customValues) {
        // Crear una nueva hoja para esta unidad
        const wsCustomUnit = workbook.addWorksheet(`Resultados por ${unitConfig.symbol}`);
        
        // Configurar columnas
        wsCustomUnit.columns = [
          { width: 4 }, { width: 4 }, { width: 4 }, { width: 4 }, // A-D
          { width: 4 }, { width: 4 }, { width: 4 }, { width: 4 }, // E-H
          { width: 4 }, { width: 4 }, { width: 4 }, { width: 4 }, // I-L
          { width: 4 }, { width: 4 }, { width: 4 }, { width: 4 }, // M-P
          { width: 4 }, { width: 4 }, { width: 4 }, { width: 4 }, // Q-T
          { width: 4 }, { width: 4 }, { width: 4 }, { width: 4 }  // U-X
        ];

        // Título de la unidad personalizada
        const unitTitle = `RESULTADOS POR ${unitConfig.name.toUpperCase()} (${formatNumberAsString(customValues.unitValue, { decimals: 3 })} ${unitConfig.symbol})`;
        const titleRow = wsCustomUnit.addRow([unitTitle]);
        titleRow.font = { bold: true, color: { argb: 'FF3B82F6' }, size: 14 };
        titleRow.alignment = { vertical: 'middle', horizontal: 'center' };
        wsCustomUnit.mergeCells('A1:X1');
        wsCustomUnit.getRow(1).height = 40;

        // Espacio en blanco
        wsCustomUnit.addRow([]);

        // Información de configuración
        const configHeaderRow = wsCustomUnit.addRow(['CONFIGURACIÓN DE LA UNIDAD']);
        configHeaderRow.getCell(1).style = headerStyle;
        configHeaderRow.height = 30;
        wsCustomUnit.mergeCells('A3:X3');

        // Mostrar la configuración específica de la unidad
        let configData: string[][] = [];
        switch (unitConfig.type) {
          case 'squareMeters':
            configData = [
              ['Largo (m)', '', '', '', customUnits.squareMeters.length.toString(), '', '', '', 
               'Ancho (m)', '', '', '', customUnits.squareMeters.width.toString(), '', '', '',
               'Total (m²)', '', '', '', formatNumberAsString(customValues.unitValue, { decimals: 3 }), '', '', '']
            ];
            break;
          case 'linearMeters':
            configData = [
              ['Longitud (m)', '', '', '', customUnits.linearMeters.length.toString(), '', '', '',
               'Total (m)', '', '', '', formatNumberAsString(customValues.unitValue, { decimals: 3 }), '', '', '',
               '', '', '', '', '', '', '', '']
            ];
            break;
          case 'cubicMeters':
            configData = [
              ['Largo (m)', '', '', '', customUnits.cubicMeters.length.toString(), '', '', '',
               'Ancho (m)', '', '', '', customUnits.cubicMeters.width.toString(), '', '', '',
               'Alto (m)', '', '', '', customUnits.cubicMeters.height.toString(), '', '', ''],
              ['Total (m³)', '', '', '', formatNumberAsString(customValues.unitValue, { decimals: 3 }), '', '', '',
               '', '', '', '', '', '', '', '',
               '', '', '', '', '', '', '', '']
            ];
            break;
          case 'kilos':
            configData = [
              ['Peso (kg)', '', '', '', customUnits.kilos.weight.toString(), '', '', '',
               'Total (kg)', '', '', '', formatNumberAsString(customValues.unitValue, { decimals: 3 }), '', '', '',
               '', '', '', '', '', '', '', '']
            ];
            break;
          case 'perimeter':
            configData = [
              ['Largo (m)', '', '', '', customUnits.perimeter.length.toString(), '', '', '',
               'Ancho (m)', '', '', '', customUnits.perimeter.width.toString(), '', '', '',
               'Total (m)', '', '', '', formatNumberAsString(customValues.unitValue, { decimals: 3 }), '', '', '']
            ];
            break;
        }

        configData.forEach((rowData) => {
          const row = wsCustomUnit.addRow(rowData);
          row.eachCell((cell: Cell, colNumber: number) => {
            if (colNumber % 8 === 1) {
              cell.style = labelStyle;
              cell.alignment = { vertical: 'middle', horizontal: 'left' };
            } else if (colNumber % 8 === 5) {
              cell.style = valueStyle;
              cell.alignment = { vertical: 'middle', horizontal: 'center' };
            }
          });
          // Unir celdas para cada grupo de cuatro columnas
          for (let i = 0; i < 24; i += 4) {
            wsCustomUnit.mergeCells(row.number, i + 1, row.number, i + 4);
          }
        });

        // Espacio entre secciones
        wsCustomUnit.addRow([]);

        // Resultados por unidad personalizada
        const resultsHeaderRow = wsCustomUnit.addRow([`RESULTADOS POR ${unitConfig.symbol.toUpperCase()}`]);
        resultsHeaderRow.getCell(1).style = headerStyle;
        wsCustomUnit.mergeCells(`A${resultsHeaderRow.number}:X${resultsHeaderRow.number}`);

        const unitResultsData = [
          [
            `${t('customUnits.normalTime', { ns: 'report' })}/${unitConfig.symbol}`, '', '', '', 
            customValues.normalCycle, '', '', '',
            `${t('customUnits.optimalTime', { ns: 'report' })}/${unitConfig.symbol}`, '', '', '', 
            customValues.optimalCycle, '', '', '',
            '', '', '', '', '', '', '', ''
          ],
          [
            `${t('customUnits.normalProdShift', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.normalProduction.toString(), '', '', '',
            `${t('customUnits.optimalProdShift', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.optimalProduction.toString(), '', '', '',
            '', '', '', '', '', '', '', ''
          ],
          [
            `${t('customUnits.normalProdHour', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.normalProductionPerHour, '', '', '',
            `${t('customUnits.optimalProdHour', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.optimalProductionPerHour, '', '', '',
            '', '', '', '', '', '', '', ''
          ],
          [
            `${t('customUnits.valueHour', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.valueHour, '', '', '',
            `${t('customUnits.valueMinute', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.valueMinute, '', '', '',
            `${t('customUnits.valuePoint', { ns: 'report' })} (${unitConfig.symbol})`, '', '', '', 
            customValues.valuePoint, '', '', ''
          ]
        ];

        unitResultsData.forEach((rowData, rowIndex) => {
          const row = wsCustomUnit.addRow(rowData);
          row.eachCell((cell: Cell, colNumber: number) => {
            if (colNumber % 8 === 1) {
              cell.style = labelStyle;
              cell.alignment = { vertical: 'middle', horizontal: 'left' };
            } else if (colNumber % 8 === 5) {
              cell.style = valueStyle;
              cell.alignment = { vertical: 'middle', horizontal: 'right' };
              
              // Aplicar formato de número si es un número
              if (typeof cell.value === 'number') {
                let decimals = 4; // Por defecto para valores de tiempo
                if (rowIndex === 2) { // Producción por hora
                  decimals = 2;
                } else if (rowIndex === 1) { // Producción por turno (enteros)
                  decimals = 0;
                }
                const numberFormat = formatNumberForExcel(cell.value, { decimals });
                cell.numFmt = numberFormat.format;
              }
            }
          });
          // Unir celdas para cada grupo de cuatro columnas
          for (let i = 0; i < 24; i += 4) {
            wsCustomUnit.mergeCells(row.number, i + 1, row.number, i + 4);
          }
        });

        // Espacio entre secciones
        wsCustomUnit.addRow([]);

        // Nota explicativa
        const noteRow = wsCustomUnit.addRow([`Nota: Estos valores representan los tiempos calculados por ${unitConfig.symbol}. Los valores de producción indican cuántos ${unitConfig.symbol} se pueden procesar en el tiempo especificado. Los valores por hora/minuto/punto indican el tiempo necesario para procesar una unidad de ${unitConfig.symbol}.`]);
        noteRow.getCell(1).style = { 
          font: { italic: true, color: GRAY_COLOR, size: 10 },
          alignment: { vertical: 'middle', horizontal: 'left', wrapText: true }
        };
        wsCustomUnit.mergeCells(`A${noteRow.number}:X${noteRow.number}`);
        noteRow.height = 40;

        // Configurar impresión para hojas de unidades personalizadas
        wsCustomUnit.pageSetup = {
          orientation: 'landscape',
          printArea: 'A:X',
          fitToPage: true,
          fitToWidth: 1,
          fitToHeight: 0,
          margins: {
            left: 0.7,
            right: 0.7,
            top: 0.75,
            bottom: 0.75,
            header: 0.3,
            footer: 0.3
          }
        };
      }
    }
  }

  // Agregar hoja de Diagrama de Flujo de Operación
  await addFlowchartWorksheet(workbook, elementStats, timeUnit, t, study);

  // Guardar el archivo (DESPUÉS de agregar todas las hojas)
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${sanitizeExcelValue(studyName)}-${t('report', { ns: 'common' })}.xlsx`;
  link.click();
  window.URL.revokeObjectURL(url);
}