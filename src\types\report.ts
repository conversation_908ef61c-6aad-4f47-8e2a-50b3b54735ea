// export type TimeUnit = 'seconds' | 'minutes' | 'mmm' | 'cmm' | 'tmu' | 'dmh';

// export interface ElementStats {
//   id: string;
//   elementId: string;
//   description: string;
//   type: string;
//   frequency: string;
//   observedTime: number;
//   averageActivity: number;
//   supplements: number;
//   finalTime: number;
//   concurrent_machine_time?: boolean;
// }

// export interface ReportStats {
//   normalCycle: number;
//   optimalCycle: number;
//   contingencyTime: number;
//   totalK: number;
//   normalProduction: number;
//   optimalProduction: number;
//   normalProductionPerHour: number;
//   optimalProductionPerHour: number;
//   normalSaturation: number;
//   optimalSaturation: number;
//   maxActivity: number;
//   machineStoppedTime: number;
//   machineRunningTime: number;
//   machineTime: number;
//   valueHour: number;
//   valueMinute: number;
//   valuePoint: number;
// }