# Time Utilities

## Descripción
Utilidades para el manejo y formateo de tiempos.

## Funciones

### formatTime
```typescript
function formatTime(ms: number): string
```
Formatea milisegundos a string con formato "ss.dd".

### calculateAverageTime
```typescript
function calculateAverageTime(times: number[]): number
```
Calcula el tiempo promedio de una lista de tiempos.

### validateTimeRange
```typescript
function validateTimeRange(time: number, min: number, max: number): boolean
```
Valida si un tiempo está dentro de un rango aceptable.

## Uso
```typescript
// Formatear tiempo
const timeString = formatTime(2044); // "2.04"

// Calcular promedio
const avgTime = calculateAverageTime([2.04, 2.15, 1.98]); // 2.06

// Validar rango
const isValid = validateTimeRange(2.04, 1.5, 2.5); // true
```