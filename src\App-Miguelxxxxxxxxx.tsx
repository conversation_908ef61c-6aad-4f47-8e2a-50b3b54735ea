import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Info, ListTodo, Timer, Gauge, Clock, ClipboardList, FileText, UserPlus, BookOpen, Watch } from 'lucide-react';
import { Header } from './components/Header';
import { StudyCard } from './components/StudyCard';
import { StudyPage } from './pages/StudyPage';
import { LoginPage } from './pages/LoginPage';
import { DeleteConfirmModal } from './components/DeleteConfirmModal';
import { StudyFilters } from './components/StudyFilters';
import { LoginForm } from './components/LoginForm';
import { MethodPage } from './pages/MethodPage';
import { ChronometerPage } from './pages/ChronometerPage';
import { FrequencyPage } from './pages/FrequencyPage';
import { MachinePage } from './pages/MachinePage';
import { SupplementsPage } from './pages/SupplementsPage';
import { LibraryPage } from './pages/LibraryPage';
import { ReportPage } from './pages/ReportPage';
import { CronoSeguidoPage } from './pages/CronoSeguidoPage';
import { useAuthStore } from './store/authStore';
import { useStudyStore } from './store/studyStore';
import { Study } from './types';
import { useWakeLock } from './hooks/useWakeLock';
import { ProfilePage } from './pages/ProfilePage';
import { DocsPage } from './pages/DocsPage';
import { Toaster } from './components/ui/Toaster'; // Actualizar la importación del Toaster
import { UpdatePrompt } from './components/UpdatePrompt';
import { InstallBanner } from './components/InstallBanner';
import { InstallPWA } from './components/InstallPWA';
import { usePWAStore } from './store/pwaStore';
import { AuthCallback } from './pages/AuthCallback';
import { useUserInitialization } from './hooks/useUserInitialization';
import { useSearchStore } from './store/searchStore';
import { ResetPassword } from './pages/ResetPassword';
import { UpdatePassword } from './pages/UpdatePassword';
import { PaymentSuccessPage } from './pages/PaymentSuccessPage';
import { PaymentCancelPage } from './pages/PaymentCancelPage';
import { BudgetPage } from './pages/BudgetPage';

function App() {
  const { t } = useTranslation(['study', 'library', 'cronoSeguido', 'common']);
  const navigate = useNavigate();
  const location = useLocation();
  const user = useAuthStore(state => state.user);
  const isLoading = useAuthStore(state => state.isLoading);
  const isInitialized = useAuthStore(state => state.isInitialized);
  const initializeSession = useAuthStore(state => state.initializeSession);
  const { studies, selectedStudy, setSelectedStudy, fetchStudies, deleteStudy } = useStudyStore();
  const { searchQuery, filterStudies } = useSearchStore();
  const { requestInstall } = usePWAStore();

  // Activar wake lock para mantener la pantalla encendida
  useWakeLock();

  // Inicializar sesión y usuario
  useEffect(() => {
    const init = async () => {
      try {
        await initializeSession();
      } catch (error) {
        console.error('Error initializing session:', error);
      }
    };
    init();
  }, []);

  useUserInitialization();

  // Fetch studies when user is logged in and session is initialized
  useEffect(() => {
    const loadStudies = async () => {
      if (user?.id && isInitialized && !isLoading) {
        console.log('Loading studies for user:', user.id);
        await fetchStudies();
      }
    };
    loadStudies();
  }, [user?.id, isInitialized, isLoading, fetchStudies]);

  // Fetch studies when navigating to home page
  useEffect(() => {
    const loadStudies = async () => {
      if (location.pathname === '/' && user?.id && isInitialized && !isLoading) {
        console.log('Reloading studies on home page navigation');
        await fetchStudies();
      }
    };
    loadStudies();
  }, [location.pathname, user?.id, isInitialized, isLoading, fetchStudies]);

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [studyToDelete, setStudyToDelete] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    company: false,
    date: false,
    process: false
  });

  // Filtrar estudios basados en la búsqueda y filtros
  const filteredStudies = useMemo(() => {
    let filtered = filterStudies(studies);

    // Aplicar filtros adicionales
    if (filters.company && selectedStudy) {
      filtered = filtered.filter(study => study.info.required.company === selectedStudy.info.required.company);
    }
    if (filters.date && selectedStudy) {
      filtered = filtered.filter(study => study.info.required.date === selectedStudy.info.required.date);
    }
    if (filters.process && selectedStudy) {
      filtered = filtered.filter(study => study.info.required.process === selectedStudy.info.required.process);
    }

    return filtered;
  }, [studies, searchQuery, filters, selectedStudy, filterStudies]);

  const handleSearch = (query: string) => {
    useSearchStore.getState().setSearchQuery(query);
  };

  // Efecto para seleccionar el estudio cuando se navega a la página principal
  useEffect(() => {
    const state = location.state as { selectStudyId?: string } | null;
    
    if (state?.selectStudyId && studies.length > 0) {
      const study = studies.find(s => s.id === state.selectStudyId);
      if (study) {
        setSelectedStudy(study);
        // Limpiar el estado después de seleccionar el estudio
        navigate(location.pathname, { replace: true });
      }
    }
  }, [location.state, studies, setSelectedStudy, navigate, location.pathname]);

  const handleNavigateToHome = () => {
    navigate('/');
  };

  const handleStudyClick = (study: Study) => {
    setSelectedStudy(study);
  };

  const handleDeleteStudy = (studyId: string) => {
    setStudyToDelete(studyId);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (studyToDelete) {
      await deleteStudy(studyToDelete);
      setShowDeleteModal(false);
      setStudyToDelete(null);
    }
  };

  const handleStudyInfo = () => {
    if (selectedStudy) {
      navigate(`/study/${selectedStudy.id}/info`);
    }
  };

  const handleNewStudy = () => {
    setSelectedStudy(null);
    navigate('/study');
  };

  const getNavigationButtons = () => {
    if (!selectedStudy) return [];

    const hasRepetitiveElements = selectedStudy.elements.some(e => 
      e.repetition_type === 'repetitive-type' || e.repetition_type === 'repetitive'
    );
    const hasMachineElements = selectedStudy.elements.some(e => 
      e.type === 'machine-time' || e.repetition_type === 'machine-type'
    );
    const hasFrequentialElements = selectedStudy.elements.some(e => 
      e.repetition_type === 'frequency-type' || e.repetition_type === 'frequency'
    );

    const buttons = [
      { to: 'info', text: t('study:info'), icon: Info },
      { to: 'method', text: t('study:method'), icon: ListTodo }
    ];

    if (hasRepetitiveElements) {
      buttons.push({ to: 'repetitive', text: t('study:chronometer'), icon: Timer });
    }

    // Cronómetro seguido siempre debe estar presente
    buttons.push({ to: 'cronoseguido', text: t('cronoSeguido:title'), icon: Watch });

    if (hasMachineElements) {
      buttons.push({ to: 'machine', text: t('study:machineTimes'), icon: Gauge });
    }

    if (hasFrequentialElements) {
      buttons.push({ to: 'frequency', text: t('study:frequencyTimes'), icon: Clock });
    }

    if (selectedStudy.elements.length > 0) {
      buttons.push({ to: 'supplements', text: t('study:supplements'), icon: UserPlus });
    }

    if (selectedStudy.elements.length > 0 && selectedStudy.elements.every(element => {
      const hasTimeRecords = selectedStudy.time_records[element.id]?.length > 0;
      const hasSupplements = selectedStudy.supplements[element.id]?.percentage > 0;
      return hasTimeRecords && hasSupplements;
    })) {
      buttons.push({ to: 'report', text: t('study:report'), icon: FileText });
    }

    return buttons;
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      );
    }

    return (
      <div className="flex">
        <div className="flex-1 flex flex-col min-h-screen">
          <Routes>
            <Route path="/login" element={!user ? <LoginPage /> : <Navigate to="/" />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/update-password" element={<UpdatePassword />} />
            <Route path="/auth/v1/callback" element={<AuthCallback />} />
            <Route path="/docs" element={<DocsPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/study" element={<StudyPage />} />
            <Route path="/study/:studyId/*" element={
              <Routes>
                <Route path="info" element={<StudyPage onBack={() => navigate(-1)} />} />
                <Route path="method" element={<MethodPage onBack={() => navigate(-1)} />} />
                <Route path="chronometer" element={<ChronometerPage onBack={() => navigate(-1)} />} />
                <Route path="repetitive" element={<ChronometerPage onBack={() => navigate(-1)} />} />
                <Route path="frequency" element={<FrequencyPage onBack={() => navigate(-1)} />} />
                <Route path="machine" element={<MachinePage onBack={() => navigate(-1)} />} />
                <Route path="supplements" element={<SupplementsPage onBack={() => navigate(-1)} />} />
                <Route path="library" element={<LibraryPage onBack={() => navigate(-1)} />} />
                <Route path="report" element={<ReportPage onBack={() => navigate(-1)} />} />
                <Route path="cronoseguido" element={<CronoSeguidoPage onBack={() => navigate(-1)} />} />
              </Routes>
            } />
            <Route path="/payment/success" element={<PaymentSuccessPage />} />
            <Route path="/payment/cancel" element={<PaymentCancelPage />} />
            <Route path="/budget" element={<BudgetPage />} />
            <Route path="/" element={
              user ? (
                <div className="min-h-screen bg-gray-100">
                  <Header 
                    title={t('common:appName')}
                    subtitle={selectedStudy?.info?.required?.name}
                    onSearch={handleSearch}
                  />
                  <main className="container mx-auto px-4 py-6">
                    <div className="space-y-6">
                      <StudyFilters
                        selectedStudy={selectedStudy}
                        filters={filters}
                        onFilterChange={(filter, value) => 
                          setFilters(prev => ({ ...prev, [filter]: value }))
                        }
                      />

                      <div className="flex space-x-4">
                        <button
                          onClick={handleNewStudy}
                          className="flex-1 bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 flex items-center justify-center space-x-2"
                        >
                          <span>{t('study:new')}</span>
                        </button>
                        <button
                          onClick={() => navigate('/library')}
                          className="flex-1 bg-purple-500 text-white py-3 px-4 rounded-lg hover:bg-purple-600 flex items-center justify-center space-x-2"
                        >
                          <BookOpen className="w-5 h-5" />
                          <span>{t('library:title')}</span>
                        </button>
                      </div>

                      {selectedStudy && (
                        <div className="w-full my-6">
                          <div className="overflow-x-auto">
                            <div className="flex items-center justify-start space-x-2 sm:space-x-4 pb-4 w-max min-w-full">
                              {getNavigationButtons().map(button => (
                                <button
                                  key={button.to}
                                  onClick={() => navigate(`/study/${selectedStudy.id}/${button.to}`)}
                                  className="flex flex-col items-center space-y-1 px-2 py-2 sm:px-4 bg-amber-400 text-gray-700 rounded-lg shadow hover:bg-amber-500 flex-shrink-0 min-w-[80px] sm:min-w-[100px]"
                                >
                                  <button.icon className="w-4 h-4 sm:w-5 sm:h-5" />
                                  <span className="text-xs sm:text-sm leading-tight text-center">{button.text}</span>
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Lista de estudios filtrada */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredStudies.map((study) => (
                          <StudyCard
                            key={study.id}
                            study={study}
                            selected={selectedStudy?.id === study.id}
                            onClick={() => handleStudyClick(study)}
                            onDelete={() => handleDeleteStudy(study.id)}
                          />
                        ))}
                      </div>
                    </div>
                  </main>
                </div>
              ) : (
                <Navigate to="/login" />
              )
            } />
            <Route path="*" element={<Navigate to={user ? "/" : "/login"} />} />
          </Routes>
        </div>
      </div>
    );
  };

  return (
    <>
      {renderContent()}
      <Toaster />
      <UpdatePrompt />
      <InstallBanner />
      <InstallPWA />
      {showDeleteModal && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleConfirmDelete}
        />
      )}
    </>
  );
}

export default App;