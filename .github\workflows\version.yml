name: Update Version and Changelog

on:
  push:
    branches:
      - main

jobs:
  update-version:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Update Version and Changelog
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          node scripts/update-version.js
      
      - name: Commit changes
        run: |
          git add src/config/version.ts CHANGELOG.md
          git commit -m "chore: update version and changelog [skip ci]" || exit 0
          git push
