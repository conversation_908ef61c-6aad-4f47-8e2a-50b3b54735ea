export interface CronoSeguidoRecord {
  id: string;
  time: number;
  activity: number;
  description: string;
  timestamp: string;
  addedToMethod: boolean;
  elementId?: string;
  comment?: string;
  frequency_repetitions?: number;
  frequency_cycles?: number;
  original_time?: number;
}

export interface TimeRecord extends CronoSeguidoRecord {}

export interface Study {
  id: string;
  user_id: string;
  required_info: {
    name: string;
    company: string;
    date: string;
    activity_scale: {
      normal: number;
      optimal: number;
    };
  };
  optional_info: {
    tools: string;
    machine: string;
    section: string;
    operator: string;
    reference: string;
    technician: string;
    study_number: string;
    studyContingency?: number;
    visibleEnBiblioteca?: boolean;
    isEstimated?: boolean;
    consolidated?: boolean;
    sourceStudies?: {
      id: string;
      name: string;
      elementCount: number;
      company: string;
      date: string;
    }[];
    customUnits?: {
      enableSquareMeters: boolean;
      enableLinearMeters: boolean;
      enableCubicMeters: boolean;
      enableKilos: boolean;
      enablePerimeter: boolean;
      squareMeters: {
        length: number;
        width: number;
      };
      linearMeters: {
        length: number;
      };
      cubicMeters: {
        length: number;
        width: number;
        height: number;
      };
      kilos: {
        weight: number;
      };
      perimeter: {
        length: number;
        width: number;
      };
    };
  };
  elements: ElementInstance[];
  time_records: Record<string, {
    id: string;
    time: number;
    activity: number;
    elementId: string;
    timestamp: string;
    comment?: string;
  }[]>;
  supplements: Record<string, {
    points: Record<string, number>;
    is_forced: boolean;
    percentage: number;
    factor_selections: Record<string, FactorSelection>;
    table_type?: string;
  }>;
  machine_cycle_data?: {
    totalCycleTime: number;
    baseTime: number;
    personalNeedsSupplement: number;
    fatigueSupplement: number;
    remainingFatigue?: number;
    inactivityTime: number;
    calculationCase: string;
    timestamp: string;
  };
  cycle_preference?: 'report' | 'supplements';
  crono_seguido_records: {
    id: string;
    time: number;
    activity: number;
    timestamp: string;
    description: string;
    elementId?: string;
    addedToMethod: boolean;
  }[];
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
  time_unit?: TimeUnit;
  type?: string;
}

export interface ElementInstance {
  id: string;
  name: string;
  type: string;
  position: number;
  description: string;
  repetition_type: string;
  frequency_cycles: number;
  frequency_repetitions: number;
  time?: number;
  activity?: number;
  isAveraged?: boolean;
  concurrent_machine_time?: boolean;
  sourceElements?: {
    id: string;
    description: string;
    time: number;
  }[];
  supplements?: {
    id: string;
    name: string;
    percentage: number;
    description: string;
    is_forced?: boolean;
    points?: Record<string, number>;
    factor_selections?: Record<string, FactorSelection>;
  }[];
  timeRecords?: TimeRecord[];
}

export interface ElementSupplements {
  points: Record<string, number>;
  percentage: number;
  is_forced: boolean;
  factor_selections: Record<string, FactorSelection>;
  standingWork?: boolean;
}

export interface FactorSelection {
  index?: number;
  indices?: number[];
  intensity?: 'Ligero' | 'Mediano' | 'Pesado';
  temperature?: number;
  humidity?: number;
}

export interface LibraryElement {
  id: string;
  user_id: string;
  description: string;
  type: 'machine-stopped' | 'machine-running' | 'machine-time';
  frequency_repetitions: number;
  frequency_cycles: number;
  repetition_type: 'repetitive' | 'frequency' | 'machine';
  is_shared: boolean;
  time_records: TimeRecord[];
  time_stats: {
    average_time: number;
    total_time: number;
    average_activity: number;
    takes: number;
  };
  supplements: ElementSupplements;
  created_at: string;
  updated_at: string;
}

export interface UserStudyLimits {
  id: string;
  user_id: string;
  user_email: string;
  created_at: string;
  updated_at: string;
  monthly_credits: number;
  extra_credits: number;
  reset_date: string;
  used_monthly_credits: number;
  used_extra_credits: number;
  subscription_plan?: string | null;
}

export interface StudyCredit {
  hasAvailableCredits: boolean;
  remainingCredits: number;
  monthlyCredits: number;
  extraCredits: number;
  usedMonthlyCredits: number;
  usedExtraCredits: number;
}

export interface StudyStore {
  studies: Study[];
  selectedStudy: Study | null;
  isLoading: boolean;
  error: string | null;
  credits: number;
  fetchStudies: () => Promise<void>;
  fetchStudyById: (studyId: string) => Promise<Study | null>;
  createStudy: (study: Partial<Study>) => Promise<Study>;
  updateStudy: (id: string, study: Partial<Study>) => Promise<Study>;
  deleteStudy: (id: string) => Promise<void>;
  setSelectedStudy: (study: Study | null) => void;
  clearSelectedStudy: () => void;
  resetError: () => void;
  cloneStudy: (study: Study) => Promise<Study>;
}

// Tipos para los suplementos
export type SupplementsData = {
  [supplementId: string]: Supplement;
}

export interface Supplement {
  points: {
    [factorId: string]: number;
  };
  is_forced: boolean;
  percentage: number;
  factor_selections: {
    [factorId: string]: FactorSelection;
  };
}

// Tipos para los factores
export interface Factor {
  id: string;
  category: string;
  name: string;
  type: FactorType;
  options?: FactorOption[];
}

export type FactorType = 'single' | 'multiple' | 'intensity' | 'environmental';

export interface FactorOption {
  description: string;
  points: number;
}

export interface UserProfile {
  id: string;
  email: string;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

export type TimeUnit = 'minutes' | 'hours' | 'seconds' | 'mmm' | 'cmm' | 'tmu' | 'dmh';

export interface ReportStats {
  totalTime?: number;
  averageTime?: number;
  totalActivity?: number;
  averageActivity?: number;
  takes?: number;
  timeUnit: TimeUnit;
  normalCycle: number;
  optimalCycle: number;
  contingencyTime: number;
  totalK: number;
  normalProduction: number;
  optimalProduction: number;
  normalProductionPerHour: number;
  optimalProductionPerHour: number;
  normalSaturation: number;
  optimalSaturation: number;
  maxActivity: number;
  machineStoppedTime: number;
  machineRunningTime: number;
  machineTime: number;
  valueHour: number;
  valueMinute: number;
  valuePoint: number;
  // Cycle discrepancy data
  supplementsCycleTime?: number;
  cycleDiscrepancy?: {
    hasDiscrepancy: boolean;
    difference: number;
    selectedCycleSource: 'report' | 'supplements';
    supplementsCycleTime: number;
    reportCycleTime: number;
  };
}

export interface ElementStats {
  id: string;
  elementId: string;
  description: string;
  type: string;
  frequency: string;
  observedTime: number;
  averageActivity: number;
  supplements: number;
  finalTime: number;
  timeUnit: TimeUnit;
  concurrent_machine_time?: boolean;
}

export interface WorkElement {
  id: string;
  description: string;
  time: number;
  supplements?: Supplement[];
  frequency_repetitions: number;
  frequency_cycles: number;
  isAveraged?: boolean;
  studyName?: string;
  sourceElements?: {
    id: string;
    description: string;
    studyName?: string;
  }[];
}

export interface StudyWithFolder extends Study {
  folder_id?: string;
  folder?: Pick<Folder, 'id' | 'name' | 'color' | 'icon'>;
  folder_path?: string;
}

export interface Folder {
  id: string;
  user_id: string;
  organization_id?: string;
  name: string;
  description?: string;
  parent_folder_id?: string;
  color: string;
  icon: string;
  is_shared: boolean;
  created_at: string;
  updated_at: string;
  
  // Computed properties (not from DB)
  path?: string;
  children?: Folder[];
  study_count?: number;
  level?: number;
}

export interface FolderTreeNode extends Folder {
  children: FolderTreeNode[];
  studies?: Study[];
  isExpanded?: boolean;
  hasChildren?: boolean;
}

export interface BulkExportRequest {
  folder_id?: string;
  include_subfolders: boolean;
  export_format: 'excel' | 'pdf' | 'csv';
  date_range?: {
    start: string;
    end: string;
  };
}