# Pantalla de Método

## Descripción
Interfaz para la definición y gestión de elementos del estudio de tiempos.

## Componentes
- `MethodList`: Lista ordenable de elementos
- `MethodForm`: Formulario de creación/edición
- `DeleteConfirmModal`: Modal de confirmación
- `ActionButton`: Botón de añadir elemento

## Funcionalidades

### 1. Gestión de Elementos
- Creación de nuevos elementos
- Edición de elementos existentes
- Eliminación de elementos
- Reordenamiento mediante drag & drop

### 2. Tipos de Elementos
- Máquina parada
- Máquina en marcha
- Tiempo de máquina

### 3. Configuración de Repeticiones
- Tipo repetitivo
- Tipo frecuencial
- Tipo máquina

### 4. Integración con Biblioteca
- Opción de guardar en biblioteca
- Reutilización de elementos existentes

## Estados
```typescript
{
  elements: WorkElement[];
  showForm: boolean;
  editingElement: WorkElement | null;
  showDeleteModal: boolean;
  elementToDelete: string | null;
}
```

## Flujo de Trabajo
1. Visualización de elementos existentes
2. Usuario puede:
   - Añadir nuevo elemento
   - Editar elemento existente
   - Eliminar elemento
   - Reordenar elementos
3. Al crear/editar:
   - Configurar tipo y repeticiones
   - Opción de guardar en biblioteca
4. Confirmación para eliminación