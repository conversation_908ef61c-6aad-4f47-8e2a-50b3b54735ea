import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock, Activity, Repeat, CheckCircle2, Circle } from 'lucide-react';
import { ElementInstance } from '../../types';

interface StudyElementsCompactListProps {
  elements: ElementInstance[];
  studyName: string;
  studyId: string;
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  onElementToggle: (element: ElementInstance, studyName: string, studyId: string) => void;
}

export const StudyElementsCompactList: React.FC<StudyElementsCompactListProps> = ({
  elements,
  studyName,
  studyId,
  selectedElements,
  onElementToggle
}) => {
  const { t } = useTranslation(['library', 'common', 'method']);

  const isElementSelected = (elementId: string) => {
    return selectedElements.some(sel => sel.element.id === elementId);
  };

  const getElementTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'machine-stopped': t('machineTypes.machine-stopped', { ns: 'method' }),
      'machine-running': t('machineTypes.machine-running', { ns: 'method' }),
      'machine-time': t('machineTypes.machine-time', { ns: 'method' }),
    };
    return typeMap[type] || type;
  };

  const getRepetitionTypeLabel = (repetitionType: string) => {
    const typeMap: Record<string, string> = {
      'repetitive': t('types.repetitive', { ns: 'method' }),
      'frequency': t('types.frequency', { ns: 'method' }),
      'machine': t('types.machine', { ns: 'method' }),
    };
    return typeMap[repetitionType] || repetitionType;
  };

  const formatTime = (time: number | undefined) => {
    if (time === undefined || time === null) return '--';
    return time.toFixed(2);
  };

  const formatFrequency = (element: ElementInstance) => {
    if (element.repetition_type === 'repetitive') {
      return '1x1';
    }
    if (element.frequency_repetitions && element.frequency_cycles) {
      return `${element.frequency_repetitions}x${element.frequency_cycles}`;
    }
    return '--';
  };

  if (elements.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>{t('noElementsInStudy', { ns: 'library' })}</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="space-y-2">
        {elements.map((element, index) => {
          const isSelected = isElementSelected(element.id);
          
          return (
            <div
              key={element.id}
              className={`
                flex items-center space-x-3 p-3 rounded-lg border transition-all cursor-pointer
                ${isSelected 
                  ? 'bg-purple-50 border-purple-200 shadow-sm' 
                  : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                }
              `}
              onClick={() => onElementToggle(element, studyName, studyId)}
            >
              {/* Número del elemento */}
              <div className="flex-shrink-0">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold
                  ${isSelected 
                    ? 'bg-purple-600 text-white' 
                    : 'bg-gray-100 text-gray-600'
                  }
                `}>
                  {index + 1}
                </div>
              </div>

              {/* Checkbox */}
              <div className="flex-shrink-0">
                {isSelected ? (
                  <CheckCircle2 className="w-5 h-5 text-purple-600" />
                ) : (
                  <Circle className="w-5 h-5 text-gray-400" />
                )}
              </div>

              {/* Información del elemento */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {element.description || t('noDescription', { ns: 'common' })}
                    </h4>
                    
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                      {/* Tipo */}
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">{t('type', { ns: 'common' })}:</span>
                        <span>{getElementTypeLabel(element.type)}</span>
                      </div>
                      
                      {/* Frecuencia */}
                      <div className="flex items-center space-x-1">
                        <Repeat className="w-3 h-3" />
                        <span>{formatFrequency(element)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Métricas del elemento */}
                  <div className="flex-shrink-0 ml-4">
                    <div className="flex items-center space-x-3 text-xs">
                      {/* Tiempo */}
                      {element.time !== undefined && (
                        <div className="flex items-center space-x-1 text-blue-600">
                          <Clock className="w-3 h-3" />
                          <span className="font-medium">{formatTime(element.time)}s</span>
                        </div>
                      )}
                      
                      {/* Actividad */}
                      {element.activity !== undefined && (
                        <div className="flex items-center space-x-1 text-green-600">
                          <Activity className="w-3 h-3" />
                          <span className="font-medium">{element.activity}%</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Resumen al final */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {elements.length} {t('elements', { ns: 'library' })} {t('total', { ns: 'common' })}
          </span>
          <span>
            {selectedElements.length} {t('selected', { ns: 'common' })}
          </span>
        </div>
      </div>
    </div>
  );
};
