import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { X, RotateCcw, InfoIcon } from 'lucide-react';
import { useSupplementTablesStore } from '../../store/supplementTablesStore';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../components/ui/tooltip"
import { Dialog, DialogHeader, DialogTitle, DialogDescription, DialogClose } from "../../components/ui/dialog"
import { Factor, FactorSelection } from '../../types';

interface Selection {
  index?: number;
  intensity?: string;
}

interface FactorModalProps {
  factor: Factor;
  tableType: 'oit' | 'tal';
  onClose: () => void;
  onSelect: (value: number, selection: FactorSelection) => void;
  onReset: () => void;
  currentValue?: number;
  currentSelection?: FactorSelection;
}

export const FactorModal: React.FC<FactorModalProps> = ({
  factor,
  tableType,
  onClose,
  onSelect,
  onReset,
  currentValue,
  currentSelection
}) => {
  const { t } = useTranslation(['supplements', 'common']);
  const tables = useSupplementTablesStore(state => state.tables);
  const updateDescriptions = useSupplementTablesStore(state => state.updateDescriptions);
  const setTableType = useSupplementTablesStore(state => state.setTableType);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        if (onClose) {
          onClose();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  useEffect(() => {
    setTableType(tableType);
    updateDescriptions();
  }, [tableType, setTableType, updateDescriptions]);

  const table = tables[factor as keyof typeof tables];
  if (!table || factor.startsWith('A1')) return null;

  // Get factor descriptions from translations
  const descriptions = t(`${tableType}.${factor}.description`, { returnObjects: true }) as string[];
  
  // Ensure we have both descriptions and points
  const options = descriptions && Array.isArray(descriptions) ? descriptions.map((desc, index) => ({
    description: desc,
    points: table.puntos[index] || 0
  })) : [];

  const handleSelect = (points: number, index: number) => {
    const selection: FactorSelection = { index };
    onSelect(points, selection);
    if (onClose) {
      onClose();
    }
  };

  return (
    <Dialog open onOpenChange={() => onClose()}>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div ref={modalRef} className="bg-white rounded-lg w-full max-w-md">
          <div className="p-4 bg-red-500 text-white flex items-center justify-between rounded-t-lg">
            <div className="flex items-center gap-2">
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild onClick={(e) => e.preventDefault()}>
                    <button 
                      className="p-1 hover:bg-red-600 rounded-full transition-colors"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                    >
                      <InfoIcon className="h-5 w-5 text-white" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent 
                    side="top"
                    align="center"
                    className="max-w-sm bg-white text-gray-900 p-3 rounded shadow-lg border border-gray-200"
                    sideOffset={5}
                  >
                    <p className="text-sm whitespace-pre-line leading-relaxed">
                      {t(`${tableType}.${factor}.tooltip`)}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <div className="flex items-center gap-1 truncate">
                <h3 className="tracking-tight text-lg font-semibold text-white truncate">
                  {t(`${tableType}.${factor}.title`)}
                </h3>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={onReset}
                className="p-2 hover:bg-red-600 rounded-full"
                title="Reiniciar"
              >
                <RotateCcw className="w-5 h-5" />
              </button>
              <button
                onClick={onClose}
                className="p-2 hover:bg-red-600 rounded-full"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
          <div className="p-4 max-h-[70vh] overflow-y-auto">
            {options.map((option, index) => (
              <button
                key={index}
                className={`w-full text-left p-3 rounded-lg mb-2 transition-colors ${
                  currentSelection?.index === index
                    ? 'bg-red-100 hover:bg-red-200'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => handleSelect(option.points, index)}
              >
                <div className="flex justify-between items-center">
                  <span className="flex-1">{option.description}</span>
                  <span className="text-red-500 font-semibold ml-4">
                    {option.points} {t('points', { ns: tableType === 'oit' ? 'oitFactors' : 'talFactors' })}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </Dialog>
  );
};