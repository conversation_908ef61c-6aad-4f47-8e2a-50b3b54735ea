-- Create organizations table
create table organizations (
    id uuid default uuid_generate_v4() primary key,
    name text not null,
    description text,
    invite_code uuid default uuid_generate_v4() not null unique, -- Código único para unirse
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    created_by uuid references auth.users(id) on delete set null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create organization_join_requests table
create table organization_join_requests (
    id uuid default uuid_generate_v4() primary key,
    organization_id uuid references organizations(id) on delete cascade,
    user_id uuid references auth.users(id) on delete cascade,
    user_email text not null,
    status text not null check (status in ('pending', 'approved', 'rejected')),
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    processed_at timestamp with time zone,
    processed_by uuid references auth.users(id) on delete set null,
    unique(organization_id, user_id, status)
);

-- Create organization_members table
create table organization_members (
    organization_id uuid references organizations(id) on delete cascade,
    user_id uuid references auth.users(id) on delete cascade,
    user_email text not null,
    role text not null check (role in ('owner', 'admin', 'member')),
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    primary key (organization_id, user_id)
);

-- Add organization fields to studies table
alter table studies 
    add column organization_id uuid references organizations(id) on delete set null,
    add column is_shared boolean default false;

-- Create updated_at trigger function
create or replace function update_updated_at_column()
returns trigger as $$
begin
    new.updated_at = now();
    return new;
end;
$$ language plpgsql;

-- Add updated_at trigger to organizations
create trigger update_organizations_updated_at
    before update on organizations
    for each row
    execute function update_updated_at_column();

-- Enable RLS
alter table organizations enable row level security;
alter table organization_members enable row level security;
alter table organization_join_requests enable row level security;

-- Organizations policies
create policy "Users can create organizations"
    on organizations for insert
    to authenticated
    with check (auth.uid() = created_by);

create policy "Organization members can view their organizations"
    on organizations for select
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organizations.id
            and user_id = auth.uid()
        )
        or auth.uid() = created_by
    );

-- Join requests policies
create policy "Users can create join requests"
    on organization_join_requests for insert
    to authenticated
    with check (
        auth.uid() = user_id 
        and status = 'pending'
        and not exists (
            select 1 from organization_members
            where organization_id = organization_join_requests.organization_id
            and user_id = auth.uid()
        )
    );

create policy "Users can view their own join requests"
    on organization_join_requests for select
    to authenticated
    using (
        auth.uid() = user_id
        or exists (
            select 1 from organization_members
            where organization_id = organization_join_requests.organization_id
            and user_id = auth.uid()
            and role in ('owner', 'admin')
        )
    );

-- Organization members policies
create policy "Organization admins can manage members"
    on organization_members for all
    to authenticated
    using (
        exists (
            select 1 from organization_members
            where organization_id = organization_members.organization_id
            and user_id = auth.uid()
            and role in ('owner', 'admin')
        )
    );

create policy "Users can view members of their organizations"
    on organization_members for select
    to authenticated
    using (
        exists (
            select 1 from organization_members om
            where om.organization_id = organization_members.organization_id
            and om.user_id = auth.uid()
        )
    );

-- Studies sharing policies
create policy "Users can view shared organization studies"
    on studies for select
    to authenticated
    using (
        auth.uid() = user_id
        or (
            is_shared = true
            and exists (
                select 1 from organization_members
                where organization_id = studies.organization_id
                and user_id = auth.uid()
            )
        )
    );

-- Create indexes for better performance
create index idx_organization_members_user_id on organization_members(user_id);
create index idx_organization_members_org_id on organization_members(organization_id);
create index idx_organization_join_requests_org_id on organization_join_requests(organization_id) where status = 'pending';
create index idx_studies_organization_id on studies(organization_id) where organization_id is not null;
create index idx_studies_is_shared on studies(is_shared) where is_shared = true;

-- Function to process a join request
create or replace function process_join_request(
    request_id uuid,
    new_status text,
    admin_user_id uuid
)
returns void
language plpgsql
security definer
as $$
declare
    v_organization_id uuid;
    v_user_id uuid;
    v_user_email text;
begin
    -- Verify the admin has rights to process the request
    if not exists (
        select 1 
        from organization_join_requests r
        join organization_members m on m.organization_id = r.organization_id
        where r.id = request_id
        and m.user_id = admin_user_id
        and m.role in ('owner', 'admin')
    ) then
        raise exception 'Unauthorized';
    end if;

    -- Get request details
    select organization_id, user_id, user_email
    into v_organization_id, v_user_id, v_user_email
    from organization_join_requests
    where id = request_id;

    -- Update request status
    update organization_join_requests
    set status = new_status,
        processed_at = now(),
        processed_by = admin_user_id
    where id = request_id;

    -- If approved, add user as member
    if new_status = 'approved' then
        insert into organization_members (
            organization_id,
            user_id,
            user_email,
            role
        )
        values (
            v_organization_id,
            v_user_id,
            v_user_email,
            'member'
        )
        on conflict (organization_id, user_id) do nothing;
    end if;
end;
$$;

-- Function to join organization by invite code
create or replace function join_organization_by_code(
    p_invite_code uuid,
    p_user_id uuid,
    p_user_email text
)
returns void
language plpgsql
security definer
as $$
declare
    v_organization_id uuid;
begin
    -- Get organization ID from invite code
    select id into v_organization_id
    from organizations
    where invite_code = p_invite_code;

    if v_organization_id is null then
        raise exception 'Invalid invite code';
    end if;

    -- Create join request
    insert into organization_join_requests (
        organization_id,
        user_id,
        user_email,
        status
    )
    values (
        v_organization_id,
        p_user_id,
        p_user_email,
        'pending'
    )
    on conflict (organization_id, user_id, status) do nothing;
end;
$$;
