import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Trash2, ChevronUp, ChevronDown } from 'lucide-react';
import { ElementInstance } from '../types/index';
import { DeleteConfirmModal } from './DeleteConfirmModal';
import { FlowchartSymbolIcon } from './FlowchartSymbols';

interface MethodListProps {
  elements: ElementInstance[];
  onDeleteElement: (id: string) => void;
  onEditElement: (element: ElementInstance) => void;
  onMoveElement?: (id: string, direction: 'up' | 'down') => Promise<void>;
}

export const MethodList: React.FC<MethodListProps> = ({ 
  elements, 
  onDeleteElement,
  onEditElement,
  onMoveElement
}) => {
  const { t } = useTranslation(['method', 'common']);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [elementToDelete, setElementToDelete] = useState<string | null>(null);

  const handleDeleteClick = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    setElementToDelete(id);
    setShowDeleteModal(true);
  };

  const handleMoveClick = (e: React.MouseEvent, id: string, direction: 'up' | 'down') => {
    e.stopPropagation();
    if (onMoveElement) {
      onMoveElement(id, direction);
    }
  };

  const handleConfirmDelete = () => {
    if (elementToDelete) {
      onDeleteElement(elementToDelete);
      setShowDeleteModal(false);
      setElementToDelete(null);
    }
  };

  const formatFrequency = (element: ElementInstance) => {
    const { frequency_repetitions, frequency_cycles, repetition_type } = element;
    
    if (repetition_type === 'repetitive') {
      return `${frequency_repetitions} ${t('repetitions')} x ${frequency_cycles} ${t('cycles')}`;
    }
    
    return `${frequency_repetitions} ${t('repetitions')} ${t('common:each')} ${frequency_cycles} ${t('cycles')}`;
  };

  const getElementTypeTranslation = (type: string) => {
    return t(`types.${type}`);
  };

  return (
    <>
      <div className="space-y-4">
        {elements.map((element, index) => (
          <div
            key={element.id}
            className="flex items-center gap-2"
          >
            {onMoveElement && (
              <div className="flex flex-col text-gray-400 p-2">
                <button 
                  onClick={(e) => handleMoveClick(e, element.id, 'up')}
                  disabled={index === 0}
                  className="p-1 hover:text-gray-600 disabled:opacity-30"
                  aria-label={t('moveUp', { ns: 'common' })}
                >
                  <ChevronUp className="w-5 h-5" />
                </button>
                <button 
                  onClick={(e) => handleMoveClick(e, element.id, 'down')}
                  disabled={index === elements.length - 1}
                  className="p-1 hover:text-gray-600 disabled:opacity-30"
                  aria-label={t('moveDown', { ns: 'common' })}
                >
                  <ChevronDown className="w-5 h-5" />
                </button>
              </div>
            )}
            <div
              onClick={() => onEditElement(element)}
              className="flex-1 bg-white rounded-lg shadow-md p-4 cursor-pointer hover:bg-gray-50"
            >
              <div className="flex justify-between items-center">
                <div className="flex-1 flex items-center gap-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-purple-600 text-xl font-bold">
                      {String(index + 1).padStart(2, '0')}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-base font-medium">
                      {element.description}
                    </h3>
                    <div className="mt-2 text-sm text-gray-600">
                      <p>{formatFrequency(element)}</p>
                      <p>{getElementTypeTranslation(element.type)} - {t(`types.${element.repetition_type}`)}</p>
                    </div>
                    {element.flowchartSymbol && (
                      <div className="mt-2 flex items-center gap-1 bg-green-50 px-2 py-1 rounded-md border border-green-200 max-w-fit">
                        <FlowchartSymbolIcon 
                          symbol={element.flowchartSymbol} 
                          className="text-green-600" 
                          size={16} 
                        />
                        <span className="text-xs font-medium text-green-700 uppercase">
                          {t(`cronoSeguido:flowchartSymbols.${element.flowchartSymbol}.name`, { 
                            defaultValue: element.flowchartSymbol 
                          })}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <button
                  onClick={(e) => handleDeleteClick(e, element.id)}
                  className="text-red-500 hover:text-red-700 transition-colors self-center"
                  aria-label={t('common:delete')}
                >
                  <Trash2 size={20} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleConfirmDelete}
        title={t('deleteElementTitle')}
        message={t('deleteElementConfirmation')}
      />
    </>
  );
};