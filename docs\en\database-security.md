# Database Security Documentation

This document describes the security policies and functions implemented in the Cronometras database to protect user data and ensure proper access to resources.

## Row Level Security (RLS) Policies

Supabase uses PostgreSQL's Row Level Security (RLS) policies to restrict data access at the row level. These policies are automatically applied to all queries made through the Supabase API.

### Policies for the `studies` table

#### 1. Study viewing policy

```sql
CREATE POLICY "Users can view shared organization studies v3"
    ON studies FOR SELECT
    TO authenticated
    USING (
        -- Users can view their own studies
        auth.uid() = user_id
        OR (
            -- Users can view shared studies from organizations they belong to
            is_shared = true
            AND organization_id IS NOT NULL
            AND is_organization_member(organization_id, auth.uid())
        )
    );
```

**Purpose**: This policy ensures that users can only view:
- Their own studies (where `user_id` matches their authentication ID)
- Shared studies from organizations they currently belong to

**Security**: The `is_organization_member` function verifies current organization membership, preventing expelled members from continuing to view shared studies.

#### 2. Study update policy

```sql
CREATE POLICY "Users can update their own studies"
    ON studies FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);
```

**Purpose**: Allows users to update only their own studies.

**Security**: Both the `USING` and `WITH CHECK` clauses verify that the authenticated user is the owner of the study.

#### 3. Study deletion policy

```sql
CREATE POLICY "Users can delete their own studies"
    ON studies FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);
```

**Purpose**: Allows users to delete only their own studies.

**Security**: The `USING` clause verifies that the authenticated user is the owner of the study.

#### 4. Study insertion policy

```sql
CREATE POLICY "Users can insert studies"
    ON studies FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);
```

**Purpose**: Allows users to insert new studies, but only if they set themselves as the owner.

**Security**: The `WITH CHECK` clause verifies that the authenticated user is set as the owner of the study.

## Security Functions

### 1. RPC for orphaned studies cleanup

```sql
CREATE OR REPLACE FUNCTION rpc_clean_orphaned_studies(org_id uuid)
RETURNS int
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
-- Implementation directly finds and updates orphaned studies
$$;
```

**Purpose**: Provides a secure API entry point for cleaning orphaned studies. This function identifies and updates "orphaned" studies - studies that belong to users who are no longer members of the organization.

**Security**:
- Verifies that the calling user is the organization owner before allowing cleanup.
- `SECURITY DEFINER`: Runs with elevated privileges.
- `SET search_path = public`: Prevents dependency confusion attacks.
- Directly implements the logic to find and update orphaned studies, ensuring it returns a single integer value.

### 2. Automatic cleanup trigger

```sql
CREATE OR REPLACE FUNCTION clean_member_studies_on_removal()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
-- Implementation...
$$;

CREATE TRIGGER clean_studies_on_member_removal
AFTER DELETE ON organization_members
FOR EACH ROW
EXECUTE FUNCTION clean_member_studies_on_removal();
```

**Purpose**: Automates the cleanup of studies when a member is removed from an organization.

**Security**:
- Runs automatically after each deletion in the `organization_members` table.
- `SECURITY DEFINER`: Runs with elevated privileges to ensure it can update studies.
- `SET search_path = public`: Prevents dependency confusion attacks.
- Only updates studies of the specific member who was removed.

## Implemented Best Practices

1. **Principle of least privilege**: Users can only access the data they need.
2. **Ownership verification**: All operations verify that the user is the owner of the resource.
3. **SECURITY DEFINER functions**: Properly used with explicit search_path.
4. **Security automation**: The trigger ensures cleanup happens automatically without relying on the application.
5. **Role validation**: The RPC function verifies that the user has the appropriate role before allowing sensitive operations.

## Additional Considerations

- RLS policies apply to all queries made through the Supabase API, but not to direct SQL queries made by database administrators.
- It's essential to keep these policies updated when changes are made to the database schema.
- Periodic audits are recommended to verify that policies are working as expected.
- The migration script is designed to be idempotent, meaning it can be run multiple times without causing errors. This is achieved by dropping existing policies and functions before creating them again.
