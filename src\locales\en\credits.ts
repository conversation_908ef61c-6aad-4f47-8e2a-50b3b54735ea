export default {
  // Titles and descriptions
  buyCreditsAndSubscriptions: 'Buy credits and subscriptions',
  selectPlanOrPackage: 'Select a subscription plan or buy individual credits',
  subscriptions: 'Subscription plans',
  individualCredits: 'Individual credits',
  credits: {
    title: 'Credits',
    description: 'To purchase additional credits, please click the button below. We will send you an email with the necessary information.',
    buy: 'Buy credits',
    request: 'Request credits',
    requestSubject: 'Credit purchase request',
    requestBody: 'I would like to request information about purchasing additional credits.',
    requestSent: 'Request sent successfully',
    requestError: 'Error sending request',
    unlimited: 'Unlimited credits',
    available: 'available',
    used: 'used',
    monthly: 'Monthly credits',
    extra: 'Extra credits',
    nextReset: 'Next reset',
    validUntil: 'Valid until',
    unlimitedDescription: 'You have unlimited access to all services',
    success: 'Success',
    error: 'Error'
  } as const,

  // Credit packages
  creditPackages: {
    single: {
      label: 'Individual credit',
      description: 'Buy an additional credit'
    }
  },

  // Subscription plans
  subscriptionPackages: {
    monthly: {
      label: 'Monthly Subscription',
      description: 'Unlimited credits for the month'
    },
    annual: {
      label: 'Annual Subscription',
      description: 'Unlimited credits for the whole year'
    }
  },

  // Common labels
  popular: 'Popular',
  save: 'Save',
  month: 'month',
  year: 'year',
  credits_plural: '{{count}} credits',
  pricePerCredit: '€{{price}} per credit',
  unlimitedCredits: 'Unlimited credits',

  // Payment process
  cardDetails: 'Card details',
  processing: 'Processing...',
  pay: 'Pay €{{amount}}',
  cancel: 'Cancel',
  close: 'Close',

  // Benefits
  benefits: {
    securePayment: {
      title: 'Secure payment',
      description: 'Processed by Stripe'
    },
    instantUse: {
      title: 'Instant use',
      description: 'Credits available immediately'
    },
    guarantee: {
      title: 'Guarantee',
      description: 'Money-back guarantee'
    }
  },

  // Errors
  errors: {
    paymentIntentFailed: 'Failed to initiate payment',
    stripeNotInitialized: 'Payment system initialization error',
    creditPurchaseFailed: 'Failed to process credit purchase',
    paymentFailed: 'Payment failed',
    generalError: 'An error has occurred'
  },

  warnings: {
    verificationFailed: 'Warning: Purchase verification failed'
  }
};
