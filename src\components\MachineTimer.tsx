import React from 'react';
import { useTranslation } from 'react-i18next';
import { Play, Pause, Square } from 'lucide-react';
import { ElementInstance } from '../types';
import { formatTime } from '../utils/time';
import { playSoundAndVibrate, initializeAudioContext } from '../utils/sounds';

interface MachineTimerProps {
  element: ElementInstance;
  time: number;
  activity: number;
  isRunning: boolean;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
}

export const MachineTimer: React.FC<MachineTimerProps> = ({
  element,
  time,
  activity,
  isRunning,
  onStart,
  onPause,
  onStop
}) => {
  const { t } = useTranslation();

  React.useEffect(() => {
    // Initialize AudioContext on first interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener('click', handleFirstInteraction);
    };
    document.addEventListener('click', handleFirstInteraction);
    return () => {
      document.removeEventListener('click', handleFirstInteraction);
    };
  }, []);

  const handleStart = async () => {
    await playSoundAndVibrate('start', 200);
    onStart();
  };

  const handlePause = async () => {
    await playSoundAndVibrate('stop', 100);
    onPause();
  };

  const handleStop = async () => {
    await playSoundAndVibrate('stop', [100, 50, 100]);
    onStop();
  };

  const formatFrequency = (element: ElementInstance) => {
    return `${element.frequency_repetitions} ${t('repetitions', { ns: 'frequency' })} ${t('each', { ns: 'common' })} ${element.frequency_cycles} ${t('cycles', { ns: 'frequency' })}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
      <div className="flex items-center justify-center mb-4">
        <div className="text-4xl font-mono font-bold">
          {formatTime(time)}
        </div>
      </div>

      <div className="text-center mb-6">
        <span className="text-2xl font-bold">{activity}</span>
      </div>

      <div className="grid grid-cols-2 gap-4">

        <button
          onClick={handleStop}
          disabled={!isRunning}
          className="p-4 rounded-lg bg-red-500 text-white font-bold disabled:opacity-50"
        >
          <Square className="mx-auto" />
        </button>

        <button
          onClick={isRunning ? handlePause : handleStart}
          className={`p-4 rounded-lg text-white font-bold
            ${isRunning ? 'bg-yellow-500' : 'bg-green-500'}`}
        >
          {isRunning ? <Pause className="mx-auto" /> : <Play className="mx-auto" />}
        </button>
      </div>
    </div>
  );
};