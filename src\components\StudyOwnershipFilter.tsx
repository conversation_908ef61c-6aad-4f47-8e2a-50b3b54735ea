import React from 'react';
import { useTranslation } from 'react-i18next';

export type StudyFilterType = 'all' | 'owned' | 'shared';

interface StudyOwnershipFilterProps {
  // Props para futura funcionalidad de carpetas
}

export const StudyOwnershipFilter: React.FC<StudyOwnershipFilterProps> = () => {
  const { t } = useTranslation(['common']);

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
      <div className="flex items-center gap-3">
        <button className="justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 folder-toggle-btn flex items-center gap-2 px-3 py-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="lucide lucide-folder-plus h-4 w-4">
            <path d="M12 10v6"></path>
            <path d="M9 13h6"></path>
            <path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path>
          </svg>
          <span className="">Carpetas</span>
        </button>
        <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg border">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="lucide lucide-folder h-4 w-4" style={{color: 'rgb(236, 72, 153)'}}>
            <path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"></path>
          </svg>
          <span className="text-sm font-medium text-gray-700 truncate max-w-32">carpeta prueba</span>
        </div>
      </div>
      <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border mt-2">
        <button className="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 rounded-md px-3 text-gray-600 hover:text-gray-800 hover:bg-gray-200" title="Subir un nivel">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="lucide lucide-arrow-up w-4 h-4">
            <path d="m5 12 7-7 7 7"></path>
            <path d="M12 19V5"></path>
          </svg>
        </button>
        <div className="h-4 w-px bg-gray-300"></div>
        <nav className="flex items-center space-x-1 text-sm overflow-x-auto">
          <button className="flex items-center text-gray-600 hover:text-blue-600 transition-colors px-2 py-1 rounded hover:bg-blue-50" title={t('common:allStudies', { defaultValue: 'All Studies' })}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="lucide lucide-home w-4 h-4">
              <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2 2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            <span className="ml-1 hidden sm:inline">{t('common:allStudies', { defaultValue: 'All Studies' })}</span>
          </button>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="lucide lucide-chevron-right w-4 h-4 text-gray-400 flex-shrink-0">
            <path d="m9 18 6-6-6-6"></path>
          </svg>
          <button className="px-2 py-1 rounded transition-colors flex-shrink-0 text-gray-900 font-medium bg-blue-100" title="Ir a carpeta: carpeta prueba">carpeta prueba</button>
        </nav>
      </div>
    </div>
  );
}; 