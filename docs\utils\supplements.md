# Supplements Utilities

## Descripción
Utilidades para cálculos de suplementos.

## Funciones

### convertPointsToPercentage
```typescript
function convertPointsToPercentage(points: number): number
```
Convierte puntos de suplemento a porcentaje según tabla.

### validatePoints
```typescript
function validatePoints(points: Record<string, number>): boolean
```
Valida que los puntos estén en rangos correctos.

### calculateTotalPoints
```typescript
function calculateTotalPoints(points: Record<string, number>): number
```
Suma total de puntos de suplementos.

## Uso
```typescript
// Convertir puntos a porcentaje
const percentage = convertPointsToPercentage(45); // 24%

// Validar puntos
const isValid = validatePoints({ A1: 5, B2: 3 }); // true

// Calcular total
const total = calculateTotalPoints({ A1: 5, B2: 3 }); // 8
```