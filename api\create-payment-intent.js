const stripe = require('stripe')(process.env.VITE_STRIPE_SECRET_KEY);
const cors = require('cors')({ origin: true });

module.exports = async (req, res) => {
  // Habilitar CORS
  await new Promise((resolve, reject) => {
    cors(req, res, (err) => {
      if (err) reject(err);
      resolve();
    });
  });

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Invalid amount' });
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount),
      currency: 'eur',
    });

    res.status(200).json({ clientSecret: paymentIntent.client_secret });
  } catch (err) {
    console.error('Error creating payment intent:', err);
    res.status(500).json({ error: err.message });
  }
};
