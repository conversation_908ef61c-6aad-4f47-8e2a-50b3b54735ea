export default {
  title: 'Informe',
  noStudySelected: 'No hay estudio seleccionado',
  timeUnit: 'Unidad de Tiempo',
  shiftMinutes: 'Minutos por Turno',
  contingency: 'Contingencia',
  activityScale: 'Escala de Actividad',
  identifyingData: 'Datos Identificativos',
  page: 'Página',
  times: 'Tiempos',
  timeRecords: 'Registros de Tiempo',
  units: {
    seconds: 'segundos',
    minutes: 'minutos',
    mmm: 'MMM',
    cmm: 'CMM',
    tmu: 'TMU',
    dmh: 'DMH',
    cyclesPerShift: 'ciclos/turno',
    cyclesPerHour: 'ciclos/hora',
    hours: 'horas',
    points: 'puntos'
  },
  elementDetails: {
    title: 'Detalles de los Elementos',
    description: 'Descripción',
    type: 'Tipo',
    frequency: 'Frecuencia',
    observedTime: 'Tiempo Observado',
    activity: 'Actividad',
    supplements: 'Suplementos',
    finalTime: 'Tiempo Final',
    repetitions: 'Repeticiones',
    cycles: 'Ciclos',
    repetitionType: 'Tipo de Repetición',
    addToLibrary: 'Agregar a la Biblioteca',
    frequencyFormat: '{{repetitions}} repeticiones, {{cycles}} ciclos {{each}}',
    totalPoints: 'Puntos',
    conversionPercentage: 'Porcentaje',
    forcedSupplements: 'Forzado',
    recordNumber: 'Número de Registro',
    time: 'Tiempo',
    observations: 'Observaciones'
  },
  results: {
    title: 'RESULTADOS',
    normalCycle: 'Ciclo Normal',
    optimalCycle: 'Ciclo Óptimo',
    contingencyTime: 'Tiempo de Contingencia',
    totalK: 'K Total',
    normalProduction: 'Producción Normal por Turno',
    optimalProduction: 'Producción Óptima por Turno',
    normalProductionPerHour: 'Producción Normal por Hora',
    optimalProductionPerHour: 'Producción Óptima por Hora',
    normalSaturation: 'Saturación Normal',
    optimalSaturation: 'Saturación Óptima',
    maxActivity: 'Actividad Máxima',
    machineStoppedTime: 'Tiempo Máquina Parada',
    machineRunningTime: 'Tiempo Máquina en Marcha',
    machineTime: 'Tiempo Máquina',
    valueHour: 'Valor por Hora',
    valueMinute: 'Valor por Minuto',
    valuePoint: 'Valor por Punto'
  },
  
  // Versiones cortas para Excel
  resultShort: {
    normalCycle: 'Ciclo Normal',
    optimalCycle: 'Ciclo Óptimo',
    contingencyTime: 'Tiempo Contingencia',
    totalK: 'K Total',
    normalProduction: 'Prod. Normal Turno',
    optimalProduction: 'Prod. Óptima Turno',
    normalProductionPerHour: 'Prod. Normal/Hora',
    optimalProductionPerHour: 'Prod. Óptima/Hora',
    normalSaturation: 'Saturación Normal',
    optimalSaturation: 'Saturación Óptima',
    maxActivity: 'Actividad Máxima',
    valueHour: 'Valor por Hora',
    valueMinute: 'Valor por Minuto',
    valuePoint: 'Valor por Punto'
  },

  // Etiquetas para unidades personalizadas
  customUnits: {
    normalTime: 'Tiempo Norm',
    optimalTime: 'Tiempo Ópt',
    normalProdShift: 'Prod. Norm/Turno',
    optimalProdShift: 'Prod. Ópt/Turno', 
    normalProdHour: 'Prod. Norm/Hora',
    optimalProdHour: 'Prod. Ópt/Hora',
    valueHour: 'Valor/Hora',
    valueMinute: 'Valor/Min',
    valuePoint: 'Valor/Punto'
  },
  descriptions: {
    normalCycle: 'Ciclo Normal',
    optimalCycle: 'Ciclo Óptimo',
    contingencyTime: 'Tiempo de Contingencia',
    totalK: 'K Total',
    normalProduction: 'Producción Normal por Turno',
    optimalProduction: 'Producción Óptima por Turno',
    normalProductionPerHour: 'Producción Normal por Hora',
    optimalProductionPerHour: 'Producción Óptima por Hora',
    normalSaturation: 'Saturación Normal',
    optimalSaturation: 'Saturación Óptima',
    maxActivity: 'Actividad Máxima',
    machineStoppedTime: 'Tiempo Máquina Parada',
    machineRunningTime: 'Tiempo Máquina en Marcha',
    machineTime: 'Tiempo Máquina',
    valueHour: 'Valor Hora',
    valueMinute: 'Valor Minuto',
    valuePoint: 'Valor Punto'
  },
  calculations: {
    normalCycle: 'Máquina Parada + (el más largo de máquina en marcha o Tiempo Máquina)',
    optimalCycle: 'Máquina Parada optimizado + El más largo de Tiempo máquina ó Tiempo Máquina en marcha optimizado',
    contingencyTime: 'Es el tiempo adicional por contingencias, calculado como: Ciclo Normal × Porcentaje de Contingencia',
    totalK: 'Es la suma total de todos los suplementos aplicados, expresado en tiempo en cada ciclo',
    normalProduction: 'Producción esperada por turno considerando el ciclo normal: (Minutos por Turno / Ciclo Normal)',
    optimalProduction: 'Producción esperada por turno considerando el ciclo óptimo: (Minutos por Turno / Ciclo Óptimo)',
    normalProductionPerHour: 'Producción normal por hora: (1 hora / Ciclo Normal)',
    optimalProductionPerHour: 'Producción óptima por hora: (1 hora / Ciclo Óptimo)',
    normalSaturation: '(Máquina Parada + Máquina en marcha)/Ciclo Normal',
    optimalSaturation: '(Máquina Parada + Máquina en marcha)/Ciclo Óptimo',
    maxActivity: 'Ciclo Normal/Ciclo Óptimo',
    valueHour: 'Valor del tiempo estándar en horas',
    valueMinute: 'Valor del tiempo estándar en minutos',
    valuePoint: 'Valor del tiempo estándar en puntos: una hora son 100 puntos.'
  },
  machineTypes: {
    'machine-stopped': 'Máquina Parada',
    'machine-running': 'Máquina en Marcha',
    'machine-time': 'Tiempo Máquina'
  },
  sections: {
    production: 'PRODUCCIÓN',
    saturation: 'SATURACIÓN',
    machineTimes: 'TIPO DE TIEMPO EN RELACIÓN A LA MÁQUINA'
  },
  types: {
    'machine-stopped': 'MP',
    'machine-running': 'MM',
    'machine-time': 'TM'
  },
  table: {
    description: 'Descripción',
    type: 'Tipo',
    frequency: 'Frecuencia',
    observedTime: 'Tiempo Observado',
    activity: 'Actividad',
    supplements: 'Suplementos',
    finalTime: 'Tiempo Final'
  },
  actions: {
    exportPDF: 'Exportar a PDF',
    exportExcel: 'Exportar a Excel'
  },
  settings: {
    title: 'CONFIGURACIÓN',
    timeUnit: 'Unidad de tiempo',
    shiftMinutes: 'Minutos por turno',
    contingency: 'Contingencia',
    contingencyDescription: 'La contingencia es un porcentaje que se añade al tiempo total para tener en cuenta eventos inesperados.',
    contingencyPlaceholder: 'Introduce el porcentaje de contingencia',
    contingencyError: 'La contingencia debe estar entre 0 y 100',
    contingencyUnit: '%'
  },
  cycleDiscrepancy: {
    title: 'Discordancia en Ciclo Normal',
    message: 'El cálculo del ciclo normal difiere del obtenido en el cálculo de fatiga. Revise los tiempos de espera.',
    reportValue: 'Valor del informe',
    supplementsValue: 'Valor de suplementos',
    difference: 'Diferencia',
    selectSource: 'Seleccionar fuente del ciclo',
    useReportCycle: 'Usar ciclo del informe',
    useSupplementsCycle: 'Usar ciclo de suplementos',
    tooltip: 'El ciclo normal calculado en el informe no considera tiempos de espera de máquina, mientras que el cálculo en suplementos sí los considera según diferentes rangos de tiempo.',
    warningIcon: '⚠️'
  },
  supplementsAlert: {
    noDataTitle: 'Cálculo de Suplementos No Ejecutado',
    noDataMessage: 'No se encontraron datos del cálculo de suplementos de máquina. Para detectar discordancias entre el ciclo normal del reporte y el cálculo de fatiga, debe ejecutar primero el cálculo de suplementos de máquina.',
    noDataTooltip: 'La detección de discordancias requiere que se haya ejecutado el cálculo de suplementos de máquina al menos una vez.',
    incompleteDataTitle: 'Datos de Suplementos Incompletos',
    incompleteDataMessage: 'Se encontraron datos de suplementos pero el cálculo parece estar incompleto. Para detectar discordancias entre el ciclo normal del reporte y el cálculo de fatiga, necesita completar el cálculo de suplementos de máquina.',
    incompleteDataTooltip: 'Los datos de suplementos existen pero el tiempo total del ciclo es 0. Esto puede ocurrir si el cálculo se interrumpió o si no hay registros de tiempo válidos.',
    buttonText: 'Ir a Suplementos de Máquina'
  },

  charts: {
    title: 'Análisis Visual del Proceso',
    noData: 'No hay datos suficientes para mostrar gráficas',
    
    machineTypes: {
      title: 'Distribución por Tipo de Máquina',
      stopped: 'Máquina Parada',
      running: 'Máquina en Marcha',
      time: 'Tiempo de Máquina'
    },
    
    saturation: {
      title: 'Saturación/Rendimiento Normal',
      used: 'Tiempo Utilizado',
      unused: 'Tiempo No Utilizado',
      label: 'Saturación'
    },
    
    elements: {
      title: 'Tiempos por Elemento',
      finalTime: 'Tiempo Final'
    },
    
    supplements: {
      title: 'Suplementos por Elemento',
      finalTime: 'Tiempo Final',
      percentage: 'Porcentaje de Suplementos (%)'
    },
    
    activity: {
      title: 'Actividad por Elemento',
      average: 'Actividad Promedio (%)'
    },
    
    cycleDistribution: {
      title: 'Distribución del Tiempo de Ciclo',
      work: 'Tiempo de Trabajo',
      rest: 'Tiempo de Descanso'
    },
    
    summary: {
      title: 'Resumen Estadístico',
      totalElements: 'Elementos Totales',
      optimalSaturation: 'Saturación Óptima',
      saturation: 'Saturación Normal',
      maxActivity: 'Actividad Máxima'
    },

    flowchartSymbols: {
      title: 'Análisis de Tipos de Operación',
      distribution: 'Distribución por Tipo de Operación',
      percentages: 'Porcentajes por Tipo de Operación',
      count: 'Cantidad de Operaciones',
      countTitle: 'Cantidad de Operaciones por Tipo',
      types: 'Tipos de Operación',
      summary: 'Análisis de Tipos de Operación',
      typesUsed: 'Tipos Utilizados',
      totalOperations: 'Operaciones Totales',
      mostCommon: 'Más Común',
      avgTime: 'Tiempo Promedio',
      element: 'elemento',
      elements: 'elementos'
    }
  }
};
