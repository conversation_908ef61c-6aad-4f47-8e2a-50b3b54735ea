import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertTriangle, FolderX, Trash2, Move } from 'lucide-react';
import { Button } from './ui/button';
import { supabase } from '../lib/supabase';
import type { Folder, Study } from '../types/index';

interface DeleteFolderModalProps {
  isOpen: boolean;
  folder: Folder | null;
  availableFolders: Folder[];
  onClose: () => void;
  onDelete: (folderId: string, action: 'move' | 'delete', targetFolderId?: string) => Promise<void>;
}

interface FolderContents {
  studies: Study[];
  subfolders: Folder[];
}

export const DeleteFolderModal: React.FC<DeleteFolderModalProps> = ({
  isOpen,
  folder,
  availableFolders,
  onClose,
  onDelete
}) => {
  const { t } = useTranslation(['common', 'study']);
  const [folderContents, setFolderContents] = useState<FolderContents>({ studies: [], subfolders: [] });
  const [selectedAction, setSelectedAction] = useState<'move' | 'delete'>('move');
  const [targetFolderId, setTargetFolderId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingContents, setIsLoadingContents] = useState(false);

  useEffect(() => {
    if (isOpen && folder) {
      loadFolderContents();
    }
  }, [isOpen, folder]);

  const loadFolderContents = async () => {
    if (!folder) return;

    setIsLoadingContents(true);
    try {
      const { data: studies, error: studiesError } = await supabase
        .from('studies')
        .select('id, required_info')
        .eq('folder_id', folder.id);

      if (studiesError) throw studiesError;

      const { data: subfolders, error: subfoldersError } = await supabase
        .from('folders')
        .select('id, name')
        .eq('parent_folder_id', folder.id);

      if (subfoldersError) throw subfoldersError;

      setFolderContents({
        studies: (studies || []) as unknown as Study[],
        subfolders: (subfolders || []) as unknown as Folder[]
      });
    } catch (error) {
      console.error('Error loading folder contents:', error);
    } finally {
      setIsLoadingContents(false);
    }
  };

  const handleDelete = async () => {
    if (!folder) return;

    setIsLoading(true);
    try {
      await onDelete(folder.id, selectedAction, selectedAction === 'move' ? targetFolderId || undefined : undefined);
      onClose();
    } catch (error) {
      console.error('Error deleting folder:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen || !folder) return null;

  const hasStudies = folderContents.studies.length > 0;
  const hasSubfolders = folderContents.subfolders.length > 0;
  const hasContent = hasStudies || hasSubfolders;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <AlertTriangle className="h-6 w-6 text-red-500" />
            <h2 className="text-lg font-semibold text-gray-900">
              Eliminar Carpeta
            </h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {isLoadingContents ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Analizando contenido de la carpeta...</p>
            </div>
          ) : (
            <>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2 text-red-800 font-medium mb-2">
                  <FolderX className="h-5 w-5" />
                  {folder.name}
                </div>
                <p className="text-sm text-red-700">
                  Estás a punto de eliminar esta carpeta.
                  {hasContent && ' Esta carpeta contiene:'}
                </p>
                
                {hasContent && (
                  <ul className="mt-3 space-y-1 text-sm text-red-700">
                    {hasStudies && <li>• {folderContents.studies.length} estudios</li>}
                    {hasSubfolders && <li>• {folderContents.subfolders.length} subcarpetas</li>}
                  </ul>
                )}
              </div>

              {hasContent ? (
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900">¿Qué deseas hacer con el contenido?</h3>
                  
                  <label className="flex items-start gap-3 p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="action"
                      value="move"
                      checked={selectedAction === 'move'}
                      onChange={(e) => setSelectedAction(e.target.value as 'move')}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 font-medium text-gray-900">
                        <Move className="h-4 w-4 text-blue-500" />
                        Mover a otra carpeta
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        El contenido se moverá a la carpeta seleccionada.
                      </p>
                      
                      {selectedAction === 'move' && (
                        <div className="mt-3">
                          <select
                            value={targetFolderId}
                            onChange={(e) => setTargetFolderId(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Mover a la raíz (sin carpeta)</option>
                            {availableFolders.filter(f => f.id !== folder.id).map((targetFolder) => (
                              <option key={targetFolder.id} value={targetFolder.id}>
                                {targetFolder.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      )}
                    </div>
                  </label>

                  <label className="flex items-start gap-3 p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="action"
                      value="delete"
                      checked={selectedAction === 'delete'}
                      onChange={(e) => setSelectedAction(e.target.value as 'delete')}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 font-medium text-red-600">
                        <Trash2 className="h-4 w-4" />
                        Eliminar todo permanentemente
                      </div>
                      <p className="text-sm text-red-600 mt-1">
                        <strong>¡ATENCIÓN!</strong> Se eliminarán permanentemente todos los estudios y subcarpetas.
                      </p>
                      
                      {selectedAction === 'delete' && (
                        <div className="mt-3 p-3 bg-red-100 border border-red-300 rounded-md">
                          <p className="text-sm text-red-800 font-medium">
                            ⚠️ Esta acción no se puede deshacer.
                          </p>
                        </div>
                      )}
                    </div>
                  </label>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-600">
                    Esta carpeta está vacía y se puede eliminar de forma segura.
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading || isLoadingContents}
            className="min-w-[120px]"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Eliminando...
              </div>
            ) : (
              'Eliminar Carpeta'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}; 