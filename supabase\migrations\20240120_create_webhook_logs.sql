-- Create webhook_logs table
create table if not exists public.webhook_logs (
    id bigint generated by default as identity primary key,
    event_type text not null,
    data jsonb,
    error text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Add RLS policies
alter table public.webhook_logs enable row level security;

-- Grant access to authenticated users
create policy "Enable read access for authenticated users" on public.webhook_logs
    for select using (auth.role() = 'authenticated');

-- Grant access to service role for inserts
create policy "Enable insert for service role" on public.webhook_logs
    for insert with check (true);

-- Grant necessary permissions
grant all on public.webhook_logs to service_role;
grant select on public.webhook_logs to authenticated;
