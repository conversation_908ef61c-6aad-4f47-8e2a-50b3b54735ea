import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle } from 'lucide-react';
import { Header } from '../components/Header';
import { ElementStats } from '../components/ElementStats';
import { MachineElementList } from '../components/MachineElementList';
import { MachineTimer } from '../components/MachineTimer';
import { MachineRecordList } from '../components/MachineRecordList';
import { DeleteConfirmModal } from '../components/DeleteConfirmModal';
import { Alert } from '../components/ui/alert';
import { Button } from '../components/ui/button';
import { useTimeRecordStore } from '../store/timeRecordStore';
import { useTimeRecords } from '../hooks/useTimeRecords';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl';
import { WorkElement, TimeRecord, ElementInstance } from '../types';
import { supabase } from '../lib/supabase';
import { useStudyStore } from '../store/studyStore';
import { VoiceInputModal } from '../components/VoiceInputModal';
import { useAuthStore } from '../store/authStore';

interface MachinePageProps {
  onBack: () => void;
}

export const MachinePage: React.FC<MachinePageProps> = ({ onBack }) => {
  const { t } = useTranslation(['machine', 'common', 'study']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  useStudyFromUrl(); 
  const elements = selectedStudy?.elements || [];
  const { user } = useAuthStore();
  const [isSharedStudy, setIsSharedStudy] = useState(false);
  
  const machineElements = useMemo(
    () => elements.filter(e => e.type === 'machine-time'),
    [elements]
  );

  const [selectedElement, setSelectedElement] = useState<ElementInstance | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [time, setTime] = useState(0);
  const [activity, setActivity] = useState(selectedStudy?.required_info?.activity_scale?.normal || 100);
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [comment, setComment] = useState('');
  const [editingRecord, setEditingRecord] = useState<TimeRecord | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { records, fetchRecords, saveRecords, deleteRecord, deleteAllRecords, updateRecord } = useTimeRecordStore();

  const timerRef = useRef<number>();
  const startTimeRef = useRef<number>();

  const selectedElementStats = useTimeRecords(
    selectedElement ? selectedStudy?.time_records[selectedElement.id] || [] : []
  );

  const loadElementRecords = useCallback(async (elementId: string) => {
    try {
      await fetchRecords(elementId);
    } catch (err) {
      throw new Error(`Failed to load records for element ${elementId}`);
    }
  }, [fetchRecords]);

  useEffect(() => {
    const loadInitialRecords = async () => {
      if (machineElements.length === 0) return;

      setIsLoading(true);
      setError(null);
      
      try {
        const promises = machineElements.map(element => loadElementRecords(element.id));
        await Promise.all(promises);
      } catch (err) {
        setError(t('errors.loadingRecords'));
        console.error('Error loading records:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadInitialRecords();
  }, [machineElements, loadElementRecords, t]);

  useEffect(() => {
    if (selectedStudy && user) {
      setIsSharedStudy(selectedStudy.user_id !== user.id);
    }
  }, [selectedStudy, user]);

  const handleElementClick = (element: WorkElement) => {
    setSelectedElement(element);
    setTime(0);
    setIsRunning(false);
    setIsPaused(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  const handleStart = () => {
    if (!selectedElement) return;

    startTimeRef.current = Date.now() - time;
    timerRef.current = window.setInterval(() => {
      setTime(Date.now() - startTimeRef.current!);
    }, 10);
    setIsRunning(true);
    setIsPaused(false);
  };

  const handlePause = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = undefined;
    }
    setIsPaused(true);
    setIsRunning(false);
  };

  const handleStop = async () => {
    if (!selectedElement || !isRunning) return;

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    try {
      const record: Omit<TimeRecord, 'id'> = {
        elementId: selectedElement.id,
        time: time / 1000,
        original_time: time / 1000,
        activity,
        timestamp: new Date().toISOString(),
        description: selectedElement.description,
        addedToMethod: false,
        comment: ''
      };

      await saveRecords([record]);
      setTime(0);
      setIsRunning(false);
      setIsPaused(false);
    } catch (err) {
      console.error('Error saving record:', err);
      setError(t('error.savingRecord'));
      setIsRunning(true);
      setIsPaused(false);
      startTimeRef.current = Date.now() - time;
      timerRef.current = window.setInterval(() => {
        setTime(Date.now() - startTimeRef.current!);
      }, 10);
    }
  };

  const handleUpdateRecord = async (record: TimeRecord) => {
    try {
      await updateRecord(record.id, record);
      setEditingRecord(null);
      setShowEditModal(false);
    } catch (err) {
      console.error('Error updating record:', err);
      setError(t('error.updatingRecord'));
    }
  };

  const handleDeleteRecord = async (recordId: string) => {
    try {
      await deleteRecord(recordId);
    } catch (err) {
      console.error('Error deleting record:', err);
      setError(t('error.deletingRecord'));
    }
  };

  const handleDeleteAllRecords = useCallback(async () => {
    if (!selectedStudy || !selectedElement) return;

    try {
      // Actualizar el estudio en Supabase
      const { error } = await supabase
        .from('studies')
        .update({
          time_records: {
            ...selectedStudy.time_records,
            [selectedElement.id]: []
          }
        })
        .eq('id', selectedStudy.id);

      if (error) {
        console.error('Error deleting records:', error);
        return;
      }

      // Actualizar el estado local
      useStudyStore.setState(state => ({
        selectedStudy: {
          ...state.selectedStudy!,
          time_records: {
            ...state.selectedStudy!.time_records,
            [selectedElement.id]: []
          }
        }
      }));

      setShowDeleteAllModal(false);
    } catch (error) {
      console.error('Error deleting records:', error);
    }
  }, [selectedStudy, selectedElement]);

  const handleElementUpdate = useCallback(async (updatedElement: ElementInstance) => {
    if (!selectedStudy) return;

    // Actualizar el elemento en el estudio
    const updatedElements = elements.map(element => {
      if (element.id === updatedElement.id) {
        // Usar el tipo actualizado del elemento
        return updatedElement;
      }
      return element;
    });

    try {
      // Actualizar el estudio en Supabase
      const { error } = await supabase
        .from('studies')
        .update({ elements: updatedElements })
        .eq('id', selectedStudy.id);

      if (error) {
        console.error('Error updating study:', error);
        return;
      }

      // Actualizar el estado local
      useStudyStore.setState(state => ({
        selectedStudy: {
          ...state.selectedStudy!,
          elements: updatedElements
        }
      }));
    } catch (error) {
      console.error('Error updating study:', error);
    }
  }, [selectedStudy, elements]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="mt-4">{t('noStudySelected')}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('title')}
        subtitle={selectedStudy.required_info?.name || ''}
        showSearch={false}
        showActions={false}
        onBack={onBack}
      />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <span>{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-500 hover:text-red-700"
                title={t('close')}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}

          {isSharedStudy && (
            <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <span>{t('permission_error.message', { ns: 'study' })}</span>
              <button
                onClick={() => setIsSharedStudy(false)}
                className="ml-auto text-amber-500 hover:text-amber-700"
                title={t('close')}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}

          {!selectedElement ? (
            <MachineElementList
              elements={machineElements}
              records={selectedStudy.time_records}
              onElementClick={handleElementClick}
              onElementUpdate={handleElementUpdate}
            />
          ) : (
            <>
              <ElementStats 
                {...selectedElementStats}
                timeRecords={selectedStudy.time_records[selectedElement.id] || []}
              />

              <MachineTimer
                element={selectedElement}
                time={time}
                activity={activity}
                isRunning={isRunning}
                onStart={handleStart}
                onStop={handleStop}
              />

              <MachineRecordList
                element={selectedElement}
                records={selectedStudy.time_records[selectedElement.id] || []}
                onDeleteRecord={handleDeleteRecord}
                onEditRecord={(record) => {
                  setEditingRecord({
                    ...record,
                    frequency_repetitions: selectedElement?.frequency_repetitions || 1,
                    frequency_cycles: selectedElement?.frequency_cycles || 1
                  });
                  setShowEditModal(true);
                }}
                onAddComment={(record) => {
                  setEditingRecord(record);
                  setShowCommentModal(true);
                }}
                onDeleteAll={() => setShowDeleteAllModal(true)}
              />

              <button
                onClick={() => setSelectedElement(null)}
                className="fixed bottom-6 right-6 w-14 h-14 bg-purple-600 text-white rounded-full shadow-lg flex items-center justify-center hover:bg-purple-700 transition-colors"
                title={t('back')}
              >
                <X className="w-6 h-6" />
              </button>
            </>
          )}
        </div>
      </main>

      {showCommentModal && editingRecord && (
        <VoiceInputModal
          isOpen={showCommentModal}
          onClose={() => setShowCommentModal(false)}
          onTranscript={(text) => {
            handleUpdateRecord({
              ...editingRecord,
              comment: text
            });
            setShowCommentModal(false);
          }}
          title={t('addComment')}
        />
      )}

      {showEditModal && editingRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-bold mb-4">{t('editRecord')}</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('time')}
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editingRecord.time}
                  onChange={(e) => setEditingRecord({
                    ...editingRecord,
                    time: parseFloat(e.target.value)
                  })}
                  className="w-full p-2 border rounded"
                  title={t('time')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('activity')}
                </label>
                <input
                  type="number"
                  value={editingRecord.activity}
                  onChange={(e) => setEditingRecord({
                    ...editingRecord,
                    activity: parseInt(e.target.value)
                  })}
                  className="w-full p-2 border rounded"
                  title={t('activity')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('frequency')}
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    min="1"
                    value={editingRecord.frequency_repetitions || 1}
                    onChange={(e) => {
                      const repetitions = parseInt(e.target.value) || 1;
                      const cycles = editingRecord.frequency_cycles || 1;
                      // Calculamos el factor de ajuste basado en la cantidad esperada vs la real
                      const expectedPieces = selectedElement?.frequency_cycles || 1;
                      const actualPieces = cycles;
                      const adjustmentFactor = expectedPieces / actualPieces;
                      // Ajustamos el tiempo observado según la fórmula T'_o = T_o × (N_e/N_r)
                      // Usamos el tiempo original del registro para el cálculo
                      const originalTime = editingRecord.original_time || editingRecord.time;
                      const adjustedTime = originalTime * adjustmentFactor;
                      setEditingRecord({
                        ...editingRecord,
                        frequency_repetitions: repetitions,
                        time: adjustedTime,
                        original_time: originalTime // Guardamos el tiempo original
                      });
                    }}
                    className="w-1/2 p-2 border rounded"
                    title={t('repetitions')}
                  />
                  <span className="text-gray-500">/</span>
                  <input
                    type="number"
                    min="1"
                    value={editingRecord.frequency_cycles || 1}
                    onChange={(e) => {
                      const cycles = parseInt(e.target.value) || 1;
                      const repetitions = editingRecord.frequency_repetitions || 1;
                      // Calculamos el factor de ajuste basado en la cantidad esperada vs la real
                      const expectedPieces = selectedElement?.frequency_cycles || 1;
                      const actualPieces = cycles;
                      const adjustmentFactor = expectedPieces / actualPieces;
                      // Ajustamos el tiempo observado según la fórmula T'_o = T_o × (N_e/N_r)
                      // Usamos el tiempo original del registro para el cálculo
                      const originalTime = editingRecord.original_time || editingRecord.time;
                      const adjustedTime = originalTime * adjustmentFactor;
                      setEditingRecord({
                        ...editingRecord,
                        frequency_cycles: cycles,
                        time: adjustedTime,
                        original_time: originalTime // Guardamos el tiempo original
                      });
                    }}
                    className="w-1/2 p-2 border rounded"
                    title={t('cycles')}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1 text-blue-700">
                  {t('comment', { ns: 'common', defaultValue: 'Comentario' })}
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-start space-x-2">
                    <textarea
                      value={editingRecord.comment || ''}
                      onChange={(e) => setEditingRecord({
                        ...editingRecord,
                        comment: e.target.value
                      })}
                      className="w-full rounded-lg border-2 border-blue-300 focus:border-blue-500 focus:ring-blue-500 bg-blue-50 p-2"
                      rows={2}
                      placeholder={t('addCommentPlaceholder', { ns: 'common', defaultValue: 'Agregar comentario sobre este registro...' })}
                    />
                    <button
                      type="button"
                      onClick={() => setEditingRecord({
                        ...editingRecord,
                        comment: ''
                      })}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600"
                      title={t('clearComment', { ns: 'common', defaultValue: 'Limpiar comentario' })}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  {editingRecord.comment && (
                    <p className="text-xs text-blue-600">
                      {t('commentWillExport', { ns: 'common', defaultValue: 'Este comentario aparecerá en el informe Excel' })}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
                title={t('cancel')}
              >
                {t('cancel')}
              </button>
              <button
                onClick={() => handleUpdateRecord(editingRecord)}
                className="px-4 py-2 bg-purple-600 text-white rounded"
                title={t('save')}
              >
                {t('save')}
              </button>
            </div>
          </div>
        </div>
      )}

      <DeleteConfirmModal
        isOpen={showDeleteAllModal}
        onClose={() => setShowDeleteAllModal(false)}
        onConfirm={handleDeleteAllRecords}
        title={t('deleteAllRecords')}
        message={t('deleteAllRecordsConfirmation')}
      />
    </div>
  );
};