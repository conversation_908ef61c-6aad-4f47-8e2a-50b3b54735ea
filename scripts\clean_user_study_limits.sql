-- <PERSON><PERSON>, eliminar todos los registros existentes excepto los que queremos mantener
DELETE FROM user_study_limits
WHERE id NOT IN (
  '481f6de0-5d2e-4835-afbc-d74619483d14',
  '96f76e91-aaab-485d-9ed4-6f39b8c251d1',
  'dcb86751-ee00-456c-9bf4-f6a5027d3ab1',
  'e920a626-b374-480f-8d85-aa0f6a2c3edd',
  'f076d687-3f89-4686-b726-6f54d4e147c2'
);

-- <PERSON><PERSON>, actualizar los registros con los valores exactos que necesitamos
UPDATE user_study_limits
SET 
  user_id = 'a7a43bb3-ac6d-4bc8-a250-93b665acbf01',
  user_email = '<EMAIL>',
  created_at = '2024-11-28 20:41:49.236627+00',
  updated_at = '2024-12-21 18:35:43.278094+00',
  monthly_credits = 20,
  extra_credits = 50,
  reset_date = '2025-01-01 00:00:00+00',
  used_monthly_credits = 13,
  used_extra_credits = 0
WHERE id = '481f6de0-5d2e-4835-afbc-d74619483d14';

UPDATE user_study_limits
SET 
  user_id = '2dc826be-106a-4be3-9fe7-e05402134cda',
  user_email = '<EMAIL>',
  created_at = '2024-12-10 15:37:29.629+00',
  updated_at = '2024-12-19 09:38:04.664323+00',
  monthly_credits = 20,
  extra_credits = 0,
  reset_date = '2025-01-01 00:00:00+00',
  used_monthly_credits = 2,
  used_extra_credits = 0
WHERE id = '96f76e91-aaab-485d-9ed4-6f39b8c251d1';

UPDATE user_study_limits
SET 
  user_id = 'a9539f2c-03eb-4321-bae3-03da69b1cd78',
  user_email = '<EMAIL>',
  created_at = '2024-11-29 00:57:39.568+00',
  updated_at = '2024-12-20 17:11:28.861874+00',
  monthly_credits = 20,
  extra_credits = 31,
  reset_date = '2025-01-01 00:00:00+00',
  used_monthly_credits = 2,
  used_extra_credits = 0
WHERE id = 'dcb86751-ee00-456c-9bf4-f6a5027d3ab1';

UPDATE user_study_limits
SET 
  user_id = '736ef922-8ec5-4893-b951-b791b5e1cd63',
  user_email = '<EMAIL>',
  created_at = '2024-11-29 00:58:42.19+00',
  updated_at = '2024-12-19 09:38:04.664323+00',
  monthly_credits = 20,
  extra_credits = 0,
  reset_date = '2025-01-01 00:00:00+00',
  used_monthly_credits = 0,
  used_extra_credits = 0
WHERE id = 'e920a626-b374-480f-8d85-aa0f6a2c3edd';

UPDATE user_study_limits
SET 
  user_id = '5ac91916-63ea-455f-9067-c22ee165779d',
  user_email = '<EMAIL>',
  created_at = '2024-12-05 15:11:25.817+00',
  updated_at = '2024-12-19 09:38:04.664323+00',
  monthly_credits = 20,
  extra_credits = 0,
  reset_date = '2025-01-01 00:00:00+00',
  used_monthly_credits = 0,
  used_extra_credits = 0
WHERE id = 'f076d687-3f89-4686-b726-6f54d4e147c2';
