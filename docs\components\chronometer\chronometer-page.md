# Página de Cronómetro

## Descripción
Componente principal para la toma de tiempos de elementos repetitivos.

## Estructura
```
ChronometerPage
├── ElementSelector
├── ElementStats
├── ChronometerControls
├── TimeRecordList
└── Modales
    ├── CommentModal
    ├── EditModal
    └── DeleteConfirmModal
```

## Flujo de Datos
1. Selección de elemento
2. Control de cronómetro
3. Registro de tiempo
4. Actualización de estadísticas
5. Gestión de registros

## Interacciones
- Navegación entre elementos
- Control de tiempo
- Ajuste de actividad
- Gestión de registros
- Edición y comentarios

## Estados Principales
```typescript
{
  currentElementIndex: number;
  viewingElementIndex: number;
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  activity: number;
  records: Record<string, TimeRecord[]>;
}
```

## Hooks Utilizados
- useChronometer
- useElementNavigation
- useActivityControl
- useTimeRecords
- useSwipe