const { createClient } = require('@supabase/supabase-js');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

exports.handler = async (req, res) => {
    try {
        // Crear nombre del archivo con fecha
        const date = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `backup-${date}.sql`;
        const backupPath = path.join(__dirname, 'backups', fileName);

        // Asegurar que existe el directorio de backups
        if (!fs.existsSync(path.join(__dirname, 'backups'))) {
            fs.mkdirSync(path.join(__dirname, 'backups'), { recursive: true });
        }

        // Ejecutar pg_dump para crear el backup
        const command = `pg_dump "${process.env.DATABASE_URL}" -f "${backupPath}"`;
        
        exec(command, async (error, stdout, stderr) => {
            if (error) {
                console.error('Error al crear backup:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Error al crear el backup',
                    error: error.message
                });
            }

            // Leer el archivo generado
            const fileStream = fs.createReadStream(backupPath);

            // Configurar headers para la descarga
            res.setHeader('Content-Type', 'application/sql');
            res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

            // Enviar el archivo
            fileStream.pipe(res);

            // Eliminar el archivo después de enviarlo
            fileStream.on('end', () => {
                fs.unlinkSync(backupPath);
            });
        });
    } catch (error) {
        console.error('Error durante el backup:', error);
        res.status(500).json({
            success: false,
            message: 'Error al realizar el backup',
            error: error.message
        });
    }
}