import React from 'react';
import { useTranslation } from 'react-i18next';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { OrganizationsList } from './OrganizationsList';
import { CreateOrganization } from './CreateOrganization';
import { JoinOrganization } from './JoinOrganization';

export const OrganizationSection: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <Tabs defaultValue="list" className="w-full">
        <TabsList>
          <TabsTrigger value="list">
            {t('organizations.myOrganizations')}
          </TabsTrigger>
          <TabsTrigger value="create">
            {t('organizations.create')}
          </TabsTrigger>
          <TabsTrigger value="join">
            {t('organizations.join')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <OrganizationsList />
        </TabsContent>

        <TabsContent value="create">
          <CreateOrganization />
        </TabsContent>

        <TabsContent value="join">
          <JoinOrganization />
        </TabsContent>
      </Tabs>
    </div>
  );
};
