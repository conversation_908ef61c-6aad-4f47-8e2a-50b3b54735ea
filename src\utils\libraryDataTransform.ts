import { Study, ElementInstance } from '../types';

export interface StudyWithElements {
  study: Study;
  elements: ElementInstance[];
}

export interface LibraryFilters {
  company?: string;
  dateFrom?: string;
  dateTo?: string;
  hasElements?: boolean;
  searchTerm?: string;
}

/**
 * Agrupa elementos de búsqueda por estudio
 */
export const groupElementsByStudy = (
  searchResults: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>
): StudyWithElements[] => {
  const studyMap = new Map<string, StudyWithElements>();
  
  searchResults.forEach(result => {
    const studyId = result.studyId;
    if (!studyMap.has(studyId)) {
      // Crear un objeto Study a partir de la información disponible
      const study: Study = {
        id: studyId,
        user_id: '', // Se llenará desde la base de datos
        required_info: {
          name: result.studyName,
          company: '', // Se puede extraer de los elementos si está disponible
          date: new Date().toISOString().split('T')[0],
          activity_scale: { normal: 100, optimal: 133 }
        },
        optional_info: {},
        elements: [],
        time_records: {},
        supplements: {},
        crono_seguido_records: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      studyMap.set(studyId, {
        study,
        elements: []
      });
    }
    studyMap.get(studyId)!.elements.push(result.element);
  });

  return Array.from(studyMap.values());
};

/**
 * Aplica filtros a la lista de estudios
 */
export const applyLibraryFilters = (
  studies: StudyWithElements[],
  filters: LibraryFilters
): StudyWithElements[] => {
  let filtered = studies;

  // Filtro por término de búsqueda
  if (filters.searchTerm?.trim()) {
    const term = filters.searchTerm.toLowerCase();
    filtered = filtered.filter(({ study, elements }) => {
      // Buscar en el nombre del estudio
      const studyNameMatch = study.required_info?.name?.toLowerCase().includes(term);
      
      // Buscar en los elementos
      const elementMatch = elements.some(element => 
        element.description?.toLowerCase().includes(term) ||
        element.name?.toLowerCase().includes(term)
      );
      
      return studyNameMatch || elementMatch;
    });
  }

  // Filtro por empresa
  if (filters.company) {
    filtered = filtered.filter(({ study }) => 
      study.required_info?.company === filters.company
    );
  }

  // Filtro por fecha desde
  if (filters.dateFrom) {
    filtered = filtered.filter(({ study }) => {
      const studyDate = study.required_info?.date;
      return studyDate && studyDate >= filters.dateFrom!;
    });
  }

  // Filtro por fecha hasta
  if (filters.dateTo) {
    filtered = filtered.filter(({ study }) => {
      const studyDate = study.required_info?.date;
      return studyDate && studyDate <= filters.dateTo!;
    });
  }

  // Filtro por estudios con elementos
  if (filters.hasElements) {
    filtered = filtered.filter(({ elements }) => 
      elements.some(el => !el.isPlaceholder)
    );
  }

  return filtered;
};

/**
 * Extrae empresas únicas de una lista de estudios
 */
export const extractUniqueCompanies = (studies: StudyWithElements[]): string[] => {
  const companies = new Set<string>();
  studies.forEach(({ study }) => {
    if (study.required_info?.company) {
      companies.add(study.required_info.company);
    }
  });
  return Array.from(companies).sort();
};

/**
 * Genera estadísticas de la biblioteca
 */
export const generateLibraryStats = (
  allStudies: StudyWithElements[],
  filteredStudies: StudyWithElements[]
) => {
  const totalStudies = allStudies.length;
  const filteredStudiesCount = filteredStudies.length;
  const totalElements = allStudies.reduce(
    (sum, { elements }) => sum + elements.filter(el => !el.isPlaceholder).length, 
    0
  );
  const filteredElements = filteredStudies.reduce(
    (sum, { elements }) => sum + elements.filter(el => !el.isPlaceholder).length, 
    0
  );

  return {
    totalStudies,
    filteredStudiesCount,
    totalElements,
    filteredElements,
    averageElementsPerStudy: totalStudies > 0 ? totalElements / totalStudies : 0,
    filteredAverageElementsPerStudy: filteredStudiesCount > 0 ? filteredElements / filteredStudiesCount : 0
  };
};

/**
 * Transforma elementos seleccionados para crear un nuevo estudio
 */
export const transformSelectedElementsToStudy = (
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>,
  studyData: {
    name: string;
    company: string;
    date: string;
    normalActivity: number;
    optimalActivity: number;
  },
  userId: string
): Study => {
  // Crear suplementos para cada elemento seleccionado
  const supplements: Record<string, any> = {
    elements: {}
  };

  // Mapear elementos para el nuevo estudio
  const elements = selectedElements.map((sel, index) => {
    const elementObj: any = {
      id: sel.element.id,
      name: sel.element.name || sel.element.description || `Elemento ${index + 1}`,
      description: sel.element.description || '',
      time: sel.element.time || 0,
      type: sel.element.type || 'machine-stopped',
      position: index + 1,
      repetition_type: sel.element.repetition_type || 'repetitive',
      frequency_cycles: sel.element.frequency_cycles || 1,
      frequency_repetitions: sel.element.frequency_repetitions || 1,
      activity: sel.element.activity || 100
    };

    // Añadir los suplementos al elemento
    if (sel.element.supplements && sel.element.supplements.length > 0) {
      const existingSupplement = sel.element.supplements[0];
      
      supplements[sel.element.id] = {
        points: existingSupplement.points || {},
        is_forced: existingSupplement.is_forced || false,
        percentage: existingSupplement.percentage || 0,
        factor_selections: existingSupplement.factor_selections || {}
      };
      
      elementObj.supplements = [{
        points: existingSupplement.points || {},
        is_forced: existingSupplement.is_forced || false,
        percentage: existingSupplement.percentage || 0,
        factor_selections: existingSupplement.factor_selections || {}
      }];
    } else {
      supplements[sel.element.id] = {
        points: {},
        is_forced: false,
        percentage: 0,
        factor_selections: {}
      };
      
      elementObj.supplements = [{
        points: {},
        is_forced: false,
        percentage: 0,
        factor_selections: {}
      }];
    }

    return elementObj;
  }).filter(el => el.id) as ElementInstance[];

  // Crear registros de tiempo
  const time_records: Record<string, any> = {};
  selectedElements.forEach(sel => {
    if (sel.element.id) {
      if (sel.element.timeRecords && sel.element.timeRecords.length > 0) {
        time_records[sel.element.id] = sel.element.timeRecords.map(record => ({
          id: crypto.randomUUID(),
          time: record.time,
          activity: record.activity,
          elementId: sel.element.id,
          timestamp: record.timestamp || new Date().toISOString(),
          comment: record.comment || sel.element.description || ''
        }));
      } else if (sel.element.time) {
        time_records[sel.element.id] = [{
          id: crypto.randomUUID(),
          time: sel.element.time,
          activity: sel.element.activity || 100,
          elementId: sel.element.id,
          timestamp: new Date().toISOString(),
          comment: sel.element.description || ''
        }];
      }
    }
  });

  // Crear el objeto de estudio completo
  const study: Study = {
    id: crypto.randomUUID(),
    user_id: userId,
    required_info: {
      name: studyData.name,
      company: studyData.company,
      date: studyData.date,
      activity_scale: {
        normal: studyData.normalActivity,
        optimal: studyData.optimalActivity
      }
    },
    optional_info: {
      tools: '',
      machine: '',
      section: '',
      operator: '',
      reference: '',
      technician: '',
      study_number: '',
      isEstimated: true
    },
    elements,
    time_records,
    supplements,
    crono_seguido_records: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  return study;
};

/**
 * Valida que los elementos seleccionados sean válidos para crear un estudio
 */
export const validateSelectedElements = (
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>
): string[] => {
  const errors: string[] = [];

  if (selectedElements.length === 0) {
    errors.push('Debe seleccionar al menos un elemento');
  }

  // Verificar que todos los elementos tengan descripción
  const elementsWithoutDescription = selectedElements.filter(
    sel => !sel.element.description?.trim()
  );
  
  if (elementsWithoutDescription.length > 0) {
    errors.push(`${elementsWithoutDescription.length} elemento(s) no tienen descripción`);
  }

  // Verificar duplicados por descripción
  const descriptions = selectedElements.map(sel => sel.element.description?.trim().toLowerCase());
  const duplicates = descriptions.filter((desc, index) => 
    desc && descriptions.indexOf(desc) !== index
  );
  
  if (duplicates.length > 0) {
    errors.push('Hay elementos con descripciones duplicadas');
  }

  return errors;
};
