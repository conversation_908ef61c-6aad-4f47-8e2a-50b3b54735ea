import React from 'react';

export type FlowchartSymbol = 'operation' | 'inspection' | 'transport' | 'delay' | 'storage' | 'combined';

export interface FlowchartSymbolInfo {
  id: FlowchartSymbol;
  name: string;
  description: string;
  icon: React.ReactNode;
}

interface FlowchartSymbolIconProps {
  symbol: FlowchartSymbol;
  className?: string;
  size?: number;
}

// Iconos SVG individuales
export const OperationIcon: React.FC<{ className?: string; size?: number }> = ({ className = "", size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="2"/>
  </svg>
);

export const InspectionIcon: React.FC<{ className?: string; size?: number }> = ({ className = "", size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect x="4" y="4" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"/>
  </svg>
);

export const TransportIcon: React.FC<{ className?: string; size?: number }> = ({ className = "", size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <path d="M7 12L17 12M17 12L13 8M17 12L13 16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const DelayIcon: React.FC<{ className?: string; size?: number }> = ({ className = "", size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <path d="M12 3C7.5 3 4 6.5 4 11C4 15.5 7.5 19 12 19C16.5 19 20 15.5 20 11C20 6.5 16.5 3 12 3Z M12 3L12 11" 
          fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    <path d="M12 19L20 19" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

export const StorageIcon: React.FC<{ className?: string; size?: number }> = ({ className = "", size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <path d="M12 16L20 8L4 8Z" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const CombinedIcon: React.FC<{ className?: string; size?: number }> = ({ className = "", size = 24 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <circle cx="12" cy="12" r="9" fill="none" stroke="currentColor" strokeWidth="1.5"/>
    <rect x="7" y="7" width="10" height="10" fill="none" stroke="currentColor" strokeWidth="1.5"/>
  </svg>
);

// Componente principal para mostrar iconos
export const FlowchartSymbolIcon: React.FC<FlowchartSymbolIconProps> = ({ symbol, className, size = 24 }) => {
  const icons = {
    operation: <OperationIcon className={className} size={size} />,
    inspection: <InspectionIcon className={className} size={size} />,
    transport: <TransportIcon className={className} size={size} />,
    delay: <DelayIcon className={className} size={size} />,
    storage: <StorageIcon className={className} size={size} />,
    combined: <CombinedIcon className={className} size={size} />
  };

  return icons[symbol] || null;
};

// Información completa de símbolos con traducciones
export const getFlowchartSymbols = (t: (key: string, options?: any) => string): FlowchartSymbolInfo[] => [
  {
    id: 'operation',
    name: t('flowchartSymbols.operation.name', { defaultValue: 'OPERACIÓN' }),
    description: t('flowchartSymbols.operation.description', { 
      defaultValue: 'Indica las principales fases del proceso. Agrega, modifica, montaje, etc.' 
    }),
    icon: <OperationIcon className="w-6 h-6" />
  },
  {
    id: 'inspection',
    name: t('flowchartSymbols.inspection.name', { defaultValue: 'INSPECCIÓN' }),
    description: t('flowchartSymbols.inspection.description', { 
      defaultValue: 'Verifica la calidad o cantidad. En general no agrega valor.' 
    }),
    icon: <InspectionIcon className="w-6 h-6" />
  },
  {
    id: 'transport',
    name: t('flowchartSymbols.transport.name', { defaultValue: 'TRANSPORTE' }),
    description: t('flowchartSymbols.transport.description', { 
      defaultValue: 'Indica el movimiento de materiales. Traslado de un lugar a otro.' 
    }),
    icon: <TransportIcon className="w-6 h-6" />
  },
  {
    id: 'delay',
    name: t('flowchartSymbols.delay.name', { defaultValue: 'ESPERA' }),
    description: t('flowchartSymbols.delay.description', { 
      defaultValue: 'Indica demora entre dos operaciones o abandono momentáneo.' 
    }),
    icon: <DelayIcon className="w-6 h-6" />
  },
  {
    id: 'storage',
    name: t('flowchartSymbols.storage.name', { defaultValue: 'ALMACENAMIENTO' }),
    description: t('flowchartSymbols.storage.description', { 
      defaultValue: 'Indica depósito de un objeto bajo vigilancia en un almacén' 
    }),
    icon: <StorageIcon className="w-6 h-6" />
  },
  {
    id: 'combined',
    name: t('flowchartSymbols.combined.name', { defaultValue: 'COMBINADA' }),
    description: t('flowchartSymbols.combined.description', { 
      defaultValue: 'Indica varias actividades simultáneas' 
    }),
    icon: <CombinedIcon className="w-6 h-6" />
  }
]; 