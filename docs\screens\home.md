# Pan<PERSON>la Principal

## Descripción
Dashboard principal que muestra la lista de estudios y proporciona acceso a todas las funcionalidades.

## Componentes
- `Header`: Barra superior con título y acciones
- `StudyFilters`: Filtros de búsqueda
- `StudyCard`: Tarjeta individual de estudio
- `ActionButtons`: Botones de acción principales

## Funcionalidades

### 1. Gestión de Estudios
- Lista de estudios con paginación
- Filtrado por:
  - Empresa
  - Fecha
  - Proceso
- Creación de nuevo estudio
- Eliminación de estudios
- Selección de estudio activo

### 2. Navegación
Acceso a módulos principales cuando hay un estudio seleccionado:
- Información del estudio
- Método
- Cronómetro (elementos repetitivos)
- Tiempos de máquina
- Tiempos frecuenciales
- Suplementos
- Informe

### 3. Biblioteca
- Acceso a la biblioteca de elementos
- Gestión de elementos compartidos

## Estados
```typescript
{
  studies: Study[];
  selectedStudy: Study | null;
  filters: {
    company: boolean;
    date: boolean;
    process: boolean;
  };
  currentView: 'studies' | 'study' | 'method' | 'chronometer' | 'machine' | 
               'frequency' | 'supplements' | 'report' | 'library';
}
```

## Flujo de Trabajo
1. Carga inicial de estudios del usuario
2. Usuario puede:
   - Filtrar estudios existentes
   - Crear nuevo estudio
   - Seleccionar estudio existente
   - Acceder a la biblioteca
3. Con estudio seleccionado:
   - Mostrar barra de navegación con módulos
   - Permitir edición/eliminación del estudio
   - Acceso a todas las funcionalidades del estudio