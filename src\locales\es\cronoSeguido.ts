export default {
  title: 'Cronómetro continuo',
  cronoseguido: 'Continuo',
  pageDescription: 'Medición de tiempos en secuencia',
  start: 'Iniciar',
  stop: 'Detener',
  reset: 'Reiniciar',
  lap: 'Vuelta',
  save: 'Guardar',
  records: 'Registros',
  addToMethod: 'Agregar al método en Bloque',
  addSingleRecordToMethod: 'Agregar registro al método',
  bulkAddToMethod: 'Agregar al método',
  selectElement: 'Seleccionar elemento',
  deleteAll: 'Eliminar todo',
  confirmDeleteAll: '¿Estás seguro de que deseas eliminar todos los registros?',
  confirmDeleteAllDescription: 'Esta acción no se puede deshacer.',
  confirmBulkAdd: 'Estás agregando TODOS los registros en bloque, ten en cuenta que se agregarán como elemento NUEVO de tipo REPETITIVO, con una FRECUENCIA de 1/1.', 
  noDescription: 'Sin descripción',
  nextElement: 'Siguiente elemento',
  previousElement: 'Elemento anterior',
  currentElement: 'Elemento actual',
  noElements: 'No hay elementos disponibles',
  noStudySelected: 'No hay estudio seleccionado',
  editRecord: 'Editar registro',
  deleteRecord: 'Eliminar registro',
  addRecord: 'Agregar registro',
  description: 'Descripción',
  time: 'Tiempo',
  activity: 'Actividad',
  addedToMethod: 'Agregado al método',
  notAddedToMethod: 'No agregado al método',
  timestamp: 'Fecha y hora',
  voiceInput: 'Entrada por voz',
  pressToSpeak: 'Presiona para hablar',
  listening: 'Escuchando...',
  noSpeech: 'No se detectó voz',
  speechError: 'Error al reconocer voz',
  noVoiceDetected: 'No se detectó ninguna voz',
  stats: 'Estadísticas',
  totalTime: 'Tiempo total',
  totalTakes: 'Tomas totales',
  saveToMethod: 'Guardar en Bloque',
  elementType: 'Tipo de elemento',
  newElement: 'Nuevo elemento',
  existingElement: 'Elemento existente',
  deleteAllTitle: 'Eliminar todos los registros',
  deleteAllConfirmation: '¿Estás seguro de que deseas eliminar todos los registros? Esta acción no se puede deshacer.',
  addDescription: 'Agregar descripción',
  addVoiceDescription: 'Añadir descripción por voz',
  flowchartSymbol: 'Símbolo de diagrama de flujo',
  selectSymbol: 'Seleccionar símbolo',
  flowchartSymbols: {
    operation: {
      name: 'OPERACIÓN',
      description: 'Indica las principales fases del proceso. Agrega, modifica, montaje, etc.'
    },
    inspection: {
      name: 'INSPECCIÓN', 
      description: 'Verifica la calidad o cantidad. En general no agrega valor.'
    },
    transport: {
      name: 'TRANSPORTE',
      description: 'Indica el movimiento de materiales. Traslado de un lugar a otro.'
    },
    delay: {
      name: 'ESPERA',
      description: 'Indica demora entre dos operaciones o abandono momentáneo.'
    },
    storage: {
      name: 'ALMACENAMIENTO',
      description: 'Indica depósito de un objeto bajo vigilancia en un almacén'
    },
    combined: {
      name: 'COMBINADA',
      description: 'Indica varias actividades simultáneas'
    }
  },
  noSymbol: 'Sin símbolo',
  noSymbolDescription: 'No asignar símbolo a este registro',
  clearDescription: 'Limpiar descripción',
  voiceInputTip: 'Usa el botón del micrófono para añadir más texto a la descripción',
  confirmDeleteRecord: '¿Estás seguro de que deseas eliminar este registro? Esta acción no se puede deshacer.',
  deleteRecordHint: 'Clic: confirmar | Doble clic: eliminar directamente',
  comment: 'Comentario',
  commentPlaceholder: 'Agregar comentario sobre este registro...',
  addVoiceComment: 'Agregar comentario por voz',
  clearComment: 'Limpiar comentario',
  commentWillExport: 'Este comentario aparecerá en el informe Excel y al pasar el tiempo al método'
} as const;
