import jsPDF from 'jspdf';
import 'jspdf-autotable';
import type { Study } from '../types/study';
import type { ElementStats, ReportStats, TimeUnit } from '../types/index';
import { TFunction } from 'i18next';
import i18next from 'i18next';
import autoTable from 'jspdf-autotable';
import { supabase } from '../lib/supabase';
import { ChartExportData } from './chartExport';

const PAGE_MARGIN = 10;
const PURPLE_COLOR = [147, 51, 234];
const GRAY_COLOR = [75, 85, 99];
const RED_COLOR = [239, 68, 68];

interface CustomUnitValues {
  unitValue: number;
  unitLabel: string;
  normalCycle: number;
  optimalCycle: number;
  normalProduction: number;
  optimalProduction: number;
  normalProductionPerHour: number;
  optimalProductionPerHour: number;
  valueHour: number;
  valueMinute: number;
  valuePoint: number;
}

// Función para calcular valores por unidad personalizada
const calculateCustomUnitValues = (
  unitType: 'squareMeters' | 'linearMeters' | 'cubicMeters' | 'kilos' | 'perimeter',
  customUnits: any,
  reportStats: ReportStats,
  shiftMinutes: number,
  timeUnit: TimeUnit
): CustomUnitValues | null => {
  if (!customUnits) return null;

  let unitValue = 0;
  let unitLabel = '';

  switch (unitType) {
    case 'squareMeters':
      unitValue = customUnits.squareMeters.length * customUnits.squareMeters.width;
      unitLabel = 'm²';
      break;
    case 'linearMeters':
      unitValue = customUnits.linearMeters.length;
      unitLabel = 'm';
      break;
    case 'cubicMeters':
      unitValue = customUnits.cubicMeters.length * customUnits.cubicMeters.width * customUnits.cubicMeters.height;
      unitLabel = 'm³';
      break;
    case 'kilos':
      unitValue = customUnits.kilos.weight;
      unitLabel = 'kg';
      break;
    case 'perimeter':
      unitValue = 2 * (customUnits.perimeter.length + customUnits.perimeter.width);
      unitLabel = 'm (perímetro)';
      break;
  }

  if (unitValue <= 0) return null;

  // Conversiones de tiempo para cálculos de producción
  const SHIFT_CONVERSIONS: Record<TimeUnit, number> = {
    seconds: 60,    // 60 seconds per minute
    minutes: 1,     // direct value in minutes
    hours: 1/60,
    mmm: 1000,      // 1000 milésimas per minute
    cmm: 100,       // 100 centésimas per minute
    tmu: 100000/60, // Valor exacto: 1666.6666666666667
    dmh: 10000/60   // Valor exacto: 166.66666666666666
  };

  const PRODUCTION_HOUR_DIVISORS: Record<TimeUnit, number> = {
    seconds: 3600,
    minutes: 60,
    hours: 1,
    mmm: 60000,
    cmm: 6000,
    tmu: 100000,
    dmh: 10000
  };

  const VALUE_HOUR_DIVISORS: Record<TimeUnit, number> = {
    seconds: 3600,
    minutes: 60,
    hours: 1,
    mmm: 60000,
    cmm: 6000,
    tmu: 100000,
    dmh: 10000
  };

  const VALUE_MINUTE_DIVISORS: Record<TimeUnit, number> = {
    seconds: 60,
    minutes: 1,
    hours: 1/60,
    mmm: 1000,
    cmm: 100,
    tmu: 100000/60,
    dmh: 10000/60
  };

  const VALUE_POINT_DIVISORS: Record<TimeUnit, number> = {
    seconds: 36,
    minutes: 0.6,
    hours: 0.01,
    mmm: 600,
    cmm: 60,
    tmu: 1000,
    dmh: 100
  };

  const shiftTimeInUnit = shiftMinutes * SHIFT_CONVERSIONS[timeUnit];

  // Calcular todos los valores divididos por la unidad personalizada
  return {
    unitValue,
    unitLabel,
    normalCycle: +(reportStats.normalCycle / unitValue).toFixed(4),
    optimalCycle: +(reportStats.optimalCycle / unitValue).toFixed(4),
    normalProduction: reportStats.normalProduction * unitValue >= 1 ? Math.floor(reportStats.normalProduction * unitValue) : +(reportStats.normalProduction * unitValue).toFixed(3),
    optimalProduction: reportStats.optimalProduction * unitValue >= 1 ? Math.floor(reportStats.optimalProduction * unitValue) : +(reportStats.optimalProduction * unitValue).toFixed(3),
    normalProductionPerHour: +(reportStats.normalProductionPerHour * unitValue).toFixed(2),
    optimalProductionPerHour: +(reportStats.optimalProductionPerHour * unitValue).toFixed(2),
    valueHour: +(reportStats.valueHour / unitValue).toFixed(4),
    valueMinute: +(reportStats.valueMinute / unitValue).toFixed(4),
    valuePoint: +(reportStats.valuePoint / unitValue).toFixed(4)
  };
};

export async function generatePDF(
  study: Study,
  elementStats: ElementStats[],
  reportStats: ReportStats,
  timeUnit: TimeUnit,
  shiftMinutes: number,
  contingency: number,
  t: TFunction,
  logoUrl?: string,
  chartData?: ChartExportData,
  pointsPerHour: number = 100
): Promise<void> {
  // Create document
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  let yPos = 20;

  // Determinar el nombre del estudio
  const studyName = study.required_info.name;

  // Logo and study name - obtener logo del usuario actual
  let profile;
  try {
    console.log('PDF: Intentando obtener logo del perfil...');
    
    // Obtener el usuario actual primero
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.warn('PDF: No hay usuario autenticado');
      profile = null;
    } else {
      console.log('PDF: Usuario autenticado, obteniendo perfil para:', user.id);
      
      const profileResult = await supabase
        .from('profiles')
        .select('logo_url')
        .eq('id', user.id)
        .single();
      
      if (profileResult.error) {
        console.warn('PDF: Error obteniendo perfil del usuario:', profileResult.error);
        profile = null;
      } else {
        console.log('PDF: Perfil obtenido exitosamente:', profileResult.data);
        profile = profileResult;
      }
    }
  } catch (error) {
    console.error('PDF: Error al obtener perfil:', error);
    profile = null;
  }

  if (profile?.data && 'logo_url' in profile.data && profile.data.logo_url) {
    try {
      console.log('PDF: Intentando cargar logo:', profile.data.logo_url);
      const logoUrl = profile.data.logo_url as string;
      
      // Verificar si es una URL pública o privada
      let imageSrc = logoUrl;
      
      if (logoUrl.includes('/object/public/')) {
        console.log('PDF: Logo es público, usando URL directa');
        imageSrc = logoUrl;
      } else {
        console.log('PDF: Logo es privado, convirtiendo a URL pública si es posible');
        // Para logos privados, intentar construir URL pública
        const fileName = logoUrl.split('/').pop() || '';
        const { data: urlData } = supabase.storage.from('logos').getPublicUrl(fileName);
        imageSrc = urlData.publicUrl;
      }
      
      const img = new Image();
      img.crossOrigin = 'anonymous'; // Para evitar problemas CORS
      img.src = imageSrc;
      
      await new Promise((resolve, reject) => {
        img.onload = () => {
          console.log('PDF: Logo cargado exitosamente');
          resolve(img);
        };
        img.onerror = (error) => {
          console.error('PDF: Error cargando imagen:', error);
          reject(error);
        };
      });
      
      const fixedHeight = 20;
      const aspectRatio = img.width / img.height;
      const width = fixedHeight * aspectRatio;
      
      doc.addImage(img, 'JPEG', PAGE_MARGIN, yPos - 10, width, fixedHeight, undefined, 'FAST');
      doc.setFontSize(16);
      doc.setTextColor(PURPLE_COLOR[0], PURPLE_COLOR[1], PURPLE_COLOR[2]);
      doc.text(studyName, PAGE_MARGIN + width + 5, yPos);
      
      console.log('PDF: Logo añadido exitosamente');
    } catch (error) {
      console.error('PDF: Error loading logo:', error);
      console.error('PDF: Detalles del error:', {
        message: error instanceof Error ? error.message : 'Mensaje no disponible',
        name: error instanceof Error ? error.name : 'Error desconocido'
      });
      
      // Fallback: texto sin logo
      doc.setFontSize(16);
      doc.setTextColor(PURPLE_COLOR[0], PURPLE_COLOR[1], PURPLE_COLOR[2]);
      doc.text(studyName, PAGE_MARGIN, yPos);
    }
  } else {
    console.log('PDF: No se encontró logo en el perfil');
    doc.setFontSize(16);
    doc.setTextColor(PURPLE_COLOR[0], PURPLE_COLOR[1], PURPLE_COLOR[2]);
    doc.text(studyName, PAGE_MARGIN, yPos);
  }
  yPos += 20;

  // Agregar técnico si existe
  if (study.optional_info?.technician) {
    doc.setFontSize(9);
    doc.setTextColor(GRAY_COLOR[0], GRAY_COLOR[1], GRAY_COLOR[2]);
    doc.setFont('helvetica', 'italic');
    doc.text(`${t('technician', { ns: 'study' })}: ${study.optional_info.technician}`, PAGE_MARGIN, yPos);
    yPos += 8;
    doc.setFont('helvetica', 'normal'); // Reset font style
  }

  // Mostrar equivalencia de puntos por hora en el PDF
  doc.setFontSize(12);
  doc.setTextColor(PURPLE_COLOR[0], PURPLE_COLOR[1], PURPLE_COLOR[2]);
  doc.text(`Equivalencia: 1 hora = ${pointsPerHour} puntos`, PAGE_MARGIN, yPos);
  yPos += 8;

  // Study information and configuration combined table
  const combinedData = [
    [
      { content: t('company', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.required_info.company },
      { content: t('studyNumber', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.optional_info?.study_number || '-' },
      { content: t('date', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.required_info.date }
    ],
    [
      { content: t('operator', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.optional_info?.operator || '-' },
      { content: t('section', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.optional_info?.section || '-' },
      { content: t('reference', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.optional_info?.reference || '-' }
    ],
    [
      { content: t('machine', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.optional_info?.machine || '-' },
      { content: t('tools', { ns: 'study' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: study.optional_info?.tools || '-' },
      { content: t('timeUnit', { ns: 'report' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: t(`units.${timeUnit}`, { ns: 'report' }) }
    ],
    [
      { content: t('shiftMinutes', { ns: 'report' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: shiftMinutes.toString() },
      { content: t('contingency', { ns: 'report' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: `${contingency}%` },
      { content: t('activityScale', { ns: 'report' }) + ':', styles: { fontStyle: 'bold' as const } },
      { content: `${study.required_info.activity_scale.normal}-${study.required_info.activity_scale.optimal}` }
    ]
  ];

  const commonTableStyles = {
    styles: {
      fontSize: 8,
      cellPadding: 2
    },
    headStyles: {
      fillColor: [147, 51, 234] as [number, number, number],
      textColor: 255,
      fontSize: 8,
      fontStyle: 'bold' as const,
      halign: 'center' as const,
      valign: 'middle' as const
    }
  };

  autoTable(doc, {
    startY: yPos,
    head: [[{ content: t('identifyingData', { ns: 'report' }).toUpperCase(), colSpan: 6, styles: { fillColor: PURPLE_COLOR, textColor: 255, fontSize: 10, fontStyle: 'bold' as const, halign: 'left' } }]],
    body: combinedData,
    theme: 'grid',
    styles: {
      fontSize: 8,
      textColor: GRAY_COLOR,
      lineWidth: 0.0,
      cellPadding: 2
    },
    columnStyles: {
      0: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
      1: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' },
      2: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
      3: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' },
      4: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
      5: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' }
    },
    ...commonTableStyles,
    margin: { left: PAGE_MARGIN, right: PAGE_MARGIN }
  });

  yPos = (doc as any).lastAutoTable.finalY + 15;

  // Results table
  const formatResult = (title: string, value: number | string, unit: string) => {
    return {
      content: title,
      styles: { 
        cellPadding: 2,
        valign: 'middle',
        fontStyle: 'bold' as const,
        fontSize: 8,
        textColor: [50, 50, 50]
      }
    };
  };

  const formatValue = (value: number | string, unit: string) => {
    return {
      content: `${typeof value === 'number' ? value.toFixed(2) : value} ${unit}`,
      styles: { 
        cellPadding: 2,
        valign: 'middle',
        fontSize: 9,
        textColor: [50, 50, 50],
        halign: 'center'
      }
    };
  };

  const resultsData = [
    [
      { content: t('results.title', { ns: 'report' }).toUpperCase(), colSpan: 6, styles: { fillColor: PURPLE_COLOR, textColor: 255, fontSize: 10, fontStyle: 'bold' as const, halign: 'left' } }
    ],
    [
      formatResult(t('results.normalCycle', { ns: 'report' }), reportStats.normalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
      formatValue(reportStats.normalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
      formatResult(t('results.optimalCycle', { ns: 'report' }), reportStats.optimalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
      formatValue(reportStats.optimalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
      formatResult(t('results.contingencyTime', { ns: 'report' }), reportStats.contingencyTime, t(`units.${timeUnit}`, { ns: 'report' })),
      formatValue(reportStats.contingencyTime, t(`units.${timeUnit}`, { ns: 'report' }))
    ],
    [
      formatResult(t('results.totalK', { ns: 'report' }), reportStats.totalK, t(`units.${timeUnit}`, { ns: 'report' })),
      formatValue(reportStats.totalK, t(`units.${timeUnit}`, { ns: 'report' })),
      formatResult(t('results.normalProduction', { ns: 'report' }), reportStats.normalProduction, t('units.cyclesPerShift', { ns: 'report' })),
      formatValue(reportStats.normalProduction, t('units.cyclesPerShift', { ns: 'report' })),
      formatResult(t('results.optimalProduction', { ns: 'report' }), reportStats.optimalProduction, t('units.cyclesPerShift', { ns: 'report' })),
      formatValue(reportStats.optimalProduction, t('units.cyclesPerShift', { ns: 'report' }))
    ],
    [
      formatResult(t('results.normalProductionPerHour', { ns: 'report' }), reportStats.normalProductionPerHour, t('units.cyclesPerHour', { ns: 'report' })),
      formatValue(reportStats.normalProductionPerHour, t('units.cyclesPerHour', { ns: 'report' })),
      formatResult(t('results.optimalProductionPerHour', { ns: 'report' }), reportStats.optimalProductionPerHour, t('units.cyclesPerHour', { ns: 'report' })),
      formatValue(reportStats.optimalProductionPerHour, t('units.cyclesPerHour', { ns: 'report' })),
      formatResult(t('results.normalSaturation', { ns: 'report' }), reportStats.normalSaturation, '%'),
      formatValue(reportStats.normalSaturation, '%')
    ],
    [
      formatResult(t('results.optimalSaturation', { ns: 'report' }), reportStats.optimalSaturation, '%'),
      formatValue(reportStats.optimalSaturation, '%'),
      formatResult(t('results.maxActivity', { ns: 'report' }), reportStats.maxActivity, '%'),
      formatValue(reportStats.maxActivity, '%'),
      formatResult(t('results.valueHour', { ns: 'report' }), reportStats.valueHour, t('units.hours', { ns: 'report' })),
      formatValue(reportStats.valueHour, t('units.hours', { ns: 'report' }))
    ],
    [
      formatResult(t('results.valueMinute', { ns: 'report' }), reportStats.valueMinute, t('units.minutes', { ns: 'report' })),
      formatValue(reportStats.valueMinute, t('units.minutes', { ns: 'report' })),
      formatResult(t('results.valuePoint', { ns: 'report' }), reportStats.valuePoint, t('units.points', { ns: 'report' })),
      formatValue(reportStats.valuePoint, t('units.points', { ns: 'report' })),
      { content: '', styles: { cellPadding: 5 } },
      { content: '', styles: { cellPadding: 5 } }
    ]
  ];

  autoTable(doc, {
    startY: yPos,
    body: resultsData,
    theme: 'grid',
    styles: {
      fontSize: 8,
      textColor: GRAY_COLOR,
      lineWidth: 0.1,
      valign: 'middle',
      overflow: 'linebreak',
      cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6
    },
    columnStyles: {
      0: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
      1: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' },
      2: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
      3: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' },
      4: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
      5: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' }
    },
    ...commonTableStyles,
    margin: { left: PAGE_MARGIN, right: PAGE_MARGIN }
  });

  yPos = (doc as any).lastAutoTable.finalY + 15;

  // Agregar secciones de unidades personalizadas si están habilitadas
  const customUnits = study.optional_info?.customUnits;
  if (customUnits) {
    const enabledUnits = [
      { type: 'squareMeters' as const, enabled: customUnits.enableSquareMeters, name: 'Metro Cuadrado', symbol: 'm²' },
      { type: 'linearMeters' as const, enabled: customUnits.enableLinearMeters, name: 'Metro Lineal', symbol: 'm' },
      { type: 'cubicMeters' as const, enabled: customUnits.enableCubicMeters, name: 'Metro Cúbico', symbol: 'm³' },
      { type: 'kilos' as const, enabled: customUnits.enableKilos, name: 'Kilogramo', symbol: 'kg' },
      { type: 'perimeter' as const, enabled: customUnits.enablePerimeter, name: 'Perímetro', symbol: 'm (perímetro)' }
    ].filter(unit => unit.enabled);

    // Calcular el espacio necesario para todas las unidades
    const estimatedHeightPerUnit = 80; // Altura estimada por unidad
    const totalEstimatedHeight = enabledUnits.length * estimatedHeightPerUnit;
    const availableHeight = doc.internal.pageSize.height - yPos - 30; // Espacio disponible

    // Si no caben todas las unidades, agregar nueva página
    if (totalEstimatedHeight > availableHeight) {
      doc.addPage();
      yPos = 20;
    }

    for (const unitConfig of enabledUnits) {
      const customValues = calculateCustomUnitValues(unitConfig.type, customUnits, reportStats, shiftMinutes, timeUnit);
      
      if (customValues) {
        // Verificar espacio disponible antes de cada unidad
        const spaceNeeded = 70; // Espacio mínimo necesario
        if (yPos + spaceNeeded > doc.internal.pageSize.height - 20) {
          doc.addPage();
          yPos = 20;
        }

        // Título de la sección de unidad personalizada (más compacto)
        const unitTitle = unitConfig.type === 'squareMeters' && customValues.unitValue > 0
          ? `RESULTADOS POR ${unitConfig.name.toUpperCase()} (${customValues.unitValue.toFixed(3)} ${customValues.unitLabel})`
          : `RESULTADOS POR ${unitConfig.name.toUpperCase()} (${customValues.unitValue.toFixed(3)} ${customValues.unitLabel})`;
        
        const unitResultsData = [
          [
            { content: unitTitle, colSpan: 6, styles: { fillColor: [59, 130, 246] as [number, number, number], textColor: 255, fontSize: 9, fontStyle: 'bold' as const, halign: 'left' } }
          ],
          [
            formatResult(`T.Normal/${unitConfig.symbol}`, customValues.normalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
            formatValue(customValues.normalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
            formatResult(`T.Óptimo/${unitConfig.symbol}`, customValues.optimalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
            formatValue(customValues.optimalCycle, t(`units.${timeUnit}`, { ns: 'report' })),
            formatResult(`Prod.Normal(${unitConfig.symbol}/turno)`, customValues.normalProduction, customValues.unitLabel),
            formatValue(customValues.normalProduction, customValues.unitLabel)
          ],
          [
            formatResult(`Prod.Óptima(${unitConfig.symbol}/turno)`, customValues.optimalProduction, customValues.unitLabel),
            formatValue(customValues.optimalProduction, customValues.unitLabel),
            formatResult(`Prod.Normal(${unitConfig.symbol}/h)`, customValues.normalProductionPerHour, `${customValues.unitLabel}/h`),
            formatValue(customValues.normalProductionPerHour, `${customValues.unitLabel}/h`),
            formatResult(`Prod.Óptima(${unitConfig.symbol}/h)`, customValues.optimalProductionPerHour, `${customValues.unitLabel}/h`),
            formatValue(customValues.optimalProductionPerHour, `${customValues.unitLabel}/h`)
          ]
        ];

        autoTable(doc, {
          startY: yPos,
          body: unitResultsData,
          theme: 'grid',
          styles: {
            fontSize: 7,
            textColor: GRAY_COLOR,
            lineWidth: 0.1,
            valign: 'middle',
            overflow: 'linebreak',
            cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6,
            cellPadding: 1
          },
          columnStyles: {
            0: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
            1: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' },
            2: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
            3: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' },
            4: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, fontStyle: 'bold' },
            5: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) / 6, halign: 'center' }
          },
          headStyles: {
            fillColor: PURPLE_COLOR,
            textColor: 255,
            fontSize: 7,
            fontStyle: 'bold' as const,
            halign: 'center',
            valign: 'middle',
            cellPadding: 1
          },
          margin: { left: PAGE_MARGIN, right: PAGE_MARGIN }
        });

        yPos = (doc as any).lastAutoTable.finalY + 8; // Espacio más compacto entre unidades
      }
    }
  }

  // Elements table
  const elementTableHead = [
    t('description', { ns: 'study' }).substring(0, 5) + '.',
    t('type', { ns: 'study' }).substring(0, 5) + '.',
    t('freq', { ns: 'study' }).substring(0, 5) + '.',
    t('time', { ns: 'study' }).substring(0, 5) + '.',
    t('act', { ns: 'study' }).substring(0, 5) + '.',
    t('supl', { ns: 'study' }).substring(0, 5) + '.',
    t('total', { ns: 'study' }).substring(0, 5) + '.'
  ];

  // Process element data for the table
  const elementTableData = elementStats.map((stat, index) => {
    const observedTime = stat.observedTime || 0;
    const activity = stat.averageActivity || 100;
    const supplements = stat.supplements || 0;
    const [numerator, denominator] = (stat.frequency || '1/1').split('/').map(Number);
    
    // For concurrent machine times, show 0 as the final time
    const totalTime = stat.concurrent_machine_time ? 0 : 
      observedTime * (numerator / denominator) * (1 + supplements / 100) * (activity / study.required_info.activity_scale.normal);
    
    // Add visual indicator for concurrent machine times
    const description = stat.concurrent_machine_time ? 
      `${stat.description || ''} [${t('concurrent', { ns: 'machine' })}]` : 
      stat.description || '';
    
    return [
      description,
      i18next.t(`types.${stat.type}`, { ns: 'report' }) || '',
      stat.frequency?.toString() || '',
      observedTime.toFixed(3),
      `${activity.toFixed(2)}%`,
      `${supplements.toFixed(2)}%`,
      totalTime.toFixed(3) + (stat.concurrent_machine_time ? ` (${t('excluded', { ns: 'report' })})` : '')
    ];
  });

  // Create a custom style for concurrent machine time rows and consolidated studies
  const rowStyles: any = {};
  elementStats.forEach((stat, index) => {
    if (stat.concurrent_machine_time) {
      rowStyles[index] = { textColor: RED_COLOR, fontStyle: 'italic', fillColor: [255, 230, 230] };
    } else if ((study.optional_info as any)?.consolidated) {
      // Detectar el estudio de origen para estudios consolidados
      let studyIndex = 0;
      const elementDescription = stat.description || '';
      const match = elementDescription.match(/^(\d+)\./);
      if (match) {
        studyIndex = parseInt(match[1]) - 1;
      }
      
      // Colores de fondo muy suaves para estudios consolidados
      const colors = [
        [240, 249, 255], // Azul muy claro
        [240, 253, 244], // Verde muy claro  
        [254, 247, 205], // Amarillo muy claro
        [253, 242, 248], // Rosa muy claro
        [243, 244, 246], // Gris muy claro
        [238, 242, 255], // Índigo muy claro
        [240, 253, 250], // Teal muy claro
        [254, 252, 232]  // Lima muy claro
      ];
      
      const colorIndex = studyIndex % colors.length;
      rowStyles[index] = { fillColor: colors[colorIndex] };
    }
  });

  autoTable(doc, {
    head: [[{ content: t('elementDetails.title', { ns: 'report' }).toUpperCase(), colSpan: 7, styles: { fillColor: PURPLE_COLOR, textColor: 255, fontSize: 10, fontStyle: 'bold' as const, halign: 'left' } }], elementTableHead],
    body: elementTableData,
    startY: yPos,
    theme: 'grid',
    columnStyles: {
      0: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.50, halign: 'left', valign: 'middle' },
      1: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.06, halign: 'center', valign: 'middle' },
      2: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.08, halign: 'center', valign: 'middle' },
      3: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.11, halign: 'center', valign: 'middle' },
      4: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.08, halign: 'center', valign: 'middle' },
      5: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.07, halign: 'center', valign: 'middle' },
      6: { cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.10, halign: 'center', valign: 'middle' }
    },
    rowStyles: rowStyles,
    ...commonTableStyles,
    margin: { left: PAGE_MARGIN, right: PAGE_MARGIN }
  });

  // Add header and footer to all pages
  const totalPages = doc.getNumberOfPages();
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    const pageHeight = doc.internal.pageSize.height;
    // Pie de página
    const today = new Date().toLocaleDateString();
    doc.setFontSize(8);
    doc.setTextColor(GRAY_COLOR[0], GRAY_COLOR[1], GRAY_COLOR[2]);
    doc.text(today, PAGE_MARGIN, pageHeight - 10);
    doc.text(`${t('page', { ns: 'report' })} ${i}`, pageWidth - PAGE_MARGIN, pageHeight - 10, { align: 'right' });
  }

  // Agregar análisis visual si hay datos de gráficas
  if (chartData && Object.keys(chartData).length > 0) {
    // Agregar nueva página para el análisis visual
    doc.addPage();
    yPos = 20;

    // Título del análisis visual
    doc.setFontSize(16);
    doc.setTextColor(PURPLE_COLOR[0], PURPLE_COLOR[1], PURPLE_COLOR[2]);
    doc.text(t('charts.title', { ns: 'report', defaultValue: 'Análisis Visual del Proceso' }), PAGE_MARGIN, yPos);
    yPos += 20;

    // Agregar resumen estadístico si existe
    if (chartData.summary) {
      doc.setFontSize(12);
      doc.setTextColor(PURPLE_COLOR[0], PURPLE_COLOR[1], PURPLE_COLOR[2]);
      doc.text(t('charts.summary.title', { ns: 'report', defaultValue: 'Resumen Estadístico' }), PAGE_MARGIN, yPos);
      yPos += 15;

      const summaryData = [
        [
          t('charts.summary.totalElements', { ns: 'report', defaultValue: 'Elementos Totales' }),
          chartData.summary.totalElements.toString()
        ],
        [
          t('charts.summary.optimalSaturation', { ns: 'report', defaultValue: 'Saturación Óptima' }),
          `${chartData.summary.optimalSaturation.toFixed(1)}%`
        ],
        [
          t('charts.summary.saturation', { ns: 'report', defaultValue: 'Saturación Normal' }),
          `${chartData.summary.normalSaturation.toFixed(1)}%`
        ],
        [
          t('charts.summary.maxActivity', { ns: 'report', defaultValue: 'Actividad Máxima' }),
          `${chartData.summary.maxActivity.toFixed(1)}%`
        ]
      ];

      autoTable(doc, {
        startY: yPos,
        body: summaryData,
        theme: 'grid',
        styles: {
          fontSize: 10,
          textColor: GRAY_COLOR,
          cellPadding: 3
        },
        columnStyles: {
          0: { fontStyle: 'bold', cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.7 },
          1: { halign: 'center', cellWidth: (pageWidth - 2 * PAGE_MARGIN) * 0.3 }
        },
        margin: { left: PAGE_MARGIN, right: PAGE_MARGIN }
      });

      yPos = (doc as any).lastAutoTable.finalY + 20;
    }

    // Agregar gráficas en formato 2x2 con tamaños optimizados
    const chartsToAdd = [
      { data: chartData.machineTypes, title: 'Distribución por Tipo de Máquina' },
      { data: chartData.saturation, title: 'Saturación/Rendimiento Normal' },
      { data: chartData.elements, title: 'Tiempos por Elemento' },
      { data: chartData.supplements, title: 'Suplementos por Elemento' },
      { data: chartData.activity, title: 'Actividad por Elemento' },
      { data: chartData.cycleDistribution, title: 'Distribución del Tiempo de Ciclo' }
    ].filter(chart => chart.data); // Solo gráficas con datos

    // Configuración para gráficos en formato 2x2 (más pequeños)
    const chartWidth = (pageWidth - 2 * PAGE_MARGIN - 15) / 2; // 50% del ancho menos espacio entre gráficos
    const chartHeight = 65; // Altura fija más pequeña para evitar que sean demasiado grandes
    const chartSpacing = 15; // Espacio entre gráficos

    for (let i = 0; i < chartsToAdd.length; i += 2) {
      // Verificar si necesitamos una nueva página
      const spaceNeeded = chartHeight + 25; // Espacio para título + gráfico
      if (yPos + spaceNeeded > doc.internal.pageSize.height - 20) {
        doc.addPage();
        yPos = 20;
      }

      // Procesar hasta 2 gráficos por fila
      const chartsInRow = chartsToAdd.slice(i, i + 2);
      let maxTitleHeight = 0;

      // Agregar títulos de los gráficos en la fila
      chartsInRow.forEach((chart, index) => {
        if (chart.data) {
          const xPos = PAGE_MARGIN + (index * (chartWidth + chartSpacing));
          doc.setFontSize(9); // Tamaño de fuente más pequeño
          doc.setTextColor(GRAY_COLOR[0], GRAY_COLOR[1], GRAY_COLOR[2]);
          
          // Calcular líneas necesarias para el título
          const maxTitleWidth = chartWidth;
          const titleLines = doc.splitTextToSize(chart.data.title, maxTitleWidth);
          const currentTitleHeight = titleLines.length * 3; // Altura de línea más pequeña
          maxTitleHeight = Math.max(maxTitleHeight, currentTitleHeight);
          
          // Dibujar título centrado
          titleLines.forEach((line: string, lineIndex: number) => {
            const textWidth = doc.getTextWidth(line);
            const textX = xPos + (chartWidth - textWidth) / 2;
            doc.text(line, textX, yPos + (lineIndex * 3));
          });
        }
      });

      yPos += maxTitleHeight + 5;

      // Agregar gráficos en la fila
      chartsInRow.forEach((chart, index) => {
        if (chart.data) {
          try {
            const xPos = PAGE_MARGIN + (index * (chartWidth + chartSpacing));

            doc.addImage(
              chart.data.imageData,
              'PNG',
              xPos,
              yPos,
              chartWidth,
              chartHeight, // Altura fija más pequeña
              undefined,
              'FAST'
            );
          } catch (error) {
            console.error(`Error agregando gráfica ${chart.title}:`, error);
            const xPos = PAGE_MARGIN + (index * (chartWidth + chartSpacing));
            doc.setFontSize(8);
            doc.setTextColor(RED_COLOR[0], RED_COLOR[1], RED_COLOR[2]);
            doc.text('Error: No se pudo cargar la gráfica', xPos, yPos + chartHeight / 2);
          }
        }
      });

      yPos += chartHeight + 12; // Espacio más compacto después de cada fila
    }

    // Actualizar numeración de páginas
    const newTotalPages = doc.getNumberOfPages();
    for (let i = totalPages + 1; i <= newTotalPages; i++) {
      doc.setPage(i);
      const pageHeight = doc.internal.pageSize.height;
      const today = new Date().toLocaleDateString();
      doc.setFontSize(8);
      doc.setTextColor(GRAY_COLOR[0], GRAY_COLOR[1], GRAY_COLOR[2]);
      doc.text(today, PAGE_MARGIN, pageHeight - 10);
      doc.text(`${t('page', { ns: 'report' })} ${i}`, pageWidth - PAGE_MARGIN, pageHeight - 10, { align: 'right' });
    }
  }

  // Guardar PDF
  doc.save(`${study.required_info.name}-${t('title', { ns: 'report' })}.pdf`);
}