import { useEffect, useState } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useStudyStore } from '../store/studyStore';
import { useAuthStore } from '../store/authStore';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { Study } from '../types';

export const useStudyFromUrl = () => {
  const { studyId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation(['common']);
  const user = useAuthStore(state => state.user);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const fetchStudyById = useStudyStore(state => state.fetchStudyById);
  const setSelectedStudy = useStudyStore(state => state.setSelectedStudy);
  const setSelectedStudyById = useStudyStore(state => state.setSelectedStudyById);
  const [loadedStudy, setLoadedStudy] = useState<Study | null>(null);

  useEffect(() => {
    const loadStudy = async () => {
      if (!user) {
        return;
      }

      // Extraer ID del estudio de la URL
      let urlStudyId = studyId;
      
      // Si no hay studyId en los parámetros, intentar extraerlo de la ruta
      if (!urlStudyId) {
        const pathParts = location.pathname.split('/');
        const studyIndex = pathParts.findIndex(part => part === 'study');
        if (studyIndex !== -1 && pathParts.length > studyIndex + 1) {
          urlStudyId = pathParts[studyIndex + 1];
        }
      }

      // Si no hay ID de estudio en la URL, no hacemos nada
      if (!urlStudyId) {
        return;
      }

      // Si estamos en una página que requiere estudio (tiene studyId en la URL)
      // pero no hay estudio seleccionado o es diferente, intentamos cargarlo
      if (!selectedStudy || selectedStudy.id !== urlStudyId) {
        console.log('Loading study from URL:', urlStudyId);
        try {
          const study = await setSelectedStudyById(urlStudyId);
          
          if (!study) {
            console.error('Study not found:', urlStudyId);
            toast.error(t('common:errors.studyNotFound'));
            return;
          }
          
          console.log('Study loaded successfully from URL:', study.required_info?.name);
          setLoadedStudy(study);
        } catch (error) {
          console.error('Error loading study from URL:', error);
          toast.error(t('common:errors.loadingStudy'));
        }
      } else {
        console.log('Study already selected:', selectedStudy.required_info?.name);
        setLoadedStudy(selectedStudy);
      }
    };

    loadStudy();
  }, [location.pathname, studyId, user, selectedStudy?.id, fetchStudyById, setSelectedStudy, setSelectedStudyById, t, navigate]);

  // Retornar el estudio seleccionado solo si coincide con el ID de la URL
  // o el estudio cargado desde la URL si está disponible
  return loadedStudy || (studyId && selectedStudy?.id === studyId ? selectedStudy : null);
};
