import React from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronRight, Home, ArrowUp } from 'lucide-react';
import { Button } from './ui/button';

interface FolderBreadcrumbsProps {
  currentFolder: any | null;
  breadcrumbs: Array<{ id: string; name: string }>;
  folderTree: any[]; // Agregar folderTree para buscar carpetas completas
  onFolderSelect: (folder: any | null) => void;
  onGoUp: () => void;
  className?: string;
}

export const FolderBreadcrumbs: React.FC<FolderBreadcrumbsProps> = ({
  currentFolder,
  breadcrumbs,
  folderTree,
  onFolderSelect,
  onGoUp,
  className = ''
}) => {
  const { t } = useTranslation(['common']);
  
  if (!currentFolder) return null;

  // Función para buscar una carpeta completa en el árbol
  const findFolderInTree = (folderId: string): any | null => {
    const findInNodes = (nodes: any[]): any | null => {
      for (const node of nodes) {
        if (node.id === folderId) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = findInNodes(node.children);
          if (found) return found;
        }
      }
      return null;
    };
    return findInNodes(folderTree);
  };

  return (
    <div className={`flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border ${className}`}>
      {/* Botón de subir nivel */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onGoUp}
        className="text-gray-600 hover:text-gray-800 hover:bg-gray-200"
        title="Subir un nivel"
      >
        <ArrowUp className="w-4 h-4" />
      </Button>

      {/* Separador */}
      <div className="h-4 w-px bg-gray-300" />

      {/* Breadcrumbs */}
      <nav className="flex items-center space-x-1 text-sm overflow-x-auto">
        {/* Home/Raíz */}
        <button
          onClick={() => onFolderSelect(null)}
          className="flex items-center text-gray-600 hover:text-blue-600 transition-colors px-2 py-1 rounded hover:bg-blue-50"
          title={t('common:allStudies', { defaultValue: 'All Studies' })}
        >
          <Home className="w-4 h-4" />
          <span className="ml-1 hidden sm:inline">{t('common:allStudies', { defaultValue: 'All Studies' })}</span>
        </button>

        {/* Breadcrumbs de carpetas */}
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.id}>
            <ChevronRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
            <button
              onClick={() => {
                // Buscar la carpeta completa en el árbol antes de navegar
                const fullFolder = findFolderInTree(crumb.id);
                console.log('📁 FolderBreadcrumbs: Navigating to folder via breadcrumb:', {
                  crumbId: crumb.id,
                  crumbName: crumb.name,
                  foundFullFolder: fullFolder ? 'YES' : 'NO',
                  fullFolderData: fullFolder
                });
                onFolderSelect(fullFolder);
              }}
              className={`px-2 py-1 rounded transition-colors flex-shrink-0 ${
                index === breadcrumbs.length - 1
                  ? 'text-gray-900 font-medium bg-blue-100'
                  : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
              }`}
              title={`Ir a carpeta: ${crumb.name}`}
            >
              {crumb.name}
            </button>
          </React.Fragment>
        ))}
      </nav>
    </div>
  );
}; 