:root {
    --primary-color: #0066cc;
    --secondary-color: #004d99;
    --accent-color: #ff9900;
    --text-color: #333333;
    --light-bg: #f8f9fa;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    color: var(--text-color);
    padding-top: 76px;
}

.hero {
    background: linear-gradient(135deg, var(--light-bg) 0%, #ffffff 100%);
    min-height: calc(100vh - 76px);
    display: flex;
    align-items: center;
}

.hero h1 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand img {
    transition: transform 0.3s ease;
}

.navbar-brand img:hover {
    transform: scale(1.05);
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.8rem 2rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.8rem 2rem;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.bi {
    color: var(--primary-color);
}

footer {
    background-color: #1a1a1a;
}

footer a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* Nuevos estilos técnicos */
.tech-specs-card {
  border: 1px solid #2A5C82;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.integration-badge {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.api-doc-section {
  background: linear-gradient(to right, #2A5C82, #1a3a57);
  color: white;
  padding: 3rem 0;
}

@media (max-width: 768px) {
    .hero {
        text-align: center;
    }
    
    .cta-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn {
        width: 100%;
    }
}
