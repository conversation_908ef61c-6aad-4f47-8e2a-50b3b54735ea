export default {
  appName: 'Time Study',
  pwa: {
    installPrompt: 'Install the app for better performance!',
    install: 'Install the app to use it offline',
    hideBanner: 'Hide notification'
  },
  search: {
    title: "Search Studies",
    placeholder: "Search",
    button: "Search",
    search: "Search studies",
    clear: "Clear search",
    modal: {
      title: "Search Studies",
      subtitle: "Find studies by name, company, process or operator",
      searchPlaceholder: "Search by keyword",
      filters: "Filters",
      clearFilters: "Clear filters",
      noResults: "No results found",
      loadingResults: "Loading results..."
    }
  },
  decreaseActivity: 'Decrease activity',
  increaseActivity: 'Increase activity',
  addComment: 'Add comment',
  save: 'Save',
  create: 'Create',
  cancel: 'Cancel',
  select: 'Select',
  delete: 'Delete',
  deleteAll: 'Delete All',
  edit: 'Edit',
  saving: 'Saving...',
  loading: 'Loading...',
  retry: 'Retry',
  refresh: 'Refresh',
  refreshing: 'Refreshing...',
  approve: 'Approve',
  reject: 'Reject',
  requestedOn: 'Requested on',
  deleteConfirmation: 'Are you sure you want to delete this item?',
  confirmDelete: 'Confirm Deletion',
  each: 'each',
  apply: 'Apply',
  reset: 'Reset',
  close: 'Close',
  add: 'Add',
  filter: 'Filter',
  error: 'Error',
  copy: 'Copy',
  since: 'Since',
  you: 'You',
  inviteCode: 'Invitation Code',
  notifications: 'Notifications',
  confirm: 'Confirm',
  next: 'Next',
  previous: 'Previous',
  all: 'All',
  total: 'Total',
  clearAll: 'Clear all',
  untitledStudy: 'Untitled study',
  clearFilters: 'Clear filters',
  selected: 'Selected',
  creating: 'Creating...',
  nameRequired: 'Name is required',
  close: 'Close',
  changelog: {
    title: 'Documentation',
    tabs: {
      changelog: 'Changelog',
      security: 'Security',
      faq: 'FAQ'
    }
  },
  version: 'Version',
  changeTypes: {
    added: 'Added',
    changed: 'Changed',
    fixed: 'Fixed',
    updated: 'Updated',
    removed: 'Removed'
  },
  units: {
    seconds: 'Seconds',
    minutes: 'Minutes',
    hours: 'Hours',
    mmm: 'MMM (Measured and Modified Minutes)',
    cmm: 'CMM (Centesimal Minutes)',
    tmu: 'TMU (Time Measurement Unit)',
    dmh: 'DMH (Ten-thousandths of an Hour)',
    currency: '$',
  },
  tal: {
    A1: {
      description: {
        Reduced: 'No activity, the operator appears half asleep and shows no interest in the work',
        Medium: 'Normal activity, the operator appears slow but does not deliberately waste time',
        Intense: 'Normal activity, the operator shows good concentration',
        Reducido: 'Reduced',
        Mediano: 'Medium',
        Intenso: 'Intense'
      }
    },
    A2: {
      description: 'Intense activity, the operator shows very good concentration'
    },
    A3: {
      description: 'Very intense activity, the operator demonstrates speed and skill'
    },
    A4: {
      description: 'Exceptional activity, the operator shows extraordinary speed and precision'
    },
    A5: {
      description: 'Sustained exceptional activity, the operator maintains a very high pace'
    },
    B1: {
      description: 'No physical effort, the operator does not show physical effort'
    },
    B2: {
      description: 'Low effort, the operator shows little physical effort'
    },
    B3: {
      description: 'Normal effort, the operator shows moderate physical effort'
    },
    B4: {
      description: 'High effort, the operator shows considerable physical effort'
    },
    C1: {
      description: 'Ideal conditions, perfect environment without disturbances'
    },
    C2: {
      description: 'Good conditions, pleasant environment with few disturbances'
    },
    C3: {
      description: 'Normal conditions, typical work environment'
    },
    C4: {
      description: 'Fair conditions, environment with some disturbances'
    },
    C5: {
      description: 'Poor conditions, environment with frequent disturbances'
    },
    C6: {
      description: 'Very poor conditions, very unfavorable environment'
    }
  },
  no_credits: {
    title: "No Credits Available",
    message: "You don't have enough credits to create a new study. Please purchase more credits to continue."
  },
  noMachineTimeElements: 'No machine time elements found',
  concurrentMachineTime: 'Concurrent time',
  concurrentMachineTimeHelp: 'Check this option if this time occurs during another machine time. It will be converted to a running machine element.',
  convertToRunningMachine: 'Convert to Running Machine',
  convertToRunningMachineDescription: 'This element will be converted to a repetitive running machine element and will be removed from the machine time list. Are you sure?',
  position: 'Position',
  averageActivity: 'Average activity',
  repetitionsEach: 'Repetitions each',
  cycles: 'Cycles',
  noRepetitionsSet: 'No repetitions set',
  machineTime: 'Machine Time',
  profile: {
    basicInfo: 'Basic Information',
    email: 'Email',
    companyName: 'Company Name',
    companyLogo: 'Company Logo',
  },
  preferences: {
    title: 'Preferences',
    defaultTimeUnit: 'Default Time Unit',
    defaultLanguage: 'Default Language',
    defaultContingency: 'Default Contingency Percentage',
    minutesPerShift: 'Minutes per Shift',
    updateSuccess: 'Preferences Updated',
    updateSuccessMessage: 'Your preferences have been updated successfully',
    updateError: 'Update Error',
    updateErrorMessage: 'An error occurred while updating your preferences'
  },
  organizations: {
    title: 'Organizations',
    myOrganizations: 'My Organizations',
    createAndManage: 'Create and manage your organizations',
    teamMembers: 'Team Members',
    manageMembers: 'Manage the members of your organization',
    shareStudiesDescription: 'Share your studies with the members of this organization',
    create: 'Create Organization',
    join: 'Join Organization',
    noOrganizations: 'You don\'t belong to any organization',
    name: 'Organization Name',
    description: 'Description (optional)',
    createButton: 'Create',
    inviteCode: 'Invite Code',
    copy: 'Copy',
    manage: 'Manage',
    viewMembers: 'View Members',
    loading: 'Loading your organizations...',
    inviteCodeCopied: 'Code Copied',
    inviteCodeCopiedDescription: 'The invite code has been copied to clipboard'
  },
  organization: {
    title: 'Organization',
    name: 'Name',
    description: 'Description',
    create: 'Create organization',
    shareStudies: 'Share studies with organization',
    noStudiesFound: 'No studies found to share',
    studyShared: 'Study shared with organization',
    studyUnshared: 'Study unshared with organization',
    allStudiesShared: 'All studies shared with organization',
    allStudiesUnshared: 'All studies unshared with organization',
    studyAutoShared: 'The study has been automatically shared with your organization',
    join: 'Join Organization',
    leave: 'Leave Organization',
    delete: 'Delete Organization',
    edit: 'Edit Organization',
    save: 'Save Changes',
    cancel: 'Cancel',
    members: 'Members',
    viewMembers: 'View Members',
    noOrganizations: 'You don\'t belong to any organization',
    createFirst: 'Create your first organization',
    joinFirst: 'Join an organization',
    inviteCode: 'Invite Code',
    createdAt: 'Created at',
    details: 'Details',
    copy: 'Copy',
    copied: 'Copied',
    inviteCodeCopied: 'Invite code copied to clipboard',
    joinSuccess: 'You have successfully joined the organization',
    createSuccess: 'Organization created successfully',
    editSuccess: 'Organization updated successfully',
    deleteSuccess: 'Organization deleted successfully',
    leaveSuccess: 'You have successfully left the organization',
    deleteConfirm: 'Are you sure you want to delete this organization?',
    leaveConfirm: 'Are you sure you want to leave this organization?',
    deleteWarning: 'This action cannot be undone',
    leaveWarning: 'You will need a new invite code to rejoin',
    invalidInviteCode: 'Invalid invite code',
    alreadyMember: 'You are already a member of this organization',
    notFound: 'Organization not found',
    error: 'An error has occurred',
    errorCreate: 'Error creating organization',
    errorJoin: 'Error joining organization',
    errorLeave: 'Error leaving organization',
    errorDelete: 'Error deleting organization',
    errorEdit: 'Error editing organization',
    errorFetch: 'Error fetching organizations',
    errorFetchMembers: 'Error fetching organization members',
    memberRemoved: 'Member removed successfully',
    studiesNoLongerShared: 'The removed member\'s studies are no longer shared with the organization',
    errorRemovingMember: 'Error removing member',
    cleanOrphanedStudies: 'Clean Orphaned Studies',
    cleanOrphanedStudiesDescription: 'Remove organization access from studies belonging to expelled members',
    cleanOrphanedStudiesNote: 'Note: Studies are automatically cleaned when a member is removed from the organization. This button is for manual cleanup if needed.',
    cleaningOrphanedStudies: 'Cleaning orphaned studies...',
    noOrphanedStudies: 'No orphaned studies found',
    errorCleaningStudies: 'Error cleaning orphaned studies',
    studiesCleanedSuccess: 'Orphaned studies cleaned successfully',
    role: 'Role',
    actions: 'Actions',
    remove: 'Remove',
    removeConfirm: 'Are you sure you want to remove this member?',
    removeMemberSuccess: 'Member removed successfully',
    removeMemberError: 'Error removing member',
    memberRemovalWarning: 'When a member is removed, their studies will no longer be shared with the organization',
    memberLossOfAccess: 'The removed member will no longer have access to organization studies',
  },
  joinRequests: {
    title: 'Join Requests',
    pendingTitle: 'Pending Requests',
    approveRequest: 'Approve',
    rejectRequest: 'Reject',
    approved: 'Request approved',
    approvedDescription: 'The request has been approved successfully',
    rejected: 'Request rejected',
    rejectedDescription: 'The request has been rejected successfully',
    approve: 'Approve',
    reject: 'Reject',
    noRequests: 'No pending requests',
    requestApproved: 'Request approved successfully',
    requestRejected: 'Request rejected successfully',
    errorProcessing: 'Error processing request',
    errorLoading: 'Error loading requests',
    errorLoadingNames: 'Error loading organization names',
    statusPending: 'Pending',
    statusApproved: 'Approved',
    statusRejected: 'Rejected',
    mySentRequests: 'My Sent Requests',
    noSentRequests: 'You haven\'t sent any join requests',
    loadingSentRequests: 'Loading your sent requests...',
    loadingOrganizationNames: 'Loading organization names...',
    yourRequest: 'Your request',
    joinInvitation: 'Want to join an existing organization? Ask an administrator for the invitation code and send your request.',
    unknownOrganization: 'Unknown Organization',
    sentAt: 'Sent {{time}}',
    processedAt: 'Processed {{time}}',
    sent: 'Sent:',
    processed: 'Processed:',
    refresh: 'Refresh',
    refreshSuccess: 'Requests updated',
    refreshing: 'Refreshing...',
    refreshed: 'Requests updated',
    refreshedDescription: 'The requests have been updated successfully',
  },
  organizationMembers: {
    title: 'Organization Members',
    email: 'Email',
    role: 'Role',
    actions: 'Actions',
    remove: 'Remove member',
    promote: 'Promote to admin',
    demote: 'Demote to member',
    owner: 'Owner',
    admin: 'Admin',
    member: 'Member',
    noMembers: 'No members in this organization',
    selectedOrganization: 'Selected organization',
    shareStudiesHint: 'Manage your organizations, members and share studies with your team',
    removeConfirm: 'Are you sure you want to remove this member?',
    removeSuccess: 'Member removed successfully',
    removeError: 'Error removing member',
    promoteSuccess: 'Member promoted successfully',
    demoteSuccess: 'Member demoted successfully',
    cannotRemoveSelf: 'You cannot remove yourself',
    cannotRemoveOwner: 'You cannot remove the owner of the organization',
    studiesUnsharedOnRemoval: 'When a member is removed, their studies will no longer be shared with the organization',
    accessRevokedOnRemoval: 'The removed member will no longer have access to organization studies'
  },
  common: {
    selectAll: 'Select all',
    deselectAll: 'Deselect all',
    ownedByYou: 'Owned by you',
    show: 'Show',
    hide: 'Hide',
    notFound: 'Not found',
    details: 'Details',
    inviteCode: 'Invite Code',
    createdAt: 'Created at',
    copy: 'Copy',
    copied: 'Copied',
    errors: {
      savingRecord: 'Error saving record',
      updatingRecord: 'Error updating record',
      deletingRecord: 'Error deleting record',
      deletingAllRecords: 'Error deleting all records',
      processRequest: 'Error processing request',
      refreshFailed: 'Error refreshing',
    },
  },
  header: {
    profile: 'Profile',
    logout: 'Logout',
  },
  subscription: {
    active: 'Active',
    cancel: 'Cancel',
    inactive: 'Inactive',
    request: 'Request subscription',
  },
  
  // Folder system translations
  folders: 'Folders',
  createFolder: 'Create Folder',
  editFolder: 'Edit Folder',
  deleteFolder: 'Delete Folder',
  confirmDeleteFolder: 'Are you sure you want to delete this folder?',
  createSubfolder: 'Create Subfolder',
  subfolder: 'Subfolder',
  parentFolder: 'Parent Folder',
  rootLevel: 'Root Level',
  allStudies: 'All Studies',
  folderNamePlaceholder: 'Folder name...',
  folderDescriptionPlaceholder: 'Optional description...',
  nameRequired: 'Name is required',
  nameTooShort: 'Name must be at least 2 characters',
  nameTooLong: 'Name cannot be more than 50 characters',
  nameAlreadyExists: 'A folder with this name already exists',
  descriptionTooLong: 'Description cannot be more than 200 characters',
  moveToFolder: 'Move to folder',
  noFolder: 'No folder',
  noFoldersAvailable: 'No folders available',
  moving: 'Moving...',
  newFolder: 'New Folder',
  preview: 'Preview',
  color: 'Color',
  exportReports: 'Export Reports',
  export: 'Export',
  studies: 'studies',
  unorganized: 'Unorganized',
  consolidate: 'Consolidate',
  consolidateStudies: 'Consolidate Studies',
  includeSubfolders: 'Include subfolders',
  consolidateSubfolders: 'Also consolidate studies from subfolders',
  
  useElement: 'Use element',
  deselect: 'Deselect',
} as const;
