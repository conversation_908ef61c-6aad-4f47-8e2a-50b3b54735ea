import React, { useState, useEffect } from 'react';
import { cn } from '../../lib/utils';

interface FeedbackToastProps {
  message: string;
  type: 'success' | 'error' | 'loading' | 'info';
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const FeedbackToast: React.FC<FeedbackToastProps> = ({
  message,
  type,
  isVisible,
  onClose,
  duration = 4000,
  position = 'top-right'
}) => {
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (isVisible && type !== 'loading') {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, type, duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose();
      setIsExiting(false);
    }, 300);
  };

  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <span className="icon-bounce">✅</span>;
      case 'error':
        return <span className="icon-bounce">❌</span>;
      case 'loading':
        return <div className="loading-micro w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />;
      case 'info':
        return <span className="icon-pulse">ℹ️</span>;
      default:
        return null;
    }
  };

  const getTypeClasses = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800 shadow-lg';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800 shadow-lg';
      case 'loading':
        return 'bg-blue-50 border-blue-200 text-blue-800 shadow-lg';
      case 'info':
        return 'bg-gray-50 border-gray-200 text-gray-800 shadow-lg';
      default:
        return '';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      default:
        return 'top-4 right-4';
    }
  };

  return (
    <div
      className={cn(
        'fixed z-50 max-w-sm w-full pointer-events-auto',
        getPositionClasses(),
        isExiting ? 'notification-slide notification-exit' : 'notification-slide'
      )}
    >
      <div
        className={cn(
          'flex items-center p-4 rounded-lg border-2 backdrop-blur-sm',
          'enhanced-hover state-transition',
          getTypeClasses(),
          type === 'success' && 'state-success'
        )}
      >
        <div className="flex-shrink-0 mr-3">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">
            {message}
          </p>
        </div>

        {type !== 'loading' && (
          <button
            onClick={handleClose}
            className={cn(
              'flex-shrink-0 ml-3 p-1 rounded-full',
              'btn-micro focus-enhanced',
              'hover:bg-black/10 focus:bg-black/20',
              'transition-colors duration-200'
            )}
            aria-label="Cerrar notificación"
          >
            <span className="text-sm">✕</span>
          </button>
        )}
      </div>
    </div>
  );
};

// Hook personalizado para usar el feedback toast
export const useFeedbackToast = () => {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    message: string;
    type: 'success' | 'error' | 'loading' | 'info';
    isVisible: boolean;
  }>>([]);

  const addToast = (message: string, type: 'success' | 'error' | 'loading' | 'info') => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts(prev => [...prev, { id, message, type, isVisible: true }]);
    return id;
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const updateToast = (id: string, updates: Partial<{ message: string; type: 'success' | 'error' | 'loading' | 'info' }>) => {
    setToasts(prev => prev.map(toast => 
      toast.id === id ? { ...toast, ...updates } : toast
    ));
  };

  const showSuccess = (message: string) => addToast(message, 'success');
  const showError = (message: string) => addToast(message, 'error');
  const showLoading = (message: string) => addToast(message, 'loading');
  const showInfo = (message: string) => addToast(message, 'info');

  return {
    toasts,
    addToast,
    removeToast,
    updateToast,
    showSuccess,
    showError,
    showLoading,
    showInfo
  };
};

// Componente para renderizar todos los toasts
export const FeedbackToastContainer: React.FC = () => {
  const { toasts, removeToast } = useFeedbackToast();

  return (
    <>
      {toasts.map((toast) => (
        <FeedbackToast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          isVisible={toast.isVisible}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </>
  );
}; 