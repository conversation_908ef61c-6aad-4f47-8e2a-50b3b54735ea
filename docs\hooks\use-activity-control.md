# useActivityControl Hook

## Descripción
Hook para gestionar el control de actividad.

## Funcionalidades
```typescript
interface UseActivityControlReturn {
  activity: number;
  increaseActivity: () => void;
  decreaseActivity: () => void;
  setActivity: (value: number) => void;
  isValidActivity: (value: number) => boolean;
}

function useActivityControl(
  normalActivity: number,
  optimalActivity: number
): UseActivityControlReturn;
```

### Características
- Control de incrementos/decrementos
- Validación de rangos
- Límites configurables
- Persistencia de valores
- Historial de cambios

### Uso
```typescript
const {
  activity,
  increaseActivity,
  decreaseActivity
} = useActivityControl(100, 133);
```