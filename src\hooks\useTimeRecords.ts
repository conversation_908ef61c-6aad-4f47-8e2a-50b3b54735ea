import { useMemo } from 'react';
import { TimeRecord } from '../types';

export function useTimeRecords(records: TimeRecord[], localRecords: TimeRecord[] = []) {
  const allRecords = useMemo(() => [...records, ...localRecords], [records, localRecords]);

  const stats = useMemo(() => {
    if (allRecords.length === 0) {
      return {
        totalTime: 0,
        averageTime: 0,
        recordCount: 0,
        averageActivity: 0
      };
    }

    const totalTime = allRecords.reduce((sum, record) => sum + record.time, 0);
    const totalActivity = allRecords.reduce((sum, record) => sum + record.activity, 0);

    return {
      totalTime,
      averageTime: totalTime / allRecords.length,
      recordCount: allRecords.length,
      averageActivity: totalActivity / allRecords.length
    };
  }, [allRecords]);

  return stats;
}