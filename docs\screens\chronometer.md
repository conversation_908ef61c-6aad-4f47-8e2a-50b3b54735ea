# Pantalla de Cronómetro

## Descripción
Interfaz principal para la toma de tiempos de elementos repetitivos.

## Componentes
- `ElementStats`: Estadísticas del elemento actual
- `ChronometerControls`: Controles del cronómetro
- `TimeRecordList`: Lista de tiempos registrados
- `ActivityControls`: Control de actividad
- `ElementSelector`: Selector de elementos repetitivos

## Funcionalidades

### 1. Control de Tiempo
- Iniciar/Pausar/Detener cronómetro
- Registro de tiempos con precisión de milisegundos
- Navegación automática al siguiente elemento
- Gestión de pausas

### 2. Control de Actividad
- Ajuste rápido de actividad (+/- 5)
- Límites basados en actividad normal y óptima
- Validación de rangos permitidos

### 3. Gestión de Registros
- Visualización de tiempos tomados
- Edición de registros existentes
- Eliminación individual o masiva
- Adición de comentarios

### 4. Estadísticas en Tiempo Real
- Tiempo medio
- Actividad media
- Total de tomas
- Tomas restantes recomendadas

## Estados
```typescript
{
  currentElementIndex: number;
  viewingElementIndex: number;
  isRunning: boolean;
  isPaused: boolean;
  time: number;
  activity: number;
  records: Record<string, TimeRecord[]>;
  editingRecord: TimeRecord | null;
  showCommentModal: boolean;
  showEditModal: boolean;
  showDeleteAllModal: boolean;
}
```

## Flujo de Trabajo
1. Selección de elemento a cronometrar
2. Control del cronómetro:
   - Iniciar medición
   - Ajustar actividad si necesario
   - Registrar tiempo
   - Avanzar al siguiente elemento
3. Gestión de registros:
   - Visualizar tiempos tomados
   - Editar/comentar registros
   - Eliminar registros si necesario
4. Monitoreo de estadísticas