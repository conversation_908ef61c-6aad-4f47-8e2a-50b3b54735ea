import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LibraryElement } from '../../types';

interface LibraryElementFormProps {
  onSubmit: (element: Partial<LibraryElement>) => Promise<void>;
  onClose: () => void;
  initialData?: LibraryElement;
}

export const LibraryElementForm: React.FC<LibraryElementFormProps> = ({
  onSubmit,
  onClose,
  initialData
}) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    description: initialData?.description || '',
    type: initialData?.type || 'machine-stopped',
    frequency_repetitions: initialData?.frequency_repetitions || 1,
    frequency_cycles: initialData?.frequency_cycles || 1,
    repetition_type: initialData?.repetition_type || 'repetitive',
    is_shared: initialData?.is_shared || false,
    is_concurrent: initialData?.is_concurrent || false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error saving library element:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    if (name === 'type' && value === 'machine-time') {
      // When changing to machine-time, automatically set repetition_type to 'machine'
      setFormData(prev => ({
        ...prev,
        [name]: value,
        repetition_type: 'machine'
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg">
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-6">
            {initialData ? t('library.edit') : t('library.new')}
          </h2>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('method.description')}
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={3}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('method.type')}
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              >
                <option value="machine-stopped">{t('method.types.machineStopped')}</option>
                <option value="machine-running">{t('method.types.machineRunning')}</option>
                <option value="machine-time">{t('method.types.machineTime')}</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('method.repetitions')}
                </label>
                <input
                  type="number"
                  name="frequency_repetitions"
                  value={formData.frequency_repetitions}
                  onChange={handleChange}
                  min="1"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('method.cycles')}
                </label>
                <input
                  type="number"
                  name="frequency_cycles"
                  value={formData.frequency_cycles}
                  onChange={handleChange}
                  min="1"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('method.repetitionType')}
              </label>
              <select
                name="repetition_type"
                value={formData.repetition_type}
                onChange={handleChange}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              >
                <option value="repetitive">{t('method.types.repetitive')}</option>
                <option value="frequency">{t('method.types.frequency')}</option>
                <option value="machine">{t('method.types.machine')}</option>
              </select>
            </div>

            {formData.type === 'machine-time' && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_concurrent"
                  name="is_concurrent"
                  checked={formData.is_concurrent}
                  onChange={handleChange}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <label htmlFor="is_concurrent" className="ml-2 block text-sm text-gray-900">
                  {t('machine.concurrentMachineTime')}
                </label>
              </div>
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_shared"
                name="is_shared"
                checked={formData.is_shared}
                onChange={handleChange}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <label htmlFor="is_shared" className="ml-2 block text-sm text-gray-900">
                {t('library.makeShared')}
              </label>
            </div>

            <div className="flex justify-end space-x-4 pt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
              >
                {isLoading ? t('common.saving') : t('common.save')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};