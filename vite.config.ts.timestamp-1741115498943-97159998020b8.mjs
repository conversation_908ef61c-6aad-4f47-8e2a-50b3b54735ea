// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/OneDrive/APPS/bolt/CRONOMETRAS/V42/PRUEBAS/29.organizaciones%20paso1%20completado/CronometrasApp-react/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/OneDrive/APPS/bolt/CRONOMETRAS/V42/PRUEBAS/29.organizaciones%20paso1%20completado/CronometrasApp-react/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { VitePWA } from "file:///C:/Users/<USER>/OneDrive/APPS/bolt/CRONOMETRAS/V42/PRUEBAS/29.organizaciones%20paso1%20completado/CronometrasApp-react/node_modules/vite-plugin-pwa/dist/index.js";
import basicSsl from "file:///C:/Users/<USER>/OneDrive/APPS/bolt/CRONOMETRAS/V42/PRUEBAS/29.organizaciones%20paso1%20completado/CronometrasApp-react/node_modules/@vitejs/plugin-basic-ssl/dist/index.mjs";
import path from "path";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\OneDrive\\APPS\\bolt\\CRONOMETRAS\\V42\\PRUEBAS\\29.organizaciones paso1 completado\\CronometrasApp-react";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    basicSsl(),
    VitePWA({
      registerType: "autoUpdate",
      includeAssets: ["/icons/icon-192x192.png", "/icons/icon-512x512.png"],
      manifest: {
        name: "Cronometras",
        short_name: "Cronometras",
        description: "Cronometras - Gesti\xF3n de Estudios de Tiempo",
        theme_color: "#ffffff",
        icons: [
          {
            src: "/icons/icon-192x192.png",
            sizes: "192x192",
            type: "image/png",
            purpose: "any"
          },
          {
            src: "/icons/icon-512x512.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "any maskable"
          }
        ]
      },
      workbox: {
        // Limitar el tiempo de ejecución de las expresiones regulares
        maximumFileSizeToCacheInBytes: 5 * 1024 * 1024,
        // 5MB
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\.cronometras\.com/,
            handler: "NetworkFirst",
            options: {
              cacheName: "api-cache",
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 * 24
                // 24 horas
              }
            }
          }
        ],
        // Configuración de seguridad adicional
        navigateFallback: null,
        // Deshabilitar fallback automático
        cleanupOutdatedCaches: true,
        sourcemap: false,
        // Evitar el uso de expresiones regulares complejas
        navigateFallbackDenylist: [/\/api\/.*/]
        // Denegar rutas de API
      }
    })
  ],
  assetsInclude: ["**/*.md"],
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  },
  server: {
    https: true,
    port: 5173,
    host: "localhost",
    cors: false,
    hmr: {
      host: "localhost"
    },
    proxy: {
      "/api": {
        target: process.env.VITE_API_URL || "http://localhost:3000",
        changeOrigin: true,
        secure: false,
        rewrite: (path2) => path2.replace(/^\/api/, "")
      }
    },
    fs: {
      allow: [".."]
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
