# Library Element Form Component

## Descripción
Formulario para crear y editar elementos de biblioteca.

## Props
```typescript
interface LibraryElementFormProps {
  onSubmit: (element: Partial<LibraryElement>) => Promise<void>;
  onClose: () => void;
  initialData?: LibraryElement;
}
```

## Características
- Campos de configuración:
  - Descripción
  - Tipo de elemento
  - Repeticiones
  - Ciclos
  - Tipo de repetición
- Opción de compartir
- Validación de datos
- Estado de carga

## Uso
```tsx
<LibraryElementForm
  onSubmit={handleSubmit}
  onClose={() => setShowForm(false)}
  initialData={editingElement}
/>
```