-- Create a function to update user credits
create or replace function update_user_credits(
  p_user_id uuid,
  p_user_email text,
  p_extra_credits integer
)
returns json
language plpgsql
security definer -- This ensures the function runs with the privileges of the creator
as $$
declare
  v_result json;
begin
  -- Update or insert the record
  insert into user_study_limits (
    user_id,
    user_email,
    monthly_credits,
    extra_credits,
    used_monthly_credits,
    used_extra_credits,
    reset_date,
    created_at,
    updated_at
  )
  values (
    p_user_id,
    p_user_email,
    1, -- default monthly credits
    p_extra_credits,
    0,
    0,
    date_trunc('month', current_date)::date,
    current_timestamp,
    current_timestamp
  )
  on conflict (user_id) do update
  set
    extra_credits = p_extra_credits,
    updated_at = current_timestamp
  returning json_build_object(
    'user_id', user_id,
    'extra_credits', extra_credits,
    'monthly_credits', monthly_credits,
    'used_extra_credits', used_extra_credits,
    'used_monthly_credits', used_monthly_credits,
    'reset_date', reset_date
  ) into v_result;

  return v_result;
end;
$$;

-- Create a function to reset monthly credits used
create or replace function reset_monthly_credits()
returns void
language plpgsql
security definer
as $$
begin
  -- Reiniciar los créditos usados si estamos en un mes posterior al último reinicio
  update user_study_limits
  set 
    used_monthly_credits = 0,
    reset_date = date_trunc('month', current_date)::date,
    updated_at = current_timestamp
  where date_trunc('month', current_date) > date_trunc('month', reset_date);
end;
$$;

-- Create a trigger to automatically reset credits used
create or replace function check_reset_credits()
returns trigger
language plpgsql
as $$
begin
  -- Reiniciar los créditos usados si estamos en un mes posterior al último reinicio
  if date_trunc('month', current_date) > date_trunc('month', NEW.reset_date) then
    NEW.used_monthly_credits := 0;
    NEW.reset_date := date_trunc('month', current_date)::date;
    NEW.updated_at := current_timestamp;
  end if;
  return NEW;
end;
$$;

-- Add the trigger to the table
drop trigger if exists reset_credits_trigger on user_study_limits;
create trigger reset_credits_trigger
  before update on user_study_limits
  for each row
  execute function check_reset_credits();
