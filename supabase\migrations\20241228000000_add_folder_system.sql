-- Migration: Add folder system for hierarchical organization
-- This enables organizing studies in folders and subfolders

-- Create folders table with hierarchical structure
CREATE TABLE IF NOT EXISTS public.folders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    parent_folder_id UUID REFERENCES public.folders(id) ON DELETE CASCADE,
    color TEXT DEFAULT '#3B82F6', -- Color for visual organization
    icon TEXT DEFAULT 'folder', -- Icon identifier
    is_shared BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Ensure folder names are unique within the same parent and user
    CONSTRAINT unique_folder_name_per_parent UNIQUE (user_id, parent_folder_id, name),
    -- Prevent circular references
    CONSTRAINT no_self_reference CHECK (id != parent_folder_id)
);

-- Add folder_id to studies table
ALTER TABLE public.studies 
ADD COLUMN IF NOT EXISTS folder_id UUID REFERENCES public.folders(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON public.folders(user_id);
CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON public.folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_folders_organization_id ON public.folders(organization_id);
CREATE INDEX IF NOT EXISTS idx_studies_folder_id ON public.studies(folder_id);

-- Enable Row Level Security
ALTER TABLE public.folders ENABLE ROW LEVEL SECURITY;

-- RLS Policies for folders
CREATE POLICY "Users can view their own folders and shared ones"
    ON public.folders
    FOR SELECT
    TO authenticated
    USING (
        user_id = auth.uid() 
        OR (
            is_shared = true 
            AND organization_id IS NOT NULL 
            AND EXISTS (
                SELECT 1 FROM public.organization_members 
                WHERE organization_id = folders.organization_id 
                AND user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create their own folders"
    ON public.folders
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own folders"
    ON public.folders
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own folders"
    ON public.folders
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- Function to get folder path (breadcrumb)
CREATE OR REPLACE FUNCTION get_folder_path(folder_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    path TEXT := '';
    current_folder RECORD;
    parent_id UUID;
BEGIN
    -- Start with the given folder
    parent_id := folder_uuid;
    
    WHILE parent_id IS NOT NULL LOOP
        SELECT id, name, parent_folder_id INTO current_folder
        FROM public.folders
        WHERE id = parent_id;
        
        IF current_folder.id IS NULL THEN
            EXIT;
        END IF;
        
        IF path = '' THEN
            path := current_folder.name;
        ELSE
            path := current_folder.name || ' / ' || path;
        END IF;
        
        parent_id := current_folder.parent_folder_id;
    END LOOP;
    
    RETURN path;
END;
$$ LANGUAGE plpgsql;

-- Function to get all studies in a folder and its subfolders (recursive)
CREATE OR REPLACE FUNCTION get_folder_studies_recursive(folder_uuid UUID)
RETURNS TABLE(study_id UUID) AS $$
WITH RECURSIVE folder_tree AS (
    -- Base case: start with the specified folder
    SELECT id
    FROM public.folders
    WHERE id = folder_uuid
    
    UNION ALL
    
    -- Recursive case: get all child folders
    SELECT f.id
    FROM public.folders f
    INNER JOIN folder_tree ft ON f.parent_folder_id = ft.id
)
SELECT s.id
FROM public.studies s
WHERE s.folder_id IN (SELECT id FROM folder_tree)
   OR (folder_uuid IS NULL AND s.folder_id IS NULL); -- Include unorganized studies when folder_uuid is NULL
$$ LANGUAGE sql;

-- Function to count studies in folder hierarchy
CREATE OR REPLACE FUNCTION count_folder_studies(folder_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM get_folder_studies_recursive(folder_uuid)
    );
END;
$$ LANGUAGE plpgsql;

-- Trigger to update folder updated_at
CREATE TRIGGER update_folders_updated_at
    BEFORE UPDATE ON public.folders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE public.folders IS 'Hierarchical folder structure for organizing studies';
COMMENT ON FUNCTION get_folder_path(UUID) IS 'Returns the full path of a folder for breadcrumb navigation';
COMMENT ON FUNCTION get_folder_studies_recursive(UUID) IS 'Returns all studies in a folder and its subfolders recursively';
COMMENT ON FUNCTION count_folder_studies(UUID) IS 'Counts total studies in a folder hierarchy'; 