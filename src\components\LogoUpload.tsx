import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../lib/supabase';
import { Upload, X } from 'lucide-react';
import imageCompression from 'browser-image-compression';

interface LogoUploadProps {
  onUpload: (url: string) => void;
  url?: string;
}

const STORAGE_BUCKET = 'logos';
const LOGOS_FOLDER = '';

export const LogoUpload: React.FC<LogoUploadProps> = ({ onUpload, url }) => {
  const { t } = useTranslation('profile');
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Verificar estado de autenticación al montar el componente
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) {
        console.error('Authentication error:', error);
        setError(t('logoUpload.notAuthenticated', { ns: 'profile' }));
      }
    };
    checkAuth();
  }, [t]);

  // Función de utilidad para esperar
  const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Función de utilidad para reintentar operaciones
  const retryOperation = async (
    operation: () => Promise<any>,
    maxRetries: number = 3,
    delay: number = 1000
  ) => {
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation();
      } catch (err: any) {
        lastError = err;
        console.log(`Attempt ${i + 1} failed:`, err);
        if (i < maxRetries - 1) {
          console.log(`Waiting ${delay}ms before retry...`);
          await wait(delay);
          delay *= 2; // Exponential backoff
        }
      }
    }
    throw lastError;
  };

  const optimizeImage = async (file: File): Promise<File> => {
    const options = {
      maxSizeMB: 0.5,
      maxWidthOrHeight: 500,
      useWebWorker: true,
      initialQuality: 0.8,
      fileType: file.type, // Mantener el tipo original del archivo
      alwaysKeepResolution: false
    };

    try {
      const compressedFile = await imageCompression(file, options);
      // Mantener la extensión original del archivo
      const fileExt = file.name.split('.').pop()?.toLowerCase() || 'png';
      const newFileName = file.name.replace(/\.[^/.]+$/, "") + '.' + fileExt;
      return new File([compressedFile], newFileName, { type: file.type });
    } catch (error) {
      console.error('Error optimizing image:', error);
      throw new Error(t('logoUpload.optimizationError', { ns: 'profile' }));
    }
  };

  const handleRemoveLogo = async () => {
    if (!url) return;

    try {
      console.log('Starting logo removal process...');
      console.log('Current logo URL:', url);

      // Verificar autenticación primero
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }
      console.log('User authenticated:', user.id);

      // Extraer el nombre del archivo
      console.log('Attempting to extract filename from URL:', url);
      const urlPattern = new RegExp(`/storage/v1/object/public/${STORAGE_BUCKET}/+([^?]+)`);
      const match = url.match(urlPattern);
      const fileName = match ? match[1] : null;

      if (!fileName) {
        console.error('Could not extract filename from URL:', url);
        throw new Error('Invalid file URL');
      }
      console.log('Extracted fileName:', fileName);

      // Verificar que el archivo existe
      const { data: files, error: listError } = await retryOperation(
        () => supabase.storage.from(STORAGE_BUCKET).list()
      );

      if (listError) {
        console.error('Error listing files:', listError);
        throw listError;
      }

      console.log('Current files in folder:', files);
      const fileInfo = files.find(f => f.name === fileName);
      
      if (!fileInfo) {
        console.log('File not found in storage, proceeding with profile update only');
      } else {
        console.log('Found file in storage:', fileInfo);
        
        // Actualizar el perfil primero para evitar referencias rotas
        await retryOperation(async () => {
          const { error: profileError } = await supabase
            .from('profiles')
            .update({ 
              logo_url: '',
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (profileError) throw profileError;
          console.log('Profile updated successfully');
        });

        // Intentar eliminar el archivo con reintentos
        await retryOperation(async () => {
          console.log('Attempting to remove file:', fileName);
          const { error: removeError } = await supabase.storage
            .from(STORAGE_BUCKET)
            .remove([fileName]);

          if (removeError) throw removeError;
          console.log('Remove operation completed successfully');
        });

        // Verificar la eliminación
        const { data: checkFiles } = await retryOperation(
          () => supabase.storage.from(STORAGE_BUCKET).list()
        );

        const stillExists = checkFiles.some(f => f.name === fileName);
        if (stillExists) {
          console.warn('Warning: File still exists in storage after removal');
        } else {
          console.log('File successfully removed from storage');
        }
      }

      onUpload('');
      console.log('Logo removal process completed');
    } catch (err: any) {
      console.error('Error in logo removal process:', err);
      
      if (err.message?.includes('timeout') || err.message?.includes('upstream connect error')) {
        setError(t('logoUpload.connectionError', { ns: 'profile' }));
      } else if (err.message?.includes('Permission denied')) {
        setError(t('logoUpload.permissionDenied', { ns: 'profile' }));
      } else {
        setError(t('logoUpload.removeError', { ns: 'profile' }));
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      console.log('No file selected');
      return;
    }

    // Validar el tipo de archivo
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type:', file.type);
      setError(t('logoUpload.invalidType', { ns: 'profile' }));
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      console.log('Starting file upload process...');
      
      // Verificar autenticación
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.error('Authentication error:', authError);
        throw authError;
      }
      if (!user) {
        console.error('No authenticated user found');
        throw new Error('Not authenticated');
      }
      console.log('User authenticated:', user.id);

      // Si hay un logo actual, eliminarlo primero
      if (url) {
        console.log('Removing current logo before upload');
        await handleRemoveLogo();
      }

      // Optimizar la imagen antes de subirla
      console.log('Optimizing image...');
      const optimizedFile = await optimizeImage(file);
      console.log('Image optimization results:',
        '\nOriginal size:', file.size,
        '\nOptimized size:', optimizedFile.size,
        '\nOriginal type:', file.type,
        '\nOptimized type:', optimizedFile.type
      );

      // Crear nombre de archivo con ID de usuario como prefijo
      const fileExt = optimizedFile.name.split('.').pop()?.toLowerCase() || 'png';
      const fileName = `${user.id}-${Date.now()}.${fileExt}`;
      const filePath = fileName;
      console.log('Generated file path:', filePath);

      // Convertir el archivo a ArrayBuffer
      const arrayBuffer = await optimizedFile.arrayBuffer();

      // Subir archivo a Supabase Storage
      console.log('Uploading file to storage...');
      const { error: uploadError, data: uploadData } = await retryOperation(
        () => supabase.storage.from(STORAGE_BUCKET).upload(filePath, arrayBuffer, {
          upsert: true,
          contentType: optimizedFile.type
        })
      );

      if (uploadError) {
        console.error('Upload error:', uploadError);
        throw uploadError;
      }
      console.log('File uploaded successfully:', uploadData);

      // Obtener URL pública
      console.log('Getting public URL...');
      const { data } = supabase.storage.from(STORAGE_BUCKET).getPublicUrl(filePath);
      const publicUrl = data.publicUrl;
      console.log('Public URL obtained:', publicUrl);

      // Actualizar el perfil con la nueva URL
      console.log('Updating profile with new logo URL...');
      const { error: profileError } = await retryOperation(
        () => supabase
          .from('profiles')
          .update({ 
            logo_url: publicUrl,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)
      );

      if (profileError) {
        console.error('Profile update error:', profileError);
        throw profileError;
      }
      console.log('Profile updated successfully with new logo');

      onUpload(publicUrl);
      console.log('Upload process completed successfully');
    } catch (err: any) {
      console.error('Error in upload process:', err);
      
      if (err.message?.includes('timeout') || err.message?.includes('upstream connect error')) {
        setError(t('logoUpload.connectionError', { ns: 'profile' }));
      } else if (err.message?.includes('not authenticated')) {
        setError(t('logoUpload.notAuthenticated', { ns: 'profile' }));
      } else if (err.message?.includes('Permission denied') || 
                 err.message?.includes('violates row-level security policy')) {
        setError(t('logoUpload.permissionDenied', { ns: 'profile' }));
      } else {
        setError(t('logoUpload.error', { ns: 'profile' }));
      }

      // Si hubo un error, intentar limpiar
      try {
        if (url) {
          onUpload(url); // Restaurar el logo anterior
        }
      } catch (cleanupErr) {
        console.error('Error during cleanup:', cleanupErr);
      }
    } finally {
      setIsUploading(false);
      if (event.target) {
        event.target.value = '';
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        {url && (
          <div className="flex justify-center bg-gray-50 p-4 rounded-lg border border-gray-200">
            <img
              src={url}
              alt="Company Logo"
              className="w-48 h-48 object-contain"
            />
          </div>
        )}
        <div className="flex flex-col sm:flex-row gap-2">
          <label className="relative cursor-pointer">
            <input
              type="file"
              className="hidden"
              accept="image/*"
              onChange={handleFileUpload}
              disabled={isUploading}
            />
            <div className="flex items-center justify-center gap-2 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 w-full sm:w-auto">
              <Upload className="w-4 h-4" />
              <span>{isUploading ? t('logoUpload.uploading', { ns: 'profile' }) : t('logoUpload.upload', { ns: 'profile' })}</span>
            </div>
          </label>
          {url && (
            <button
              onClick={handleRemoveLogo}
              className="flex items-center justify-center gap-2 px-4 py-2 text-red-500 hover:text-red-600 border border-red-500 hover:border-red-600 rounded-lg w-full sm:w-auto"
            >
              <X className="w-4 h-4" />
              <span>{t('logoUpload.remove', { ns: 'profile' })}</span>
            </button>
          )}
        </div>
      </div>
      {error && (
        <p className="text-red-500 text-sm">{error}</p>
      )}
    </div>
  );
};
