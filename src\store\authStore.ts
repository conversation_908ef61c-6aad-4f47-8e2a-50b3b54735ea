import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { User, AuthError } from '@supabase/supabase-js';
import { useOrganizationStore } from './organizationStore';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  initializeSession: () => Promise<void>;
  initializeUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => {
  let unsubscribeAuth: (() => void) | null = null;

  const setupAuthListener = () => {
    if (unsubscribeAuth) {
      unsubscribeAuth();
      unsubscribeAuth = null;
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);
      
      if (event === 'SIGNED_OUT') {
        set({ user: null, isInitialized: true, isLoading: false });
      } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED' || event === 'INITIAL_SESSION') {
        if (session?.user) {
          set({ 
            user: session.user,
            isInitialized: true,
            isLoading: false
          });
        } else {
          console.error('No hay datos de usuario en la sesión');
          set({ user: null, isInitialized: true, isLoading: false });
        }
      }
    });

    unsubscribeAuth = subscription.unsubscribe;
  };

  // Configurar el listener inmediatamente
  setupAuthListener();

  return {
    user: null,
    isLoading: false,
    error: null,
    isInitialized: false,

    signIn: async (email: string, password: string) => {
      // Limpiar el error al iniciar una nueva solicitud
      set({ isLoading: true, error: null });
      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          console.error('Error en signIn (Supabase):', error);
          set({ error: error.message, isLoading: false });
          throw error;
        }
        
        set({ user: data.user, isLoading: false });
      } catch (error) {
        console.error('Error en signIn (catch):', error);
        if (error instanceof Error) {
          set({ error: error.message, isLoading: false });
        } else {
          set({ error: 'Error desconocido durante el inicio de sesión', isLoading: false });
        }
        throw error;
      }
    },

    signUp: async (email: string, password: string) => {
      // Limpiar el error al iniciar una nueva solicitud
      set({ isLoading: true, error: null });
      try {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/v1/callback`,
            data: {
              email_confirmed: false
            }
          },
        });

        if (error) {
          console.error('Error en signUp (Supabase):', error);
          set({ error: error.message, isLoading: false });
          throw error;
        }

        if (!data.user) {
          const errorMsg = 'No se pudo crear el usuario';
          console.error(errorMsg);
          set({ error: errorMsg, isLoading: false });
          throw new Error(errorMsg);
        }

        // Crear perfil de usuario
        const { error: profileError } = await supabase
          .from('profiles')
          .insert([
            {
              id: data.user.id,
              email: data.user.email,
              updated_at: new Date().toISOString(),
            },
          ]);

        if (profileError) {
          console.error('Error al crear perfil:', profileError);
          throw profileError;
        }

        set({ 
          user: data.user,
          isLoading: false 
        });
        return;
      } catch (error) {
        console.error('Error en signUp (catch):', error);
        if (error instanceof Error) {
          set({ error: error.message, isLoading: false });
        } else {
          set({ error: 'Error desconocido durante el registro', isLoading: false });
        }
        throw error;
      }
    },

    signInWithGoogle: async () => {
      // Limpiar el error al iniciar una nueva solicitud
      set({ isLoading: true, error: null });
      try {
        const { error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/auth/v1/callback`,
            queryParams: {
              access_type: 'offline',
              prompt: 'consent',
            }
          }
        });

        if (error) throw error;
      } catch (error) {
        console.error('Error in signInWithGoogle:', error);
        set({ error: (error as AuthError).message, isLoading: false });
        throw error;
      }
    },

    signOut: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Error signing out:', error.message);
        throw error;
      }
    },

    initializeSession: async () => {
      try {
        set({ isLoading: true });
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) throw error;

        if (session?.user) {
          // Verificar si el usuario ya tiene un perfil
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError && profileError.code !== 'PGRST116') {
            throw profileError;
          }

          // Si no existe el perfil, crearlo
          if (!profile) {
            const { error: insertError } = await supabase
              .from('profiles')
              .insert([
                {
                  id: session.user.id,
                  email: session.user.email,
                  updated_at: new Date().toISOString(),
                },
              ]);

            if (insertError) throw insertError;
          }

          set({ user: session.user });
        }

        set({ isInitialized: true, isLoading: false });
      } catch (error) {
        console.error('Error initializing session:', error);
        set({ isInitialized: true, isLoading: false });
      }
    },

    initializeUser: async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          console.log('No hay usuario autenticado');
          set({ user: null, isLoading: false });
          return;
        }
        
        console.log('Usuario autenticado:', user.id);
        set({ user, isLoading: false });
        
        // Verificar si el usuario tiene un perfil
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
        
        if (profileError) {
          console.error('Error al verificar perfil:', profileError);
          return;
        }
        
        // Si no tiene perfil, crearlo
        if (!profile) {
          console.log('Creando perfil para usuario:', user.id);
          const { error: createError } = await supabase
            .from('profiles')
            .insert({
              id: user.id,
              email: user.email,
              updated_at: new Date().toISOString(),
            });
          
          if (createError) {
            console.error('Error al crear perfil:', createError);
          } else {
            console.log('Perfil creado correctamente');
          }
        }
        
        // Verificar acceso a organizaciones
        try {
          const organizationStore = useOrganizationStore.getState();
          await organizationStore.checkOrganizationExists();
        } catch (error) {
          console.error('Error al verificar acceso a organizaciones:', error);
          // No bloqueamos la inicialización si esto falla
        }
        
        // Cargar organizaciones del usuario
        try {
          const organizationStore = useOrganizationStore.getState();
          if (organizationStore && typeof organizationStore.fetchOrganizations === 'function') {
            await organizationStore.fetchOrganizations();
          } else {
            console.warn('fetchOrganizations no está disponible en el store');
          }
        } catch (error) {
          console.error('Error al cargar organizaciones:', error);
        }
        
      } catch (error) {
        console.error('Error al inicializar usuario:', error);
        set({ error: (error as Error).message, isLoading: false });
      }
    },
  };
});