# Supplements Factors Component

## Descripción
Gestión de factores de suplementos.

## Props
```typescript
interface SupplementsFactorsProps {
  element: WorkElement;
  tableType: 'oit' | 'tal';
  onSaved: () => void;
  supplement?: {
    points: Record<string, number>;
    percentage: number;
    isForced: boolean;
    factorSelections: Record<string, Selection>;
  };
}
```

## Características
- Lista de factores disponibles
- Selección de valores por factor
- Cálculo automático de puntos
- Conversión a porcentaje
- Persistencia de selecciones

## Uso
```tsx
<SupplementsFactors
  element={currentElement}
  tableType="tal"
  onSaved={handleSave}
  supplement={currentSupplement}
/>
```