import React, { useState, useCallback } from 'react';
import { cn } from '../../lib/utils';

export type ButtonState = 'idle' | 'loading' | 'success' | 'error';
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
export type ButtonSize = 'sm' | 'md' | 'lg';

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  state?: ButtonState;
  children: React.ReactNode;
  loadingText?: string;
  successText?: string;
  errorText?: string;
  onAsyncClick?: () => Promise<void>;
  showStateIcon?: boolean;
  autoResetDelay?: number;
}

export const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  variant = 'primary',
  size = 'md',
  state: externalState,
  children,
  loadingText,
  successText,
  errorText,
  onAsyncClick,
  showStateIcon = true,
  autoResetDelay = 2000,
  className,
  onClick,
  disabled,
  ...props
}) => {
  const [internalState, setInternalState] = useState<ButtonState>('idle');
  
  // Usar estado externo si se proporciona, sino usar estado interno
  const currentState = externalState ?? internalState;

  const handleAsyncClick = useCallback(async () => {
    if (!onAsyncClick || currentState !== 'idle') return;

    try {
      setInternalState('loading');
      await onAsyncClick();
      setInternalState('success');
      
      // Auto-reset después de mostrar éxito
      setTimeout(() => {
        setInternalState('idle');
      }, autoResetDelay);
    } catch (error) {
      setInternalState('error');
      
      // Auto-reset después de mostrar error
      setTimeout(() => {
        setInternalState('idle');
      }, autoResetDelay);
    }
  }, [onAsyncClick, currentState, autoResetDelay]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onAsyncClick) {
      handleAsyncClick();
    } else if (onClick) {
      onClick(e);
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'btn-primary text-white';
      case 'secondary':
        return 'bg-gray-100 text-gray-900 hover:bg-gray-200';
      case 'outline':
        return 'border-2 border-gray-300 bg-transparent hover:bg-gray-50';
      case 'ghost':
        return 'bg-transparent hover:bg-gray-100';
      case 'destructive':
        return 'bg-red-600 text-white hover:bg-red-700';
      default:
        return '';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-4 py-2 text-base';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return '';
    }
  };

  const getStateClasses = () => {
    switch (currentState) {
      case 'loading':
        return 'state-loading loading-micro';
      case 'success':
        return 'state-success';
      case 'error':
        return 'bg-red-600 text-white';
      default:
        return '';
    }
  };

  const getStateIcon = () => {
    if (!showStateIcon) return null;
    
    switch (currentState) {
      case 'loading':
        return (
          <div className="loading-micro w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
        );
      case 'success':
        return <span className="mr-2 icon-bounce">✅</span>;
      case 'error':
        return <span className="mr-2 icon-bounce">❌</span>;
      default:
        return null;
    }
  };

  const getStateText = () => {
    switch (currentState) {
      case 'loading':
        return loadingText || 'Cargando...';
      case 'success':
        return successText || '¡Éxito!';
      case 'error':
        return errorText || 'Error';
      default:
        return children;
    }
  };

  const isDisabled = disabled || currentState === 'loading';

  return (
    <button
      className={cn(
        // Base classes
        'inline-flex items-center justify-center rounded-lg font-medium',
        'transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
        
        // Micro-interactions
        'btn-micro ripple-enhanced focus-enhanced state-transition',
        
        // Variant, size, and state classes
        getVariantClasses(),
        getSizeClasses(),
        getStateClasses(),
        
        // Disabled state
        isDisabled && 'state-disabled',
        
        // Custom className
        className
      )}
      onClick={handleClick}
      disabled={isDisabled}
      {...props}
    >
      {getStateIcon()}
      <span className={cn(
        'transition-all duration-200',
        currentState !== 'idle' && 'enter-fade-in'
      )}>
        {getStateText()}
      </span>
    </button>
  );
};

// Componente wrapper para casos comunes
export const AsyncButton: React.FC<{
  onSubmit: () => Promise<void>;
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  successMessage?: string;
  errorMessage?: string;
  className?: string;
}> = ({
  onSubmit,
  children,
  variant = 'primary',
  size = 'md',
  successMessage = '¡Guardado!',
  errorMessage = 'Error al guardar',
  className
}) => {
  return (
    <EnhancedButton
      variant={variant}
      size={size}
      onAsyncClick={onSubmit}
      successText={successMessage}
      errorText={errorMessage}
      className={className}
    >
      {children}
    </EnhancedButton>
  );
};

// Componente para acciones de confirmación
export const ConfirmButton: React.FC<{
  onConfirm: () => Promise<void>;
  children: React.ReactNode;
  confirmText?: string;
  className?: string;
}> = ({
  onConfirm,
  children,
  confirmText = 'Confirmar',
  className
}) => {
  const [needsConfirmation, setNeedsConfirmation] = useState(false);

  const handleClick = async () => {
    if (!needsConfirmation) {
      setNeedsConfirmation(true);
      setTimeout(() => setNeedsConfirmation(false), 3000);
      return;
    }

    await onConfirm();
    setNeedsConfirmation(false);
  };

  return (
    <EnhancedButton
      variant={needsConfirmation ? 'destructive' : 'outline'}
      onAsyncClick={handleClick}
      className={cn(
        'transition-all duration-300',
        needsConfirmation && 'glow-effect',
        className
      )}
    >
      {needsConfirmation ? confirmText : children}
    </EnhancedButton>
  );
}; 