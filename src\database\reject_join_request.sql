-- Función para rechazar una solicitud de unión
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.reject_join_request(p_request_id UUID, p_processor_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_count INTEGER;
BEGIN
  -- Actualizar el estado de la solicitud
  UPDATE public.organization_join_requests
  SET 
    status = 'rejected',
    processed_at = NOW(),
    processed_by = p_processor_id
  WHERE id = p_request_id;
  
  -- Verificar si la actualización fue exitosa
  GET DIAGNOSTICS v_count = ROW_COUNT;
  
  -- Devolver true si se actualizó al menos una fila
  RETURN v_count > 0;
END;
$$;
