<VirtualHost *:80>
    ServerName stadler.cronometras.com
    DocumentRoot /var/www/html/cronometras

    <Directory /var/www/html/cronometras>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted

        # Habilitar la reescritura de URLs
        RewriteEngine On
        
        # Si el archivo solicitado no existe, redirigir a index.html
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.html [L]
    </Directory>

    # Configuración de CORS
    Header set Access-Control-Allow-Origin "*"
    
    # Configuración de caché para archivos estáticos
    <FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|svg|woff2?|ttf|eot)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>

    # Evitar caché para HTML y datos
    <FilesMatch "\.(html|json)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>

    # Configuración de logs
    ErrorLog ${APACHE_LOG_DIR}/cronometras_error.log
    CustomLog ${APACHE_LOG_DIR}/cronometras_access.log combined
</VirtualHost>
