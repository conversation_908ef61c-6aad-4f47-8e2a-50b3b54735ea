import { create } from 'zustand';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<{ outcome: 'accepted' | 'dismissed' }>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAStore {
  deferredPrompt: BeforeInstallPromptEvent | null;
  setDeferredPrompt: (prompt: BeforeInstallPromptEvent | null) => void;
  isInstallable: boolean;
  setIsInstallable: (value: boolean) => void;
  handleInstall: () => Promise<void>;
}

export const usePWAStore = create<PWAStore>((set, get) => ({
  deferredPrompt: null,
  isInstallable: false,
  setDeferredPrompt: (prompt) => {
    console.log('Setting deferred prompt:', prompt);
    set({ deferredPrompt: prompt });
  },
  setIsInstallable: (value) => {
    console.log('Setting isInstallable:', value);
    set({ isInstallable: value });
  },
  handleInstall: async () => {
    const { deferredPrompt } = get();
    console.log('Handle install called, deferredPrompt:', deferredPrompt);
    
    if (!deferredPrompt) {
      console.log('No installation prompt available');
      return;
    }

    try {
      console.log('Triggering installation prompt');
      const result = await deferredPrompt.prompt();
      console.log('Installation prompt result:', result.outcome);
      
      if (result.outcome === 'accepted') {
        console.log('User accepted installation');
        localStorage.setItem('pwa-installed', 'true');
      } else {
        console.log('User dismissed installation');
        localStorage.setItem('pwa-dismissed-time', Date.now().toString());
      }
      
      set({ deferredPrompt: null, isInstallable: false });
    } catch (error) {
      console.error('Error during installation:', error);
    }
  }
}));
