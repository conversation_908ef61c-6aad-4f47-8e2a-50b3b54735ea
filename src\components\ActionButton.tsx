import React from 'react';
import { Plus } from 'lucide-react';
import { TouchFeedback } from './TouchFeedback';

interface ActionButtonProps {
  onClick: () => void;
  icon?: React.ReactNode;
  label?: string;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  onClick,
  icon = <Plus />,
  label,
  variant = 'primary',
  disabled = false
}) => {
  const baseClasses = "flex items-center justify-center space-x-2 rounded-lg";
  const variantClasses = variant === 'primary' 
    ? "bg-green-500 text-white active:bg-green-600"
    : "bg-yellow-500 text-white active:bg-yellow-600";
  const sizeClasses = label ? 'w-full p-4' : 'p-3 rounded-full';
  const disabledClasses = disabled ? 'opacity-50' : '';

  return (
    <TouchFeedback
      onClick={disabled ? undefined : onClick}
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${disabledClasses}`}
      activeClassName="transform scale-95 opacity-90"
      disabled={disabled}
    >
      {icon}
      {label && <span className="font-medium select-none">{label}</span>}
    </TouchFeedback>
  );
};