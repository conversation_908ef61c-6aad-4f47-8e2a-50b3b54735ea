import React, { useEffect, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ElementInstance, ElementStats, ElementSupplements } from '../../types/index';
import { useTimeRecords } from '../../hooks/useTimeRecords';
import { useStudyStore } from '../../store/studyStore';
import PrintButton from './PrintButton';
import '../../styles/print.css';
import { FormattedText } from './FormattedText';
import { MachineFatigueCalculation } from './MachineFatigueCalculation';
import { exportToPDF, exportToExcel } from '../../utils/machineFatigueExports';

interface SupplementsMachineProps {
  element: ElementInstance;
  onSaved: (supplements: ElementSupplements) => void;
  supplement?: ElementSupplements;
}

interface CalculationResults {
  totalCycleTime: number;
  baseTime: number;
  personalNeedsSupplement: number;
  fatigueSupplement: number;
  remainingFatigue?: number;
  explanation: {
    supplementsExplanation: string;
    baseTimeCalculation: string;
    personalNeedsCalculation: string;
    fatigueCalculation: string;
    totalTimeCalculation: string;
    remainingFatigueCalculation?: string;
  };
}

export const SupplementsMachine: React.FC<SupplementsMachineProps> = ({
  element,
  onSaved,
  supplement
}) => {
  const { t } = useTranslation('supplements');

  // State for input values
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const { updateStudy } = useStudyStore();
  const [machineRunTime, setMachineRunTime] = useState<number>(0);
  const [machineStopTime, setMachineStopTime] = useState<number>(0);
  const [machineTime, setMachineTime] = useState<number>(0);
  const [inactivityTime, setInactivityTime] = useState<number>(0);
  const [personalNeedsPercentage, setPersonalNeedsPercentage] = useState<number>(5);
  const [fatiguePercentage, setFatiguePercentage] = useState<number>(10.00);
  const [calculationCase, setCalculationCase] = useState<string>("case1");

  // State for results
  const [results, setResults] = useState<CalculationResults | null>(null);

  // State for fatigue explanation details
  const [fatigueExplanationDetails, setFatigueExplanationDetails] = useState<string>('');

  // State for save notifications
  const [saveNotification, setSaveNotification] = useState<{
    type: 'success' | 'error' | 'warning' | null;
    message: string;
  }>({ type: null, message: '' });

  // Get all machine elements
  const machineElements = useMemo(() => {
    if (!selectedStudy?.elements) return { running: [], stopped: [], machine: [] };

    return {
      running: selectedStudy.elements.filter(e => e.type === 'machine-running'),
      stopped: selectedStudy.elements.filter(e => e.type === 'machine-stopped'),
      machine: selectedStudy.elements.filter(e => e.type === 'machine-time' && !e.concurrent_machine_time)
    };
  }, [selectedStudy]);

  // Hooks para los registros de tiempo deben estar en el nivel superior
  const runningRecords = machineElements.running.map(element =>
    useTimeRecords(selectedStudy?.time_records[element.id] || [])
  );

  const stoppedRecords = machineElements.stopped.map(element =>
    useTimeRecords(selectedStudy?.time_records[element.id] || [])
  );

  const machineTimeRecords = machineElements.machine.map(element =>
    useTimeRecords(selectedStudy?.time_records[element.id] || [])
  );

  // Calculate stats for each type of element
  const runningStats = useMemo(() =>
    machineElements.running.map((element, index) => ({
      totalTime: runningRecords[index].totalTime,
      averageTime: runningRecords[index].averageTime,
      recordCount: runningRecords[index].recordCount,
      averageActivity: runningRecords[index].averageActivity,
      elementId: element.id,
      frequency: {
        repetitions: Number(element.frequency_repetitions) || 1,
        cycles: Number(element.frequency_cycles) || 1
      }
    })),
    [machineElements.running, runningRecords]
  );

  const stoppedStats = useMemo(() =>
    machineElements.stopped.map((element, index) => ({
      totalTime: stoppedRecords[index].totalTime,
      averageTime: stoppedRecords[index].averageTime,
      recordCount: stoppedRecords[index].recordCount,
      averageActivity: stoppedRecords[index].averageActivity,
      elementId: element.id,
      frequency: {
        repetitions: Number(element.frequency_repetitions) || 1,
        cycles: Number(element.frequency_cycles) || 1
      }
    })),
    [machineElements.stopped, stoppedRecords]
  );

  const machineTimeStats = useMemo(() =>
    machineElements.machine.map((element, index) => ({
      totalTime: machineTimeRecords[index].totalTime,
      averageTime: machineTimeRecords[index].averageTime,
      recordCount: machineTimeRecords[index].recordCount,
      averageActivity: machineTimeRecords[index].averageActivity,
      elementId: element.id,
      frequency: {
        repetitions: Number(element.frequency_repetitions) || 1,
        cycles: Number(element.frequency_cycles) || 1
      }
    })),
    [machineElements.machine, machineTimeRecords]
  );

  // Calculate weighted average fatigue percentage
  // IMPORTANTE: Solo se incluyen elementos donde el operario trabaja activamente
  // - runningStats: trabajo interior (operario activo mientras máquina funciona)
  // - stoppedStats: trabajo exterior (operario activo mientras máquina parada)
  // NO se incluyen machineTimeStats (tiempos de máquina puros = solo supervisión/espera)
  const calculateWeightedFatiguePercentage = (stats: ElementStats[]) => {
    if (!selectedStudy?.elements) {
      return 10.00;
    }

    // Determinar el caso y su explicación
    let caseExplanation = '';
    const baseTime = machineStopTime + Math.max(machineTime, machineRunTime);

    // Cálculo del suplemento por necesidades personales sobre el tiempo base
    const personalNeedsSupplement = baseTime * (personalNeedsPercentage / 100);

    // Cálculo del suplemento por fatiga sobre el tiempo base (sin incluir NP)
    const fatigueSupplement = baseTime * (fatiguePercentage / 100);
    let totalCycleTime = baseTime;
    let remainingFatigue = 0;

    switch (calculationCase) {
      case "case1":
        caseExplanation = `${t('machineSupplements.caseExplanations.case1')}\n`;
        break;
      case "case2":
        caseExplanation = `${t('machineSupplements.caseExplanations.case2')}\n`;
        break;
      case "case3":
        caseExplanation = `${t('machineSupplements.caseExplanations.case3')}\n`;
        break;
      case "case4":
        caseExplanation = `${t('machineSupplements.caseExplanations.case4')}\n`;
        break;
    }

    if (inactivityTime <= 30) {
      // Caso 1: Ambos suplementos fuera
      totalCycleTime = baseTime + personalNeedsSupplement + fatigueSupplement;
      caseExplanation += `▸ ${t('machineSupplements.baseTime')}: ${machineStopTime} + ${Math.max(machineTime, machineRunTime)} = ${baseTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.npSupplement')}: ${personalNeedsSupplement.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.totalFatigue')}: ${fatigueSupplement.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.totalCycleTime')}: ${totalCycleTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
    } else if (inactivityTime > 30 && inactivityTime < 91) {
      // Caso 2: Fatiga parcialmente dentro
      const inactivityAboveTreshold = inactivityTime - 30;
      const absorbedFatigue = Math.min(fatigueSupplement, inactivityAboveTreshold * 1.5);
      remainingFatigue = Math.max(0, fatigueSupplement - absorbedFatigue);
      totalCycleTime = baseTime + personalNeedsSupplement + remainingFatigue;
      caseExplanation += `▸ ${t('machineSupplements.baseTime')}: ${machineStopTime} + ${Math.max(machineTime, machineRunTime)} = ${baseTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.npSupplement')}: ${personalNeedsSupplement.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.totalFatigue')}: ${fatigueSupplement.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.inactivityTime')}: ${inactivityTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `\nCálculo de fatiga restante:\n`;
      caseExplanation += `1. Inactividad por encima del umbral:\n`;
      caseExplanation += `   ▸ ${inactivityTime.toFixed(2)} - 30 = ${inactivityAboveTreshold.toFixed(2)} seg\n`;
      caseExplanation += `2. Fatiga absorbida:\n`;
      caseExplanation += `   ▸ Fórmula: (Duración Real - 0.5) × 1.5\n`;
      caseExplanation += `   ▸ (${inactivityTime.toFixed(2)} - 30) × 1.5 = ${(inactivityAboveTreshold * 1.5).toFixed(3)} seg\n`;
      caseExplanation += `   ▸ Se toma el mínimo entre la fatiga total y la fatiga absorbida\n`;
      caseExplanation += `   ▸ min(${fatigueSupplement.toFixed(2)}, ${(inactivityAboveTreshold * 1.5).toFixed(3)}) = ${absorbedFatigue.toFixed(3)} seg\n`;
      caseExplanation += `3. Fatiga restante:\n`;
      caseExplanation += `   ▸ ${fatigueSupplement.toFixed(2)} - ${absorbedFatigue.toFixed(3)} = ${(fatigueSupplement - absorbedFatigue).toFixed(3)} seg\n`;
      caseExplanation += `   ▸ Se asegura que no sea negativa: max(0, ${(fatigueSupplement - absorbedFatigue).toFixed(3)}) = ${remainingFatigue.toFixed(3)} seg\n`;
      caseExplanation += `▸ ${t('machineSupplements.remainingFatigue')}: ${remainingFatigue.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.totalCycleTime')}: ${totalCycleTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
    } else if (inactivityTime >= 91 && inactivityTime <= 600) {
      // Caso 3: NP fuera, fatiga dentro
      totalCycleTime = baseTime + personalNeedsSupplement;
      caseExplanation += `▸ ${t('machineSupplements.baseTime')}: ${machineStopTime} + ${Math.max(machineTime, machineRunTime)} = ${baseTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.npSupplement')}: ${personalNeedsSupplement.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.totalCycleTime')}: ${totalCycleTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
    } else {
      // Caso 4: Ambos dentro
      totalCycleTime = baseTime;
      caseExplanation += `▸ ${t('machineSupplements.baseTime')}: ${machineStopTime} + ${Math.max(machineTime, machineRunTime)} = ${baseTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
      caseExplanation += `▸ ${t('machineSupplements.totalCycleTime')}: ${totalCycleTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
    }

    let explanationText = caseExplanation;

    explanationText += `${t('machineSupplements.weightedFatigueCalculation')}\n`;
    explanationText += `${t('machineSupplements.calculationHeaders.onlyWorkElements')}\n`;
    explanationText += `\n${'<hr>'.repeat(2)}\n\n`;
    explanationText += `${t('machineSupplements.calculationHeaders.inputDataByElement')}\n\n`;

    // Paso 1: Mapear y preparar los datos de los elementos
    const elementsData = stats
      .map(stat => {
        const element = selectedStudy.elements.find(e => e.id === stat.elementId);
        // Skip concurrent machine time elements and elements without records
        if (!element || (element.type === 'machine-time' && element.concurrent_machine_time) || !stat.recordCount) {
          return null;
        }

        const frequencyFactor = stat.frequency.repetitions / stat.frequency.cycles;
        const activityScale = selectedStudy?.required_info?.activity_scale?.normal || 100;
        const normalizedTime = stat.averageTime * (stat.averageActivity / activityScale) * frequencyFactor;
        const elementSupplements = selectedStudy.supplements?.[element.id];
        const supplementPercentage = elementSupplements?.percentage || 0;

        // Determine element type using translations
        const elementType = element.type === 'machine-running' ? t('machineSupplements.machineRunning') :
                          element.type === 'machine-stopped' ? t('machineSupplements.machineStopped') :
                          element.type === 'machine-time' ? t('machineSupplements.machineTime') : t('machineSupplements.unknown');

        explanationText += `\n${t('machineSupplements.element')} "${element.description}":\n`;
        explanationText += `▸ ${t('machineSupplements.type')}: ${elementType}\n`;
        explanationText += `▸ ${t('machineSupplements.averageTime')}: ${stat.averageTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
        explanationText += `▸ ${t('machineSupplements.activity')}: ${stat.averageActivity}%\n`;
        explanationText += `▸ ${t('machineSupplements.repetitions')}: ${stat.frequency.repetitions}\n`;
        explanationText += `▸ ${t('machineSupplements.cycles')}: ${stat.frequency.cycles}\n`;
        explanationText += `▸ ${t('machineSupplements.assignedSupplement')}: ${supplementPercentage}%\n`;

        return {
          name: element.description || `Elemento ${element.position + 1}`,
          time: normalizedTime,
          supplements: supplementPercentage,
          originalTime: stat.averageTime,
          activity: stat.averageActivity,
          frequencyFactor,
          type: elementType
        };
      })
      .filter(Boolean);

    if (elementsData.length === 0) {
      return 10.00;
    }
    explanationText += `\n${'<hr>'.repeat(2)}\n`;

    // Paso 2: Normalización de tiempos
    explanationText += `\n${t('machineSupplements.calculationHeaders.timeNormalization')}\n`;
    elementsData.forEach(data => {
      explanationText += `\n${t('machineSupplements.element')} "${data.name}":\n`;
      explanationText += `▸ ${t('machineSupplements.frequencyFactor')}: ${data.frequencyFactor}\n`;
      explanationText += `▸ ${t('machineSupplements.normalizedTime')}: ${data.originalTime} × (${data.activity}/${selectedStudy?.required_info?.activity_scale?.normal || 100}) × ${data.frequencyFactor} = ${data.time.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
    });
    explanationText += `\n${'<hr>'.repeat(2)}\n`;

    // Paso 3: Cálculo del tiempo total
    const totalTime = elementsData.reduce((sum, data) => sum + data.time, 0);
    explanationText += `\n${t('machineSupplements.calculationHeaders.totalTimeCalculation')}\n`;
    explanationText += `▸ ${t('machineSupplements.totalTime')}: ${totalTime.toFixed(2)} ${t('machineSupplements.seconds')}\n`;
    explanationText += `\n${'<hr>'.repeat(2)}\n`;

    // Paso 4: Cálculo de pesos por tiempo
    explanationText += `\n${t('machineSupplements.calculationHeaders.weightCalculation')}\n`;
    const weightsData = elementsData.map(data => {
      const weight = data.time / totalTime;
      explanationText += `▸ ${t('machineSupplements.weight')} "${data.name}" = ${data.time.toFixed(2)}/${totalTime.toFixed(2)} = ${(weight * 100).toFixed(1)}%\n`;
      return { ...data, weight };
    });
    explanationText += `\n${'<hr>'.repeat(2)}\n`;

    // Paso 5: Cálculo del suplemento ponderado
    explanationText += `\n${t('machineSupplements.calculationHeaders.weightedSupplementCalculation')}\n`;
    let weightedSupplements = 0;
    weightsData.forEach(data => {
      const contribution = data.weight * data.supplements;
      weightedSupplements += contribution;
      explanationText += `▸ ${t('machineSupplements.contribution')} "${data.name}" = ${(data.weight * 100).toFixed(1)}% × ${data.supplements}% = ${contribution.toFixed(3)}%\n`;
    });

    explanationText += `\n${t('machineSupplements.totalSupplement')}: ${weightedSupplements.toFixed(3)}%\n`;
    explanationText += `\n${'<hr>'.repeat(2)}\n`;

    // Paso 6: Ajuste final
    // Restamos el 5% de necesidades personales del suplemento total
    const finalSupplement = weightedSupplements - 5;
    
    // IMPORTANTE: El suplemento se calcula sobre el TIEMPO TOTAL DE ELEMENTOS DE TRABAJO
    // Solo sobre los elementos donde el operario trabaja activamente
    const finalSupplementInSeconds = totalTime * (finalSupplement / 100);

    explanationText += `\n${t('machineSupplements.calculationHeaders.finalAdjustment')}\n`;
    explanationText += `▸ ${t('machineSupplements.fatigueSupplement')}: ${finalSupplement.toFixed(3)}%\n\n`;
    explanationText += `APLICACIÓN SOBRE EL TIEMPO TOTAL DE ELEMENTOS DE TRABAJO:\n`;
    explanationText += `▸ Tiempo total elementos activos = ${totalTime.toFixed(2)} seg\n`;
    explanationText += `▸ Suplemento en segundos = ${totalTime.toFixed(2)} × ${finalSupplement.toFixed(3)}% = ${finalSupplementInSeconds.toFixed(2)} seg\n`;
    explanationText += `▸ ${t('machineSupplements.result')}: ${finalSupplementInSeconds.toFixed(2)} ${t('machineSupplements.seconds')}`;
    explanationText += `\n${'<hr>'.repeat(7)}\n`;

    explanationText += `${t('machineSupplements.theoreticalInformation')}\n`;
    explanationText += `\n${'<hr>'.repeat(2)}\n\n`;
    explanationText += `${t('machineSupplements.explanationText.introduction')}\n\n`;

    explanationText += `${t('machineSupplements.timeScales')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.timeScales.cycleTime')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.timeScales.machineTime')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.timeScales.manualWork')}\n`;
    explanationText += `      ${t('machineSupplements.explanationText.timeScales.externalWork')}\n`;
    explanationText += `      ${t('machineSupplements.explanationText.timeScales.internalWork')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.timeScales.unusedTime')}\n\n`;

    explanationText += `${t('machineSupplements.explanationText.supplements.title')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.supplements.personalNeeds')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.supplements.fatigue')}\n\n`;

    explanationText += `${t('machineSupplements.explanationText.supplementsApplication.title')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.supplementsApplication.case1')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.supplementsApplication.case2')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.supplementsApplication.case3')}\n\n`;
    explanationText += `   ${t('machineSupplements.explanationText.supplementsApplication.case4')}\n`;

    setFatigueExplanationDetails(explanationText);
    return Number(finalSupplement.toFixed(2));
  };

  // Effect to update fatigue percentage when stats change
  // IMPORTANTE: Solo se incluyen elementos donde el operario trabaja activamente
  // - runningStats: trabajo interior (operario activo mientras máquina funciona)
  // - stoppedStats: trabajo exterior (operario activo mientras máquina parada)
  // NO se incluyen machineTimeStats (tiempos de máquina puros = solo supervisión/espera)
  useEffect(() => {
    const combinedStats = [...runningStats, ...stoppedStats]; // Excluimos machineTimeStats
    const calculatedFatigue = calculateWeightedFatiguePercentage(combinedStats);
    setFatiguePercentage(Number(calculatedFatigue.toFixed(2)));
  }, [runningStats, stoppedStats]); // Removemos machineTimeStats de las dependencias

  // Auto-fill machine times based on records
  useEffect(() => {
    // Obtener la escala de actividad del estudio
    const activityScale = selectedStudy?.required_info?.activity_scale?.normal || 100;

    // Calculate machine run time from machine-running elements
    const totalRunTime = runningStats.reduce((total, stats) => {
      if (stats.recordCount > 0) {
        const frequencyFactor = stats.frequency.repetitions / stats.frequency.cycles;
        const normalizedTime = stats.averageTime * (stats.averageActivity / activityScale) * frequencyFactor;
        return total + normalizedTime;
      }
      return total;
    }, 0);

    // Calculate machine stop time as sum of normalized times from machine-stopped elements
    // Formula: averageTime × (averageActivity / activityScale) × (repetitions / cycles)
    // This calculation maintains high precision internally and only rounds for display
    const totalStopTime = stoppedStats.reduce((total, stats) => {
      if (stats.recordCount > 0) {
        const frequencyFactor = stats.frequency.repetitions / stats.frequency.cycles;
        const normalizedTime = stats.averageTime * (stats.averageActivity / activityScale) * frequencyFactor;
        return total + normalizedTime;
      }
      return total;
    }, 0);

    // Calculate machine time from machine-time elements
    const totalMachineTime = machineTimeStats.reduce((total, stats) => {
      if (stats.recordCount > 0) {
        const frequencyFactor = stats.frequency.repetitions / stats.frequency.cycles;
        const normalizedTime = stats.averageTime * (stats.averageActivity / activityScale) * frequencyFactor;
        return total + normalizedTime;
      }
      return total;
    }, 0);

    const roundedRunTime = totalRunTime;
    const roundedMachineTime = totalMachineTime;

    setMachineRunTime(Number(totalRunTime.toFixed(3)));
    setMachineStopTime(Number(totalStopTime.toFixed(3)));
    setMachineTime(Number(totalMachineTime.toFixed(3)));

    // Calculate inactivity time as machine time minus machine run time
    // Ensure we're only considering non-concurrent machine time
    setInactivityTime(Number((totalMachineTime - totalRunTime).toFixed(3)));
  }, [runningStats, stoppedStats, machineTimeStats, selectedStudy]);

  useEffect(() => {
    if (inactivityTime <= 30) {
      setCalculationCase('case1');
    } else if (inactivityTime > 30 && inactivityTime < 91) {
      setCalculationCase('case2');
    } else if (inactivityTime >= 91 && inactivityTime <= 600) {
      setCalculationCase('case3');
    } else if (inactivityTime > 600) {
      setCalculationCase('case4');
    }
  }, [inactivityTime]);

  // Debounced calculation to prevent multiple saves
  useEffect(() => {
    const timeoutId = setTimeout(() => {
    // Solo calcular cuando todos los valores necesarios estén disponibles
    if (machineStopTime !== undefined && machineTime !== undefined && machineRunTime !== undefined) {
      calculateSupplements();
    }
    }, 500); // Esperar 500ms antes de calcular

    return () => clearTimeout(timeoutId);
  }, [machineStopTime, machineTime, machineRunTime, personalNeedsPercentage, fatiguePercentage, calculationCase]);

  const calculateSupplements = () => {
    // NUEVO MÉTODO: CÁLCULO CORRECTO SEGÚN LA IMAGEN
    
    // 1. CALCULAR TIEMPOS FINALES POR ELEMENTO (CON SUPLEMENTOS INCLUIDOS)
    let totalSupplementTime = 0;
    let totalPersonalNeedsTime = 0;
    let totalFatigueTime = 0;
    
    const elementCalculations: Array<{
      elementName: string;
      type: string;
      observedTime: number;
      activity: number;
      normalizedTime: number;
      supplementPercentage: number;
      supplementFactor: number;
      supplementTime: number;
      personalNeedsTime: number;
      fatigueTime: number;
      finalTime: number;
    }> = [];

    // Obtener todos los elementos y calcular sus tiempos finales
    const allStats = [...runningStats, ...stoppedStats, ...machineTimeStats];
    
    allStats.forEach(stat => {
      const element = selectedStudy?.elements?.find(e => e.id === stat.elementId);
      if (!element || !stat.recordCount) return;

      const elementSupplements = selectedStudy?.supplements?.[element.id];
      const supplementPercentage = elementSupplements?.percentage || 0;
      
      // Calcular tiempo normalizado (tiempo observado × actividad ÷ escala normal × factor frecuencia)
      const frequencyFactor = stat.frequency.repetitions / stat.frequency.cycles;
      const activityScale = selectedStudy?.required_info?.activity_scale?.normal || 100;
      const normalizedTime = stat.averageTime * (stat.averageActivity / activityScale) * frequencyFactor;
      
      // SEPARAR SUPLEMENTOS: 5% necesidades personales + resto fatiga
      const personalNeedsPercentage = 5; // Siempre 5%
      const fatiguePercentage = Math.max(0, supplementPercentage - personalNeedsPercentage); // Resto para fatiga
      
      // Calcular tiempos de suplementos por separado
      const personalNeedsTime = normalizedTime * (personalNeedsPercentage / 100);
      const fatigueTime = normalizedTime * (fatiguePercentage / 100);
      const supplementTime = personalNeedsTime + fatigueTime;
      
      // Calcular factor de suplementos (1 + porcentaje/100)
      const supplementFactor = 1 + (supplementPercentage / 100);
      
      // Calcular tiempo final (normalizado + suplementos)
      const finalTime = normalizedTime + supplementTime;
      
      totalSupplementTime += supplementTime;
      totalPersonalNeedsTime += personalNeedsTime;
      totalFatigueTime += fatigueTime;
      
      elementCalculations.push({
        elementName: element.description || `Elemento ${element.position + 1}`,
        type: element.type,
        observedTime: stat.averageTime,
        activity: stat.averageActivity,
        normalizedTime,
        supplementPercentage,
        supplementFactor,
        supplementTime,
        personalNeedsTime,
        fatigueTime,
        finalTime
      });
    });

    // 2. SEPARAR POR TIPOS Y CALCULAR SUMAS
    const machineStoppedElements = elementCalculations.filter(elem => elem.type === 'machine-stopped');
    const machineRunningElements = elementCalculations.filter(elem => elem.type === 'machine-running');
    const machineTimeElements = elementCalculations.filter(elem => elem.type === 'machine-time');

    const totalMachineStoppedTime = machineStoppedElements.reduce((sum, elem) => sum + elem.finalTime, 0);
    const totalMachineRunningTime = machineRunningElements.reduce((sum, elem) => sum + elem.finalTime, 0);
    const totalMachineTimeTime = machineTimeElements.reduce((sum, elem) => sum + elem.finalTime, 0);

    // 3. CALCULAR CICLO NORMAL = MP + MAX(MM, TM)
    const normalCycle = totalMachineStoppedTime + Math.max(totalMachineRunningTime, totalMachineTimeTime);

    // 4. CALCULAR TIEMPO DE ESPERA (basado en tiempos finales)
    const waitTime = Math.max(0, totalMachineTimeTime - totalMachineRunningTime);

    // 5. APLICAR CASOS PARA AJUSTAR SUPLEMENTOS (solo si hay tiempo de espera significativo)
    let finalCycle = normalCycle;
    let adjustmentExplanation = '';
    let adjustedPersonalNeeds = totalPersonalNeedsTime;
    let adjustedFatigue = totalFatigueTime;
    
    if (waitTime >= 30 && waitTime < 91) {
      // CASO 2: Ajuste parcial
      const adjustedWaitTime = (waitTime - 30) * 1.5;
      const absorbedSupplements = Math.min(totalSupplementTime, adjustedWaitTime);
      
      // Ajustar el ciclo normal reduciendo los suplementos absorbidos
      finalCycle = normalCycle - absorbedSupplements;
      
      adjustmentExplanation = `
AJUSTE POR CASO 2 (Tiempo de espera: ${waitTime.toFixed(2)} seg):
▸ Capacidad de absorción: (${waitTime.toFixed(2)} - 30) × 1.5 = ${adjustedWaitTime.toFixed(3)} seg
▸ Suplementos absorbidos: min(${totalSupplementTime.toFixed(3)}, ${adjustedWaitTime.toFixed(3)}) = ${absorbedSupplements.toFixed(3)} seg
▸ Ciclo ajustado: ${normalCycle.toFixed(3)} - ${absorbedSupplements.toFixed(3)} = ${finalCycle.toFixed(3)} seg`;
      
    } else if (waitTime >= 91 && waitTime <= 600) {
      // CASO 3: Solo fatiga absorbida, necesidades personales se mantienen
      adjustedFatigue = 0; // Fatiga completamente absorbida
      finalCycle = normalCycle - totalFatigueTime; // Solo restamos la fatiga
      
      adjustmentExplanation = `
AJUSTE POR CASO 3 (Tiempo de espera: ${waitTime.toFixed(2)} seg):
▸ Fatiga completamente absorbida: ${totalFatigueTime.toFixed(3)} seg
▸ Necesidades personales se mantienen: ${totalPersonalNeedsTime.toFixed(3)} seg
▸ Ciclo ajustado: ${normalCycle.toFixed(3)} - ${totalFatigueTime.toFixed(3)} = ${finalCycle.toFixed(3)} seg`;
      
    } else if (waitTime > 600) {
      // CASO 4: Todos los suplementos absorbidos
      adjustedPersonalNeeds = 0;
      adjustedFatigue = 0;
      finalCycle = normalCycle - totalSupplementTime;
      
      adjustmentExplanation = `
AJUSTE POR CASO 4 (Tiempo de espera: ${waitTime.toFixed(2)} seg):
▸ Todos los suplementos absorbidos: ${totalSupplementTime.toFixed(3)} seg
▸ Ciclo ajustado: ${normalCycle.toFixed(3)} - ${totalSupplementTime.toFixed(3)} = ${finalCycle.toFixed(3)} seg`;
    }

    // Crear explicación detallada con separación de suplementos
    const supplementTimeExplanation = `
CÁLCULO DETALLADO POR ELEMENTO (SUPLEMENTOS SEPARADOS):

${elementCalculations.map((elem, index) => `
${index + 1}. ${elem.elementName} (${elem.type}):
   ▸ Tiempo observado: ${elem.observedTime.toFixed(3)} seg
   ▸ Actividad: ${elem.activity}%
   ▸ Tiempo normalizado: ${elem.normalizedTime.toFixed(3)} seg
   ▸ Suplementos totales: ${elem.supplementPercentage}%
   ▸ Necesidades personales (5%): ${elem.personalNeedsTime.toFixed(3)} seg
   ▸ Fatiga (${Math.max(0, elem.supplementPercentage - 5)}%): ${elem.fatigueTime.toFixed(3)} seg
   ▸ Total suplementos: ${elem.supplementTime.toFixed(3)} seg
   ▸ Tiempo final: ${elem.finalTime.toFixed(3)} seg`).join('')}

RESUMEN POR TIPOS:
▸ MP (máquina parada): ${totalMachineStoppedTime.toFixed(3)} seg
▸ MM (máquina en marcha): ${totalMachineRunningTime.toFixed(3)} seg  
▸ TM (tiempo de máquina): ${totalMachineTimeTime.toFixed(3)} seg

RESUMEN DE SUPLEMENTOS:
▸ Total necesidades personales: ${totalPersonalNeedsTime.toFixed(3)} seg
▸ Total fatiga: ${totalFatigueTime.toFixed(3)} seg
▸ Total suplementos: ${totalSupplementTime.toFixed(3)} seg
▸ Tiempo de espera: ${waitTime.toFixed(3)} seg`;

    const baseTimeExplanation = `
CÁLCULO DEL CICLO NORMAL:

1. Fórmula: Ciclo Normal = MP + MAX(MM, TM)
   ▸ MP (suma tiempos finales máquina parada): ${totalMachineStoppedTime.toFixed(3)} seg
   ▸ MM (suma tiempos finales máquina en marcha): ${totalMachineRunningTime.toFixed(3)} seg
   ▸ TM (suma tiempos finales tiempo máquina): ${totalMachineTimeTime.toFixed(3)} seg
   ▸ MAX(MM, TM) = max(${totalMachineRunningTime.toFixed(3)}, ${totalMachineTimeTime.toFixed(3)}) = ${Math.max(totalMachineRunningTime, totalMachineTimeTime).toFixed(3)} seg

2. Resultado:
   ▸ Ciclo Normal = ${totalMachineStoppedTime.toFixed(3)} + ${Math.max(totalMachineRunningTime, totalMachineTimeTime).toFixed(3)} = ${normalCycle.toFixed(3)} seg${adjustmentExplanation}`;

    const personalNeedsExplanation = `
SUPLEMENTO POR NECESIDADES PERSONALES:
▸ Se aplica un 5% fijo sobre el tiempo normalizado de cada elemento
▸ Total necesidades personales calculadas: ${totalPersonalNeedsTime.toFixed(3)} seg
▸ Necesidades personales aplicadas: ${adjustedPersonalNeeds.toFixed(3)} seg
${adjustedPersonalNeeds !== totalPersonalNeedsTime ? '▸ (Parte absorbida por tiempo de inactividad)' : '▸ (Aplicadas completamente)'}`;

    const fatigueExplanation = `
SUPLEMENTO POR FATIGA:
▸ Se calcula como el porcentaje restante después del 5% de necesidades personales
▸ Total fatiga calculada: ${totalFatigueTime.toFixed(3)} seg
▸ Fatiga aplicada: ${adjustedFatigue.toFixed(3)} seg
${adjustedFatigue !== totalFatigueTime ? '▸ (Completamente absorbida por tiempo de inactividad)' : '▸ (Aplicada completamente)'}`;

    const totalTimeExplanation = `
TIEMPO TOTAL DEL CICLO:
▸ El ciclo ${finalCycle !== normalCycle ? 'ajustado' : 'normal'} incluye todos los suplementos calculados por elemento
▸ Total: ${finalCycle.toFixed(3)} seg`;

    // Para compatibilidad con la interfaz existente
    const calculationResults = {
      totalCycleTime: Number(finalCycle.toFixed(3)),
      baseTime: Number((finalCycle - adjustedPersonalNeeds - adjustedFatigue).toFixed(3)), // Tiempo sin suplementos aplicados
      personalNeedsSupplement: Number(adjustedPersonalNeeds.toFixed(3)),
      fatigueSupplement: Number(adjustedFatigue.toFixed(3)),
      remainingFatigue: undefined,
      explanation: {
        supplementsExplanation: supplementTimeExplanation,
        baseTimeCalculation: baseTimeExplanation,
        personalNeedsCalculation: personalNeedsExplanation,
        fatigueCalculation: fatigueExplanation,
        totalTimeCalculation: totalTimeExplanation,
        remainingFatigueCalculation: undefined
      }
    };

    setResults(calculationResults);

    // Save machine cycle data to study
    saveMachineCycleData(calculationResults);
  };

  // Function to save machine cycle data to study
  const saveMachineCycleData = async (results: CalculationResults) => {
    if (!selectedStudy) {
      console.error('❌ Cannot save machine cycle data: No selected study');
      setSaveNotification({
        type: 'error',
        message: t('machineSupplements.dataSync.saveError')
      });
      return;
    }

    // Validation: Don't save if totalCycleTime is 0 or invalid
    if (!results.totalCycleTime || results.totalCycleTime <= 0) {
      console.warn('⚠️ Skipping save - Invalid totalCycleTime:', results.totalCycleTime);
      setSaveNotification({
        type: 'warning',
        message: 'Cálculo inválido - no se guardará'
      });
      return;
    }

    // Show saving notification
    setSaveNotification({
      type: 'success',
      message: t('machineSupplements.dataSync.savingData')
    });

    const machineCycleData = {
      totalCycleTime: results.totalCycleTime,
      baseTime: results.baseTime,
      personalNeedsSupplement: results.personalNeedsSupplement,
      fatigueSupplement: results.fatigueSupplement,
      remainingFatigue: results.remainingFatigue,
      inactivityTime,
      calculationCase,
      timestamp: new Date().toISOString()
    };

    // Debug logging para verificar qué se está guardando
    console.log('💾 Guardando datos de ciclo de máquina:', {
      studyId: selectedStudy.id,
      totalCycleTime: results.totalCycleTime,
      calculationCase,
      timestamp: machineCycleData.timestamp
    });

    try {
      // Temporarily save in supplements field with a special key
      const updatedSupplements = {
        ...(selectedStudy.supplements || {}),
        '__machine_cycle_data__': machineCycleData
      };

      const updateResult = await updateStudy(selectedStudy.id, {
        supplements: updatedSupplements,
        updated_at: new Date().toISOString()
      });

      console.log('✅ Datos de ciclo guardados exitosamente:', {
        studyId: selectedStudy.id,
        totalCycleTime: machineCycleData.totalCycleTime,
        savedAt: machineCycleData.timestamp
      });

      // Show success notification
      setSaveNotification({
        type: 'success',
        message: t('machineSupplements.dataSync.savedSuccessfully')
      });

      // Hide notification after 3 seconds
      setTimeout(() => {
        setSaveNotification({ type: null, message: '' });
      }, 3000);

      // Verificar que se guardó correctamente
      if (updateResult?.supplements?.['__machine_cycle_data__']?.totalCycleTime !== results.totalCycleTime) {
        console.warn('⚠️ Warning: El valor guardado no coincide con el calculado', {
          calculated: results.totalCycleTime,
          saved: updateResult?.supplements?.['__machine_cycle_data__']?.totalCycleTime
        });
        
        setSaveNotification({
          type: 'warning',
          message: t('machineSupplements.dataSync.dataInconsistency') + '. ' + t('machineSupplements.dataSync.recheckCalculation')
        });
      }

    } catch (error) {
      console.error('❌ Error saving machine cycle data:', error);
      console.error('Datos que se intentaron guardar:', machineCycleData);
      
      setSaveNotification({
        type: 'error',
        message: t('machineSupplements.dataSync.saveError')
      });
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    if (!results) return;

    const exportData = {
      results,
      machineStopTime,
      machineRunTime,
      machineTime,
      inactivityTime,
      personalNeedsPercentage,
      fatiguePercentage,
      calculationCase,
      fatigueExplanationDetails,
      studyName: selectedStudy?.name,
      timestamp: new Date().toLocaleString()
    };

    exportToPDF(exportData);
  };

  const handleExportExcel = () => {
    if (!results) return;

    const exportData = {
      results,
      machineStopTime,
      machineRunTime,
      machineTime,
      inactivityTime,
      personalNeedsPercentage,
      fatiguePercentage,
      calculationCase,
      fatigueExplanationDetails,
      studyName: selectedStudy?.name,
      timestamp: new Date().toLocaleString()
    };

    exportToExcel(exportData);
  };

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900">{t('machineSupplements.title')}</h1>
      
      {/* Save notification */}
      {saveNotification.type && (
        <div className={`mb-4 p-4 rounded-md ${
          saveNotification.type === 'success' ? 'bg-green-50 border border-green-200 text-green-700' :
          saveNotification.type === 'error' ? 'bg-red-50 border border-red-200 text-red-700' :
          'bg-yellow-50 border border-yellow-200 text-yellow-700'
        }`}>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {saveNotification.type === 'success' && (
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              {saveNotification.type === 'error' && (
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
              {saveNotification.type === 'warning' && (
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium">{saveNotification.message}</p>
            </div>
          </div>
        </div>
      )}

    <MachineFatigueCalculation
      results={results}
      machineStopTime={machineStopTime}
      machineRunTime={machineRunTime}
      machineTime={machineTime}
      inactivityTime={inactivityTime}
      personalNeedsPercentage={personalNeedsPercentage}
      fatiguePercentage={fatiguePercentage}
      calculationCase={calculationCase}
      fatigueExplanationDetails={fatigueExplanationDetails}
      onPrint={handlePrint}
      onExportPDF={handleExportPDF}
      onExportExcel={handleExportExcel}
    />
    </div>
  );
};