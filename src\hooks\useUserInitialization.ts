import { useEffect, useRef } from 'react';
import { useAuthStore } from '../store/authStore';
import { supabase } from '../lib/supabaseClient';
import { useCreditStore } from '../store/creditStore';

export const useUserInitialization = () => {
  const authStore = useAuthStore();
  const user = authStore?.user;
  const isInitialized = authStore?.isInitialized ?? false;
  const fetchUserLimits = useCreditStore(state => state.fetchUserLimits);
  const initializationAttempted = useRef(false);

  const initializeUser = async () => {
    if (initializationAttempted.current) return;
    
    try {
      // Verificar la sesión actual
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session?.user) {
        console.log('No hay sesión activa:', sessionError || 'No session user');
        return;
      }

      const userId = session.user.id;
      const userEmail = session.user.email;

      if (!userId || !userEmail) {
        console.error('Session user missing required fields:', session.user);
        return;
      }

      // Verificar si el usuario ya tiene un registro de límites
      const { data: existingLimits, error: fetchError } = await supabase
        .from('user_study_limits')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error checking user limits:', fetchError);
        return;
      }

      // Verificar si el usuario ya tiene un perfil
      const { data: existingProfile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      // Inicializar perfil si no existe
      if (!existingProfile) {
        const { error: profileInsertError } = await supabase
          .from('profiles')
          .insert([
            {
              id: userId,
              email: userEmail,
              company_name: '',
              default_time_unit: 'dmh',
              default_language: 'es',
              default_contingency: 5,
              minutes_per_shift: 480,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ]);

        if (profileInsertError) {
          console.error('Error creating profile:', profileInsertError);
        }
      }

      // Si no existen límites, crearlos con suscripción gratuita por defecto
      if (!existingLimits) {
        const { error: insertError } = await supabase
          .from('user_study_limits')
          .insert([
            {
              user_id: userId,
              user_email: userEmail,
              monthly_credits: 1,
              extra_credits: 0,
              used_monthly_credits: 0,
              used_extra_credits: 0,
              subscription_plan: 'free',
              subscription_end_date: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ]);

        if (insertError) {
          console.error('Error creating user limits:', insertError);
          return;
        }
      }

      // Actualizar los límites en el store
      await fetchUserLimits();

      console.log('User initialization completed successfully');
    } catch (error) {
      console.error('Error in initializeUser:', error);
    } finally {
      initializationAttempted.current = true;
    }
  };

  useEffect(() => {
    if (!isInitialized || initializationAttempted.current) {
      return;
    }

    initializeUser();
  }, [isInitialized]);

  return null;
};
