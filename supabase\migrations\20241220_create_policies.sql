-- Habilitar RLS
ALTER TABLE "public"."user_study_limits" ENABLE ROW LEVEL SECURITY;

-- Eliminar políticas existentes si existen
DROP POLICY IF EXISTS "Users can view their own study limits" ON "public"."user_study_limits";
DROP POLICY IF EXISTS "Users can update their own study limits" ON "public"."user_study_limits";
DROP POLICY IF EXISTS "Users can insert their own study limits" ON "public"."user_study_limits";

-- <PERSON><PERSON><PERSON> política para SELECT
CREATE POLICY "Users can view their own study limits"
ON "public"."user_study_limits"
AS PERMISSIVE
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- <PERSON><PERSON><PERSON> pol<PERSON><PERSON> para UPDATE
CREATE POLICY "Users can update their own study limits"
ON "public"."user_study_limits"
AS PERMISSIVE
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- <PERSON><PERSON><PERSON> polí<PERSON> para INSERT
CREATE POLICY "Users can insert their own study limits"
ON "public"."user_study_limits"
AS PERMISSIVE
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);
