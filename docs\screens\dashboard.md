# Dashboard Principal - Cronometras

## Descripción General
El Dashboard Principal de Cronometras es la pantalla central de la aplicación, diseñada como una interfaz intuitiva que permite gestionar estudios de tiempos industriales y acceder a todas las funcionalidades principales del sistema.

## Componentes del Dashboard

### 1. <PERSON>er (Barra Superior)
**Archivo**: `src/components/Header.tsx`

#### Funcionalidades:
- **Título y Subtítulo**: Muestra el nombre de la aplicación y el estudio seleccionado
- **Búsqueda**: Campo de búsqueda con filtrado en tiempo real
- **Menú de I<PERSON>mas**: Selector entre Español e Inglés
- **Menú de Usuario**: Acceso a perfil, exportación/importación y cierre de sesión

#### Opciones del Menú:
- **Perfil**: Configuración de preferencias del usuario
- **Exportar Estudio**: Exporta el estudio seleccionado a JSON
- **Exportar Todos**: Exporta todos los estudios del usuario
- **Importar Estudios**: Importa estudios desde archivo JSON
- **Cerrar Sesión**: Salida segura de la aplicación

### 2. Panel de Control Principal
**Archivo**: `src/pages/StudyListPage.tsx`

#### Controles Unificados:
- **Filtros**:
  - Por Empresa
  - Por Fecha
  - Estudios Compartidos (configurable en perfil)

- **Visualización**:
  - Vista de Rejilla (Grid)
  - Vista de Lista

- **Ordenación**:
  - Por Fecha
  - Por Empresa
  - Orden Ascendente/Descendente

#### Acciones Principales:
- **Nuevo Estudio**: Crear un nuevo estudio de tiempos
- **Biblioteca**: Acceder a la biblioteca de elementos

### 3. Navegación Contextual (cuando hay estudio seleccionado)
Barra de navegación horizontal con acceso directo a los módulos del estudio:

#### Módulos Básicos (siempre disponibles):
1. **Información** (`/study/{id}/info`)
   - Datos generales del estudio
   - Información de la empresa
   - Configuración de escalas de actividad

2. **Método** (`/study/{id}/method`)
   - Definición de elementos del método
   - Gestión de elementos de trabajo
   - Configuración de tipos de repetición

3. **Crono Seguido** (`/study/{id}/cronoseguido`)
   - Cronometraje continuo de múltiples elementos
   - Análisis de secuencias de trabajo

#### Módulos Dinámicos (según contenido del estudio):

3. **Cronómetro Repetitivo** (`/study/{id}/repetitive`)
   - **Condición**: Elementos con tipo `repetitive-type` o `repetitive`
   - Cronometraje de elementos repetitivos
   - Registro de tiempos y actividades

4.

5. **Tiempos de Máquina** (`/study/{id}/machine`)
   - **Condición**: Elementos tipo `machine-time` o `machine-type`
   - Análisis de tiempos de máquina
   - Cálculo de inactividad

6. **Tiempos Frecuenciales** (`/study/{id}/frequency`)
   - **Condición**: Elementos con tipo `frequency-type` o `frequency`
   - Análisis estadístico de frecuencias
   - Cálculo de tiempos estándar

7. **Suplementos** (`/study/{id}/supplements`)
   - **Condición**: Al menos un elemento en el estudio
   - Cálculo de suplementos por fatiga y necesidades personales
   - Aplicación de casos según tiempo de inactividad

8. **Informe** (`/study/{id}/report`)
   - **Condiciones**:
     - Todos los elementos deben tener tiempos registrados
     - Debe haber suplementos configurados (porcentaje > 0)
   - Generación de informes completos
   - Exportación a PDF y Excel
   - Análisis de productividad

### 4. Gestión de Estudios

#### Tarjetas de Estudio (StudyCard):
- **Vista Grid**: Tarjetas visuales con información resumida
- **Vista Lista**: Formato compacto con datos tabulares

#### Información Mostrada:
- Nombre del estudio
- Empresa
- Fecha de creación
- Estado del estudio
- Número de elementos
- Progreso de cronometraje

#### Acciones por Estudio:
- **Seleccionar**: Activa el estudio para trabajo
- **Eliminar**: Borrado con confirmación
- **Navegación Directa**: Acceso a cualquier módulo del estudio

## Flujo de Trabajo Típico

### 1. Inicio de Sesión
1. Usuario accede desde `/login`
2. Redirección automática al dashboard (`/`)
3. Carga de estudios del usuario

### 2. Selección/Creación de Estudio
```mermaid
graph TD
    A[Dashboard] --> B{¿Estudio Existente?}
    B -->|Sí| C[Seleccionar Estudio]
    B -->|No| D[Nuevo Estudio]
    D --> E[Configurar Información]
    C --> F[Módulos Disponibles]
    E --> G[Definir Método]
```

### 3. Navegación entre Módulos
1. **Información**: Configuración inicial
2. **Método**: Definición de elementos
3. **Cronometraje**: Registro de tiempos (Repetitivo/Seguido/Máquina/Frecuencial)
4. **Suplementos**: Cálculo de factores adicionales
5. **Informe**: Análisis final y exportación

## Funcionalidades Avanzadas

### Filtros Inteligentes
- **Filtro por Empresa**: Muestra solo estudios de la empresa del estudio seleccionado
- **Filtro por Fecha**: Agrupa estudios por fecha
- **Estudios Compartidos**: Control de visibilidad de estudios de otros usuarios

### Búsqueda en Tiempo Real
- Búsqueda instantánea por nombre de estudio
- Búsqueda sin distinción de mayúsculas/minúsculas
- Actualización automática de resultados

### Exportación/Importación
- **Exportar Estudio Individual**: JSON con todos los datos
- **Exportar Todos los Estudios**: Backup completo
- **Importar**: Restauración desde archivo JSON
- **Validación**: Verificación de integridad de datos

### Responsividad
- **Móvil**: Navegación touch-friendly, menús colapsables
- **Tablet**: Vista optimizada con controles adaptados
- **Desktop**: Máximo aprovechamiento del espacio disponible

## Estados del Dashboard

### Sin Estudio Seleccionado
```typescript
{
  selectedStudy: null,
  navigationButtons: [],
  showCreateButton: true,
  showLibraryButton: true
}
```

### Con Estudio Seleccionado
```typescript
{
  selectedStudy: Study,
  navigationButtons: NavigationButton[],
  availableModules: ModuleType[],
  studyProgress: StudyProgress
}
```

### Navegación Contextual
```typescript
interface NavigationButton {
  to: string;           // Ruta del módulo
  icon: LucideIcon;     // Ícono del módulo
  text: string;         // Texto localizado
  visible: boolean;     // Visibilidad basada en contenido
  disabled?: boolean;   // Estado deshabilitado
}
```

## Accesibilidad y UX

### Indicadores Visuales
- **Estudio Seleccionado**: Resaltado visual de la tarjeta activa
- **Progreso**: Indicadores de completitud por módulo
- **Estados**: Colores diferenciados para cada estado

### Navegación
- **Breadcrumbs**: Ruta actual visible
- **Accesos Directos**: Teclado para funciones principales
- **Estados de Carga**: Indicadores durante operaciones

### Mensajes del Sistema
- **Confirmaciones**: Diálogos para acciones destructivas
- **Notificaciones**: Feedback de operaciones exitosas/fallidas
- **Validaciones**: Mensajes de error contextuales

## Configuración y Personalización

### Preferencias de Usuario
- **Idioma**: Español/Inglés
- **Vista Predeterminada**: Grid/Lista
- **Estudios Compartidos**: Mostrar/Ocultar
- **Ordenación Predeterminada**: Fecha/Empresa

### Temas y Estilos
- **Colores**: Esquema corporativo consistente
- **Tipografía**: Optimizada para legibilidad industrial
- **Iconografía**: Conjunto coherente de íconos Lucide

## Integración con Otros Módulos

### Biblioteca de Elementos
- Acceso directo desde botón principal
- Integración con selección de elementos en métodos
- Sincronización de elementos compartidos

### Sistema de Autenticación
- Control de sesión automático
- Redirección segura tras login/logout
- Gestión de permisos por usuario

### Base de Datos
- Sincronización en tiempo real con Supabase
- Caching inteligente para mejor rendimiento
- Manejo de errores de conectividad

## Mantenimiento y Actualizaciones

### Versionado
- Control de versiones de la aplicación visible en interfaz
- Notificaciones de actualizaciones disponibles
- Proceso de actualización no disruptivo

### Monitoreo
- Tracking de uso de funcionalidades
- Métricas de rendimiento
- Logs de errores para debugging

### Escalabilidad
- Paginación inteligente para grandes volúmenes de estudios
- Lazy loading de componentes pesados
- Optimización de consultas a base de datos 