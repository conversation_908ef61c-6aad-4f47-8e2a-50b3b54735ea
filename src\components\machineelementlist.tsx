import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ElementInstance } from '../types';
import { Button } from './ui/button';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { Info, Clock } from 'lucide-react';
import { cn } from '../lib/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./ui/tooltip";

interface MachineElementListProps {
  elements: ElementInstance[];
  onElementUpdate?: (element: ElementInstance) => void;
  onElementClick: (element: ElementInstance) => void;
  records: Record<string, any[]>;
}

export const MachineElementList: React.FC<MachineElementListProps> = ({
  elements,
  onElementUpdate,
  onElementClick,
  records,
}) => {
  const { t } = useTranslation(['machine']);
  const [elementToConvert, setElementToConvert] = useState<ElementInstance | null>(null);

  const formatFrequency = (element: ElementInstance) => {
    if (!element.frequency_repetitions || !element.frequency_cycles) {
      return t('noRepetitionsSet');
    }
    return `${element.frequency_repetitions} ${t('repetitions')} ${t('each')} ${element.frequency_cycles} ${t('cycles')}`;
  };

  const machineTimeElements = useMemo(() => 
    elements.filter(element => 
      element.type === 'machine-time'
    ),
    [elements]
  );

  const showConcurrentOption = machineTimeElements.length > 1;

  const getElementStats = (elementId: string) => {
    const elementRecords = records[elementId] || [];
    if (elementRecords.length === 0) return null;
    
    const times = elementRecords.map(r => r.time);
    const activities = elementRecords.map(r => r.activity);
    
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const avgActivity = activities.reduce((sum, activity) => sum + activity, 0) / activities.length;
    
    return {
      averageTime: avgTime.toFixed(2),
      averageActivity: avgActivity.toFixed(0)
    };
  };

  const handleConcurrentToggle = (element: ElementInstance, checked: boolean): void => {
    if (!onElementUpdate) return;
    
    const updatedElement: ElementInstance = {
      ...element,
      concurrent_machine_time: checked
    };
    
    onElementUpdate(updatedElement);
  };

  const handleConfirmConversion = (): void => {
    if (!elementToConvert || !onElementUpdate) return;

    const updatedElement: ElementInstance = {
      ...elementToConvert,
      type: 'machine-running' as const,
      concurrent_machine_time: false,
      repetition_type: 'frequency',
      frequency_repetitions: elementToConvert.frequency_repetitions,
      frequency_cycles: elementToConvert.frequency_cycles
    };

    onElementUpdate(updatedElement);
    setElementToConvert(null);
  };

  const handleDialogOpenChange = (open: boolean): void => {
    if (!open) {
      setElementToConvert(null);
    }
  };

  if (machineTimeElements.length === 0) {
    return (
      <div className="py-8 text-center text-muted-foreground">
        {t('noMachineTimeElements')}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {machineTimeElements.map((element) => {
        const stats = getElementStats(element.id);
        
        return (
          <div
            key={element.id}
            onClick={() => onElementClick(element)}
            className="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:bg-gray-50"
          >
            <div className="flex flex-col sm:flex-row sm:items-start space-y-3 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center sm:block">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 text-lg sm:text-xl font-bold">
                    {String(element.position + 1).padStart(2, '0')}
                  </span>
                </div>
              </div>
              <div className="flex-1 space-y-2">
                <div className="flex flex-wrap gap-2 items-center">
                  <h3 className="font-semibold text-lg">{element.description}</h3>
                  {element.concurrent_machine_time && (
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                      {t('concurrent')}
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded-md">
                  {formatFrequency(element)}
                </div>
                {onElementUpdate && showConcurrentOption ? (
                  <div 
                    className="flex items-center gap-2 bg-gray-50/50 p-2 rounded-md" 
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="flex items-center gap-2 flex-1">
                      <Label htmlFor={`concurrent-${element.id}`} className="text-sm text-gray-600">
                        {t('concurrent')}
                      </Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs text-sm">{t('concurrentInfo')}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Switch
                      id={`concurrent-${element.id}`}
                      checked={element.concurrent_machine_time}
                      onCheckedChange={(checked) => handleConcurrentToggle(element, checked)}
                      className="data-[state=checked]:bg-purple-600 data-[state=checked]:hover:bg-purple-700 data-[state=unchecked]:bg-purple-200 data-[state=unchecked]:hover:bg-purple-300"
                    />
                  </div>
                ) : (
                  <p className="text-sm text-gray-600">
                    {t('machineTime')}
                  </p>
                )}
                {stats && (
                  <div className="flex flex-wrap gap-4 mt-2">
                    <div className="text-sm text-gray-600">
                      {t('averageActivity')}: {stats.averageActivity}
                    </div>
                    <div className="flex items-center text-sm font-medium text-gray-800">
                      <Clock className="w-4 h-4 mr-1" />
                      {stats.averageTime}s
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};