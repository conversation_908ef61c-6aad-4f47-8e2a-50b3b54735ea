import { supabase } from '../lib/supabase';
import { Study, ElementInstance, TimeRecord, ElementStats, ReportStats, TimeUnit } from '../types/index';
import { BulkExportRequest } from '../types/folder';
import { generatePDF } from './reportPDF';
import { generateExcel } from './reportExcel';
import { calculateElementStats, calculateReportStats } from './reportCalculations';
import { captureAllCharts } from './chartExport';

/**
 * Obtiene estudios completos de una carpeta para exportación
 */
async function getStudiesForExport(folderId?: string, includeSubfolders = true): Promise<any[]> {
  try {
    let query = supabase
      .from('studies')
      .select(`
        id,
        user_id,
        required_info,
        optional_info,
        elements,
        time_records,
        supplements,
        created_at,
        updated_at,
        folder_id
      `);

    if (folderId) {
      if (includeSubfolders) {
        // TODO: Implementar lógica recursiva para subcarpetas
        // Por ahora, solo obtenemos de la carpeta específica
        query = query.eq('folder_id', folderId);
      } else {
        query = query.eq('folder_id', folderId);
      }
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;

    return (data || []) as any[];

  } catch (error) {
    console.error('Error getting studies for export:', error);
    throw error;
  }
}

/**
 * Calcula estadísticas para un estudio específico usando sus configuraciones reales
 */
function calculateStudyStats(study: Study): { 
  elementStats: ElementStats[], 
  reportStats: ReportStats,
  timeUnit: TimeUnit,
  shiftMinutes: number,
  contingency: number
} {
  // Usar los valores reales del estudio desde reportSettings
  const reportSettings = (study.optional_info as any)?.reportSettings;
  const timeUnit: TimeUnit = reportSettings?.timeUnit || 'seconds';
  const shiftMinutes = reportSettings?.shiftMinutes || 480;
  const contingency = reportSettings?.contingency || 0;

  console.log(`📊 Calculando estadísticas para: ${study.required_info?.name}`, {
    timeUnit,
    shiftMinutes,
    contingency,
    activityScale: study.required_info?.activity_scale
  });

  // Calcular estadísticas de cada elemento
  const elementStats = (study.elements || []).map((element: ElementInstance) =>
    calculateElementStats(
      element,
      (study.time_records?.[element.id] || []) as any,
      study.supplements?.[element.id],
      timeUnit,
      study.required_info?.activity_scale
    )
  );

  // Calcular estadísticas generales del reporte
  const reportStats = calculateReportStats(
    elementStats,
    study.required_info?.activity_scale,
    shiftMinutes,
    contingency,
    timeUnit,
    100, // pointsPerHour por defecto para exportación masiva
    study
  );

  return { elementStats, reportStats, timeUnit, shiftMinutes, contingency };
}

/**
 * Exporta estudios a PDF individuales
 */
export async function exportStudiesToPDF(request: BulkExportRequest): Promise<void> {
  try {
    const studies = await getStudiesForExport(request.folder_id, request.include_subfolders);
    
    if (studies.length === 0) {
      throw new Error('No hay estudios para exportar');
    }

    console.log(`Exportando ${studies.length} estudios a PDF...`);

    // Función de traducción mejorada basada en idioma del navegador
    const language = navigator.language.startsWith('es') ? 'es' : 'en';
    const t = (key: string, options?: any) => {
      // Traducciones en español
      const esTranslations: Record<string, string> = {
        'company': 'Empresa',
        'date': 'Fecha',
        'operator': 'Operador',
        'page': 'Página',
        'report': 'Informe',
        'identifyingData': 'DATOS IDENTIFICATIVOS',
        'results.title': 'RESULTADOS',
        'timeUnit': 'Unidad de Tiempo',
        'shiftMinutes': 'Minutos por Turno',
        'contingency': 'Contingencia',
        'activityScale': 'Escala de Actividad',
        'units.seconds': 'segundos',
        'units.minutes': 'minutos', 
        'units.dmh': 'DMH',
        'units.mmm': 'MMM',
        'units.cmm': 'CMM',
        'units.tmu': 'TMU',
        'units.cyclesPerShift': 'ciclos/turno',
        'units.cyclesPerHour': 'ciclos/hora',
        'units.hours': 'horas',
        'units.points': 'puntos'
      };
      
      // Traducciones en inglés
      const enTranslations: Record<string, string> = {
        'company': 'Company',
        'date': 'Date',
        'operator': 'Operator',
        'page': 'Page',
        'report': 'Report',
        'identifyingData': 'IDENTIFYING DATA',
        'results.title': 'RESULTS',
        'timeUnit': 'Time Unit',
        'shiftMinutes': 'Minutes per Shift',
        'contingency': 'Contingency',
        'activityScale': 'Activity Scale',
        'units.seconds': 'seconds',
        'units.minutes': 'minutes',
        'units.dmh': 'DMH',
        'units.mmm': 'MMM',
        'units.cmm': 'CMM',
        'units.tmu': 'TMU',
        'units.cyclesPerShift': 'cycles/shift',
        'units.cyclesPerHour': 'cycles/hour',
        'units.hours': 'hours',
        'units.points': 'points'
      };
      
      const translations = language === 'es' ? esTranslations : enTranslations;
      return translations[key] || key;
    };

    for (let i = 0; i < studies.length; i++) {
      const study = studies[i];
      
      try {
        console.log(`Exportando estudio ${i + 1}/${studies.length}: ${study.required_info?.name}`);
        
        const { elementStats, reportStats, timeUnit, shiftMinutes, contingency } = calculateStudyStats(study);
        
        // Capturar gráficas (opcional)
        let chartData;
        try {
          chartData = await captureAllCharts(reportStats, elementStats, t);
        } catch (chartError) {
          console.warn('Error capturando gráficas, continuando sin ellas:', chartError);
          chartData = undefined;
        }
        
        // Generar PDF con las configuraciones reales del estudio
        await generatePDF(
          study as any,
          elementStats,
          reportStats,
          timeUnit,
          shiftMinutes,
          contingency,
          t as any,
          undefined,
          chartData,
          100 // pointsPerHour por defecto para exportación masiva
        );
        
        // Pequeña pausa entre exportaciones
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (studyError) {
        console.error(`Error exportando estudio ${study.required_info?.name}:`, studyError);
        // Continuar con el siguiente estudio
      }
    }

    console.log(`Exportación completada: ${studies.length} archivos PDF generados`);

  } catch (error) {
    console.error('Error in PDF bulk export:', error);
    throw error;
  }
}

/**
 * Exporta estudios a Excel individuales
 */
export async function exportStudiesToExcel(request: BulkExportRequest): Promise<void> {
  try {
    const studies = await getStudiesForExport(request.folder_id, request.include_subfolders);
    
    if (studies.length === 0) {
      throw new Error('No hay estudios para exportar');
    }

    console.log(`Exportando ${studies.length} estudios a Excel...`);

    // Función de traducción mejorada basada en idioma del navegador
    const language = navigator.language.startsWith('es') ? 'es' : 'en';
    const t = (key: string, options?: any) => {
      // Traducciones en español
      const esTranslations: Record<string, string> = {
        'company': 'Empresa',
        'date': 'Fecha',
        'operator': 'Operador',
        'page': 'Página',
        'report': 'Informe',
        'identifyingData': 'DATOS IDENTIFICATIVOS',
        'results.title': 'RESULTADOS',
        'timeUnit': 'Unidad de Tiempo',
        'shiftMinutes': 'Minutos por Turno',
        'contingency': 'Contingencia',
        'activityScale': 'Escala de Actividad',
        'units.seconds': 'segundos',
        'units.minutes': 'minutos', 
        'units.dmh': 'DMH',
        'units.mmm': 'MMM',
        'units.cmm': 'CMM',
        'units.tmu': 'TMU',
        'units.cyclesPerShift': 'ciclos/turno',
        'units.cyclesPerHour': 'ciclos/hora',
        'units.hours': 'horas',
        'units.points': 'puntos'
      };
      
      // Traducciones en inglés
      const enTranslations: Record<string, string> = {
        'company': 'Company',
        'date': 'Date',
        'operator': 'Operator',
        'page': 'Page',
        'report': 'Report',
        'identifyingData': 'IDENTIFYING DATA',
        'results.title': 'RESULTS',
        'timeUnit': 'Time Unit',
        'shiftMinutes': 'Minutes per Shift',
        'contingency': 'Contingency',
        'activityScale': 'Activity Scale',
        'units.seconds': 'seconds',
        'units.minutes': 'minutes',
        'units.dmh': 'DMH',
        'units.mmm': 'MMM',
        'units.cmm': 'CMM',
        'units.tmu': 'TMU',
        'units.cyclesPerShift': 'cycles/shift',
        'units.cyclesPerHour': 'cycles/hour',
        'units.hours': 'hours',
        'units.points': 'points'
      };
      
      const translations = language === 'es' ? esTranslations : enTranslations;
      return translations[key] || key;
    };

    for (let i = 0; i < studies.length; i++) {
      const study = studies[i];
      
      try {
        console.log(`Exportando estudio ${i + 1}/${studies.length}: ${study.required_info?.name}`);
        
        const { elementStats, reportStats, timeUnit, shiftMinutes, contingency } = calculateStudyStats(study);
        
        // Capturar gráficas (opcional)
        let chartData;
        try {
          chartData = await captureAllCharts(reportStats, elementStats, t);
        } catch (chartError) {
          console.warn('Error capturando gráficas, continuando sin ellas:', chartError);
          chartData = undefined;
        }
        
        // Generar Excel con las configuraciones reales del estudio
        await generateExcel(
          study as any,
          elementStats,
          reportStats,
          timeUnit,
          shiftMinutes,
          contingency,
          t as any,
          chartData,
          100 // pointsPerHour por defecto para exportación masiva
        );
        
        // Pequeña pausa entre exportaciones
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (studyError) {
        console.error(`Error exportando estudio ${study.required_info?.name}:`, studyError);
        // Continuar con el siguiente estudio
      }
    }

    console.log(`Exportación completada: ${studies.length} archivos Excel generados`);

  } catch (error) {
    console.error('Error in Excel bulk export:', error);
    throw error;
  }
}

/**
 * Función principal de exportación masiva
 */
export async function bulkExportStudies(request: BulkExportRequest): Promise<void> {
  try {
    console.log('Iniciando exportación masiva:', request);
    
    if (request.export_format === 'excel') {
      await exportStudiesToExcel(request);
    } else if (request.export_format === 'pdf') {
      await exportStudiesToPDF(request);
    } else {
      throw new Error(`Formato no soportado: ${request.export_format}`);
    }
    
    console.log('Exportación masiva completada exitosamente');
    
  } catch (error) {
    console.error('Error in bulk export:', error);
    throw error;
  }
} 