import { ElementInstance } from './index';

/**
 * Representa una fila de datos del Excel
 */
export interface ExcelRow {
  [columnName: string]: any;
}

/**
 * Mapeo de columnas del Excel a campos del elemento
 */
export interface ColumnMapping {
  description: string;
  time: string;
  activity: string;
  frequency: string;
  type: string;
  repetitionType: string;
  supplements: string;
  repetitions: string;
  cycles: string;
}

/**
 * Información básica del estudio a crear
 */
export interface StudyInfo {
  name: string;
  company: string;
  date: string;
  normalActivity?: number;
  optimalActivity?: number;
}

/**
 * Resultado del procesamiento de un archivo Excel
 */
export interface ExcelProcessingResult {
  headers: string[];
  data: ExcelRow[];
  errors: string[];
  warnings?: string[];
}

/**
 * Elemento procesado desde Excel
 */
export interface ProcessedElement extends Omit<ElementInstance, 'supplements'> {
  supplements?: Array<{
    id: string;
    name?: string;
    description?: string;
    percentage: number;
    is_forced: boolean;
    points: Record<string, any>;
    factor_selections: Record<string, any>;
  }>;
  sourceRow?: ExcelRow;
  processingWarnings?: string[];
}

/**
 * Estado del proceso de importación
 */
export interface ImportState {
  step: 'upload' | 'mapping' | 'preview' | 'importing' | 'complete' | 'error';
  progress: number;
  currentOperation?: string;
  errors: string[];
  warnings: string[];
}

/**
 * Configuración de validación para elementos
 */
export interface ValidationConfig {
  requireDescription: boolean;
  requireTime: boolean;
  minTime?: number;
  maxTime?: number;
  minActivity?: number;
  maxActivity?: number;
  allowedTypes?: string[];
  allowedRepetitionTypes?: string[];
}

/**
 * Resultado de validación de elementos
 */
export interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    row: number;
    field: string;
    message: string;
    value?: any;
  }>;
  warnings: Array<{
    row: number;
    field: string;
    message: string;
    value?: any;
  }>;
}

/**
 * Estadísticas del archivo procesado
 */
export interface ProcessingStats {
  totalRows: number;
  validRows: number;
  invalidRows: number;
  elementsWithTime: number;
  elementsWithSupplements: number;
  averageTime: number;
  typeDistribution: Record<string, number>;
  repetitionTypeDistribution: Record<string, number>;
}

/**
 * Configuración de auto-mapeo de columnas
 */
export interface AutoMappingConfig {
  patterns: Record<keyof ColumnMapping, string[]>;
  caseSensitive: boolean;
  exactMatch: boolean;
}

/**
 * Plantilla de mapeo predefinida
 */
export interface MappingTemplate {
  id: string;
  name: string;
  description: string;
  mapping: ColumnMapping;
  validationConfig?: ValidationConfig;
  isDefault?: boolean;
}

/**
 * Opciones de importación
 */
export interface ImportOptions {
  skipEmptyRows: boolean;
  skipInvalidRows: boolean;
  createTimeRecords: boolean;
  markAsLibraryVisible: boolean;
  defaultActivityScale: {
    normal: number;
    optimal: number;
  };
  defaultSupplementPercentage: number;
}

/**
 * Contexto completo de importación
 */
export interface ImportContext {
  file: File;
  studyInfo: StudyInfo;
  columnMapping: ColumnMapping;
  validationConfig: ValidationConfig;
  importOptions: ImportOptions;
  processingResult: ExcelProcessingResult;
  processedElements: ProcessedElement[];
  validationResult: ValidationResult;
  stats: ProcessingStats;
  state: ImportState;
}

/**
 * Evento de progreso de importación
 */
export interface ImportProgressEvent {
  step: ImportState['step'];
  progress: number;
  operation: string;
  data?: any;
}

/**
 * Callback para manejar progreso de importación
 */
export type ImportProgressCallback = (event: ImportProgressEvent) => void;

/**
 * Configuración de exportación de plantilla
 */
export interface TemplateExportConfig {
  includeExampleData: boolean;
  includeInstructions: boolean;
  format: 'xlsx' | 'csv';
  filename?: string;
}

/**
 * Datos de ejemplo para plantilla
 */
export interface TemplateExampleData {
  description: string;
  time: number;
  activity: number;
  type: string;
  repetitionType: string;
  supplements: number;
  repetitions: number;
  cycles: number;
}

/**
 * Utilidades de tipo para validación
 */
export type RequiredColumnMapping = Pick<ColumnMapping, 'description'>;
export type OptionalColumnMapping = Omit<ColumnMapping, 'description'>;

/**
 * Tipos de error específicos de importación
 */
export enum ImportErrorType {
  FILE_READ_ERROR = 'FILE_READ_ERROR',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  EMPTY_FILE = 'EMPTY_FILE',
  NO_HEADERS = 'NO_HEADERS',
  MAPPING_ERROR = 'MAPPING_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR'
}

/**
 * Error específico de importación
 */
export interface ImportError extends Error {
  type: ImportErrorType;
  details?: any;
  row?: number;
  column?: string;
}

/**
 * Factory para crear errores de importación
 */
export const createImportError = (
  type: ImportErrorType,
  message: string,
  details?: any,
  row?: number,
  column?: string
): ImportError => {
  const error = new Error(message) as ImportError;
  error.type = type;
  error.details = details;
  error.row = row;
  error.column = column;
  return error;
};
