import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Mic, X } from 'lucide-react';

interface VoiceInputModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTranscript: (description: string) => void;
  title: string;
  autoStart?: boolean;
}

export const VoiceInputModal: React.FC<VoiceInputModalProps> = ({
  isOpen,
  onClose,
  onTranscript,
  title,
  autoStart = true
}) => {
  const { t } = useTranslation();
  const [isListening, setIsListening] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  const startListening = () => {
    try {
      // Limpiar cualquier instancia previa
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }

      // Crear nueva instancia
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      if (!SpeechRecognition) {
        setError('Tu navegador no soporta el reconocimiento de voz');
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.lang = 'es-ES';
      recognition.continuous = false;
      recognition.interimResults = false;

      recognition.onstart = () => {
        console.log('Reconocimiento iniciado');
        setIsListening(true);
        setError(null);
      };

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        const last = event.results.length - 1;
        const text = event.results[last][0].transcript;
        console.log('Texto reconocido:', text);
        onTranscript(text);
        recognition.stop();
        onClose();
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Error en reconocimiento:', event.error);
        setIsListening(false);
        switch (event.error) {
          case 'not-allowed':
            setError('Por favor, permite el acceso al micrófono');
            break;
          case 'no-speech':
            setError('No se detectó ninguna voz');
            break;
          default:
            setError('Error en el reconocimiento de voz');
        }
      };

      recognition.onend = () => {
        console.log('Reconocimiento finalizado');
        setIsListening(false);
      };

      recognitionRef.current = recognition;
      recognition.start();
    } catch (err) {
      console.error('Error al iniciar reconocimiento:', err);
      setError('Error al iniciar el reconocimiento de voz');
    }
  };

  // Iniciar reconocimiento cuando se abre el modal
  useEffect(() => {
    if (isOpen && autoStart) {
      startListening();
    }
    // Limpiar al cerrar
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (err) {
          console.error('Error al detener reconocimiento:', err);
        }
      }
    };
  }, [isOpen, autoStart]);

  // Click fuera del modal para cerrar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div ref={modalRef} className="bg-white rounded-lg p-6 w-full max-w-md tablet:max-w-2xl laptop:max-w-4xl relative">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex flex-col items-center justify-center space-y-4">
          {error ? (
            <>
              <div className="text-red-500 mb-4 text-center">
                {error}
              </div>
              <button
                onClick={startListening}
                className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Intentar de nuevo
              </button>
            </>
          ) : (
            <div className="text-center">
              {isListening ? (
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-20 h-20 bg-red-500 rounded-full animate-pulse flex items-center justify-center">
                    <Mic className="w-10 h-10 text-white" />
                  </div>
                  <p className="text-lg font-medium">Escuchando...</p>
                  <p className="text-sm text-gray-500">Di la descripción de la vuelta</p>
                </div>
              ) : (
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                    <Mic className="w-10 h-10 text-gray-400" />
                  </div>
                  <p className="text-lg font-medium">Iniciando...</p>
                  <p className="text-sm text-gray-500">Preparando reconocimiento de voz</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};