export type SubscriptionPlan = 'monthly' | 'annual' | null;

export interface UserCredits {
  id: string;
  user_id: string;
  user_email: string;
  created_at: string;
  updated_at: string;
  monthly_credits: number;
  extra_credits: number;
  reset_date: string;
  used_monthly_credits: number;
  used_extra_credits: number;
  subscription_plan: SubscriptionPlan;
  subscription_start_date: string | null;
  subscription_end_date: string | null;
  stripe_subscription_id: string | null;
}

export interface SubscriptionStatus {
  isSubscribed: boolean;
  plan: 'monthly' | 'annual' | null;
  endDate: string | null;
  hasUnlimitedCredits: boolean;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  monthly_credits: number;
  price: number;
  duration: 'monthly' | 'annual';
  is_unlimited: boolean;
}

export const DEFAULT_SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Registro Gratuito',
    description: 'Un crédito gratuito para probar el software',
    monthly_credits: 1,
    price: 0,
    duration: 'monthly',
    is_unlimited: false
  },
  {
    id: 'monthly',
    name: 'Suscripción Mensual',
    description: 'Créditos ilimitados durante el mes',
    monthly_credits: 999999,
    price: 100,
    duration: 'monthly',
    is_unlimited: true
  },
  {
    id: 'annual',
    name: 'Suscripción Anual',
    description: 'Créditos ilimitados durante todo el año',
    monthly_credits: 999999,
    price: 800,
    duration: 'annual',
    is_unlimited: true
  }
];
