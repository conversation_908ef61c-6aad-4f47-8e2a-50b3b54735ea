import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';
import { Organization } from '../../types/organization';
import { supabase } from '../../lib/supabase';
import { LoadingState } from '../ui/skeleton';
import { RequestCard } from '../ui/enhanced-card';

interface PendingJoinRequestsProps {
  organizationId?: string; // Opcional, si se proporciona, solo muestra las solicitudes de esa organización
  forceRefresh?: boolean; // Nueva prop para forzar la actualización
}

export const PendingJoinRequests: React.FC<PendingJoinRequestsProps> = ({ organizationId, forceRefresh = false }) => {
  const { t } = useTranslation('common');
  const { toast } = useToast();
  const { 
    organizations, 
    fetchJoinRequests, 
    joinRequests, 
    approveJoinRequest, 
    rejectJoinRequest,
    isLoading,
    members,
    fetchMembers
  } = useOrganizationStore();
  const [pendingRequestsCount, setPendingRequestsCount] = useState(0);
  const [showRequests, setShowRequests] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminOrganizations, setAdminOrganizations] = useState<string[]>([]);

  // Verificar si el usuario es administrador o propietario de alguna organización
  useEffect(() => {
    const checkAdminStatus = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      let isAdminOfAny = false;
      const adminOrgIds: string[] = [];

      for (const org of organizations) {
        // Asegurarse de que los miembros estén cargados
        if (!members[org.id]) {
          await fetchMembers(org.id);
        }

        const orgMembers = members[org.id] || [];
        const isAdminOrOwner = orgMembers.some(m => 
          m.user_id === user.id && (m.role === 'admin' || m.role === 'owner')
        );

        if (isAdminOrOwner) {
          isAdminOfAny = true;
          adminOrgIds.push(org.id);
        }
      }

      setIsAdmin(isAdminOfAny);
      setAdminOrganizations(adminOrgIds);
    };

    if (organizations.length > 0) {
      checkAdminStatus();
    }
  }, [organizations, members, fetchMembers]);

  // Obtener todas las solicitudes pendientes para las organizaciones del usuario
  useEffect(() => {
    const fetchAllPendingRequests = async () => {
      // Si el usuario no es administrador de ninguna organización, no mostrar nada
      if (!isAdmin && !organizationId) {
        return;
      }

      setIsRefreshing(true);
      
      if (organizationId) {
        // Si se proporciona un ID de organización, verificar si el usuario es admin de esa organización
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        // Asegurarse de que los miembros estén cargados
        if (!members[organizationId]) {
          await fetchMembers(organizationId);
        }

        const orgMembers = members[organizationId] || [];
        const isAdminOrOwner = orgMembers.some(m => 
          m.user_id === user.id && (m.role === 'admin' || m.role === 'owner')
        );

        if (!isAdminOrOwner) {
          setIsRefreshing(false);
          return;
        }

        // Si es admin, obtener las solicitudes
        console.log('Fetching join requests for specific organization:', organizationId);
        await fetchJoinRequests(organizationId, forceRefresh);
        const orgRequests = joinRequests[organizationId] || [];
        setPendingRequestsCount(orgRequests.length);
      } else if (organizations.length && adminOrganizations.length > 0) {
        // Si no se proporciona un ID, obtener las solicitudes solo para las organizaciones donde el usuario es admin
        let totalPending = 0;
        
        for (const orgId of adminOrganizations) {
          console.log('Fetching join requests for organization:', orgId);
          await fetchJoinRequests(orgId, forceRefresh);
          const orgRequests = joinRequests[orgId] || [];
          totalPending += orgRequests.length;
        }
        
        setPendingRequestsCount(totalPending);
      }
      
      setIsRefreshing(false);
    };
    
    fetchAllPendingRequests();
  }, [organizations, fetchJoinRequests, organizationId, forceRefresh, isAdmin, adminOrganizations, members, fetchMembers]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      if (organizationId) {
        await fetchJoinRequests(organizationId, true);
        const orgRequests = joinRequests[organizationId] || [];
        setPendingRequestsCount(orgRequests.length);
      } else if (adminOrganizations.length > 0) {
        let totalPending = 0;
        
        for (const orgId of adminOrganizations) {
          await fetchJoinRequests(orgId, true);
          const orgRequests = joinRequests[orgId] || [];
          totalPending += orgRequests.length;
        }
        
        setPendingRequestsCount(totalPending);
      }
      
      toast({
        description: t('joinRequests.refreshSuccess'),
      });
    } catch (error) {
      toast({
        title: t('errors.refreshFailed'),
        description: error instanceof Error ? error.message : t('errors.refreshFailed'),
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleApprove = async (requestId: string) => {
    try {
      await approveJoinRequest(requestId);
      
      // Actualizar el contador de solicitudes pendientes
      setPendingRequestsCount(prev => Math.max(0, prev - 1));
      
      toast({
        title: t('joinRequests.approved'),
        description: t('joinRequests.approvedDescription'),
      });
    } catch (error) {
      toast({
        title: t('errors.processRequest'),
        description: error instanceof Error ? error.message : t('errors.processRequest'),
        variant: 'destructive',
      });
    }
  };

  const handleReject = async (requestId: string) => {
    try {
      await rejectJoinRequest(requestId);
      
      // Actualizar el contador de solicitudes pendientes
      setPendingRequestsCount(prev => Math.max(0, prev - 1));
      
      toast({
        title: t('joinRequests.rejected'),
        description: t('joinRequests.rejectedDescription'),
      });
    } catch (error) {
      toast({
        title: t('errors.processRequest'),
        description: error instanceof Error ? error.message : t('errors.processRequest'),
        variant: 'destructive',
      });
    }
  };

  // Si el usuario no es administrador, no mostrar nada
  if (!isAdmin && !organizationId) {
    return null;
  }

  // Si es admin pero no hay solicitudes pendientes, mostrar estado vacío
  if (pendingRequestsCount === 0 && !isRefreshing && isAdmin) {
    return (
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-3xl">✅</span>
            </div>
          </div>
          <h3 className="text-lg font-semibold text-green-900 mb-2">
            ¡Todo al día!
          </h3>
          <p className="text-green-700 text-sm">
            No tienes solicitudes pendientes de unión a tus organizaciones.
          </p>
          <div className="mt-4 flex justify-center">
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="border-green-300 text-green-700 hover:bg-green-100"
            >
              <span className="mr-2">🔄</span>
              {isRefreshing ? 'Verificando...' : 'Verificar nuevamente'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Si está cargando por primera vez, no mostrar nada
  if (pendingRequestsCount === 0 && isRefreshing) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">
          {t('joinRequests.pendingTitle')}
          <span className="ml-2 inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-red-500 rounded-full">
            {pendingRequestsCount}
          </span>
        </h2>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? t('refreshing') : t('refresh')}
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setShowRequests(!showRequests)}
          >
            {showRequests ? t('hide') : t('show')}
          </Button>
        </div>
      </div>
      
      {showRequests && (
        <div className="space-y-4">
          {isLoading || isRefreshing ? (
            <LoadingState 
              message="Cargando solicitudes pendientes..." 
              icon="📬" 
              variant="requests"
            />
          ) : (
            <>
              {organizationId ? (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">{organizations.find(org => org.id === organizationId)?.name}</h3>
                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    {(joinRequests[organizationId] || []).map(request => (
                      <RequestCard
                        key={request.id}
                        request={{
                          ...request,
                          organization_name: organizations.find(org => org.id === organizationId)?.name
                        }}
                        type="incoming"
                        onApprove={() => handleApprove(request.id)}
                        onReject={() => handleReject(request.id)}
                        className="h-full"
                      />
                    ))}
                  </div>
                  {(joinRequests[organizationId] || []).length === 0 && (
                    <p className="text-center text-gray-500 py-8">{t('joinRequests.noRequests')}</p>
                  )}
                </div>
              ) : (
                // Mostrar solicitudes para todas las organizaciones donde el usuario es admin
                <div className="space-y-6">
                  {adminOrganizations.map(orgId => {
                    const orgRequests = joinRequests[orgId] || [];
                    if (orgRequests.length === 0) return null;
                    
                    return (
                      <div key={orgId} className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">
                          {organizations.find(org => org.id === orgId)?.name}
                        </h3>
                        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                          {orgRequests.map(request => (
                            <RequestCard
                              key={request.id}
                              request={{
                                ...request,
                                organization_name: organizations.find(org => org.id === orgId)?.name
                              }}
                              type="incoming"
                              onApprove={() => handleApprove(request.id)}
                              onReject={() => handleReject(request.id)}
                              className="h-full"
                            />
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};
