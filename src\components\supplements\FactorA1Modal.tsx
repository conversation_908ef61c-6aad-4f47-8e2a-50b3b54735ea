import React, { useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, RotateCcw, InfoIcon } from 'lucide-react';
import { useSupplementTablesStore } from '../../store/supplementTablesStore';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { Dialog } from "../ui/dialog";

interface FactorA1ModalProps {
  intensity: 'Reducido' | 'Mediano' | 'Intenso';
  onClose: () => void;
  onSelect: (value: number, index?: number) => void;
  onReset: () => void;
  currentValue?: number;
  currentSelection?: number;
  tableType?: 'oit' | 'tal';
}

export const FactorA1Modal: React.FC<FactorA1ModalProps> = ({
  intensity,
  onClose,
  onSelect,
  onReset,
  currentValue,
  currentSelection,
  tableType = 'tal'
}) => {
  const { t } = useTranslation(['supplements', 'talFactors', 'oitFactors']);
  const tables = useSupplementTablesStore(state => state.tables);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const handleSelect = (points: number, index: number) => {
    onSelect(points, index);
    onClose();
  };

  // Get descriptions from translations and points from store
  const descriptions = tableType === 'tal' 
    ? t(`${tableType}.A1.description.${intensity}`, { returnObjects: true }) as string[]
    : t(`${tableType}.A1.description`, { returnObjects: true }) as string[];

  const points = tableType === 'tal'
    ? tables.A1[intensity]?.puntos || []
    : tables.A1.puntos || [];

  // Get translated intensity directly from the translations
  const translatedIntensity = t(`${tableType}.A1.intensities.${intensity}`, {
    defaultValue: intensity // fallback to original value if translation not found
  });

  // Create options by combining descriptions with points
  const options = (Array.isArray(descriptions) ? descriptions : []).map((description, index) => ({
    description,
    points: points[index] || 0
  }));

  return (
    <Dialog open onOpenChange={() => onClose()}>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div ref={modalRef} className="bg-white rounded-lg w-full max-w-md">
          <div className="p-4 bg-red-500 text-white flex items-center justify-between rounded-t-lg">
            <div className="flex items-center gap-2">
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild onClick={(e) => e.preventDefault()}>
                    <button 
                      className="p-1 hover:bg-red-600 rounded-full transition-colors"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                    >
                      <InfoIcon className="w-4 h-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('tooltips.A1', { ns: 'supplements' })}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <h2 className="text-lg font-semibold">
                {t('factors.A1', { ns: 'supplements' })} - {translatedIntensity}
              </h2>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={onReset}
                className="p-1 hover:bg-red-600 rounded-full transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
              <button
                onClick={onClose}
                className="p-1 hover:bg-red-600 rounded-full transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div className="p-4 max-h-[70vh] overflow-y-auto">
            <div className="space-y-2">
              {options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => handleSelect(option.points, index)}
                  className={`w-full p-3 text-left rounded-lg transition-colors ${
                    currentSelection === index
                      ? 'bg-red-100 hover:bg-red-200'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span className="flex-1">{option.description}</span>
                    <span className="ml-4 text-red-500 font-semibold">
                      {option.points} {t('points', { ns: 'supplements' })}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};