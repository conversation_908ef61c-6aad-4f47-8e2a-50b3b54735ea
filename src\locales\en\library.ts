export default {
  title: 'Library',
  subtitle: 'Search and reuse elements from your studies',
  searchPlaceholder: 'Search elements by name or description',
  selectedElements: 'Selected elements: {{count}}',
  createNewStudy: 'Create new study',
  studyName: 'Study name',
  studyCompany: 'Company',
  studyDate: 'Date',
  studyDescription: 'Study description',
  normalActivity: 'Normal activity',
  optimalActivity: 'Optimal activity',
  fromStudy: 'From: {{name}}',
  noResults: 'No elements found',
  loading: 'Loading elements...',
  error: 'Error loading elements',
  studyCreated: 'Study created successfully',
  createError: 'Error creating study',
  companies: 'Companies',
  departments: 'Departments',
  operations: 'Operations',
  machines: 'Machines',
  parts: 'Parts',
  analysts: 'Analysts',
  new: 'New',
  edit: 'Edit',
  delete: 'Delete',
  name: 'Name',
  description: 'Description',
  code: 'Code',
  type: 'Type',
  saveSuccess: 'Item saved successfully',
  saveError: 'Error saving item',
  deleteSuccess: 'Item deleted successfully',
  deleteError: 'Error deleting item',
  confirmDelete: 'Are you sure you want to delete this item?',
  noItems: 'No items available',
  loadError: 'Error loading items',
  search: 'Search',
  filter: 'Filter',
  sort: 'Sort',
  fromLibrary: 'From Library',
  studies: 'Studies',
  libraryLoading: 'Loading...',
  libraryError: 'Error loading library',
  library: 'Library',
  libraryTab: 'Library',
  averageTab: 'Averages',
  calculateAverage: 'Calculate Average',
  averagedElement: 'Averaged Element',
  sendToLibrary: 'Send to Library',
  sendToAverage: 'Send to Average',
  baseTime: 'Base Time',
  totalTakes: 'Total Takes',
  clear: 'Clear',
  elements: 'elements',

  // New translations for redesigned library
  noStudiesInLibrary: 'No studies in library',
  noStudiesInLibraryDescription: 'Studies will appear here when they have elements marked as visible in the library',
  noElementsInStudy: 'This study has no available elements',
  searchStudiesAndElements: 'Search studies and elements...',
  viewSelection: 'View selection',
  selectedElements: 'Selected elements',
  noElementsSelected: 'No elements selected',
  selectElementsToCreateStudy: 'Select elements from different studies to create a new one',
  createStudyFromSelection: 'Create study from selection',
  allCompanies: 'All companies',
  dateFrom: 'Date from',
  dateTo: 'Date to',
  onlyStudiesWithElements: 'Only studies with elements',
  activeFilters: 'Active filters',
  applyFilters: 'Apply filters',
  withElements: 'With elements',
  errorCreatingStudy: 'Error creating study',
  fillRequiredFields: 'Please fill all required fields',
  selectAtLeastOneElement: 'Select at least one element'
};
