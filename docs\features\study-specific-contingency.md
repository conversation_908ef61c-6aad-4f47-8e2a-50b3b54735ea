# Contingencia Específica por Estudio

## Descripción
Esta funcionalidad permite que cada estudio guarde y mantenga su propio porcentaje de contingencia, independientemente del valor por defecto configurado en el perfil del usuario.

## Comportamiento

### Carga Inicial
1. **Al abrir un estudio por primera vez**: Se usa el porcentaje de contingencia del perfil del usuario (`default_contingency`)
2. **Al abrir un estudio que ya tiene contingencia específica**: Se usa el valor guardado del estudio (`settings.studyContingency`)

### Guardado Automático
- Cuando el usuario cambia el valor de contingencia en la pantalla de reporte, se guarda automáticamente después de 1 segundo de inactividad (debounce)
- El valor se almacena en `study.settings.studyContingency`
- Los cambios son inmediatos en la interfaz y se reflejan en todos los cálculos

### Persistencia
- El valor se mantiene al recargar el estudio
- El valor se mantiene al navegar entre pantallas
- Cada estudio conserva su valor independientemente

## Implementación Técnica

### Estructura de Datos
```typescript
interface Study {
  // ... otros campos
  optional_info?: {
    study_number?: string;
    operator?: string;
    section?: string;
    reference?: string;
    machine?: string;
    tools?: string;
    technician?: string;
    studyContingency?: number; // NUEVO: Contingencia específica del estudio
    visibleEnBiblioteca?: boolean;
    // ... otros campos
  };
}
```

### Flujo de Carga
```typescript
// En ReportPage.tsx
if (selectedStudy?.optional_info?.studyContingency !== undefined) {
  setContingency(selectedStudy.optional_info.studyContingency);
  console.log('🎯 Usando contingencia específica del estudio');
} else {
  const defaultContingency = profileData.default_contingency || 0;
  setContingency(defaultContingency);
  console.log('🔄 Usando contingencia por defecto del perfil');
}
```

### Flujo de Guardado
```typescript
const saveStudyContingency = async (contingencyValue: number) => {
  const updatedOptionalInfo = {
    ...selectedStudy.optional_info,
    studyContingency: contingencyValue
  };

  await updateStudy(selectedStudy.id, {
    optional_info: updatedOptionalInfo
  });
};
```

## Ventajas

1. **Flexibilidad**: Cada estudio puede tener su propia contingencia según las necesidades específicas
2. **Consistencia**: Los valores se mantienen al reabrir estudios
3. **Experiencia de Usuario**: El cambio es transparente y automático
4. **Compatibilidad**: Los estudios existentes seguirán funcionando con la contingencia del perfil

## Compatibilidad con Versiones Anteriores

- Los estudios creados antes de esta funcionalidad seguirán usando la contingencia del perfil del usuario
- La primera vez que se modifique la contingencia en un estudio existente, se guardará como contingencia específica
- No se requiere migración de datos existentes

## Casos de Uso

1. **Estudios de alto riesgo**: Pueden requerir mayor contingencia (10-15%)
2. **Estudios estándar**: Pueden usar la contingencia normal del perfil (5%)
3. **Estudios de precisión**: Pueden requerir menor contingencia (2-3%)
4. **Estudios con condiciones especiales**: Cada uno puede tener su valor específico según el contexto 