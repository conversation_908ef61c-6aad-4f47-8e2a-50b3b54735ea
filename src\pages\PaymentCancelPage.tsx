import React from 'react';
import { useNavigate } from 'react-router-dom';
import { XCircle } from 'lucide-react';
import { Button } from '../components/ui/button';

export const PaymentCancelPage = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg">
        <div className="text-center">
          <XCircle className="mx-auto h-16 w-16 text-red-500" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Pago Cancelado
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Has cancelado el proceso de pago. Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.
          </p>
        </div>
        <div className="mt-8 space-y-4">
          <Button
            onClick={() => navigate('/profile')}
            className="w-full"
          >
            Volver al Perfil
          </Button>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-full"
          >
            Intentar de Nuevo
          </Button>
        </div>
      </div>
    </div>
  );
};
