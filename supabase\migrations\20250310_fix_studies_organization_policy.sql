-- Drop existing policy for viewing studies
DROP POLICY IF EXISTS "Users can view their own studies" ON studies;

-- Create improved policy that allows organization members to view all studies in their organizations
CREATE POLICY "Users can view their own studies" 
ON studies 
FOR SELECT 
TO authenticated 
USING (
    user_id = auth.uid() 
    OR (
        organization_id IS NOT NULL 
        AND organization_id IN (
            SELECT organization_id 
            FROM organization_members 
            WHERE user_id = auth.uid()
        )
    )
);

-- Create index to improve performance
CREATE INDEX IF NOT EXISTS idx_studies_organization_id ON studies(organization_id);