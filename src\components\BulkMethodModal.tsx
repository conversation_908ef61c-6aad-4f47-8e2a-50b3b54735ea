import React from 'react';
import { useTranslation } from 'react-i18next';
import { X, AlertCircle, Loader2 } from 'lucide-react';
import { CronoSeguidoRecord } from '../types/study';

interface BulkMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  records?: CronoSeguidoRecord[];
  loading?: boolean;
}

export const BulkMethodModal: React.FC<BulkMethodModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  records = [],
  loading = false
}) => {
  const { t } = useTranslation(['method', 'common', 'cronoSeguido']);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4">{t('addToMethod', { ns: 'cronoSeguido' })}</h2>
        <p className="mb-4">{t('confirmBulkAdd', { ns: 'cronoSeguido', count: records.length })}</p>
        
        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
            disabled={loading}
          >
            {t('cancel', { ns: 'common' })}
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 flex items-center space-x-2"
            disabled={loading}
          >
            {loading && <Loader2 className="w-4 h-4 animate-spin" />}
            <span>{t('confirm', { ns: 'common' })}</span>
          </button>
        </div>
      </div>
    </div>
  );
};