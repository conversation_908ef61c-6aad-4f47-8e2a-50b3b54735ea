import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Send } from 'lucide-react';
import { WorkElement } from '../../types';
import { useStudyStore } from '../../store/studyStore';

interface SupplementsForceProps {
  element: WorkElement;
  onSaved: () => void;
  supplement?: {
    points: Record<string, number>;
    percentage: number;
    is_forced: boolean;
  };
}

export const SupplementsForce: React.FC<SupplementsForceProps> = ({ 
  element,
  onSaved,
  supplement
}) => {
  const { t } = useTranslation(['supplements', 'common']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const { updateSupplements } = useStudyStore();
  const [percentage, setPercentage] = useState(
    supplement?.is_forced ? supplement.percentage : 0
  );
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!selectedStudy) return;

    try {
      setIsLoading(true);
      const updatedSupplements = {
        ...selectedStudy.supplements,
        [element.id]: {
          points: {},
          percentage,
          is_forced: true,
          factor_selections: {}
        }
      };

      await updateSupplements(selectedStudy.id, updatedSupplements);
      onSaved();
    } catch (error) {
      console.error('Error saving forced supplement:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <p className="text-gray-600 mb-6">
        {t('forceDescription', { ns: 'supplements' })}
      </p>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('percentage', { ns: 'supplements' })}
        </label>
        <input
          type="number"
          min="0"
          max="100"
          value={percentage}
          onChange={(e) => setPercentage(Number(e.target.value))}
          className="w-full p-2 border rounded-lg focus:ring-purple-500 focus:border-purple-500"
        />
      </div>

      <button
        onClick={handleSubmit}
        disabled={isLoading}
        className="w-full bg-green-500 text-white py-3 rounded-lg flex items-center justify-center space-x-2 hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <Send className="w-5 h-5" />
        <span>{isLoading ? t('saving', { ns: 'common' }) : t('apply', { ns: 'common' })}</span>
      </button>
    </div>
  );
};