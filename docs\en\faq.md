
---

# Frequently Asked Questions

## Login and Authentication

<details>
<summary>How do I log in to the Cronometras app?</summary>

To log in:
1. Open the app and go to the login screen.
2. Enter your registered email and password.
3. Click "Login" to submit your credentials.
4. If valid, you’ll be redirected to the dashboard.  
The app verifies your credentials with the backend and stores an authentication token for your session.
</details>

<details>
<summary>Can I log in using my Google account?</summary>

Yes, you can:
1. On the login screen, select "Login with Google."
2. A Google authentication window will appear.
3. Sign in with your Google credentials.
4. Upon success, you’ll be redirected to the dashboard with a stored authentication token.
</details>

<details>
<summary>What should I do if I forget my password?</summary>

To reset your password:
1. On the login screen, click "Forgot Password."
2. Enter your registered email address.
3. Check your inbox for a reset link.
4. Follow the link to set a new password.  
The process uses the `resetPassword(email)` function to initiate recovery.
</details>

<details>
<summary>How do I register as a new user?</summary>

To register:
1. On the login screen, click "Register."
2. Enter your email and a password.
3. Submit the form.
4. Check your email for a confirmation link and verify your account.  
Once verified, you can log in with your new credentials.
</details>

<details>
<summary>Why do I need to verify my email address?</summary>

Email verification ensures account security and enables password recovery. After registering, you receive a link with a token. Clicking it verifies your email via the `verifyEmail(token)` function, marking your account as active.
</details>

<details>
<summary>How can I change my password?</summary>

To change your password:
1. Log in and go to your profile.
2. Select "Change Password."
3. Enter your current password and your new password.
4. Submit to update it.  
The app verifies your current password before applying the change using `changePassword(oldPassword, newPassword)`.
</details>

<details>
<summary>What happens when I log out?</summary>

When you log out:
1. Click "Logout" in the app.
2. The authentication token is removed from the store.
3. You’re redirected to the login screen.  
This ends your session securely using the `logout()` function.
</details>

<details>
<summary>How does the app keep me logged in?</summary>

The app uses `checkAuth()` to verify your session:
1. It checks the stored authentication token on startup.
2. If valid, you stay logged in and access the dashboard.
3. If invalid, you’re redirected to the login screen.
</details>

<details>
<summary>Can I use the app on multiple devices?</summary>

Yes, you can log in on multiple devices with your credentials. Each device maintains a separate session, and the app verifies the token independently on each.
</details>

<details>
<summary>Can I stay logged in without re-entering my credentials?</summary>

Yes, if the app retains a valid token (via `checkAuth()`), you remain logged in until you log out or the token expires. There’s no explicit "Remember Me" option, but sessions persist based on token validity.
</details>

## Dashboard

<details>
<summary>How do I view my existing studies?</summary>

On the dashboard:
1. You’ll see a list of your own studies and those shared with you.
2. Scroll or use filters to find a specific study.  
The dashboard displays studies based on ownership and sharing permissions.
</details>

<details>
<summary>What’s the difference between my studies and shared studies?</summary>

- **My Studies**: Ones you created and own, fully editable.
- **Shared Studies**: Ones shared with you by others in your organization, viewable but not editable unless you’re the owner.  
Permissions are enforced by Row Level Security (RLS) policies.
</details>

<details>
<summary>How can I create a new study?</summary>

To create a study:
1. Click "New Study" on the dashboard.
2. Fill in required fields (name, company, date, etc.).
3. Click "Save" to create and redirect to the dashboard.  
The study is auto-shared with your organization if applicable.
</details>

<details>
<summary>Can I edit a shared study?</summary>

No, only the owner can edit a study. If you try:
1. An amber alert appears.
2. Changes won’t save unless you’re the owner or create a copy.  
This is enforced by ownership verification.
</details>

<details>
<summary>How do I delete a study?</summary>

To delete:
1. Select your study from the dashboard.
2. Click "Delete" and confirm in the prompt.  
Only owners can delete studies; others see a permission alert.
</details>

<details>
<summary>How do I share a study with my organization?</summary>

To share:
1. Select your study on the dashboard.
2. Click "Share."
3. Choose organization members to share with.  
Shared studies appear in their dashboards with view-only access.
</details>

<details>
<summary>Can I filter studies by company or date?</summary>

Yes:
1. On the dashboard, use the filter options.
2. Select "By Company" or "By Date" and apply your criteria.  
This narrows down the study list accordingly.
</details>

<details>
<summary>How does the search function work?</summary>

To search:
1. Enter keywords (study name or description) in the dashboard’s search bar.
2. Results update in real-time as you type.  
It uses a basic keyword match across visible studies.
</details>

<details>
<summary>What are alerts and notifications for?</summary>

Alerts notify you of:
- Permission issues (e.g., editing a shared study).
- Changes to shared studies by others.
- Action confirmations (e.g., saving).  
They appear as toasts or amber alerts on-screen.
</details>

<details>
<summary>Why do I see an amber alert when editing a study?</summary>

The amber alert appears when:
1. You’re editing a shared study you don’t own.
2. It warns that changes won’t save unless you’re the owner or clone it.  
This is a security feature to protect original data.
</details>

<details>
<summary>How can I change the app’s language?</summary>

To change the language:
1. Go to your profile settings.
2. Select "Spanish" or "English" from the language option.
3. The interface updates immediately.  
This uses the app’s internationalization feature.
</details>

<details>
<summary>Where do I find navigation options?</summary>

On the dashboard:
1. Use the navigation bar or menu.
2. Access pages like chronometer, method, or report via buttons or links.  
Options vary based on the study’s state.
</details>

## Study Information

<details>
<summary>What’s required to create a new study?</summary>

You must provide:
1. Study name.
2. Company.
3. Date.
4. Activity scale (normal and optimal).  
Optional fields include analyst name and department.
</details>

<details>
<summary>Can I leave some study fields optional?</summary>

Yes, fields like:
- Analyst name.
- Department.
- Operation number.  
These are optional and can be left blank during creation or editing.
</details>

<details>
<summary>How do I edit an existing study?</summary>

To edit:
1. Select the study from the dashboard.
2. Click "Edit" or navigate to `/study/{studyId}/info`.
3. Modify fields and save.  
Owners can edit freely; others see an amber alert.
</details>

<details>
<summary>What if I don’t have enough credits to edit a study?</summary>

If credits are insufficient:
1. You’ll see a prompt to buy more or subscribe.
2. Without credits, the app may create a copy for editing.  
Credits are checked via `useCreditStore`.
</details>

<details>
<summary>Why does the app clone a study automatically?</summary>

Cloning occurs if:
1. You’re not the owner of a shared study.
2. You lack credits to edit.  
The copy (e.g., "Study (Copy)") lets you work without altering the original.
</details>

<details>
<summary>How do I navigate study sections?</summary>

Use the navigation bar in the study:
1. Go to "Info" for basic data.
2. "Method" for elements.
3. "Chronometer" or others as needed.  
It’s accessible from `/study/{studyId}/info`.
</details>

<details>
<summary>What does the amber alert mean when editing?</summary>

It indicates:
1. You’re editing a shared study you don’t own.
2. Changes won’t save unless you’re the owner or clone it.  
This protects shared study integrity.
</details>

<details>
<summary>How are credits used with studies?</summary>

Credits are consumed when:
1. Creating a new study.
2. Modifying an existing one (if not on an unlimited plan).  
Check your credit balance in the profile.
</details>

## Method Screen

<details>
<summary>How do I add a work element?</summary>

To add:
1. Go to `/study/{studyId}/method`.
2. Click "Add Element."
3. Fill in description, type, and frequency in the modal.
4. Save to add it to the list.
</details>

<details>
<summary>What types of work elements can I create?</summary>

You can create:
- Repetitive (repetitive-type).
- Frequency (frequency-type).
- Machine (machine-type, stopped, running, time).  
Select the type in the element creation form.
</details>

<details>
<summary>Can I edit an existing element?</summary>

Yes:
1. On the method screen, click an element.
2. Edit its details in the form.
3. Save changes.  
Non-owners see an amber alert on shared studies.
</details>

<details>
<summary>How do I delete an element?</summary>

To delete:
1. Select the element on the method screen.
2. Click "Delete" and confirm in the modal.  
Associated time records and supplements are also removed.
</details>

<details>
<summary>What’s the voice recognition feature for?</summary>

It lets you:
1. Click the microphone button in the element form.
2. Dictate the element description.
3. The app converts speech to text (Spanish or English).  
It stops after 2 seconds of silence.
</details>

<details>
<summary>How does library integration work?</summary>

With the library:
1. Save elements by checking "Add to Library" when creating/editing.
2. Reuse them in other studies from the library screen.  
Library elements are marked for identification.
</details>

<details>
<summary>Can I reuse elements from past studies?</summary>

Yes:
1. Go to the library screen.
2. Search and select elements.
3. Add them to a new study.  
This ensures consistency across studies.
</details>

<details>
<summary>What happens if I edit a shared study’s elements?</summary>

If you’re not the owner:
1. An amber alert appears.
2. Changes won’t save unless you clone the study.  
This is a security restriction.
</details>

<details>
<summary>How do I know if an element is from the library?</summary>

Library elements:
1. Are marked with a special tag or indicator in the method screen list.  
This distinguishes them from study-specific elements.
</details>

<details>
<summary>Can I reorder elements?</summary>

The manual doesn’t specify drag-and-drop reordering in the method screen, but you can:
1. Delete and recreate elements in your desired order.
2. Use the chronometer screen to adjust timing sequence.  
Check updates for reordering features.
</details>

## Chronometer Screens

<details>
<summary>What’s the difference between repetitive and continuous chronometer?</summary>

- **Repetitive**: Measures elements with defined cycles (e.g., repeated tasks).
- **Continuous**: Records ongoing activities sequentially without predefined breaks.  
Choose based on your study’s element types.
</details>

<details>
<summary>How do I start timing an element?</summary>

For repetitive chronometer:
1. Go to the chronometer screen.
2. Select an element.
3. Click the green "Start" button.  
The timer runs until paused or stopped.
</details>

<details>
<summary>Can I pause the chronometer?</summary>

Yes:
1. During timing, click the yellow "Pause" button.
2. Click "Resume" to continue.  
This works in both repetitive and continuous modes.
</details>

<details>
<summary>How do I adjust activity level during timing?</summary>

To adjust:
1. Use the increment/decrement buttons during measurement.
2. Set the observed work pace (50% to optimal scale).  
The value displays on-screen.
</details>

<details>
<summary>What statistics are shown for elements?</summary>

Statistics include:
- Total measurements taken.
- Average time.
- Total time.
- Average activity.
- Recommended remaining measurements.  
View them on the chronometer screen per element.
</details>

<details>
<summary>How can I edit a recorded time?</summary>

To edit:
1. Select a record from the chronometer list.
2. Click "Edit."
3. Adjust time, activity, or frequency.
4. Save changes.  
Comments can also be added.
</details>

<details>
<summary>Can I add comments to time records?</summary>

Yes:
1. After recording, select the record.
2. Add comments via text or voice recognition.  
This documents special observations.
</details>

<details>
<summary>What does “concurrent” mean for an element?</summary>

Concurrent elements:
1. Occur simultaneously with others (e.g., machine times).
2. Are marked (e.g., purple background) and excluded from some calculations.  
They don’t add to operator workload time.
</details>

<details>
<summary>How do I handle machine times differently?</summary>

For machine times:
1. Use the "Machine Times" screen.
2. Select a machine-type element.
3. Time it without activity adjustments (fixed pace).  
It’s distinct from manual repetitive timing.
</details>

<details>
<summary>Can I reorder elements during timing?</summary>

Yes:
1. Deselect all elements except your starting one.
2. Reselect others in the desired order.  
The chronometer follows this new sequence.
</details>

## Supplements Screen

<details>
<summary>What are supplements in a time study?</summary>

Supplements are:
1. Extra time allowances for fatigue, personal needs, or conditions.
2. Added to normal time for a standard time calculation.  
They adjust for human and environmental factors.
</details>

<details>
<summary>How do I choose between OIT and TAL tables?</summary>

To choose:
1. On the supplements screen, select OIT (ILO standard) or TAL (alternative with objective measures like decibels).
2. Pick based on your study’s methodology.  
The app warns if an element uses a different system.
</details>

<details>
<summary>Can I assign supplements to machine elements?</summary>

Yes:
1. Select the machine element.
2. Assign supplements via points or forced percentage.
3. Fatigue calculations differ based on machine state (running/stopped).  
Use the fatigue tool for accuracy.
</details>

<details>
<summary>How do I calculate fatigue for machines?</summary>

To calculate:
1. Go to the supplements screen.
2. Select a machine element.
3. Use the fatigue tool, which applies formulas based on inactivity (e.g., ≤30s, 31-90s).  
Results are weighted by cycle contribution.
</details>

<details>
<summary>How do I force a supplement percentage?</summary>

To force:
1. Select an element.
2. Choose "Force Supplement."
3. Enter a percentage and apply.  
This overrides point-based calculations.
</details>

<details>
<summary>Can I copy supplements between elements?</summary>

Yes:
1. On the supplements screen, select an element with assigned supplements.
2. Click "Copy" and choose target elements.
3. Apply to duplicate the values.  
This saves time for similar elements.
</details>

<details>
<summary>What happens if I switch supplement systems?</summary>

If you switch:
1. Existing assignments may need reassignment.
2. OIT and TAL use different factor scales, so values won’t transfer directly.  
Recalculate for consistency.
</details>

<details>
<summary>How are supplements calculated from points?</summary>

Points are:
1. Assigned to factors (effort, conditions).
2. Converted to a percentage via OIT/TAL tables.
3. Total points yield the supplement percentage automatically.  
View totals live on-screen.
</details>

<details>
<summary>Can I reset supplement assignments?</summary>

Yes:
1. Select an element on the supplements screen.
2. Click "Reset" to clear all assigned values.  
This removes both point-based and forced supplements.
</details>

<details>
<summary>How do supplements affect final times?</summary>

Supplements:
1. Are added to normal time: `Time Normal × (1 + Supplement %/100)`.
2. Result in the standard time for each element.  
This reflects adjusted work conditions.
</details>

## Report Screen

<details>
<summary>How do I generate a report?</summary>

To generate:
1. Go to the report screen for your study.
2. Configure parameters (units, shift duration).
3. Click "Generate Report" to view results.  
All elements must have times and supplements.
</details>

<details>
<summary>What parameters can I configure in a report?</summary>

You can set:
- Time units (seconds, minutes, TMU, etc.).
- Shift duration (minutes).
- Contingency percentage for unforeseen delays.  
These adjust the report’s calculations.
</details>

<details>
<summary>Can I change time units in the report?</summary>

Yes:
1. On the report screen, select from seconds, minutes, MMM, CMM, TMU, or DMH.
2. The report recalculates automatically.  
Conversions are precise per the manual.
</details>

<details>
<summary>How is production per shift calculated?</summary>

It’s calculated as:
1. Shift duration (minutes) divided by cycle time.
2. Adjusted by contingency percentage.  
Results show normal and optimal production.
</details>

<details>
<summary>What does saturation percentage mean?</summary>

Saturation:
1. Shows the percentage of cycle time occupied by operator tasks.
2. Excludes concurrent machine times.  
It reflects workload intensity.
</details>

<details>
<summary>How are machine elements handled in reports?</summary>

Machine elements:
1. Are listed with times and supplements.
2. Concurrent ones (red background) are excluded from saturation and total cycle calculations.  
This isolates operator-focused metrics.
</details>

<details>
<summary>Can I export reports to PDF or Excel?</summary>

Yes:
1. On the report screen, choose "Export to PDF" or "Export to Excel."
2. Download the file with all study data.  
The logo and study details are included.
</details>

<details>
<summary>Is my company logo included in reports?</summary>

Yes:
1. If uploaded in your profile, it appears in exported PDF/Excel reports.  
Upload via the profile’s logo management.
</details>

<details>
<summary>How do I interpret cycle times?</summary>

- **Normal Cycle**: Total time adjusted by frequency, activity, and supplements.
- **Optimal Cycle**: Normal cycle scaled to optimal activity.  
Both are shown in the report’s results.
</details>

<details>
<summary>What’s the difference between normal and optimal cycles?</summary>

- **Normal**: Based on observed activity and pace.
- **Optimal**: Adjusted to the best possible performance (e.g., 133% of normal).  
It’s derived from the study’s activity scale.
</details>

## Profile Screen

<details>
<summary>How do I update my personal information?</summary>

To update:
1. Go to the profile screen.
2. Edit fields like email or name.
3. Save changes.  
Basic data (e.g., registration date) is read-only.
</details>

<details>
<summary>How do I check or change my subscription status?</summary>

In your profile:
1. View your subscription status (active or not).
2. Click options to upgrade, cancel, or request a subscription.  
It’s linked to credit usage limits.
</details>

<details>
<summary>How does the credit system work?</summary>

Credits:
1. Are used for creating/editing studies (unless on unlimited plan).
2. Reset monthly, with extras purchasable.
3. Are tracked in the profile with totals and reset dates.
</details>

<details>
<summary>Can I buy more credits?</summary>

Yes:
1. In your profile, click "Buy Credits."
2. Follow the simplified purchase process.  
Extra credits supplement your monthly allowance.
</details>

<details>
<summary>How do I set my preferred time unit?</summary>

To set:
1. Go to profile preferences.
2. Choose from seconds, minutes, MMM, CMM, TMU, or DMH.
3. Save to apply as default.  
This affects studies and reports.
</details>

<details>
<summary>Can I change the app’s language from my profile?</summary>

Yes:
1. In profile settings, select "Spanish" or "English."
2. The change applies instantly app-wide.  
It uses the internationalization system.
</details>

<details>
<summary>What’s the contingency percentage for?</summary>

It’s:
1. A buffer (e.g., 5%) added to cycle times for unexpected delays.
2. Configurable in your profile or report settings.  
It impacts production estimates.
</details>

<details>
<summary>How do I upload my company logo?</summary>

To upload:
1. In your profile, go to "Logo Management."
2. Click "Upload Logo" and select an image.
3. Save to store it.  
The app optimizes it automatically.
</details>

<details>
<summary>Can I manage multiple organizations?</summary>

Yes:
1. In your profile, view your organizations under "Organization Management."
2. Create, join, or edit them via tabs.  
Each has separate study-sharing settings.
</details>

<details>
<summary>How do I add or remove organization members?</summary>

To manage:
1. Select an organization in your profile.
2. Go to "Members."
3. Add via invite or remove members (if permitted).  
Pending requests are also visible.
</details>

## Library Screen

<details>
<summary>How do I search for elements in the library?</summary>

To search:
1. Go to the library screen.
2. Type in the search bar (name, description, study).
3. Results filter in real-time with debounce.  
It shows elements you have access to.
</details>

<details>
<summary>Can I filter library elements?</summary>

Yes:
1. Use filters for study or organization.
2. Apply to narrow down the element list.  
This works alongside the search bar.
</details>

<details>
<summary>How do I select multiple elements for a study?</summary>

To select:
1. In the library, check boxes next to elements.
2. Click "Create Study from Selection."
3. Fill in study details in the modal.  
Selected elements are pre-loaded.
</details>

<details>
<summary>What happens when I create a study from library elements?</summary>

When created:
1. A new study opens with selected elements.
2. Original times, supplements, and settings are preserved.  
You’re redirected to the study page.
</details>

<details>
<summary>Can I average similar elements?</summary>

Yes:
1. Select elements in the library.
2. Go to the "Average" tab.
3. Calculate a new element with averaged times and supplements.  
Save it to the library if desired.
</details>

<details>
<summary>How do I add elements to the library?</summary>

To add:
1. When creating/editing an element in a study, check "Add to Library."
2. Save to store it for reuse.  
It’s then accessible across studies.
</details>

<details>
<summary>How do I identify shared study elements?</summary>

Shared elements:
1. Are labeled with their study or organization origin.  
This appears in the library’s element cards.
</details>

<details>
<summary>Is there a limit to selecting library elements?</summary>

No:
1. You can select as many elements as needed.  
The library supports unlimited selections for new studies.
</details>

<details>
<summary>Can I edit library elements directly?</summary>

No:
1. Library elements are read-only.
2. Add them to a study to edit a copy.  
This preserves the original for consistency.
</details>

<details>
<summary>How does the library ensure consistency?</summary>

It:
1. Stores standardized elements for reuse.
2. Maintains original timings and settings across studies.  
This reduces variability in measurements.
</details>

## General FAQs

<details>
<summary>How do I get started with Cronometras?</summary>

To start:
1. Log in or register.
2. Click "New Study" on the dashboard.
3. Follow the guided steps to define and time elements.  
The app walks you through the process.
</details>

<details>
<summary>Is there a tutorial or guide?</summary>

Yes:
1. Go to the "Documentation" tab in the app.
2. Find a general overview and feature guides.  
The DocsPage also includes a changelog.
</details>

<details>
<summary>Can I use the app on multiple devices?</summary>

Yes:
1. Log in on any device with your credentials.
2. Access your studies and data seamlessly.  
It’s responsive across platforms.
</details>

<details>
<summary>How is my data secured?</summary>

Data security includes:
1. Encryption via Supabase.
2. Row Level Security (RLS) for study access.
3. Authentication token verification.  
Only authorized users see your data.
</details>

<details>
<summary>What support is available?</summary>

Support options:
1. Check the "Help" section or documentation.
2. Contact support via email or in-app links.  
Premium plans may offer enhanced support.
</details>

<details>
<summary>Are there keyboard shortcuts?</summary>

The manual doesn’t list shortcuts, but:
1. Check the documentation for any updates.
2. Common actions use button clicks (e.g., start/stop chronometer).  
Future updates may add shortcuts.
</details>

<details>
<summary>How often is the app updated?</summary>

Updates are:
1. Tracked in the "Changelog" on DocsPage.
2. Released periodically with new features/fixes.  
Check the changelog for specifics.
</details>

<details>
<summary>Can I suggest new features?</summary>

Yes:
1. Use the feedback option (if available) or contact support.
2. Submit your suggestions for consideration.  
The team values user input.
</details>

<details>
<summary>Is there a mobile app?</summary>

Currently:
1. The app is responsive on mobile browsers.
2. A dedicated mobile app is not mentioned but may be in development.  
Use it on any device with a browser.
</details>

<details>
<summary>How do I contact customer support?</summary>

To contact:
1. Find the support email or link in the app (e.g., footer or Help section).
2. Send your query or issue.  
Response times vary by plan.
</details>

---