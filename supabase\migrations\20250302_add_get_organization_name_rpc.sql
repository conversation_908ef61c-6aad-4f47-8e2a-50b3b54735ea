-- Create or replace the get_organization_name function
CREATE OR R<PERSON><PERSON>CE FUNCTION public.get_organization_name(p_organization_id uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_name text;
BEGIN
    -- Get the organization name
    SELECT name INTO v_name
    FROM public.organizations
    WHERE id = p_organization_id;
    
    RETURN COALESCE(v_name, 'Unknown Organization');
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_organization_name(uuid) TO authenticated;

-- Add row level security policy
ALTER FUNCTION public.get_organization_name(uuid) SET search_path = public;