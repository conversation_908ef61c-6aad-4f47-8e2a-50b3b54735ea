-- Política de UPDATE para permitir a los administradores de la organización actualizar solicitudes
-- Esta política permite que:
-- 1. El usuario que creó la solicitud pueda actualizarla
-- 2. Los administradores de la organización puedan actualizar cualquier solicitud para su organización

-- Crear la política
CREATE POLICY "Admins can update join requests" 
ON public.organization_join_requests
FOR UPDATE 
USING (
  -- El usuario es el creador de la solicitud
  (auth.uid() = user_id)
  OR
  -- O el usuario es administrador de la organización
  EXISTS (
    SELECT 1 
    FROM public.organization_members 
    WHERE 
      organization_members.organization_id = organization_join_requests.organization_id
      AND organization_members.user_id = auth.uid()
      AND organization_members.role IN ('admin', 'owner')
  )
);

-- Habilitar RLS si aún no está habilitado
ALTER TABLE public.organization_join_requests ENABLE ROW LEVEL SECURITY;

-- Instrucciones para implementar esta política en Supabase:
-- 1. Ve al panel de administración de Supabase
-- 2. Navega a "Authentication" > "Policies"
-- 3. Selecciona la tabla "organization_join_requests"
-- 4. Haz clic en "New Policy"
-- 5. Selecciona "Create a policy from scratch"
-- 6. Nombre de la política: "Admins can update join requests"
-- 7. Operación: "UPDATE"
-- 8. Expresión USING: Copia la expresión de arriba
-- 9. Haz clic en "Save Policy"
