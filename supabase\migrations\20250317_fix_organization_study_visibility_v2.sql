[vite] connecting... client:495:9
[vite] connected. client:618:15
Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools chunk-67DEV5DB.js:21551:25
Browser language: es-ES i18n.ts:63:9
Detected language: es i18n.ts:64:9
Setting language to: es i18n.ts:66:11
Auth state changed: SIGNED_IN fcbfb210-196d-4a93-9b26-418bef5da2c3 authStore.ts:14:15
Loading studies for user: fcbfb210-196d-4a93-9b26-418bef5da2c3 App.tsx:94:17
Reloading studies on home page navigation App.tsx:103:17
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition. react-router-dom.js:4374:13
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath. react-router-dom.js:4374:13
Loading studies for user: fcbfb210-196d-4a93-9b26-418bef5da2c3 App.tsx:94:17
Reloading studies on home page navigation App.tsx:103:17
Wake lock acquired 2 useWakeLock.ts:8:17
Auth state changed: INITIAL_SESSION fcbfb210-196d-4a93-9b26-418bef5da2c3 authStore.ts:14:15
Usuario autenticado: fcbfb210-196d-4a93-9b26-418bef5da2c3 authStore.ts:174:17
Fetching studies for user: fcbfb210-196d-4a93-9b26-418bef5da2c3 2 studyStore.ts:34:15
Usuario autenticado: fcbfb210-196d-4a93-9b26-418bef5da2c3 authStore.ts:174:17
Fetching studies for user: fcbfb210-196d-4a93-9b26-418bef5da2c3 2 studyStore.ts:34:15
Verificando si podemos acceder a las organizaciones... organizationStore.ts:424:13
User is member of organizations: 
Array [ "4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83" ]
studyStore.ts:47:17
Query filter: user_id.eq.fcbfb210-196d-4a93-9b26-418bef5da2c3 OR organization_id IN (4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83) studyStore.ts:49:17
Verificando si podemos acceder a las organizaciones... organizationStore.ts:424:13
User is member of organizations: 
Array [ "4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83" ]
studyStore.ts:47:17
Query filter: user_id.eq.fcbfb210-196d-4a93-9b26-418bef5da2c3 OR organization_id IN (4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83) studyStore.ts:49:17
User is member of organizations: 
Array [ "4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83" ]
studyStore.ts:47:17
Query filter: user_id.eq.fcbfb210-196d-4a93-9b26-418bef5da2c3 OR organization_id IN (4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83) studyStore.ts:49:17
User is member of organizations: 
Array [ "4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83" ]
studyStore.ts:47:17
Query filter: user_id.eq.fcbfb210-196d-4a93-9b26-418bef5da2c3 OR organization_id IN (4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83) studyStore.ts:49:17
Found studies: 1 studyStore.ts:67:15
Studies data: 
Array [ {…} ]
studyStore.ts:68:15
Found studies: 1 studyStore.ts:67:15
Studies data: 
Array [ {…} ]
studyStore.ts:68:15
Found studies: 1 studyStore.ts:67:15
Studies data: 
Array [ {…} ]
studyStore.ts:68:15
User initialization completed successfully useUserInitialization.ts:70:15
Found studies: 1 studyStore.ts:67:15
Studies data: 
Array [ {…} ]
studyStore.ts:68:15
User initialization completed successfully useUserInitialization.ts:70:15
Fetching organizations for user: fcbfb210-196d-4a93-9b26-418bef5da2c3 2 organizationStore.ts:20:15
Verificando si podemos acceder a las organizaciones... organizationStore.ts:424:13
Verificando si podemos acceder a las organizaciones... organizationStore.ts:424:13
Fetching studies for user: fcbfb210-196d-4a93-9b26-418bef5da2c3 2 studyStore.ts:34:15
User is member of organizations: 
Array [ "4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83" ]
studyStore.ts:47:17
Query filter: user_id.eq.fcbfb210-196d-4a93-9b26-418bef5da2c3 OR organization_id IN (4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83) studyStore.ts:49:17
User is member of organizations: 
Array [ "4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83" ]
studyStore.ts:47:17
Query filter: user_id.eq.fcbfb210-196d-4a93-9b26-418bef5da2c3 OR organization_id IN (4bd2c452-8d3d-4d39-9bc4-57cfed1dbd83) studyStore.ts:49:17
Found studies: 1 studyStore.ts:67:15
Studies data: 
Array [ {…} ]
studyStore.ts:68:15
Found studies: 1 studyStore.ts:67:15
Studies data: 
Array [ {…} ]
studyStore.ts:68:15
Error: Promised response from onMessage listener went out of scope 2 background.js:16:8742
