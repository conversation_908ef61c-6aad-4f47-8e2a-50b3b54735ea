import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkEmoji from 'remark-emoji';
import { Header } from '../components/Header';
import { Box, Container, IconButton, Typography, Tabs, Tab, Table, TableBody, TableCell, TableHead, TableRow, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import { ArrowLeft } from 'lucide-react';
import { ArrowDown } from 'lucide-react';
import { FAQ } from '../components/FAQ';

// Importar los archivos markdown
import es_changelog from '../../docs/es/changelog.md?raw';
import es_security from '../../docs/es/security.md?raw';
import en_changelog from '../../docs/en/changelog.md?raw';
import en_security from '../../docs/en/security.md?raw';
import es_faq from '../../docs/es/faq.md?raw';
import en_faq from '../../docs/en/faq.md?raw';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Mapa de contenido por idioma
const contentMap: { [key: string]: { [key: string]: string } } = {
  es: {
    changelog: es_changelog,
    security: es_security,
    faq: es_faq
  },
  en: {
    changelog: en_changelog,
    security: en_security,
    faq: en_faq
  }
};

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const markdownComponents = {
  h1: ({ children }: { children: React.ReactNode }) => (
    <Typography variant="h3" component="h1" sx={{ mb: 4, fontWeight: 'bold' }}>
      {children}
    </Typography>
  ),
  h2: ({ children }: { children: React.ReactNode }) => (
    <Typography variant="h4" component="h2" sx={{ mt: 4, mb: 2, fontWeight: 'bold' }}>
      {children}
    </Typography>
  ),
  h3: ({ children }: { children: React.ReactNode }) => (
    <Typography variant="h5" component="h3" sx={{ mt: 3, mb: 2, color: 'text.secondary' }}>
      {children}
    </Typography>
  ),
  a: ({ node, ...props }) => (
    <a {...props} target="_blank" rel="noopener noreferrer" />
  ),
  table: ({ children }) => (
    <Table sx={{ my: 3 }}>{children}</Table>
  ),
  thead: ({ children }) => (
    <TableHead sx={{ 
      '& th': { 
        fontWeight: 'bold',
        backgroundColor: 'action.hover'
      } 
    }}>{children}</TableHead>
  ),
  tbody: ({ children }) => (
    <TableBody>{children}</TableBody>
  ),
  tr: ({ children }) => (
    <TableRow>{children}</TableRow>
  ),
  td: ({ children }) => (
    <TableCell>{children}</TableCell>
  ),
  th: ({ children }) => (
    <TableCell component="th">{children}</TableCell>
  ),
  details: ({ children }) => (
    <Accordion sx={{ 
      '&:not(:last-child)': { mb: 1 },
      '&:before': { display: 'none' },
      boxShadow: 'none',
      border: '1px solid',
      borderColor: 'divider',
      borderRadius: '8px !important',
      overflow: 'hidden',
      '&.Mui-expanded': {
        margin: '0 0 8px 0'
      }
    }}>
      {children}
    </Accordion>
  ),
  summary: ({ children }) => (
    <AccordionSummary
      expandIcon={<ArrowDown size={20} />}
      sx={{
        '& .MuiAccordionSummary-content': {
          my: 1
        },
        backgroundColor: 'background.default',
        '&:hover': {
          backgroundColor: 'action.hover'
        }
      }}
    >
      <Typography sx={{ fontWeight: 500, fontSize: '1rem' }}>{children}</Typography>
    </AccordionSummary>
  ),
  p: ({ children, ...props }) => {
    const parentDetails = props?.node?.parent?.tagName === 'details';
    if (parentDetails && props?.node?.previousSibling?.tagName === 'summary') {
      return (
        <AccordionDetails sx={{ 
          backgroundColor: 'background.paper',
          px: 3,
          py: 2,
          '& ul, & ol': {
            mt: 1,
            mb: 2,
            pl: 3
          },
          '& li': {
            mb: 1
          }
        }}>
          {children}
        </AccordionDetails>
      );
    }
    return <Typography paragraph>{children}</Typography>;
  }
};

export const ChangelogPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState(0);
  const [markdownContent, setMarkdownContent] = useState({
    changelog: '',
    security: '',
    faq: ''
  });

  useEffect(() => {
    const content = contentMap[i18n.language] || contentMap.en;
    setMarkdownContent({
      changelog: content.changelog,
      security: content.security,
      faq: content.faq
    });
  }, [i18n.language]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Header 
        showSearch={false}
        leftComponent={
          <IconButton onClick={handleBack} color="inherit">
            <ArrowLeft />
          </IconButton>
        }
      >
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          {t('changelog.title', 'Documentación')}
        </Typography>
      </Header>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={selectedTab} 
          onChange={handleTabChange}
          centered
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500
            }
          }}
        >
          <Tab label={t('changelog.tabs.changelog', 'Changelog')} />
          <Tab label={t('changelog.tabs.security', 'Seguridad')} />
          <Tab label={t('changelog.tabs.faq', 'FAQ')} />
        </Tabs>
      </Box>

      <Container maxWidth="md" sx={{ flex: 1, overflow: 'auto' }}>
        <TabPanel value={selectedTab} index={0}>
          <Box sx={{ 
            backgroundColor: 'background.paper',
            borderRadius: 1,
            p: 3,
            '& ul': { pl: 3, mt: 2 },
            '& li': { mb: 1 }
          }}>
            <ReactMarkdown 
              remarkPlugins={[remarkGfm, remarkEmoji]}
              components={markdownComponents}
            >
              {markdownContent.changelog}
            </ReactMarkdown>
          </Box>
        </TabPanel>

        <TabPanel value={selectedTab} index={1}>
          <Box sx={{ 
            backgroundColor: 'background.paper',
            borderRadius: 1,
            p: 3,
            '& ul': { pl: 3, mt: 2 },
            '& li': { mb: 1 },
            '& a': { 
              color: 'primary.main',
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline'
              }
            }
          }}>
            <ReactMarkdown 
              remarkPlugins={[remarkGfm, remarkEmoji]}
              components={markdownComponents}
            >
              {markdownContent.security}
            </ReactMarkdown>
          </Box>
        </TabPanel>

        <TabPanel value={selectedTab} index={2}>
          <Box sx={{
            backgroundColor: 'background.paper',
            borderRadius: 1,
            p: { xs: 2, sm: 3 }
          }}>
            <FAQ />
          </Box>
        </TabPanel>
      </Container>
    </Box>
  );
};
