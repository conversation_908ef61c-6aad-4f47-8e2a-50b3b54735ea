# Cronometras Windsurf Database Structure

## Tables

### user_study_limits
Stores user credit information for time studies.
- `id`: UUID (Primary Key)
- `user_id`: UUID (References auth.users)
- `user_email`: TEXT
- `total_credits`: INTEGER (Default: 10)
- `used_credits`: INTEGER (Default: 0)
- `password_hash`: TEXT (Optional)
- `created_at`: TIMESTAMPTZ
- `updated_at`: TIMESTAMPTZ

### library_elements
Stores reusable elements that can be shared between users.
- `id`: UUID (Primary Key)
- `user_id`: UUID (References auth.users)
- `name`: TEXT
- `description`: TEXT
- `is_shared`: BOOLEAN
- `created_at`: TIMESTAMPTZ
- `updated_at`: TIMESTAMPTZ

### studies
Stores time study data using JSONB for flexibility.
- `id`: UUID (Primary Key)
- `user_id`: UUID (References auth.users)
- `required`: J<PERSON>N<PERSON> (name, company, date, activity_scale)
- `optional`: J<PERSON>NB
- `elements`: JSONB[]
- `time_records`: J<PERSON>NB
- `supplements`: JSONB
- `created_at`: TIMESTAMPTZ
- `updated_at`: TIMESTAMPTZ

### user_devices
Stores registered devices for users.
- `id`: UUID (Primary Key)
- `user_id`: UUID (References auth.users)
- `device_name`: TEXT
- `device_id`: TEXT (Unique)
- `last_used`: TIMESTAMPTZ
- `created_at`: TIMESTAMPTZ
- `updated_at`: TIMESTAMPTZ

## Row Level Security (RLS)

### user_study_limits
- Users can only view their own limits
- Admin (<EMAIL>) and service_role have full access

### library_elements
- Users can view their own and shared elements
- Users can only modify their own elements

### studies
- Users can only access their own studies
- Full CRUD operations on own studies

### user_devices
- Users can only access their own devices
- Full CRUD operations on own devices

## Indexes
- user_id indexes on all tables
- device_id index on user_devices

## Triggers
All tables have automatic updated_at timestamp updates

## Constraints
- Credits check: used_credits <= total_credits
- Required fields check on studies table
- Foreign key constraints to auth.users
- Unique device_id in user_devices
