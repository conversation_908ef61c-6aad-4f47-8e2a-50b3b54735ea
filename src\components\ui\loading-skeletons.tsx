import React from 'react';
import { cn } from '../../lib/utils';

interface SkeletonProps {
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
  speed?: 'slow' | 'normal' | 'fast';
}

const BaseSkeleleton: React.FC<SkeletonProps> = ({ 
  className, 
  variant = 'wave',
  speed = 'normal'
}) => {
  const getAnimationClass = () => {
    switch (variant) {
      case 'pulse':
        return 'animate-pulse';
      case 'wave':
        return 'loading-micro';
      case 'glow':
        return 'glow-effect';
      default:
        return 'loading-micro';
    }
  };

  const getSpeedClass = () => {
    switch (speed) {
      case 'slow':
        return 'animation-duration-[2s]';
      case 'normal':
        return 'animation-duration-[1.5s]';
      case 'fast':
        return 'animation-duration-[1s]';
      default:
        return '';
    }
  };

  return (
    <div
      className={cn(
        'bg-gray-200 rounded-md',
        getAnimationClass(),
        getSpeedClass(),
        className
      )}
    />
  );
};

// Skeleton para texto
export const TextSkeleton: React.FC<{
  lines?: number;
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ lines = 1, className, variant = 'wave' }) => {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }, (_, i) => (
        <BaseSkeleleton
          key={i}
          variant={variant}
          className={cn(
            'h-4',
            i === lines - 1 && lines > 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
};

// Skeleton para tarjetas
export const CardSkeleton: React.FC<{
  hasAvatar?: boolean;
  hasImage?: boolean;
  textLines?: number;
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ 
  hasAvatar = false, 
  hasImage = false, 
  textLines = 3, 
  className,
  variant = 'wave'
}) => {
  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 p-4 space-y-4',
      'enhanced-hover enter-from-bottom',
      className
    )}>
      {/* Header con avatar opcional */}
      <div className="flex items-center space-x-3">
        {hasAvatar && (
          <BaseSkeleleton
            variant={variant}
            className="w-10 h-10 rounded-full flex-shrink-0"
          />
        )}
        <div className="flex-1 space-y-2">
          <BaseSkeleleton variant={variant} className="h-4 w-3/4" />
          <BaseSkeleleton variant={variant} className="h-3 w-1/2" />
        </div>
      </div>

      {/* Imagen opcional */}
      {hasImage && (
        <BaseSkeleleton
          variant={variant}
          className="w-full h-48 rounded-md"
        />
      )}

      {/* Líneas de texto */}
      <div className="space-y-2">
        {Array.from({ length: textLines }, (_, i) => (
          <BaseSkeleleton
            key={i}
            variant={variant}
            className={cn(
              'h-3',
              i === textLines - 1 ? 'w-2/3' : 'w-full'
            )}
          />
        ))}
      </div>

      {/* Botones del footer */}
      <div className="flex space-x-2 pt-2">
        <BaseSkeleleton variant={variant} className="h-8 w-20 rounded-md" />
        <BaseSkeleleton variant={variant} className="h-8 w-16 rounded-md" />
      </div>
    </div>
  );
};

// Skeleton para lista
export const ListSkeleton: React.FC<{
  items?: number;
  hasAvatar?: boolean;
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ items = 5, hasAvatar = true, className, variant = 'wave' }) => {
  return (
    <div className={cn('space-y-4 stagger-children', className)}>
      {Array.from({ length: items }, (_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-100">
          {hasAvatar && (
            <BaseSkeleleton
              variant={variant}
              className="w-12 h-12 rounded-full flex-shrink-0"
            />
          )}
          <div className="flex-1 space-y-2">
            <BaseSkeleleton variant={variant} className="h-4 w-3/4" />
            <BaseSkeleleton variant={variant} className="h-3 w-1/2" />
          </div>
          <BaseSkeleleton variant={variant} className="h-8 w-16 rounded-md" />
        </div>
      ))}
    </div>
  );
};

// Skeleton para tabla
export const TableSkeleton: React.FC<{
  rows?: number;
  columns?: number;
  hasHeader?: boolean;
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ 
  rows = 5, 
  columns = 4, 
  hasHeader = true, 
  className,
  variant = 'wave'
}) => {
  return (
    <div className={cn('bg-white rounded-lg border border-gray-200 overflow-hidden', className)}>
      {hasHeader && (
        <div className="border-b border-gray-200 p-4 bg-gray-50">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }, (_, i) => (
              <BaseSkeleleton key={i} variant={variant} className="h-4 w-full" />
            ))}
          </div>
        </div>
      )}
      <div className="divide-y divide-gray-200">
        {Array.from({ length: rows }, (_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }, (_, colIndex) => (
                <BaseSkeleleton
                  key={colIndex}
                  variant={variant}
                  className={cn(
                    'h-4',
                    colIndex === 0 ? 'w-3/4' : 'w-full'
                  )}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Skeleton para formulario
export const FormSkeleton: React.FC<{
  fields?: number;
  hasButtons?: boolean;
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ fields = 4, hasButtons = true, className, variant = 'wave' }) => {
  return (
    <div className={cn('bg-white rounded-lg border border-gray-200 p-6 space-y-6', className)}>
      {/* Título del formulario */}
      <div className="space-y-2">
        <BaseSkeleleton variant={variant} className="h-6 w-1/3" />
        <BaseSkeleleton variant={variant} className="h-4 w-2/3" />
      </div>

      {/* Campos del formulario */}
      <div className="space-y-4">
        {Array.from({ length: fields }, (_, i) => (
          <div key={i} className="space-y-2">
            <BaseSkeleleton variant={variant} className="h-4 w-1/4" />
            <BaseSkeleleton variant={variant} className="h-10 w-full rounded-md" />
          </div>
        ))}
      </div>

      {/* Botones */}
      {hasButtons && (
        <div className="flex space-x-3 pt-4">
          <BaseSkeleleton variant={variant} className="h-10 w-24 rounded-md" />
          <BaseSkeleleton variant={variant} className="h-10 w-20 rounded-md" />
        </div>
      )}
    </div>
  );
};

// Skeleton para dashboard/estadísticas
export const StatsSkeleton: React.FC<{
  items?: number;
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ items = 4, className, variant = 'wave' }) => {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 stagger-children', className)}>
      {Array.from({ length: items }, (_, i) => (
        <div key={i} className="bg-white rounded-lg border border-gray-200 p-6 space-y-4">
          <div className="flex items-center space-x-3">
            <BaseSkeleleton variant={variant} className="w-8 h-8 rounded-md" />
            <BaseSkeleleton variant={variant} className="h-4 w-20" />
          </div>
          <BaseSkeleleton variant={variant} className="h-8 w-16" />
          <BaseSkeleleton variant={variant} className="h-3 w-full" />
        </div>
      ))}
    </div>
  );
};

// Skeleton para perfil de usuario
export const ProfileSkeleton: React.FC<{
  className?: string;
  variant?: 'pulse' | 'wave' | 'glow';
}> = ({ className, variant = 'wave' }) => {
  return (
    <div className={cn('bg-white rounded-lg border border-gray-200 p-6 space-y-6', className)}>
      {/* Header del perfil */}
      <div className="flex items-center space-x-4">
        <BaseSkeleleton variant={variant} className="w-16 h-16 rounded-full" />
        <div className="flex-1 space-y-2">
          <BaseSkeleleton variant={variant} className="h-6 w-1/3" />
          <BaseSkeleleton variant={variant} className="h-4 w-1/2" />
        </div>
      </div>

      {/* Información del perfil */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }, (_, i) => (
            <div key={i} className="space-y-2">
              <BaseSkeleleton variant={variant} className="h-4 w-1/3" />
              <BaseSkeleleton variant={variant} className="h-8 w-full rounded-md" />
            </div>
          ))}
        </div>
      </div>

      {/* Acciones */}
      <div className="flex space-x-3 pt-4">
        <BaseSkeleleton variant={variant} className="h-10 w-24 rounded-md" />
        <BaseSkeleleton variant={variant} className="h-10 w-20 rounded-md" />
      </div>
    </div>
  );
};

// Skeleton con efecto de carga progresiva
export const ProgressiveSkeleton: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}> = ({ isLoading, children, fallback, className }) => {
  if (!isLoading) return <>{children}</>;

  return (
    <div className={cn('space-y-4 enter-fade-in', className)}>
      {fallback || <CardSkeleton variant="wave" />}
    </div>
  );
}; 