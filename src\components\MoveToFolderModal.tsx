import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Folder, Home } from 'lucide-react';
import { Button } from './ui/button';
import { FolderTreeNode } from '../types/folder';

interface MoveToFolderModalProps {
  isOpen: boolean;
  studyName: string;
  folders: FolderTreeNode[];
  currentFolderId?: string | null;
  onClose: () => void;
  onMove: (folderId?: string) => Promise<void>;
}

export const MoveToFolderModal: React.FC<MoveToFolderModalProps> = ({
  isOpen,
  studyName,
  folders,
  currentFolderId,
  onClose,
  onMove
}) => {
  const { t } = useTranslation(['common', 'study']);
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(currentFolderId || null);
  const [isLoading, setIsLoading] = useState(false);

  const handleMove = async () => {
    setIsLoading(true);
    try {
      await onMove(selectedFolderId || undefined);
      onClose();
    } catch (error) {
      console.error('Error moving study:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderFolderTree = (folderList: FolderTreeNode[], level = 0): JSX.Element[] => {
    return folderList.map((folder) => (
      <div key={folder.id}>
        <div 
          className={`
            flex items-center gap-2 p-2 hover:bg-gray-50 cursor-pointer rounded-md
            ${selectedFolderId === folder.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''}
          `}
          style={{ paddingLeft: `${12 + level * 16}px` }}
          onClick={() => setSelectedFolderId(folder.id)}
        >
          <Folder 
            className="h-5 w-5" 
            style={{ color: folder.color }} 
          />
          <span className="text-sm font-medium">{folder.name}</span>
          
          {folder.study_count && folder.study_count > 0 && (
            <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
              {folder.study_count}
            </span>
          )}
        </div>
        
        {/* Render children */}
        {folder.children && folder.children.length > 0 && (
          <div>
            {renderFolderTree(folder.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900">
            {t('common:moveToFolder')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            aria-label={t('common:close')}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-y-auto">
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              {t('study:movingStudy')}: <strong>{studyName}</strong>
            </p>
          </div>

          {/* Root option */}
          <div 
            className={`
              flex items-center gap-2 p-2 hover:bg-gray-50 cursor-pointer rounded-md mb-2
              ${selectedFolderId === null ? 'bg-blue-50 border-l-4 border-blue-500' : ''}
            `}
            onClick={() => setSelectedFolderId(null)}
          >
            <Home className="h-5 w-5 text-gray-600" />
            <span className="text-sm font-medium">{t('common:noFolder')}</span>
          </div>

          {/* Folder tree */}
          <div className="space-y-1">
            {folders.length === 0 ? (
              <p className="text-sm text-gray-500 italic">
                {t('common:noFoldersAvailable')}
              </p>
            ) : (
              renderFolderTree(folders)
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-2 p-4 border-t">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {t('common:cancel')}
          </Button>
          <Button
            onClick={handleMove}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? t('common:moving') : t('common:move')}
          </Button>
        </div>
      </div>
    </div>
  );
}; 