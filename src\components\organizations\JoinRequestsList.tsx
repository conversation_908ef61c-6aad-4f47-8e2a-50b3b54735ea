import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export const JoinRequestsList: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { 
    joinRequests, 
    currentOrganization, 
    isLoading, 
    approveJoinRequest, 
    rejectJoinRequest 
  } = useOrganizationStore();

  const handleApprove = async (requestId: string) => {
    try {
      await approveJoinRequest(requestId);
      
      toast({
        title: t('joinRequests.approved'),
        description: t('joinRequests.approvedDescription'),
      });
    } catch (error) {
      toast({
        title: t('errors.processRequest'),
        description: error instanceof Error ? error.message : t('errors.processRequest'),
        variant: 'destructive',
      });
    }
  };

  const handleReject = async (requestId: string) => {
    try {
      await rejectJoinRequest(requestId);
      
      toast({
        title: t('joinRequests.rejected'),
        description: t('joinRequests.rejectedDescription'),
      });
    } catch (error) {
      toast({
        title: t('errors.processRequest'),
        description: error instanceof Error ? error.message : t('errors.processRequest'),
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!joinRequests.length) {
    return (
      <p className="text-gray-500">{t('joinRequests.noRequests')}</p>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.email')}
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.joinDate')}
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.actions')}
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {joinRequests.map((request) => (
            <tr key={request.id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{request.user_email}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {format(new Date(request.created_at), 'PPP', { locale: es })}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex space-x-2">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleApprove(request.id)}
                  >
                    {t('joinRequests.approve')}
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleReject(request.id)}
                  >
                    {t('joinRequests.reject')}
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
