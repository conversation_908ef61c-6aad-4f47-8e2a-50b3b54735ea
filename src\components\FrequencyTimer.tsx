import React from 'react';
import { useTranslation } from 'react-i18next';
import { Play, Pause, Square, ArrowUp, ArrowDown } from 'lucide-react';
import { WorkElement } from '../types';
import { formatTime } from '../utils/time';
import { playSoundAndVibrate, initializeAudioContext } from '../utils/sounds';

interface FrequencyTimerProps {
  element: WorkElement;
  time: number;
  activity: number;
  isRunning: boolean;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onActivityChange: (increment: boolean) => void;
}

export const FrequencyTimer: React.FC<FrequencyTimerProps> = ({
  element,
  time,
  activity,
  isRunning,
  onStart,
  onPause,
  onStop,
  onActivityChange
}) => {
  const { t } = useTranslation(['frequency', 'common']);

  React.useEffect(() => {
    // Initialize AudioContext on first interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener('click', handleFirstInteraction);
    };
    document.addEventListener('click', handleFirstInteraction);
    return () => {
      document.removeEventListener('click', handleFirstInteraction);
    };
  }, []);

  const formatFrequency = (element: WorkElement) => {
    return `${element.frequency_repetitions} ${t('repetitions', { ns: 'frequency' })} ${t('each', { ns: 'common' })} ${element.frequency_cycles} ${t('cycles', { ns: 'frequency' })}`;
  };

  const handleStart = async () => {
    await playSoundAndVibrate('start', 200);
    onStart();
  };

  const handlePause = async () => {
    await playSoundAndVibrate('pause', 200);
    onPause();
  };

  const handleStop = async () => {
    await playSoundAndVibrate('stop', 200);
    onStop();
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
      <h2 className="text-xl font-bold mb-4">{element.description}</h2>
      <p className="text-gray-600 mb-6">{formatFrequency(element)}</p>

      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => onActivityChange(false)}
          className="p-2 rounded-full hover:bg-gray-100"
          title={t('decreaseActivity', { ns: 'frequency' })}
        >
          <ArrowDown className="w-8 h-8" />
        </button>
        
        <div className="text-4xl font-mono font-bold">
          {formatTime(time)}
        </div>

        <button
          onClick={() => onActivityChange(true)}
          className="p-2 rounded-full hover:bg-gray-100"
          title={t('increaseActivity', { ns: 'frequency' })}
        >
          <ArrowUp className="w-8 h-8" />
        </button>
      </div>

      <div className="text-center mb-6">
        <span className="text-2xl font-bold">{activity}</span>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={handleStop}
          disabled={!isRunning}
          className="p-4 rounded-lg bg-red-500 text-white font-bold disabled:opacity-50"
          title={t('stop', { ns: 'frequency' })}
        >
          <Square className="mx-auto" />
        </button>
        <button
          onClick={isRunning ? handlePause : handleStart}
          className={`p-4 rounded-lg text-white font-bold
            ${isRunning ? 'bg-yellow-500' : 'bg-green-500'}`}
          title={isRunning ? t('pause', { ns: 'frequency' }) : t('start', { ns: 'frequency' })}
        >
          {isRunning ? <Pause className="mx-auto" /> : <Play className="mx-auto" />}
        </button>
      </div>
    </div>
  );
};