import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface SpeechToTextProps {
  onResult: (text: string) => void;
  onError?: (error: string) => void;
}

export const SpeechToText: React.FC<SpeechToTextProps> = ({ onResult, onError }) => {
  const { i18n } = useTranslation();
  const [isListening, setIsListening] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);

  useEffect(() => {
    // Check browser support
    if (!('webkitSpeechRecognition' in window)) {
      onError?.('Speech recognition not supported');
      return;
    }

    // Create speech recognition instance
    const SpeechRecognition = window.webkitSpeechRecognition;
    const recognitionInstance = new SpeechRecognition();

    // Configure recognition
    recognitionInstance.continuous = false;
    recognitionInstance.interimResults = false;
    recognitionInstance.lang = i18n.language === 'es' ? 'es-ES' : 'en-US';

    // Event handlers
    recognitionInstance.onstart = () => {
      setIsListening(true);
    };

    recognitionInstance.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      onResult(transcript);
      setIsListening(false);
    };

    recognitionInstance.onerror = (event) => {
      onError?.(event.error);
      setIsListening(false);
    };

    recognitionInstance.onend = () => {
      setIsListening(false);
    };

    setRecognition(recognitionInstance);

    // Cleanup
    return () => {
      recognitionInstance.stop();
    };
  }, [i18n.language]);

  const startListening = () => {
    if (recognition) {
      // Update language before starting
      recognition.lang = i18n.language === 'es' ? 'es-ES' : 'en-US';
      recognition.start();
    }
  };

  const stopListening = () => {
    if (recognition) {
      recognition.stop();
    }
  };

  return (
    <div>
      <button 
        onClick={startListening}
        disabled={isListening}
        className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
        </svg>
        <span>{isListening ? 'Listening...' : 'Speak'}</span>
      </button>
    </div>
  );
};
