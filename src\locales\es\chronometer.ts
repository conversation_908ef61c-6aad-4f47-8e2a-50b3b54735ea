export default {
  repetitiveElements: 'Elementos Repetitivos',
  start: 'Iniciar',
  stop: 'Detener',
  reset: 'Reiniciar',
  lap: 'Vuelta',
  element: 'Elemento',
  time: 'Tiempo',
  rating: 'Valoración',
  observations: 'Observaciones',
  cycle: '<PERSON><PERSON><PERSON>',
  currentElement: 'Elemento Actual',
  nextElement: 'Guardar vuelta',
  previousElement: 'Elemento Anterior',
  totalTime: 'Tiempo Total',
  averageTime: 'Tiempo Medio',
  standardTime: 'Tiempo Estándar',
  normalTime: 'Tiempo Normal',
  cycles: 'Ciclos',
  saveReading: 'Guardar Lectura',
  discardReading: 'Descartar Lectura',
  confirmDiscard: '¿Está seguro de que desea descartar esta lectura?',
  readingSaved: 'Lectura guardada correctamente',
  readingError: 'Error al guardar la lectura',
  noReadings: 'No hay lecturas disponibles',
  loadError: 'Error al cargar las lecturas',
  description: 'Descripción',
  noStudySelected: 'No hay estudio seleccionado',
  takes: 'Tomas',
  averageActivity: 'Actividad Media',
  remainingTakes: 'Tomas Restantes',
  addComment: 'Añadir Comentario',
  editRecord: 'Editar Registro',
  activity: 'Actividad',
  comment: 'Comentario',
  deleteAllRecords: 'Eliminar Todos los Tiempos',
  deleteAllRecordsConfirmation: '¿Está seguro de que desea eliminar todos los tiempos de este elemento? Esta acción no se puede deshacer.',
  increaseActivity: 'Aumentar Actividad',
  decreaseActivity: 'Disminuir Actividad',
  addVoiceDescription: 'Añadir descripción por voz',
  clearDescription: 'Borrar descripción',
  voiceInputTip: 'Hable para añadir una descripción',
  descriptionPlaceholder: 'Descripción del elemento o actividad...',
  frequency: 'Frecuencia',
  repetitions: 'Repeticiones',
  error: {
    savingRecord: 'Error al guardar el registro',
    updatingRecord: 'Error al actualizar el registro',
    deletingRecord: 'Error al eliminar el registro',
    deletingAllRecords: 'Error al eliminar todos los registros'
  }
};
