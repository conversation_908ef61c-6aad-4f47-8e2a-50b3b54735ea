-- Migración para agregar carpeta por defecto al perfil de usuario
-- Archivo: 20250130_add_default_folder_preference.sql

-- Agregar campo default_folder_id a la tabla profiles
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS default_folder_id UUID REFERENCES public.folders(id) ON DELETE SET NULL;

-- <PERSON><PERSON><PERSON> índice para mejorar el rendimiento de consultas
CREATE INDEX IF NOT EXISTS idx_profiles_default_folder ON public.profiles(default_folder_id);

-- Comentario explicativo
COMMENT ON COLUMN public.profiles.default_folder_id IS 'Carpeta que se carga por defecto al abrir la aplicación. NULL significa raíz'; 