# Delete Confirm Modal Component

## Descripción
Modal de confirmación para acciones de eliminación.

## Props
```typescript
interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
}
```

## Características
- Overlay con fondo oscurecido
- Animación de entrada/salida
- Botones de confirmación/cancelación
- Títulos y mensajes personalizables
- Cierre al hacer clic fuera

## Accesibilidad
- Foco automático en botón de cancelar
- Manejo de tecla Escape
- Roles ARIA apropiados
- Orden de tabulación correcto

## Uso
```tsx
<DeleteConfirmModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  onConfirm={handleDelete}
  title="Confirmar Eliminación"
  message="¿Está seguro de eliminar este elemento?"
/>
```