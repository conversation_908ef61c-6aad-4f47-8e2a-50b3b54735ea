import React from 'react';
import { useTranslation } from 'react-i18next';
import { ElementInstance } from '../../types';

interface DataSupplementsTableProps {
  elements: ElementInstance[];
  stats: any[]; // Adjust the type according to your stats structure
}

export const DataSupplementsTable: React.FC<DataSupplementsTableProps> = ({ elements, stats }) => {
  const { t } = useTranslation(['supplements']);

  const getElementStat = (elementId: string) => {
    return stats.find(stat => stat.elementId === elementId);
  };

  if (elements.length === 0) {
    return <p className="text-gray-500 text-sm">{t('machineSupplements.noElementsOfType')}</p>;
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full text-sm text-left text-gray-700">
        <thead className="bg-gray-100 text-xs text-gray-700 uppercase">
          <tr>
            <th scope="col" className="px-4 py-2">{t('machineSupplements.element')}</th>
            <th scope="col" className="px-4 py-2 text-right">{t('machineSupplements.normalizedTime')}</th>
            <th scope="col" className="px-4 py-2 text-right">{t('machineSupplements.activity')}</th>
          </tr>
        </thead>
        <tbody>
          {elements.map((element) => {
            const stat = getElementStat(element.id);
            if (!stat) return null;

            return (
              <tr key={element.id} className="bg-white border-b hover:bg-gray-50">
                <td className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
                  {element.name}
                </td>
                <td className="px-4 py-2 text-right">
                  {stat.normalizedTime ? stat.normalizedTime.toFixed(3) : 'N/A'}s
                </td>
                <td className="px-4 py-2 text-right">
                  {stat.averageActivity ? stat.averageActivity.toFixed(0) : 'N/A'}%
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}; 