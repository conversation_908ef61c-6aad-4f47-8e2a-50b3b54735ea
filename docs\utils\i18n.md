# i18n Configuration

## Descripción
Configuración de internacionalización.

## Recursos
```typescript
const resources = {
  es: {
    translation: {
      appName: 'Estudio de Tiempos',
      // ... más traducciones
    }
  }
};
```

## Configuración
```typescript
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'es',
    fallbackLng: 'es',
    interpolation: {
      escapeValue: false
    }
  });
```

## Uso
```typescript
// En componentes
const { t } = useTranslation();
t('appName'); // "Estudio de Tiempos"

// Cambiar idioma
i18n.changeLanguage('es');
```