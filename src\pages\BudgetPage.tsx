import React from 'react';
import { Timer, BarChart3, LineChart, Shield, HeadphonesIcon } from 'lucide-react';

export const BudgetPage: React.FC = () => {
  const plans = [
    {
      name: 'Mensual',
      price: '100€',
      period: '/mes'
    },
    {
      name: 'Anual',
      price: '800€',
      period: '/año',
      savings: 'Ahorra 400€ al año'
    }
  ];

  const features = [
    {
      title: 'Automatización de Informes',
      benefit: 'Reduzca el tiempo de análisis de datos hasta en un 50%, permitiendo a sus analistas realizar más estudios y tomar decisiones más rápidas.',
      icon: <BarChart3 className="w-6 h-6" />
    },
    {
      title: 'Tiempos Estándar',
      benefit: 'Aumente la eficiencia operativa estableciendo métricas claras y medibles para cada proceso, mejorando la productividad hasta un 30%.',
      icon: <Timer className="w-6 h-6" />
    },
    {
      title: 'Análisis en Tiempo Real',
      benefit: 'Tome decisiones informadas al instante con dashboards actualizados en tiempo real, optimizando recursos y reduciendo costes operativos.',
      icon: <LineChart className="w-6 h-6" />
    },
    {
      title: 'Seguridad Avanzada',
      benefit: 'Proteja sus datos críticos con cifrado de nivel empresarial y control de acceso granular.',
      icon: <Shield className="w-6 h-6" />
    },
    {
      title: 'Soporte Premium',
      benefit: 'Asistencia técnica prioritaria para garantizar la continuidad de sus operaciones.',
      icon: <HeadphonesIcon className="w-6 h-6" />
    }
  ];

  const faqs = [
    {
      question: '¿Qué incluye la suscripción?',
      answer: 'La suscripción incluye acceso completo a todas las funcionalidades de CRONOMETRAS, incluyendo medición de tiempos, análisis de datos, generación de informes, y soporte técnico. También incluye todas las actualizaciones y mejoras del sistema.'
    },
    {
      question: '¿Necesito instalar algún software especial?',
      answer: 'No, CRONOMETRAS es una aplicación web que funciona directamente desde tu navegador. No requiere instalación y es compatible con todos los dispositivos modernos.'
    },
    {
      question: '¿Puedo cambiar de plan en cualquier momento?',
      answer: 'Sí, puedes cambiar entre el plan mensual y anual en cualquier momento. Si cambias a un plan anual, se te aplicará el descuento inmediatamente.'
    },
    {
      question: '¿Cómo funciona el soporte técnico?',
      answer: 'Ofrecemos soporte técnico por correo electrónico durante horario laboral. Los clientes con suscripción tienen acceso prioritario al soporte técnico.'
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8 print:py-4">
      <h1 className="text-4xl font-bold text-center mb-4">
        CRONOMETRAS
      </h1>
      <p className="text-xl text-center text-gray-600 mb-8">
        Solución Profesional para Gestión y Análisis de Tiempos
      </p>

      {/* Planes de Precios */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {plans.map((plan) => (
          <div key={plan.name} className="bg-white rounded-lg shadow-lg p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">
              Plan {plan.name}
            </h2>
            <div className="text-3xl font-bold mb-2">
              {plan.price}
              <span className="text-base text-gray-600 ml-1">
                {plan.period}
              </span>
            </div>
            {plan.savings && (
              <p className="text-green-600 text-sm">
                {plan.savings}
              </p>
            )}
            <button className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
              Seleccionar Plan
            </button>
          </div>
        ))}
      </div>

      {/* Características y Beneficios */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-center mb-8">
          Características y Beneficios
        </h2>
        <div className="space-y-6">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start gap-4 p-4 bg-white rounded-lg shadow">
              <div className="text-blue-600 flex-shrink-0">
                {feature.icon}
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.benefit}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Preguntas Frecuentes */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-center mb-8">
          Preguntas Frecuentes
        </h2>
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
              <p className="text-gray-600">{faq.answer}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Pie de página */}
      <div className="text-center text-gray-600 mt-8">
        <p className="text-sm">
          Contacto: <EMAIL>
        </p>
      </div>
    </div>
  );
};
