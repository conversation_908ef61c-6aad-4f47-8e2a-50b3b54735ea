# Device Utilities

## Descripción
Utilidades para gestión de dispositivos.

## Funciones

### generateDeviceId
```typescript
function generateDeviceId(): string
```
Genera ID único basado en características del dispositivo.

### getPlatformInfo
```typescript
function getPlatformInfo(): {
  platform: string;
  userAgent: string;
  screenInfo: {
    width: number;
    height: number;
    colorDepth: number;
  }
}
```
Obtiene información del dispositivo.

## Uso
```typescript
// Generar ID de dispositivo
const deviceId = generateDeviceId();

// Obtener info de plataforma
const { platform, screenInfo } = getPlatformInfo();
```