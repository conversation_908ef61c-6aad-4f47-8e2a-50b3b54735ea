import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Folder, FolderTreeNode, FolderStats, MoveItemRequest, BulkExportRequest, StudyWithFolder } from '../types/folder';
import { Study } from '../types/index';
import { toast } from '../components/ui/use-toast';
import { 
  getFoldersWithStats, 
  buildFolderTree, 
  countFolderStudies, 
  getFolderPath,
  getFolderBreadcrumbs,
  validateFolderMove
} from '../utils/folderUtils';

interface FolderState {
  folders: Folder[];
  folderTree: FolderTreeNode[];
  currentFolder: Folder | null;
  breadcrumbs: Array<{ id: string; name: string }>;
  isLoading: boolean;
  error: string | null;

  // CRUD Operations
  fetchFolders: () => Promise<void>;
  createFolder: (folder: Partial<Folder>) => Promise<Folder>;
  updateFolder: (id: string, updates: Partial<Folder>) => Promise<void>;
  deleteFolder: (id: string) => Promise<void>;
  deleteFolderWithContent: (id: string, action: 'move' | 'delete', targetFolderId?: string) => Promise<void>;
  deleteFolderRecursively: (id: string) => Promise<void>;
  
  // Navigation
  setCurrentFolder: (folder: Folder | null) => void;
  navigateToFolder: (folderId: string | null) => Promise<void>;
  
  // Tree operations
  buildFolderTree: () => void;
  expandFolder: (folderId: string) => void;
  collapseFolder: (folderId: string) => void;
  
  // Study management
  moveStudyToFolder: (studyId: string, folderId?: string) => Promise<void>;
  moveFolderToFolder: (folderId: string, targetFolderId?: string) => Promise<void>;
  getStudiesInFolder: (folderId?: string, includeSubfolders?: boolean) => Promise<StudyWithFolder[]>;
  
  // Bulk operations
  bulkExportFolderReports: (request: BulkExportRequest) => Promise<void>;
  
  // Stats
  getFolderStats: (folderId: string) => Promise<FolderStats>;
  
  // Utility
  resetError: () => void;
}

export const useFolderStore = create<FolderState>((set, get) => ({
  folders: [],
  folderTree: [],
  currentFolder: null,
  breadcrumbs: [],
  isLoading: false,
  error: null,

  fetchFolders: async () => {
    console.log('📁 FolderStore: fetchFolders called');
    set({ isLoading: true, error: null });
    try {
      const { data: session } = await supabase.auth.getSession();
      console.log('📁 FolderStore: Session user ID:', session?.session?.user?.id);
      
      if (!session?.session?.user?.id) {
        throw new Error('No hay sesión activa');
      }

      // Usar función local en lugar de función SQL
      console.log('📁 FolderStore: Calling getFoldersWithStats...');
      const foldersWithStats = await getFoldersWithStats();
      console.log('📁 FolderStore: Folders fetched:', foldersWithStats);

      set({ folders: foldersWithStats, isLoading: false });
      get().buildFolderTree();
      console.log('📁 FolderStore: Folder tree built');
    } catch (error) {
      console.error('📁 FolderStore: Error fetching folders:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Error al cargar carpetas',
        isLoading: false 
      });
    }
  },

  createFolder: async (folderData) => {
    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session?.session?.user?.id) {
        throw new Error('No hay sesión activa');
      }

      const { data, error } = await supabase
        .from('folders')
        .insert({
          ...folderData,
          user_id: session.session.user.id
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Carpeta creada",
        description: `La carpeta "${data.name}" se ha creado exitosamente.`
      });

      // Refresh folders
      await get().fetchFolders();
      return data;
    } catch (error) {
      console.error('Error creating folder:', error);
      const message = error instanceof Error ? error.message : 'Error al crear carpeta';
      toast({
        title: "Error",
        description: message,
        variant: "destructive"
      });
      throw error;
    }
  },

  updateFolder: async (id, updates) => {
    try {
      console.log('📁 updateFolder called with:', { id, updates });
      console.log('📁 parent_folder_id value:', updates.parent_folder_id);
      console.log('📁 parent_folder_id type:', typeof updates.parent_folder_id);
      
      const { error } = await supabase
        .from('folders')
        .update(updates)
        .eq('id', id);

      if (error) {
        console.error('📁 updateFolder error:', error);
        throw error;
      }

      console.log('📁 updateFolder successful for folder:', id);

      toast({
        title: "Carpeta actualizada",
        description: "Los cambios se han guardado exitosamente."
      });

      // Refresh folders
      await get().fetchFolders();
    } catch (error) {
      console.error('Error updating folder:', error);
      const message = error instanceof Error ? error.message : 'Error al actualizar carpeta';
      toast({
        title: "Error",
        description: message,
        variant: "destructive"
      });
      throw error;
    }
  },

  deleteFolder: async (id) => {
    try {
      console.log('🗑️ Attempting to delete folder:', id);
      
      // Check if folder exists first
      const { data: folderExists, error: folderCheckError } = await supabase
        .from('folders')
        .select('id, name')
        .eq('id', id)
        .single();

      if (folderCheckError) {
        console.error('Error checking if folder exists:', folderCheckError);
        throw new Error('La carpeta no existe o no se puede acceder a ella');
      }

      console.log('🗑️ Folder exists:', folderExists?.name);
      
      // Check if folder has children or studies
      const { data: children, error: childrenError } = await supabase
        .from('folders')
        .select('id')
        .eq('parent_folder_id', id);

      const { data: studies, error: studiesError } = await supabase
        .from('studies')
        .select('id')
        .eq('folder_id', id);

      if (childrenError) {
        console.error('Error checking children:', childrenError);
        throw childrenError;
      }

      if (studiesError) {
        console.error('Error checking studies:', studiesError);
        throw studiesError;
      }

      console.log('🗑️ Folder contents check:', { 
        children: children?.length || 0, 
        studies: studies?.length || 0 
      });

      if ((children && children.length > 0) || (studies && studies.length > 0)) {
        console.log('🗑️ Folder has content, throwing FOLDER_HAS_CONTENT error');
        throw new Error('FOLDER_HAS_CONTENT');
      }

      console.log('🗑️ Folder is empty, proceeding with deletion');

      // Delete the folder
      const { error, count } = await supabase
        .from('folders')
        .delete({ count: 'exact' })
        .eq('id', id);

      if (error) {
        console.error('Error deleting folder:', error);
        throw error;
      }

      console.log('🗑️ Deletion result:', { count, error });

      if (count === 0) {
        console.warn('🗑️ No rows were deleted, folder may not exist');
        throw new Error('La carpeta no se pudo eliminar. Puede que ya haya sido eliminada.');
      }

      console.log('🗑️ Folder deleted successfully, count:', count);

      toast({
        title: "Carpeta eliminada",
        description: "La carpeta se ha eliminado exitosamente."
      });

      // Refresh folders
      await get().fetchFolders();
    } catch (error) {
      console.error('Error deleting folder:', error);
      const message = error instanceof Error ? error.message : 'Error al eliminar carpeta';
      
      // Don't show toast for the special "has content" error
      if (message !== 'FOLDER_HAS_CONTENT') {
        toast({
          title: "Error",
          description: message,
          variant: "destructive"
        });
      }
      throw error;
    }
  },

  deleteFolderWithContent: async (id: string, action: 'move' | 'delete', targetFolderId?: string) => {
    try {
      console.log('🗑️ Deleting folder with content:', { id, action, targetFolderId });

      if (action === 'move') {
        // First, move all studies to target folder
        console.log('🗑️ Moving studies from folder:', id);
        const { data: movedStudies, error: studiesError } = await supabase
          .from('studies')
          .update({ folder_id: targetFolderId || null })
          .eq('folder_id', id)
          .select('id');

        if (studiesError) {
          console.error('Error moving studies:', studiesError);
          throw studiesError;
        }

        console.log('🗑️ Studies moved:', movedStudies?.length || 0);

        // Then, move all subfolders to target folder
        console.log('🗑️ Moving subfolders from folder:', id);
        const { data: movedFolders, error: foldersError } = await supabase
          .from('folders')
          .update({ parent_folder_id: targetFolderId || null })
          .eq('parent_folder_id', id)
          .select('id');

        if (foldersError) {
          console.error('Error moving subfolders:', foldersError);
          throw foldersError;
        }

        console.log('🗑️ Subfolders moved:', movedFolders?.length || 0);

        // Wait a moment for database to fully process the updates
        await new Promise(resolve => setTimeout(resolve, 500));

        // Verify the folder is now empty before deletion
        const { data: remainingStudies } = await supabase
          .from('studies')
          .select('id')
          .eq('folder_id', id);

        const { data: remainingSubfolders } = await supabase
          .from('folders')
          .select('id')
          .eq('parent_folder_id', id);

        console.log('🗑️ Verification after move - remaining:', {
          studies: remainingStudies?.length || 0,
          subfolders: remainingSubfolders?.length || 0
        });

        if ((remainingStudies && remainingStudies.length > 0) || 
            (remainingSubfolders && remainingSubfolders.length > 0)) {
          throw new Error('No se pudo mover todo el contenido. Algunos elementos permanecen en la carpeta.');
        }

        console.log('🗑️ Content moved successfully, folder is now empty');
      } else if (action === 'delete') {
        // Recursively delete all content
        await get().deleteFolderRecursively(id);
        console.log('🗑️ All content deleted recursively');
        
        // Early return since deleteFolderRecursively already deletes the folder
        toast({
          title: "Carpeta eliminada",
          description: "La carpeta y todo su contenido se han eliminado exitosamente."
        });
        await get().fetchFolders();
        return;
      }

      // Finally, delete the folder itself
      console.log('🗑️ Attempting to delete empty folder:', id);
      const { error } = await supabase
        .from('folders')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting folder:', error);
        throw error;
      }

      console.log('🗑️ Folder deleted successfully');

      toast({
        title: "Carpeta eliminada",
        description: action === 'move' 
          ? "La carpeta se ha eliminado y su contenido se ha movido exitosamente."
          : "La carpeta se ha eliminado exitosamente."
      });

      // Refresh folders
      await get().fetchFolders();
    } catch (error) {
      console.error('Error deleting folder with content:', error);
      const message = error instanceof Error ? error.message : 'Error al eliminar carpeta';
      toast({
        title: "Error",
        description: message,
        variant: "destructive"
      });
      throw error;
    }
  },

  deleteFolderRecursively: async (id) => {
    try {
      // Get all subfolders
      const { data: subfolders } = await supabase
        .from('folders')
        .select('id')
        .eq('parent_folder_id', id);

      // Recursively delete subfolders
      if (subfolders && subfolders.length > 0) {
        for (const subfolder of subfolders) {
          await get().deleteFolderRecursively(subfolder.id);
        }
      }

      // Delete all studies in this folder
      const { error: studiesError } = await supabase
        .from('studies')
        .delete()
        .eq('folder_id', id);

      if (studiesError) throw studiesError;

      // Delete the folder itself
      const { error: folderError } = await supabase
        .from('folders')
        .delete()
        .eq('id', id);

      if (folderError) throw folderError;

      console.log('🗑️ Folder and all content deleted recursively:', id);
    } catch (error) {
      console.error('Error in recursive folder deletion:', error);
      throw error;
    }
  },

  setCurrentFolder: (folder) => {
    set({ currentFolder: folder });
    
    // Build breadcrumbs
    const breadcrumbs = [];
    let current = folder;
    const { folders } = get();
    
    while (current) {
      breadcrumbs.unshift({ id: current.id, name: current.name });
      current = folders.find(f => f.id === current?.parent_folder_id) || null;
    }
    
    set({ breadcrumbs });
  },

  navigateToFolder: async (folderId) => {
    const { folders } = get();
    const folder = folderId ? folders.find(f => f.id === folderId) : null;
    get().setCurrentFolder(folder);
  },

  buildFolderTree: () => {
    const { folders } = get();
    console.log('📁 buildFolderTree: Input folders:', folders);
    
    // Usar función local para construir el árbol
    const tree = buildFolderTree(folders);
    console.log('📁 buildFolderTree: Built tree:', tree);
    set({ folderTree: tree });
  },

  expandFolder: (folderId) => {
    const updateExpanded = (nodes: FolderTreeNode[]): FolderTreeNode[] => {
      return nodes.map(node => {
        if (node.id === folderId) {
          return { ...node, isExpanded: true };
        }
        return { ...node, children: updateExpanded(node.children) };
      });
    };

    set({ folderTree: updateExpanded(get().folderTree) });
  },

  collapseFolder: (folderId) => {
    const updateExpanded = (nodes: FolderTreeNode[]): FolderTreeNode[] => {
      return nodes.map(node => {
        if (node.id === folderId) {
          return { ...node, isExpanded: false };
        }
        return { ...node, children: updateExpanded(node.children) };
      });
    };

    set({ folderTree: updateExpanded(get().folderTree) });
  },

  moveStudyToFolder: async (studyId, folderId) => {
    try {
      console.log('📁 moveStudyToFolder: Moving study', studyId, 'to folder', folderId);
      
      const { data, error } = await supabase
        .from('studies')
        .update({ folder_id: folderId })
        .eq('id', studyId)
        .select();

      if (error) throw error;
      
      console.log('📁 moveStudyToFolder: Study moved successfully', data);

      toast({
        title: "Estudio movido",
        description: folderId 
          ? "El estudio se ha movido a la carpeta seleccionada."
          : "El estudio se ha movido al nivel raíz."
      });

      // Refresh folders to update counts
      await get().fetchFolders();
    } catch (error) {
      console.error('Error moving study:', error);
      const message = error instanceof Error ? error.message : 'Error al mover estudio';
      toast({
        title: "Error",
        description: message,
        variant: "destructive"
      });
      throw error;
    }
  },

  moveFolderToFolder: async (folderId, targetFolderId) => {
    try {
      console.log('📁 Moving folder:', { folderId, targetFolderId });

      // Validate folder exists
      const { data: folderToMove, error: folderCheckError } = await supabase
        .from('folders')
        .select('id, name, parent_folder_id')
        .eq('id', folderId)
        .single();

      if (folderCheckError || !folderToMove) {
        console.error('Error: Folder to move not found:', folderCheckError);
        throw new Error('La carpeta a mover no existe');
      }

      console.log('📁 Folder to move:', folderToMove);

      // Validate target folder exists (if not null for root)
      if (targetFolderId) {
        const { data: targetFolder, error: targetCheckError } = await supabase
          .from('folders')
          .select('id, name')
          .eq('id', targetFolderId)
          .single();

        if (targetCheckError || !targetFolder) {
          console.error('Error: Target folder not found:', targetCheckError);
          throw new Error('La carpeta destino no existe');
        }

        console.log('📁 Target folder:', targetFolder);
      } else {
        console.log('📁 Moving to root level');
      }

      // Prevent moving folder into itself
      if (folderId === targetFolderId) {
        throw new Error('No se puede mover una carpeta dentro de sí misma');
      }

      // Check for circular references by building the path from target to root
      if (targetFolderId) {
        const { folders } = get();
        console.log('📁 Checking for circular references...');
        
        let current = folders.find(f => f.id === targetFolderId);
        const pathToRoot = [targetFolderId];
        
        while (current?.parent_folder_id) {
          if (current.parent_folder_id === folderId) {
            console.error('📁 Circular reference detected:', pathToRoot);
            throw new Error('No se puede crear una referencia circular. La carpeta destino es descendiente de la carpeta que se quiere mover.');
          }
          pathToRoot.push(current.parent_folder_id);
          current = folders.find(f => f.id === current?.parent_folder_id);
        }
        
        console.log('📁 Path to root checked, no circular reference found:', pathToRoot);
      }

      // Check if the move is actually necessary
      if (folderToMove.parent_folder_id === targetFolderId) {
        console.log('📁 Folder is already in the target location');
        toast({
          title: "Sin cambios",
          description: "La carpeta ya se encuentra en esa ubicación."
        });
        return;
      }

      console.log('📁 Performing move operation...');

      // Perform the move
      const { data, error } = await supabase
        .from('folders')
        .update({ parent_folder_id: targetFolderId || null })
        .eq('id', folderId)
        .select('id, name, parent_folder_id');

      if (error) {
        console.error('📁 Error performing move:', error);
        throw error;
      }

      console.log('📁 Move successful:', data);

      toast({
        title: "Carpeta movida",
        description: `La carpeta "${folderToMove.name}" se ha movido exitosamente.`
      });

      // Refresh folders to update the tree
      await get().fetchFolders();
      console.log('📁 Folders refreshed after move');
    } catch (error) {
      console.error('Error moving folder:', error);
      const message = error instanceof Error ? error.message : 'Error al mover carpeta';
      toast({
        title: "Error",
        description: message,
        variant: "destructive"
      });
      throw error;
    }
  },

  getStudiesInFolder: async (folderId, includeSubfolders = true) => {
    try {
      let query = supabase
        .from('studies')
        .select(`
          *,
          required_info,
          optional_info,
          folder:folders(id, name, color, icon)
        `);

      if (includeSubfolders && folderId) {
        // Use the recursive function to get all studies in subfolders
        const { data: studyIds } = await supabase
          .rpc('get_folder_studies_recursive', { folder_uuid: folderId });
        
        if (studyIds && studyIds.length > 0) {
          query = query.in('id', studyIds.map(s => s.study_id));
        } else {
          return [];
        }
      } else {
        query = query.eq('folder_id', folderId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching studies in folder:', error);
      throw error;
    }
  },

  bulkExportFolderReports: async (request) => {
    try {
      set({ isLoading: true });
      
      // Get all studies in the specified folder (and subfolders if requested)
      let studies: StudyWithFolder[] = [];
      
      if (request.folder_id) {
        studies = await get().getStudiesInFolder(request.folder_id, request.include_subfolders);
      } else {
        // Export all studies if no folder specified
        const { data, error } = await supabase
          .from('studies')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        studies = data || [];
      }
      
      if (studies.length === 0) {
        throw new Error('No hay estudios para exportar en la carpeta seleccionada');
      }

      toast({
        title: "Exportación iniciada",
        description: `Exportando ${studies.length} estudios en formato ${request.export_format}...`
      });

      // Get reports for each study and export them
      const exports = [];
      for (const study of studies) {
        try {
          // Here you would call the existing report generation functions
          // For now, we'll just collect the study data
          exports.push({
            studyName: study.required_info?.name || study.name || 'Estudio sin nombre',
            studyId: study.id,
            data: study
          });
        } catch (error) {
          console.warn(`Error exporting study ${study.id}:`, error);
        }
      }

      // For demonstration, create a combined file
      if (request.export_format === 'excel') {
        // This would be implemented with actual Excel generation
        const fileName = `export_${new Date().toISOString().split('T')[0]}.xlsx`;
        console.log('Would generate Excel file:', fileName, 'with', exports.length, 'studies');
      } else if (request.export_format === 'pdf') {
        // This would be implemented with actual PDF generation
        const fileName = `export_${new Date().toISOString().split('T')[0]}.pdf`;
        console.log('Would generate PDF file:', fileName, 'with', exports.length, 'studies');
      }

      toast({
        title: "Exportación completada",
        description: `Se han procesado ${exports.length} de ${studies.length} estudios exitosamente.`
      });

    } catch (error) {
      console.error('Error in bulk export:', error);
      const message = error instanceof Error ? error.message : 'Error en la exportación masiva';
      toast({
        title: "Error",
        description: message,
        variant: "destructive"
      });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  getFolderStats: async (folderId) => {
    try {
      // Count total studies recursively
      const { data: studyCount } = await supabase
        .rpc('count_folder_studies', { folder_uuid: folderId });

      // Count direct subfolders
      const { data: subfolders } = await supabase
        .from('folders')
        .select('id')
        .eq('parent_folder_id', folderId);

      // Get last updated timestamp
      const { data: lastUpdated } = await supabase
        .from('studies')
        .select('updated_at')
        .eq('folder_id', folderId)
        .order('updated_at', { ascending: false })
        .limit(1);

      return {
        total_studies: studyCount || 0,
        total_subfolders: subfolders?.length || 0,
        last_updated: lastUpdated?.[0]?.updated_at || new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching folder stats:', error);
      throw error;
    }
  },

  resetError: () => set({ error: null })
})); 