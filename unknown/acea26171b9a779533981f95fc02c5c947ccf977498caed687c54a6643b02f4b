import React from 'react';
import { useTranslation } from 'react-i18next';
import { ChartVisibilitySettings } from '../../types/profile';
import { CHART_LABELS } from '../../config/chartDefaults';
import { Switch } from '../ui/switch';
import { Card } from '../ui/enhanced-card';

interface ChartToggleControlProps {
  chartVisibility: ChartVisibilitySettings;
  onToggle: (chartKey: keyof ChartVisibilitySettings, enabled: boolean) => void;
  className?: string;
}

export const ChartToggleControl: React.FC<ChartToggleControlProps> = ({
  chartVisibility,
  onToggle,
  className = ''
}) => {
  const { t } = useTranslation(['report']);

  const chartOptions: Array<{
    key: keyof ChartVisibilitySettings;
    label: string;
    description: string;
  }> = [
    {
      key: 'machineTypes',
      label: t('charts.machineTypes.title', { defaultValue: CHART_LABELS.machineTypes }),
      description: 'Muestra la distribución de tiempo entre máquina parada, en marcha y tiempo de máquina'
    },
    {
      key: 'saturation',
      label: t('charts.saturation.title', { defaultValue: CHART_LABELS.saturation }),
      description: 'Visualiza el porcentaje de saturación y rendimiento normal del proceso'
    },
    {
      key: 'elements',
      label: t('charts.elements.title', { defaultValue: CHART_LABELS.elements }),
      description: 'Muestra los tiempos de cada elemento del proceso'
    },
    {
      key: 'supplements',
      label: t('charts.supplements.title', { defaultValue: CHART_LABELS.supplements }),
      description: 'Presenta los suplementos aplicados a cada elemento'
    },
    {
      key: 'cycleDistribution',
      label: t('charts.cycleDistribution.title', { defaultValue: CHART_LABELS.cycleDistribution }),
      description: 'Distribución del tiempo de ciclo entre trabajo y descanso'
    },
    {
      key: 'flowchartSymbols',
      label: t('charts.flowchartSymbols.distribution', { defaultValue: CHART_LABELS.flowchartSymbols }),
      description: 'Distribución de tiempo por tipo de operación según símbolos de diagrama de flujo'
    },
    {
      key: 'operationPercentages',
      label: t('charts.flowchartSymbols.percentages', { defaultValue: CHART_LABELS.operationPercentages }),
      description: 'Porcentajes de cada tipo de operación en el proceso'
    },
    {
      key: 'operationCount',
      label: t('charts.flowchartSymbols.countTitle', { defaultValue: CHART_LABELS.operationCount }),
      description: 'Cantidad de operaciones por cada tipo de símbolo'
    }
  ];

  return (
    <Card variant="outlined" className={`p-4 ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('charts.toggleControl.title', { defaultValue: 'Control de Gráficas' })}
        </h3>
        <p className="text-sm text-gray-600">
          {t('charts.toggleControl.description', { 
            defaultValue: 'Selecciona qué gráficas mostrar en el reporte y en las exportaciones' 
          })}
        </p>
      </div>

      <div className="space-y-3">
        {chartOptions.map((option) => (
          <div
            key={option.key}
            className="flex items-start justify-between p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
          >
            <div className="flex-1 min-w-0 mr-4">
              <div className="flex items-center gap-2 mb-1">
                <label
                  htmlFor={`chart-toggle-${option.key}`}
                  className="text-sm font-medium text-gray-900 cursor-pointer"
                >
                  {option.label}
                </label>
              </div>
              <p className="text-xs text-gray-600 leading-relaxed">
                {option.description}
              </p>
            </div>
            
            <div className="flex-shrink-0">
              <Switch
                id={`chart-toggle-${option.key}`}
                checked={chartVisibility[option.key]}
                onCheckedChange={(checked) => onToggle(option.key, checked)}
                aria-label={`Toggle ${option.label}`}
              />
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row gap-2">
          <button
            onClick={() => {
              chartOptions.forEach(option => onToggle(option.key, true));
            }}
            className="flex-1 px-3 py-2 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
          >
            {t('charts.toggleControl.enableAll', { defaultValue: 'Habilitar Todas' })}
          </button>
          <button
            onClick={() => {
              chartOptions.forEach(option => onToggle(option.key, false));
            }}
            className="flex-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors"
          >
            {t('charts.toggleControl.disableAll', { defaultValue: 'Deshabilitar Todas' })}
          </button>
        </div>
      </div>
    </Card>
  );
};
