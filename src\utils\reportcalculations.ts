import { ElementInstance, TimeRecord, TimeUnit, ElementSupplements, ElementStats, ReportStats } from '../types/index';

const TIME_UNIT_CONVERSIONS = {
  seconds: 1,
  minutes: 1/60,
  hours: 1/3600,
  mmm: 1000/60,         // Valor exacto: 16.666666666666668
  cmm: 100/60,          // Valor exacto: 1.6666666666666667
  tmu: 100000/3600,     // Valor exacto: 27.77777777777778
  dmh: 10000/3600       // Valor exacto: 2.7777777777777777
};

// Production per hour divisors for each time unit
const PRODUCTION_HOUR_DIVISORS = {
  seconds: 3600,
  minutes: 60,
  hours: 1,
  mmm: 60000,
  cmm: 6000,
  tmu: 100000,
  dmh: 10000
};

// Shift minutes to time unit conversion
const SHIFT_CONVERSIONS = {
  seconds: 60,
  minutes: 1,
  hours: 1/60,
  mmm: 1000,
  cmm: 100,
  tmu: 100000/60,   // Valor exacto: 1666.6666666666667
  dmh: 10000/60     // Valor exacto: 166.66666666666666
};

// Value hour divisors for each time unit
const VALUE_HOUR_DIVISORS = {
  seconds: 3600, // 3600 seconds in an hour
  minutes: 60,   // 60 minutes in an hour
  hours: 1,
  mmm: 60000,    // 60000 milésimas in an hour
  cmm: 6000,     // 6000 centésimas in an hour
  tmu: 100000,   // 100000 TMU in an hour
  dmh: 10000     // 10000 DMH in an hour
};

// Value minute divisors for each time unit
const VALUE_MINUTE_DIVISORS = {
  seconds: 60,    // 60 seconds in a minute
  minutes: 1,     // direct value in minutes
  hours: 1/60,
  mmm: 1000,      // 1000 milésimas in a minute
  cmm: 100,       // 100 centésimas in a minute
  tmu: 100000/60, // Valor exacto: 1666.6666666666667
  dmh: 10000/60   // Valor exacto: 166.66666666666666
};

// Value point divisors for each time unit
const VALUE_POINT_DIVISORS = {
  seconds: 36,    // 3600/100 seconds per point
  minutes: 0.6,   // 60/100 minutes per point
  hours: 0.01,
  mmm: 600,       // 60000/100 milésimas per point
  cmm: 60,        // 6000/100 centésimas per point
  tmu: 1000,      // 100000/100 TMU per point
  dmh: 100        // 10000/100 DMH per point
};

export function convertTime(time: number, unit: TimeUnit): number {
  return time * TIME_UNIT_CONVERSIONS[unit];
}

export function calculateElementStats(
  element: ElementInstance,
  timeRecords: TimeRecord[],
  supplements: ElementSupplements,
  timeUnit: TimeUnit,
  activityScale: { normal: number; optimal: number } = { normal: 100, optimal: 133 }
): ElementStats {
  // Return a properly formed ElementStats object for concurrent machine time elements
  // so they can be displayed in the table but won't affect calculations
  if (element.concurrent_machine_time) {
    const observedTime = timeRecords.reduce((sum, record) => sum + record.time, 0) /
      (timeRecords.length || 1);

    const averageActivity = timeRecords.reduce((sum, record) => sum + record.activity, 0) /
      (timeRecords.length || 1);

    const frequency = `${element.frequency_repetitions}/${element.frequency_cycles}`;

    return {
      id: element.id,
      elementId: element.id,
      description: element.description,
      type: element.type,
      frequency,
      observedTime: convertTime(observedTime, timeUnit),
      averageActivity,
      supplements: 0,
      finalTime: 0, // Set to 0 as it won't be included in calculations
      timeUnit: timeUnit,
      concurrent_machine_time: true,
      flowchartSymbol: element.flowchartSymbol
    };
  }

  // Calculate average time and activity
  const observedTime = timeRecords.reduce((sum, record) => sum + record.time, 0) /
    (timeRecords.length || 1);

  const averageActivity = timeRecords.reduce((sum, record) => sum + record.activity, 0) /
    (timeRecords.length || 1);

  // Calculate frequency
  const frequency = `${element.frequency_repetitions}/${element.frequency_cycles}`;

  // Get supplements percentage
  const supplementsPercentage = supplements?.percentage || 0;

  // Calculate final time with all factors - CORREGIDO para coincidir con la página de suplementos
  // 1. Calcular factor de frecuencia (repeticiones / ciclos)
  const frequencyFactor = element.frequency_repetitions / element.frequency_cycles;

  // 2. Calcular tiempo normalizado (tiempo observado × actividad ÷ escala normal × factor frecuencia)
  const activityFactor = averageActivity / (activityScale?.normal || 100);
  const normalizedTime = observedTime * activityFactor * frequencyFactor;

  // 3. SEPARAR SUPLEMENTOS: 5% necesidades personales + resto fatiga
  const personalNeedsPercentage = 5; // Siempre 5%
  const fatiguePercentage = Math.max(0, supplementsPercentage - personalNeedsPercentage); // Resto para fatiga
  
  // 4. Calcular tiempos de suplementos por separado
  const personalNeedsTime = normalizedTime * (personalNeedsPercentage / 100);
  const fatigueTime = normalizedTime * (fatiguePercentage / 100);
  const supplementTime = personalNeedsTime + fatigueTime;
  
  // 5. Calcular tiempo final (normalizado + suplementos)
  const finalTime = convertTime(normalizedTime + supplementTime, timeUnit);

  return {
    id: element.id,
    elementId: element.id, // Add elementId to match with supplements
    description: element.description,
    type: element.type,
    frequency,
    observedTime: convertTime(observedTime, timeUnit),
    averageActivity,
    supplements: supplementsPercentage,
    finalTime,
    timeUnit: timeUnit,
    concurrent_machine_time: element.concurrent_machine_time,
    flowchartSymbol: element.flowchartSymbol
  };
}

export function calculateReportStats(
  elements: ElementStats[] = [],
  activityScale: { normal: number; optimal: number } = { normal: 100, optimal: 133 },
  shiftMinutes: number = 480,
  contingency: number = 0,
  timeUnit: TimeUnit = 'seconds',
  pointsPerHour: number = 100,
  study?: any // Study object to access machine_cycle_data
): ReportStats {
  console.log('🚀 calculateReportStats INICIADO:', {
    studyId: study?.id,
    elementsCount: elements?.length || 0,
    hasStudy: !!study,
    hasSupplements: !!study?.supplements,
    machineDataKey: '__machine_cycle_data__',
    machineCycleDataExists: !!study?.supplements?.['__machine_cycle_data__']
  });

  elements = elements.filter(e => !e.concurrent_machine_time);

  // Group elements by type
  const machineStoppedElements = elements.filter(e => e.type === 'machine-stopped');
  const machineRunningElements = elements.filter(e => e.type === 'machine-running');
  // Filter out concurrent machine time elements
  const machineTimeElements = elements.filter(e => e.type === 'machine-time' && !e.concurrent_machine_time);

  // Calculate total times by type
  const machineStoppedTime = machineStoppedElements.reduce((sum, e) => sum + e.finalTime, 0);
  const machineRunningTime = machineRunningElements.reduce((sum, e) => sum + e.finalTime, 0);
  const machineTime = machineTimeElements.reduce((sum, e) => sum + e.finalTime, 0);

  console.log('⏱️ TIEMPOS CALCULADOS:', {
    machineStoppedTime,
    machineRunningTime,
    machineTime,
    maxMachineTime: Math.max(machineRunningTime, machineTime)
  });

  // Calculate normal cycle
  const baseCycle = machineStoppedTime + Math.max(machineRunningTime, machineTime);

  // Calculate contingency time and add it to normal cycle
  const contingencyTime = (baseCycle * contingency) / 100;
  const normalCycle = baseCycle + contingencyTime;

  console.log('🔢 CICLOS BASE:', {
    baseCycle,
    contingencyTime,
    normalCycle,
    contingencyPercent: contingency
  });

  // Calculate optimal times with contingency included
  const optimalFactor = activityScale.optimal / activityScale.normal;
  const optimalMachineStoppedTime = machineStoppedTime / optimalFactor;
  const optimalMachineRunningTime = machineRunningTime / optimalFactor;

  // Calculate optimal cycle including contingency
  const optimalBaseCycle = optimalMachineStoppedTime + Math.max(optimalMachineRunningTime, machineTime);
  const optimalCycle = optimalBaseCycle + (optimalBaseCycle * contingency) / 100;
  
  // Calculate total K (total supplements time)
  const totalK = elements.reduce((sum, element) => {
    // Skip concurrent machine time elements
    if (element.concurrent_machine_time) return sum;

    // Use the activity scale's normal value instead of hardcoded 100
    // This ensures we correctly calculate supplements for studies with different activity scales (e.g., 60-80)
    const freqParts = element.frequency.split('/');
    const repetitions = parseFloat(freqParts[0]);
    const cycles = parseFloat(freqParts[1]);

    // Asegurarse de que cycles no sea cero para evitar división por cero
    if (cycles === 0) return sum;

    const basicTime = (element.observedTime * element.averageActivity / activityScale.normal) *
                     (repetitions / cycles);
    return sum + (basicTime * (element.supplements / 100));
  }, 0);

  // Calculate shift production (no need to add contingencyTime as it's already included in cycles)
  const shiftTimeInUnit = shiftMinutes * SHIFT_CONVERSIONS[timeUnit];
  const normalProductionRaw = shiftTimeInUnit / normalCycle;
  const optimalProductionRaw = shiftTimeInUnit / optimalCycle;
  
  // Only use Math.floor when production is >= 1, otherwise keep the decimal value
  const normalProduction = normalProductionRaw >= 1 ? Math.floor(normalProductionRaw) : +normalProductionRaw.toFixed(3);
  const optimalProduction = optimalProductionRaw >= 1 ? Math.floor(optimalProductionRaw) : +optimalProductionRaw.toFixed(3);

  console.log('📊 DEBUG PRODUCCIONES INICIALES:', {
    shiftMinutes,
    timeUnit,
    shiftConversion: SHIFT_CONVERSIONS[timeUnit],
    shiftTimeInUnit,
    normalCycle,
    optimalCycle,
    normalProduction,
    optimalProduction,
    calcManual: `${shiftTimeInUnit} / ${normalCycle} = ${shiftTimeInUnit / normalCycle}`
  });

  // Calculate hourly production
  const hourlyTimeInUnit = PRODUCTION_HOUR_DIVISORS[timeUnit];
  const normalProductionPerHour = +(hourlyTimeInUnit / normalCycle).toFixed(2);
  const optimalProductionPerHour = +(hourlyTimeInUnit / optimalCycle).toFixed(2);

  // Calculate saturations
  const normalSaturation = ((machineStoppedTime + machineRunningTime) / baseCycle) * 100;
  const optimalSaturation = ((optimalMachineStoppedTime + optimalMachineRunningTime) / optimalBaseCycle) * 100;

  // These calculations will be updated after determining the final cycle to use

  // Check for cycle discrepancy with supplements calculation
  let cycleDiscrepancy = undefined;
  let supplementsCycleTime = undefined;
  let finalNormalCycle = normalCycle; // This will be the cycle used for all calculations

  // Check for machine cycle data in supplements field (temporary solution)
  const machineCycleData = study?.supplements?.['__machine_cycle_data__'];
  const selectedCycleSource = study?.supplements?.['__cycle_preference__'] || 'report';

  // Solo calcular suplementos automáticamente si hay elementos de máquina (no solo máquina parada)
  // Para que tenga sentido calcular fatiga, debe haber elementos de máquina en marcha o tiempo de máquina
  const hasMachineElements = machineRunningTime > 0 || machineTime > 0;

  console.log('📊 DATOS DE SUPLEMENTOS ENCONTRADOS:', {
    hasMachineCycleData: !!machineCycleData,
    totalCycleTime: machineCycleData?.totalCycleTime,
    selectedCycleSource,
    currentNormalCycle: normalCycle,
    hasMachineElements,
    machineRunningTime,
    machineTime,
    note: 'Solo procesará suplementos si hay elementos de máquina'
  });
  
  // SIEMPRE recalcular automáticamente si hay elementos de máquina para asegurar datos actualizados
  if (machineStoppedTime > 0 && hasMachineElements) {
    console.log('🔄 CALCULANDO SUPLEMENTOS AUTOMÁTICAMENTE (tiene elementos de máquina)...');
    
    try {
      const calculatedSupplements = calculateMachineSupplements(
        machineStoppedTime,
        machineRunningTime,
        machineTime,
        elements,
        study,
        timeUnit
      );
      
      console.log('✅ SUPLEMENTOS CALCULADOS AUTOMÁTICAMENTE:', calculatedSupplements);
      
      // Use the calculated supplements (always fresh calculation)
      supplementsCycleTime = calculatedSupplements.totalCycleTime;
    } catch (error) {
      console.error('❌ ERROR al calcular suplementos automáticamente:', error);
      
      // Solo como fallback, usar datos guardados si existe y el estudio tiene elementos de máquina
      if (machineCycleData?.totalCycleTime && hasMachineElements) {
        supplementsCycleTime = machineCycleData.totalCycleTime;
        console.log('📋 USANDO DATOS DE SUPLEMENTOS COMO FALLBACK:', supplementsCycleTime);
      }
    }
  } else if (machineStoppedTime > 0 && !hasMachineElements) {
    console.log('⚠️ NO SE CALCULA SUPLEMENTOS: Solo hay elementos de máquina parada, sin elementos de máquina en marcha o tiempo de máquina');
  } else {
    console.log('⚠️ NO SE CALCULA SUPLEMENTOS: No hay elementos de máquina parada o datos insuficientes');
  }

  // Check for discrepancy with reduced threshold
  if (supplementsCycleTime) {
    // CORREGIDO: calculateMachineSupplements ya retorna el tiempo en la unidad del estudio
    // No necesitamos convertir desde segundos porque ya está en la unidad correcta
    const supplementsCycleTimeInStudyUnit = supplementsCycleTime;
    
    console.log('🔄 VERIFICACIÓN DE UNIDADES DE SUPLEMENTOS:', {
      supplementsCycleTime: supplementsCycleTime,
      studyTimeUnit: timeUnit,
      supplementsCycleTimeInStudyUnit,
      normalCycleInStudyUnit: normalCycle,
      note: 'calculateMachineSupplements ya retorna en la unidad del estudio'
    });

    // CORRECCIÓN: Comparar ciclos base (sin contingencia) para evitar detectar discordancias 
    // causadas únicamente por el porcentaje de contingencia del usuario
    const difference = Math.abs(baseCycle - supplementsCycleTimeInStudyUnit);
    
    // Ajustar el umbral según la unidad de tiempo para que sea más sensible
    // Usar un umbral equivalente a 0.1 segundos en cada unidad
    const THRESHOLD_BY_UNIT: Record<TimeUnit, number> = {
      seconds: 0.1,     // 0.1 segundos de diferencia
      minutes: 0.0017,  // 0.1 segundos convertido a minutos (0.1/60)
      hours: 0.000028,  // 0.1 segundos convertido a horas (0.1/3600)
      mmm: 1.67,        // 0.1 segundos convertido a milésimas de minuto (100/60)
      cmm: 0.167,       // 0.1 segundos convertido a centésimas de minuto (10/60)
      tmu: (0.1 * 100000)/3600,  // 0.1 segundos convertido a TMU (valor exacto: 2.777777...)
      dmh: (0.1 * 10000)/3600     // 0.1 segundos convertido a DMH (valor exacto: 0.277777...)
    };
    
    const threshold = THRESHOLD_BY_UNIT[timeUnit];

    console.log('🔍 VERIFICANDO DISCORDANCIA (COMPARANDO CICLOS BASE SIN CONTINGENCIA):', {
      baseCycle,
      supplementsCycleTimeInStudyUnit,
      difference,
      threshold,
      hasDiscrepancy: difference > threshold,
      contingency,
      note: 'Comparando ciclos base sin contingencia para evitar falsas discordancias'
    });

    if (difference > threshold) {
      console.log('🚨 DISCORDANCIA DETECTADA (en ciclos base)!', {
        difference,
        threshold,
        baseCycle,
        supplementsCycleTimeInStudyUnit,
        contingency,
        explanation: 'Diferencia real en tiempos base, no causada por contingencia'
      });

      // Calcular ambos ciclos con contingencia aplicada para mostrar valores comparables
      const supplementsWithContingency = supplementsCycleTimeInStudyUnit + (supplementsCycleTimeInStudyUnit * contingency) / 100;
      const reportWithContingency = normalCycle; // Ya tiene contingencia aplicada

      cycleDiscrepancy = {
        hasDiscrepancy: true,
        difference,
        selectedCycleSource: selectedCycleSource as 'report' | 'supplements',
        supplementsCycleTime: supplementsWithContingency, // Ahora con contingencia aplicada
        reportCycleTime: reportWithContingency // Mantiene la contingencia original
      };

      console.log('📊 VALORES PARA MOSTRAR EN ALERTA (ambos con contingencia):', {
        supplementsBase: supplementsCycleTimeInStudyUnit,
        supplementsWithContingency,
        reportWithContingency,
        contingencyPercent: contingency,
        note: 'Ambos valores incluyen la contingencia del usuario para comparación justa'
      });

      // Use the selected cycle for all calculations
      if (selectedCycleSource === 'supplements') {
        finalNormalCycle = supplementsWithContingency;
        console.log('✅ USANDO CICLO DE SUPLEMENTOS CON CONTINGENCIA para cálculos finales:', {
          supplementsBase: supplementsCycleTimeInStudyUnit,
          contingencyPercent: contingency,
          finalCycle: finalNormalCycle
        });
      } else {
        console.log('✅ USANDO CICLO DE INFORME para cálculos finales');
      }
    } else {
      console.log('✅ NO HAY DISCORDANCIA SIGNIFICATIVA (ciclos base son similares):', {
        baseCycle,
        supplementsCycleTimeInStudyUnit,
        difference,
        threshold,
        contingency,
        explanation: 'Las diferencias se deben únicamente a la contingencia aplicada'
      });
    }
  } else {
    console.log('⚠️ NO HAY DATOS DE SUPLEMENTOS para comparar');
  }

  // IMPORTANTE: Recalcular el ciclo óptimo basado en el ciclo final seleccionado
  // El ciclo óptimo debe recalcularse con la misma lógica pero usando el ciclo final como base
  let finalOptimalCycle = optimalCycle; // Default value
  
  if (finalNormalCycle !== normalCycle) {
    // Si el ciclo cambió, necesitamos recalcular el ciclo óptimo manteniendo la misma lógica
    // pero basado en los componentes del ciclo final
    
    // Extraer el ciclo base del ciclo final (sin contingencia)
    const finalBaseCycle = finalNormalCycle - (finalNormalCycle * contingency) / 100;
    
    // Calcular los tiempos óptimos proporcionalmente al ciclo base final
    const baseRatio = finalBaseCycle / baseCycle;
    const adjustedOptimalMachineStoppedTime = optimalMachineStoppedTime * baseRatio;
    const adjustedOptimalMachineRunningTime = optimalMachineRunningTime * baseRatio;
    // El tiempo de máquina no cambia con la actividad, se mantiene igual
    
    // Recalcular el ciclo óptimo base
    const finalOptimalBaseCycle = adjustedOptimalMachineStoppedTime + Math.max(adjustedOptimalMachineRunningTime, machineTime);
    
    // Aplicar contingencia al ciclo óptimo
    finalOptimalCycle = finalOptimalBaseCycle + (finalOptimalBaseCycle * contingency) / 100;
    
    console.log('🔄 RECALCULANDO CICLO ÓPTIMO:', {
      originalOptimalCycle: optimalCycle,
      finalBaseCycle,
      baseCycle,
      baseRatio,
      adjustedOptimalMachineStoppedTime,
      adjustedOptimalMachineRunningTime,
      finalOptimalBaseCycle,
      finalOptimalCycle,
      explanation: 'Ciclo óptimo recalculado basado en el ciclo final seleccionado'
    });
  } else {
    console.log('✅ MANTENIENDO CICLO ÓPTIMO ORIGINAL:', {
      finalOptimalCycle,
      reason: 'No hubo cambio en el ciclo normal'
    });
  }

  // Recalculate all values that depend on both normal and optimal cycles
  const finalNormalProductionRaw = shiftTimeInUnit / finalNormalCycle;
  const finalOptimalProductionRaw = shiftTimeInUnit / finalOptimalCycle;
  
  // Only use Math.floor when production is >= 1, otherwise keep the decimal value
  const finalNormalProduction = finalNormalProductionRaw >= 1 ? Math.floor(finalNormalProductionRaw) : +finalNormalProductionRaw.toFixed(3);
  const finalOptimalProduction = finalOptimalProductionRaw >= 1 ? Math.floor(finalOptimalProductionRaw) : +finalOptimalProductionRaw.toFixed(3);
  const finalNormalProductionPerHour = +(hourlyTimeInUnit / finalNormalCycle).toFixed(2);
  const finalOptimalProductionPerHour = +(hourlyTimeInUnit / finalOptimalCycle).toFixed(2);

  console.log('🔄 DEBUG PRODUCCIONES FINALES:', {
    finalNormalCycle,
    finalOptimalCycle,
    shiftTimeInUnit,
    hourlyTimeInUnit,
    finalNormalProduction,
    finalOptimalProduction,
    finalNormalProductionPerHour,
    finalOptimalProductionPerHour,
    calcManualNormal: `${shiftTimeInUnit} / ${finalNormalCycle} = ${shiftTimeInUnit / finalNormalCycle}`,
    calcManualOptimal: `${shiftTimeInUnit} / ${finalOptimalCycle} = ${shiftTimeInUnit / finalOptimalCycle}`,
    cambioDeProduccion: {
      normalAntes: shiftTimeInUnit / normalCycle >= 1 ? Math.floor(shiftTimeInUnit / normalCycle) : +(shiftTimeInUnit / normalCycle).toFixed(3),
      normalDespues: finalNormalProduction,
              diferencia: finalNormalProduction - (shiftTimeInUnit / normalCycle >= 1 ? Math.floor(shiftTimeInUnit / normalCycle) : +(shiftTimeInUnit / normalCycle).toFixed(3))
    }
  });

  const finalMaxActivity = (finalNormalCycle / finalOptimalCycle) * 100;
  const finalValueHour = +(finalNormalCycle / VALUE_HOUR_DIVISORS[timeUnit]).toFixed(4);
  const finalValueMinute = +(finalNormalCycle / VALUE_MINUTE_DIVISORS[timeUnit]).toFixed(4);
  // Calcular valor punto usando la equivalencia configurada por el usuario
  console.log('🔍 DEBUG VALOR PUNTO:', {
    finalNormalCycle,
    timeUnit,
    VALUE_HOUR_DIVISORS_timeUnit: VALUE_HOUR_DIVISORS[timeUnit],
    pointsPerHour,
    division: VALUE_HOUR_DIVISORS[timeUnit] / pointsPerHour,
    calculation: `${finalNormalCycle} / (${VALUE_HOUR_DIVISORS[timeUnit]} / ${pointsPerHour})`
  });
  
  // Validaciones para evitar NaN
  const hourDivisor = VALUE_HOUR_DIVISORS[timeUnit] || 3600;
  const validPointsPerHour = pointsPerHour && pointsPerHour > 0 ? pointsPerHour : 100;
  const pointDivisor = hourDivisor / validPointsPerHour;
  const finalValuePoint = finalNormalCycle / pointDivisor;
  
  console.log('✅ VALOR PUNTO CALCULADO:', {
    hourDivisor,
    validPointsPerHour,
    pointDivisor,
    finalValuePoint,
    isValidNumber: !isNaN(finalValuePoint) && isFinite(finalValuePoint)
  });

  // Recalculate normal saturation if the cycle changed
  const finalNormalSaturation = finalNormalCycle !== normalCycle
    ? ((machineStoppedTime + machineRunningTime) / (finalNormalCycle - (finalNormalCycle * contingency) / 100)) * 100
    : normalSaturation;

  // Recalculate optimal saturation based on final optimal cycle
  const finalOptimalBaseCycle = finalOptimalCycle - (finalOptimalCycle * contingency) / 100;
  let finalOptimalSaturation = optimalSaturation; // Default value
  
  if (finalNormalCycle !== normalCycle) {
    // Recalcular con los tiempos óptimos ajustados
    const baseRatio = (finalNormalCycle - (finalNormalCycle * contingency) / 100) / baseCycle;
    const adjustedOptimalMachineStoppedTime = optimalMachineStoppedTime * baseRatio;
    const adjustedOptimalMachineRunningTime = optimalMachineRunningTime * baseRatio;
    finalOptimalSaturation = ((adjustedOptimalMachineStoppedTime + adjustedOptimalMachineRunningTime) / finalOptimalBaseCycle) * 100;
  }

  // Recalculate contingency time if using supplements cycle
  let finalContingencyTime = contingency > 0 ? (finalNormalCycle * contingency) / (100 + contingency) : 0;

  console.log('🎯 RESULTADO FINAL COMPLETO:', {
    finalNormalCycle,
    finalOptimalCycle,
    finalNormalProduction,
    finalOptimalProduction,
    finalOptimalProductionPerHour,
    finalMaxActivity,
    finalOptimalSaturation,
    cycleDiscrepancy: cycleDiscrepancy ? 'SÍ' : 'NO',
    selectedSource: cycleDiscrepancy?.selectedCycleSource,
    verificaciones: {
      cicloOptimoEsDiferente: finalOptimalCycle !== optimalCycle,
      produccionOptimaCorrecta: finalOptimalProduction < 100, // Verificación de sanidad
      horaOptimaCorrecta: finalOptimalProductionPerHour < 200 // Verificación de sanidad
    }
  });

  return {
    timeUnit: timeUnit,
    normalCycle: finalNormalCycle,
    optimalCycle: finalOptimalCycle, // Use recalculated optimal cycle
    contingencyTime: finalContingencyTime,
    totalK,
    normalProduction: finalNormalProduction,
    optimalProduction: finalOptimalProduction, // Use recalculated optimal production
    normalProductionPerHour: finalNormalProductionPerHour,
    optimalProductionPerHour: finalOptimalProductionPerHour, // Use recalculated optimal production per hour
    normalSaturation: finalNormalSaturation,
    optimalSaturation: finalOptimalSaturation, // Use recalculated optimal saturation
    maxActivity: finalMaxActivity,
    machineStoppedTime,
    machineRunningTime,
    machineTime,
    valueHour: finalValueHour,
    valueMinute: finalValueMinute,
    valuePoint: finalValuePoint,
    supplementsCycleTime,
    cycleDiscrepancy
  };
}

// Function to automatically calculate machine supplements when missing
function calculateMachineSupplements(
  machineStoppedTime: number,
  machineRunningTime: number, 
  machineTime: number,
  elements: ElementStats[],
  study: any,
  timeUnit: TimeUnit = 'seconds'
): { totalCycleTime: number; calculationCase: string } {
  console.log('🔧 calculateMachineSupplements INICIADO:', {
    machineStoppedTime,
    machineRunningTime,
    machineTime,
    elementsCount: elements.length
  });

  // CORREGIDO: Usar la misma lógica que la página de suplementos
  // 1. CALCULAR TIEMPOS FINALES POR ELEMENTO (CON SUPLEMENTOS INCLUIDOS)
  let totalSupplementTime = 0;
  let totalPersonalNeedsTime = 0;
  let totalFatigueTime = 0;
  
  const elementCalculations: Array<{
    elementName: string;
    type: string;
    normalizedTime: number;
    supplementPercentage: number;
    personalNeedsTime: number;
    fatigueTime: number;
    finalTime: number;
  }> = [];

  // Obtener todos los elementos y calcular sus tiempos finales
  elements.forEach(element => {
    if (element.concurrent_machine_time) return;
    
    const elementInstance = study?.elements?.find((e: any) => e.id === element.id);
    if (!elementInstance) return;

    const elementSupplements = study?.supplements?.[element.id];
    const supplementPercentage = elementSupplements?.percentage || 0;
    
    // Para elementos calculados desde finalTime del report, necesitamos extraer el tiempo normalizado
    // finalTime = normalizedTime * (1 + supplementPercentage/100)
    // Por lo tanto: normalizedTime = finalTime / (1 + supplementPercentage/100)
    const normalizedTime = supplementPercentage > 0 
      ? element.finalTime / (1 + supplementPercentage / 100)
      : element.finalTime;
    
    // SEPARAR SUPLEMENTOS: 5% necesidades personales + resto fatiga
    const personalNeedsPercentage = 5; // Siempre 5%
    const fatiguePercentage = Math.max(0, supplementPercentage - personalNeedsPercentage); // Resto para fatiga
    
    // Calcular tiempos de suplementos por separado
    const personalNeedsTime = normalizedTime * (personalNeedsPercentage / 100);
    const fatigueTime = normalizedTime * (fatiguePercentage / 100);
    const supplementTime = personalNeedsTime + fatigueTime;
    
    // Calcular tiempo final (normalizado + suplementos)
    const finalTime = normalizedTime + supplementTime;
    
    totalSupplementTime += supplementTime;
    totalPersonalNeedsTime += personalNeedsTime;
    totalFatigueTime += fatigueTime;
    
    elementCalculations.push({
      elementName: elementInstance.description || `Elemento ${elementInstance.position + 1}`,
      type: elementInstance.type,
      normalizedTime,
      supplementPercentage,
      personalNeedsTime,
      fatigueTime,
      finalTime
    });
    
    console.log(`📊 Elemento ${elementInstance.description}:`, {
      originalFinalTime: element.finalTime,
      extractedNormalizedTime: normalizedTime,
      supplementPercentage,
      personalNeedsTime,
      fatigueTime,
      supplementTime,
      calculatedFinalTime: finalTime
    });
  });

  // 2. SEPARAR POR TIPOS Y CALCULAR SUMAS
  const machineStoppedElements = elementCalculations.filter(elem => elem.type === 'machine-stopped');
  const machineRunningElements = elementCalculations.filter(elem => elem.type === 'machine-running');
  const machineTimeElements = elementCalculations.filter(elem => elem.type === 'machine-time');

  const totalMachineStoppedTime = machineStoppedElements.reduce((sum, elem) => sum + elem.finalTime, 0);
  const totalMachineRunningTime = machineRunningElements.reduce((sum, elem) => sum + elem.finalTime, 0);
  const totalMachineTimeTime = machineTimeElements.reduce((sum, elem) => sum + elem.finalTime, 0);

  // 3. CALCULAR CICLO NORMAL = MP + MAX(MM, TM)
  const normalCycle = totalMachineStoppedTime + Math.max(totalMachineRunningTime, totalMachineTimeTime);

  // 4. CALCULAR TIEMPO DE ESPERA (basado en tiempos finales)
  const waitTime = Math.max(0, totalMachineTimeTime - totalMachineRunningTime);

  // 5. DETERMINAR CASO Y APLICAR AJUSTES
  // CORREGIDO: Convertir umbrales de segundos a la unidad del estudio
  
  // Umbrales originales en segundos: 30, 91, 600
  const thresholds = {
    threshold1: convertTime(30, timeUnit), // 30 segundos
    threshold2: convertTime(91, timeUnit), // 91 segundos  
    threshold3: convertTime(600, timeUnit) // 600 segundos (10 minutos)
  };

  console.log('🔧 UMBRALES CONVERTIDOS:', {
    timeUnit,
    waitTime,
    thresholds,
    originalThresholds: { t1: 30, t2: 91, t3: 600 },
    note: 'Umbrales convertidos desde segundos a la unidad del estudio'
  });

  let calculationCase = 'case1';
  let finalCycle = normalCycle;
  
  if (waitTime <= thresholds.threshold1) {
    calculationCase = 'case1';
    // Caso 1: No hay ajuste, el ciclo final es el ciclo normal
    finalCycle = normalCycle;
  } else if (waitTime > thresholds.threshold1 && waitTime < thresholds.threshold2) {
    calculationCase = 'case2';
    // Caso 2: Ajuste parcial
    const adjustedWaitTime = (waitTime - thresholds.threshold1) * 1.5;
    const absorbedSupplements = Math.min(totalSupplementTime, adjustedWaitTime);
    finalCycle = normalCycle - absorbedSupplements;
  } else if (waitTime >= thresholds.threshold2 && waitTime <= thresholds.threshold3) {
    calculationCase = 'case3';
    // Caso 3: Solo fatiga absorbida
    finalCycle = normalCycle - totalFatigueTime;
  } else if (waitTime > thresholds.threshold3) {
    calculationCase = 'case4';
    // Caso 4: Todos los suplementos absorbidos
    finalCycle = normalCycle - totalSupplementTime;
  }

  console.log('🎯 RESULTADO CÁLCULO AUTOMÁTICO CORREGIDO:', {
    elementCalculations: elementCalculations.length,
    totalMachineStoppedTime,
    totalMachineRunningTime,
    totalMachineTimeTime,
    normalCycle,
    waitTime,
    timeUnit,
    thresholds,
    calculationCase,
    totalSupplementTime,
    totalFatigueTime,
    finalCycle,
    methodology: 'Usando misma lógica que página de suplementos con umbrales convertidos'
  });

  // IMPORTANTE: Retornamos el tiempo en la misma unidad que el estudio
  // (no en segundos) porque element.finalTime ya está convertido
  return {
    totalCycleTime: finalCycle,
    calculationCase
  };
}