import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: true, // Usar TLS
  requireTLS: true, // Requerir TLS
  tls: {
    // Rechazar conexiones no autorizadas
    rejectUnauthorized: true
  },
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
}

export const emailService = {
  async sendEmail({ to, subject, html }: EmailOptions) {
    try {
      await transporter.sendMail({
        from: process.env.SMTP_FROM,
        to,
        subject,
        html,
      });
      return { success: true };
    } catch (error) {
      console.error('Error sending email:', error);
      return { success: false, error };
    }
  },

  async sendPasswordResetEmail(to: string, resetToken: string) {
    const resetUrl = `${process.env.VITE_APP_URL}/reset-password?token=${resetToken}`;
    const subject = 'Recuperación de contraseña';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #6b46c1;">Recuperación de contraseña</h1>
        <p>Has solicitado restablecer tu contraseña. Haz clic en el siguiente enlace para crear una nueva contraseña:</p>
        <div style="margin: 20px 0;">
          <a href="${resetUrl}" 
             style="background-color: #6b46c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Restablecer contraseña
          </a>
        </div>
        <p>Si no has solicitado este cambio, puedes ignorar este mensaje.</p>
        <p>Este enlace expirará en 1 hora por seguridad.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            Este es un email automático, por favor no respondas a este mensaje.
          </p>
        </div>
      </div>
    `;

    return this.sendEmail({ to, subject, html });
  }
};
