import 'regenerator-runtime/runtime';
import React from 'react';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App.tsx';
import './index.css';
import './i18n';
import './registerSW';
import { useSupplementTablesStore } from './store/supplementTablesStore';

// Asegurarse de que las descripciones se actualicen cuando se carga la aplicación
useSupplementTablesStore.getState().updateDescriptions();

const root = createRoot(document.getElementById('root')!);

root.render(
  <StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </StrictMode>
);