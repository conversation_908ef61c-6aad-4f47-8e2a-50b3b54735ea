import { create } from 'zustand';
import { supabase } from '../lib/supabase';

interface Workspace {
  id: string;
  name: string;
  user_id: string;
}

interface WorkspaceState {
  workspace: Workspace | null;
  isLoading: boolean;
  error: string | null;
  fetchWorkspace: () => Promise<void>;
  createWorkspace: (name: string) => Promise<void>;
}

export const useWorkspaceStore = create<WorkspaceState>((set) => ({
  workspace: null,
  isLoading: false,
  error: null,

  fetchWorkspace: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      // First try to get existing workspace
      const { data: existingWorkspace, error: fetchError } = await supabase
        .from('user_workspaces')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (fetchError && fetchError.code !== 'PGRST116') throw fetchError;

      // If workspace exists, use it
      if (existingWorkspace) {
        set({ workspace: existingWorkspace, isLoading: false });
        return;
      }

      // If no workspace exists, create one
      const { data: newWorkspace, error: createError } = await supabase
        .from('user_workspaces')
        .insert({ 
          user_id: user.id,
          name: 'My Workspace'
        })
        .select()
        .single();

      if (createError) throw createError;
      set({ workspace: newWorkspace, isLoading: false });

    } catch (error) {
      console.error('Error in fetchWorkspace:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  createWorkspace: async (name: string) => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('user_workspaces')
        .insert({ name, user_id: user.id })
        .select()
        .single();

      if (error) throw error;

      set({ workspace: data, isLoading: false });
    } catch (error) {
      console.error('Error in createWorkspace:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  }
}));