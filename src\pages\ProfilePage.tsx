import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { supabase } from '../lib/supabase';
import { UserProfile } from '../types/profile';
import { UserCredits } from '../types/credits';
import { LogoUpload } from '../components/LogoUpload';
import { ProfilePreferences } from '../components/ProfilePreferences';
import { OrganizationSection } from '../components/organizations/OrganizationSection';
import { PendingJoinRequests } from '../components/organizations/PendingJoinRequests';
import { MySentJoinRequests } from '../components/organizations/MySentJoinRequests';
import { OrganizationMembers } from '../components/organizations/OrganizationMembers';
import OrganizationStudyList from '../components/organizations/OrganizationStudyList';
import { Header } from '../components/Header';
import { creditService } from '../services/creditService';
import { Button } from '../components/ui/button';
import { useToast } from '../components/ui/use-toast';
import { CreditsCard } from '../components/CreditsCard';
import { useOrganizationStore } from '../store/organizationStore';
import { ChangePassword } from '../components/ChangePassword';
import '../styles/micro-interactions.css';

export const ProfilePage: React.FC = () => {
  const { t } = useTranslation(['profile', 'common']);
  const navigate = useNavigate();
  const { user, isLoading: authLoading, isInitialized } = useAuthStore();
  const { fetchOrganizations, organizations } = useOrganizationStore();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const { toast } = useToast();
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string | undefined>(undefined);

  // Inicializar selectedOrganizationId con la primera organización si no se ha seleccionado ninguna
  useEffect(() => {
    if (!selectedOrganizationId && organizations.length > 0) {
      setSelectedOrganizationId(organizations[0].id);
    }
  }, [organizations, selectedOrganizationId]);

  const handleRequestSubscription = () => {
    try {
      const subject = t('subscription:requestSubject');
      const body = t('subscription:requestBody');

      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.location.href = mailtoLink;

      toast({
        description: t('subscription:requestSent')
      });
    } catch (error) {
      console.error('Error:', error);
      toast({
        description: t('subscription:requestError'),
        variant: "destructive"
      });
    }
  };

  const handleCancelSubscription = () => {
    try {
      const subject = t('subscription:cancelSubject');
      const body = t('subscription:cancelBody');

      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.location.href = mailtoLink;

      toast({
        description: t('subscription:cancelSent')
      });
    } catch (error) {
      console.error('Error:', error);
      toast({
        description: t('subscription:cancelError'),
        variant: "destructive"
      });
    }
  };

  const handleProfileUpdate = async (updates: Partial<UserProfile>) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user?.id);

      if (error) throw error;

      setProfile(prev => prev ? { ...prev, ...updates } : null);
      toast({
        description: t('profile:updateSuccess')
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        description: t('profile:updateError'),
        variant: "destructive"
      });
    }
  };

  const handleLogoUpdate = async (url: string | null) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ logo_url: url })
        .eq('id', user?.id);

      if (error) throw error;

      setProfile(prev => prev ? { ...prev, logo_url: url } : null);
      toast({
        description: t('profile:logoUpdateSuccess')
      });
    } catch (error) {
      console.error('Error updating logo:', error);
      toast({
        description: t('profile:logoUpdateError'),
        variant: "destructive"
      });
    }
  };

  const checkSubscription = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('user_study_limits')
        .select('subscription_plan, subscription_end_date')
        .eq('user_id', user.id)
        .single();

      if (error) throw error;

      const hasValidSubscription = data?.subscription_plan === 'true' && 
        data?.subscription_end_date && 
        new Date(data.subscription_end_date) > new Date();

      setHasActiveSubscription(hasValidSubscription);
    } catch (error) {
      console.error('Error checking subscription:', error);
      setHasActiveSubscription(false);
    }
  };

  useEffect(() => {
    if (!user) return;
    checkSubscription();
  }, [user]);

  const fetchUserData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      const creditsData = await creditService.getUserCredits(user.id);
      
      setProfile(profileData);
      setCredits(creditsData);

      // Verificar la suscripción después de obtener los datos
      await checkSubscription();
    } catch (error: any) {
      console.error('Error fetching user data:', error);
      setError(error.message);
      toast({
        description: t('profile:errors.fetch'),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!isInitialized) return;
    
    if (!user) {
      navigate('/login');
      return;
    }

    fetchUserData();
    
    // Cargar las organizaciones del usuario con manejo de errores
    try {
      fetchOrganizations();
    } catch (error) {
      console.error('Error al cargar organizaciones:', error);
    }
  }, [user, isInitialized]);

  const handleBuyCredits = () => {
    // Implementar lógica para abrir el modal de compra de créditos
    // Por ahora, simplemente redirigimos al correo
    try {
      const subject = t('credits:requestSubject', { defaultValue: 'Solicitud de compra de créditos' });
      const body = t('credits:requestBody', { defaultValue: 'Me gustaría obtener más información sobre cómo comprar créditos adicionales.' });

      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.location.href = mailtoLink;

      toast({
        description: t('credits:requestSent', { defaultValue: 'Solicitud enviada' })
      });
    } catch (error) {
      console.error('Error:', error);
      toast({
        description: t('credits:requestError', { defaultValue: 'Error al enviar la solicitud' }),
        variant: "destructive"
      });
    }
  };

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{error}</h2>
          <Button
            onClick={fetchUserData}
            className="mt-4"
          >
            {t('profile:retry')}
          </Button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!profile || !credits) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('profile:noProfile')}</h2>
          <p className="text-gray-600">{t('profile:pleaseLogin')}</p>
          <Button
            onClick={fetchUserData}
            className="mt-4"
          >
            {t('profile:retry')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 lg:py-8">
        <div className="space-y-6 sm:space-y-8 lg:space-y-12">
          {/* Bloque de información del usuario */}
          <div className="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-100 p-4 sm:p-6 lg:p-8 enhanced-hover enter-from-bottom">
            <div className="flex items-center mb-4 sm:mb-6">
              <div className="bg-blue-100 rounded-full p-2 sm:p-3 mr-3 sm:mr-4 flex-shrink-0 icon-bounce">
                <span className="text-xl sm:text-2xl">👤</span>
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">{t('profile:userInfo.title')}</h2>
                <p className="text-sm sm:text-base text-gray-600 mt-1">{t('profile:userInfo.description', { defaultValue: 'Basic information about your account' })}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10">
              <div className="space-y-4 sm:space-y-6">
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border-l-4 border-blue-500">
                  <div className="flex items-center mb-2">
                    <span className="text-blue-600 mr-2 flex-shrink-0">📧</span>
                    <label className="block text-sm font-semibold text-gray-700">
                      {t('profile:userInfo.email')}
                    </label>
                  </div>
                  <p className="text-base sm:text-lg font-medium text-gray-900 ml-6 break-all">{profile.email}</p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border-l-4 border-green-500">
                  <div className="flex items-center mb-2">
                    <span className="text-green-600 mr-2 flex-shrink-0">📅</span>
                    <label className="block text-sm font-semibold text-gray-700">
                      {t('profile:userInfo.registeredAt')}
                    </label>
                  </div>
                  <p className="text-base sm:text-lg font-medium text-gray-900 ml-6">
                    {new Date(profile.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              
              <div className="space-y-4 sm:space-y-6">
                <div className={`rounded-lg p-3 sm:p-4 border-l-4 ${hasActiveSubscription ? 'bg-green-50 border-green-500' : 'bg-orange-50 border-orange-500'}`}>
                  <div className="flex items-center mb-2">
                    <span className={`mr-2 flex-shrink-0 ${hasActiveSubscription ? 'text-green-600' : 'text-orange-600'}`}>
                      {hasActiveSubscription ? '✅' : '⚠️'}
                    </span>
                    <label className="block text-sm font-semibold text-gray-700">
                      {t('profile:userInfo.subscriptionStatus')}
                    </label>
                  </div>
                  <p className={`text-base sm:text-lg font-medium ml-6 ${hasActiveSubscription ? 'text-green-700' : 'text-orange-700'}`}>
                    {hasActiveSubscription ? t('subscription.active', { ns: 'common' }) : t('subscription.inactive', { ns: 'common' })}
                  </p>
                </div>
                
                <div className="ml-0 sm:ml-6">
                  <Button
                    onClick={hasActiveSubscription ? handleCancelSubscription : handleRequestSubscription}
                    variant={hasActiveSubscription ? "outline" : "default"}
                    className={`w-full sm:w-auto btn-micro ripple-enhanced focus-enhanced ${
                      hasActiveSubscription 
                        ? "text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400" 
                        : "btn-primary text-white"
                    }`}
                    size="lg"
                  >
                    <span className="mr-2">
                      {hasActiveSubscription ? '❌' : '🚀'}
                    </span>
                    <span className="text-sm sm:text-base">
                      {hasActiveSubscription ? t('subscription.cancel', { ns: 'common' }) : t('subscription.request', { ns: 'common' })}
                    </span>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Tarjeta de créditos */}
          <CreditsCard 
            credits={credits}
            hasActiveSubscription={hasActiveSubscription}
            onBuyCredits={handleBuyCredits}
          />

          {/* Sección de perfil */}
          <div className="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-100 p-4 sm:p-6 lg:p-8 enhanced-hover enter-from-left">
            <div className="flex items-center mb-6 sm:mb-8">
              <div className="bg-purple-100 rounded-full p-2 sm:p-3 mr-3 sm:mr-4 flex-shrink-0 icon-rotate">
                <span className="text-xl sm:text-2xl">⚙️</span>
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">{t('profile:profile.title')}</h2>
                <p className="text-sm sm:text-base text-gray-600 mt-1">{t('profile:profile.description', { defaultValue: 'Customize your profile and preferences' })}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
              <div className="space-y-6 sm:space-y-8 lg:space-y-10">
                {/* Logo Section */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg sm:rounded-xl p-4 sm:p-6 transition-all duration-300 hover:shadow-lg hover:from-blue-100 hover:to-indigo-100 hover:border-blue-300">
                  <div className="flex items-center mb-4">
                    <span className="text-blue-600 mr-2 text-lg sm:text-xl transition-transform duration-200 hover:scale-110 flex-shrink-0">🎨</span>
                    <h3 className="text-base sm:text-lg font-semibold text-blue-900">{t('profile:profile.companyLogo', { defaultValue: 'Company Logo' })}</h3>
                  </div>
                  <LogoUpload
                    url={profile?.logo_url || null}
                    onUpload={handleLogoUpdate}
                  />
                </div>
                
                {/* Sección de cambio de contraseña */}
                <div className="bg-gradient-to-br from-red-50 to-orange-50 border border-red-200 rounded-lg sm:rounded-xl p-4 sm:p-6 transition-all duration-300 hover:shadow-lg hover:from-red-100 hover:to-orange-100 hover:border-red-300">
                  <div className="flex items-center mb-4 sm:mb-6">
                    <span className="text-red-600 mr-2 text-lg sm:text-xl transition-transform duration-200 hover:scale-110 flex-shrink-0">🔐</span>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-base sm:text-lg font-semibold text-red-900 truncate">{t('profile:changePassword.title')}</h3>
                      <p className="text-red-600 text-xs sm:text-sm mt-1">{t('profile:changePassword.description', { defaultValue: 'Keep your account secure' })}</p>
                    </div>
                  </div>
                  <ChangePassword />
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg sm:rounded-xl p-4 sm:p-6">
                <div className="flex items-center mb-4 sm:mb-6">
                  <span className="text-green-600 mr-2 text-lg sm:text-xl flex-shrink-0">🎛️</span>
                  <div className="min-w-0 flex-1">
                    <h3 className="text-base sm:text-lg font-semibold text-green-900">{t('profile:preferences.title')}</h3>
                    <p className="text-green-600 text-xs sm:text-sm mt-1">{t('profile:preferences.description', { defaultValue: 'Customize the application to your needs' })}</p>
                  </div>
                </div>
                <ProfilePreferences
                  profile={profile}
                  onUpdate={handleProfileUpdate}
                />
              </div>
            </div>
          </div>

          {/* ===== SECCIÓN DE ORGANIZACIONES ===== */}
          <div className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 border border-indigo-200 rounded-xl shadow-lg p-4 sm:p-6 lg:p-8 enhanced-hover enter-from-right glow-effect">
            <div className="flex items-center mb-8">
              <div className="bg-indigo-100 rounded-full p-4 mr-5 icon-pulse">
                <span className="text-3xl">🏢</span>
              </div>
              <div>
                <h2 className="text-3xl font-bold text-indigo-900">{t('common:organizations.title')}</h2>
                <p className="text-indigo-700 text-lg mt-1">
                  {t('common:organizationMembers.shareStudiesHint', { defaultValue: 'Gestiona tus organizaciones, miembros y comparte estudios con tu equipo' })}
                </p>
              </div>
            </div>
            
            <div className="space-y-10 stagger-children">
              {/* Notificaciones de solicitudes pendientes */}
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 sm:p-6 lg:p-8">
                <div className="flex items-center mb-4">
                  <span className="text-yellow-600 mr-2">🔔</span>
                  <h3 className="text-lg font-semibold text-yellow-900">{t('common:notifications', { defaultValue: 'Notifications' })}</h3>
                </div>
                <div className="space-y-4">
                  <PendingJoinRequests forceRefresh={true} />
                  <MySentJoinRequests />
                </div>
              </div>

              {/* Gestión de organizaciones */}
              <div className="bg-white rounded-xl shadow-lg border border-indigo-100 p-4 sm:p-6 lg:p-8">
                <div className="flex items-center mb-6">
                  <span className="text-indigo-600 mr-3">🏭</span>
                  <div>
                    <h3 className="text-xl font-semibold text-indigo-900">{t('common:organizations.myOrganizations', { defaultValue: 'My Organizations' })}</h3>
                    <p className="text-indigo-600 text-sm">{t('common:organizations.createAndManage', { defaultValue: 'Create and manage your organizations' })}</p>
                  </div>
                </div>
                <div className="mt-6">
                  <OrganizationSection />
                </div>
              </div>

              {/* Miembros de la organización */}
              <div className="bg-white rounded-xl shadow-lg border border-indigo-100 p-4 sm:p-6 lg:p-8">
                <div className="flex items-center mb-6">
                  <span className="text-indigo-600 mr-3">👥</span>
                  <div>
                    <h3 className="text-xl font-semibold text-indigo-900">{t('common:organizations.teamMembers', { defaultValue: 'Team Members' })}</h3>
                    <p className="text-indigo-600 text-sm">{t('common:organizations.manageMembers', { defaultValue: 'Manage the members of your organization' })}</p>
                  </div>
                </div>
                <OrganizationMembers 
                  selectedOrganizationId={selectedOrganizationId}
                  onSelectOrganization={setSelectedOrganizationId}
                />
              </div>

              {/* Compartir estudios con la organización */}
              {selectedOrganizationId && (
                <div className="bg-white rounded-xl shadow-lg border border-indigo-100 p-4 sm:p-6 lg:p-8">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 sm:p-6 lg:p-8 mb-8">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center">
                        <div className="bg-blue-100 rounded-full p-3 mr-4">
                          <span className="text-2xl">📊</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-blue-900 mb-2">
                            {t('common:organization.shareStudies')}
                          </h3>
                          <p className="text-blue-700 font-semibold text-lg">
                            {organizations.find(org => org.id === selectedOrganizationId)?.name}
                          </p>
                          <p className="text-blue-600 text-sm mt-1">
                            {t('common:organizations.shareStudiesDescription', { defaultValue: 'Share your studies with the members of this organization' })}
                          </p>
                        </div>
                      </div>
                      <div className="bg-white rounded-xl p-4 shadow-md border border-blue-200">
                        <span className="text-3xl">🤝</span>
                      </div>
                    </div>
                  </div>
                  <OrganizationStudyList organizationId={selectedOrganizationId} />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
