import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, ArrowLeft, AlertCircle, X, ChevronUp, ChevronDown } from 'lucide-react';
import { useParams, Navigate } from 'react-router-dom';
import { Header } from '../components/Header';
import { MethodList } from '../components/MethodList';
import { MethodForm } from '../components/methodform';
import { ActionButton } from '../components/ActionButton';
import { useMethodStore } from '../store/methodStore';
import { useStudyStore } from '../store/studyStore';
import { ElementInstance, TimeRecord } from '../types/index';
import { useAuthStore } from '../store/authStore';

interface MethodPageProps {
  onBack: () => void;
}

export const MethodPage: React.FC<MethodPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['method', 'study', 'common']);
  const { studyId } = useParams<{ studyId: string }>();
  const [showForm, setShowForm] = useState(false);
  const [editingElement, setEditingElement] = useState<ElementInstance | null>(null);
  const [isSharedStudy, setIsSharedStudy] = useState(false);
  
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const setSelectedStudy = useStudyStore(state => state.setSelectedStudy);
  const updateStudy = useStudyStore(state => state.updateStudy);
  const studies = useStudyStore(state => state.studies);
  const fetchStudies = useStudyStore(state => state.fetchStudies);
  const { elements, fetchElements, createElement, updateElement, deleteElement } = useMethodStore();
  const { user } = useAuthStore();

  const handleClickOutside = useCallback((event: MouseEvent) => {
    const modalElement = document.getElementById('method-form-modal');
    const target = event.target as HTMLElement;
    
    if (modalElement && !modalElement.contains(target) && showForm) {
      setShowForm(false);
      setEditingElement(null);
    }
  }, [showForm]);

  // Cargar los estudios si no están cargados
  useEffect(() => {
    if (studies.length === 0) {
      fetchStudies();
    }
  }, [fetchStudies, studies.length]);

  // Cargar el estudio si no está seleccionado
  useEffect(() => {
    if (studyId && (!selectedStudy || selectedStudy.id !== studyId) && studies.length > 0) {
      console.log('Looking for study:', studyId);
      const study = studies.find(s => s.id === studyId);
      console.log('Found study:', study);
      if (study) {
        setSelectedStudy(study);
      }
    }
  }, [studyId, selectedStudy, studies, setSelectedStudy]);

  // Verificar si el usuario es propietario del estudio
  useEffect(() => {
    if (selectedStudy && user) {
      setIsSharedStudy(selectedStudy.user_id !== user.id);
    }
  }, [selectedStudy, user]);

  useEffect(() => {
    if (selectedStudy) {
      fetchElements();
    }
  }, [selectedStudy, fetchElements]);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  // Si no hay ID de estudio, redirigir a la página principal
  if (!studyId) {
    return <Navigate to="/" />;
  }

  // Si no se encuentra el estudio, mostrar mensaje
  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Header
          title={t('title')}
          leftIcon={<ArrowLeft className="w-6 h-6" />}
          onLeftIconClick={onBack}
        />
        <div className="container mx-auto px-4 py-6">
          <p className="text-center text-gray-600">{t('noStudySelected')}</p>
        </div>
      </div>
    );
  }

  const handleSubmit = async (element: Omit<ElementInstance, 'id'>) => {
    if (!selectedStudy) return;
    
    if (editingElement) {
      await updateElement(editingElement.id, element);
    } else {
      await createElement(selectedStudy.id, element);
    }
    setShowForm(false);
    setEditingElement(null);
  };

  const handleDelete = async (elementId: string) => {
    await deleteElement(elementId);
  };

  const handleEdit = (element: ElementInstance) => {
    if (selectedStudy && selectedStudy.time_records) {
      const recordsForElement = selectedStudy.time_records[element.id] || [];
      const typedTimeRecords: TimeRecord[] = recordsForElement.map((record: any) => ({
        id: record.id || crypto.randomUUID(),
        time: Number(record.time) || 0,
        activity: Number(record.activity) || 0,
        elementId: record.elementId || element.id, 
        timestamp: record.timestamp || new Date().toISOString(),
        description: record.description || element.description || '',
        addedToMethod: typeof (record as any).addedToMethod === 'boolean' ? (record as any).addedToMethod : false,
        frequency_repetitions: Number((record as any).frequency_repetitions) || element.frequency_repetitions || 1,
        frequency_cycles: Number((record as any).frequency_cycles) || element.frequency_cycles || 1,
        comment: record.comment || '',
        original_time: Number(record.original_time) || undefined
      }));
      setEditingElement({
        ...element,
        timeRecords: typedTimeRecords
      });
    } else {
    setEditingElement(element);
    }
    setShowForm(true);
  };

  const handleMoveElement = async (elementId: string, direction: 'up' | 'down') => {
    if (!selectedStudy || !elements || elements.length < 2) return;

    // Encontrar el elemento a mover
    const elementIndex = elements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    // Verificar si el movimiento es válido
    if ((direction === 'up' && elementIndex === 0) || 
        (direction === 'down' && elementIndex === elements.length - 1)) {
      return;
    }

    // Determinar la nueva posición
    const newIndex = direction === 'up' ? elementIndex - 1 : elementIndex + 1;
    
    // Crear un nuevo array con los elementos reordenados
    const newElements = [...elements];
    const [movedElement] = newElements.splice(elementIndex, 1);
    newElements.splice(newIndex, 0, movedElement);
    
    // Actualizar las posiciones de todos los elementos
    const updatedElements = newElements.map((element, index) => ({
      ...element,
      position: index
    }));

    try {
      // Actualizar el estudio con los elementos reordenados
      if (selectedStudy && selectedStudy.id) {
        await updateStudy(selectedStudy.id, {
          elements: updatedElements
        });
        
        // Recargar los elementos para reflejar el cambio
        await fetchElements();
      }
    } catch (error) {
      console.error('Error reordenando elementos:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header 
        title={t('title')}
        subtitle={selectedStudy.required_info?.name || ''}
        showSearch={false}
        showActions={true}
        onBack={onBack}
      />
      
      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {isSharedStudy && (
            <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <span>{t('permission_error.message', { ns: 'study' })}</span>
              <button
                onClick={() => setIsSharedStudy(false)}
                className="ml-auto text-amber-500 hover:text-amber-700"
                aria-label={t('close', { ns: 'common' })}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}

          <div className="mx-auto">
            <div className="bg-white rounded-lg shadow-md">
              <ActionButton
                icon={<Plus />}
                label={t('addElement')}
                onClick={() => {
                  setEditingElement(null);
                  setShowForm(true);
                }}
              />
            </div>
          </div>

          <div className="mt-6">
            <div className="mx-auto">
              <MethodList
                elements={elements}
                onDeleteElement={handleDelete}
                onEditElement={handleEdit}
                onMoveElement={handleMoveElement}
              />
            </div>
          </div>

          {showForm && (
            <div className="max-w-3xl mx-auto">
              <div id="method-form-modal">
                <MethodForm
                  onSubmit={handleSubmit}
                  onClose={() => {
                    setShowForm(false);
                    setEditingElement(null);
                  }}
                  initialData={editingElement || undefined}
                />
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};