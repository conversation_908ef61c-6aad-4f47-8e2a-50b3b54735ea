import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Plus, 
  Edit2, 
  Trash2, 
  <PERSON><PERSON>, 
  Star, 
  StarOff, 
  Save, 
  X,
  AlertCircle 
} from 'lucide-react';
import { useImportTemplateStore, ImportTemplate } from '../../store/importTemplateStore';

interface TemplateManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate?: (templateId: string) => void;
}

export const TemplateManager: React.FC<TemplateManagerProps> = ({
  isOpen,
  onClose,
  onSelectTemplate
}) => {
  const { t } = useTranslation(['library', 'common']);
  const {
    templates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    setDefaultTemplate,
    getDefaultTemplate
  } = useImportTemplateStore();

  const [editingTemplate, setEditingTemplate] = useState<ImportTemplate | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  const handleCreateNew = () => {
    const newTemplate: Omit<ImportTemplate, 'id' | 'created_at' | 'updated_at'> = {
      name: t('newTemplate', { ns: 'library' }),
      description: '',
      isDefault: false,
      columnMapping: {
        description: 'Descripción',
        type: 'Tipo',
        repetition_type: 'Tipo de Repetición',
        frequency_cycles: 'Ciclos de Frecuencia',
        frequency_repetitions: 'Repeticiones por Ciclo',
        time_records: 'Tiempo Observado (segundos)',
        activity_records: 'Actividad',
        supplements_percentage: 'Suplementos (%)',
        comments: 'Comentarios'
      },
      studyDefaults: {
        timeUnit: 'seconds',
        shiftMinutes: 455,
        contingency: 2,
        pointsPerHour: 136,
        normalActivity: 100,
        optimalActivity: 133
      }
    };

    setEditingTemplate({ ...newTemplate, id: 'new', created_at: '', updated_at: '' });
    setIsCreating(true);
  };

  const handleSaveTemplate = () => {
    if (!editingTemplate) return;

    if (isCreating) {
      const { id, created_at, updated_at, ...templateData } = editingTemplate;
      createTemplate(templateData);
      setIsCreating(false);
    } else {
      updateTemplate(editingTemplate.id, editingTemplate);
    }

    setEditingTemplate(null);
  };

  const handleCancelEdit = () => {
    setEditingTemplate(null);
    setIsCreating(false);
  };

  const handleDuplicate = (template: ImportTemplate) => {
    const newName = `${template.name} (${t('copy', { ns: 'common' })})`;
    duplicateTemplate(template.id, newName);
  };

  const handleDelete = (templateId: string) => {
    deleteTemplate(templateId);
    setConfirmDelete(null);
  };

  const handleSetDefault = (templateId: string) => {
    setDefaultTemplate(templateId);
  };

  const handleSelectAndClose = (templateId: string) => {
    onSelectTemplate?.(templateId);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {t('templateManager', { ns: 'library' })}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            title={t('close', { ns: 'common' })}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Botón crear nueva plantilla */}
          <div className="mb-6">
            <button
              type="button"
              onClick={handleCreateNew}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              {t('createTemplate', { ns: 'library' })}
            </button>
          </div>

          {/* Lista de plantillas */}
          <div className="space-y-4">
            {templates.map(template => (
              <div
                key={template.id}
                className={`border rounded-lg p-4 ${
                  template.isDefault ? 'border-yellow-300 bg-yellow-50' : 'border-gray-200'
                }`}
              >
                {editingTemplate?.id === template.id ? (
                  /* Modo edición */
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('templateName', { ns: 'library' })}
                        </label>
                        <input
                          type="text"
                          value={editingTemplate.name}
                          onChange={(e) => setEditingTemplate({
                            ...editingTemplate,
                            name: e.target.value
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('description', { ns: 'common' })}
                        </label>
                        <input
                          type="text"
                          value={editingTemplate.description}
                          onChange={(e) => setEditingTemplate({
                            ...editingTemplate,
                            description: e.target.value
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      </div>
                    </div>

                    {/* Mapeo de columnas */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">
                        {t('columnMapping', { ns: 'library' })}
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {Object.entries(editingTemplate.columnMapping).map(([key, value]) => (
                          <div key={key}>
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              {t(key, { ns: 'library' })}
                            </label>
                            <input
                              type="text"
                              value={value}
                              onChange={(e) => setEditingTemplate({
                                ...editingTemplate,
                                columnMapping: {
                                  ...editingTemplate.columnMapping,
                                  [key]: e.target.value
                                }
                              })}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder={t(`${key}Placeholder`, { ns: 'library' })}
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Configuraciones por defecto */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">
                        {t('studyDefaults', { ns: 'library' })}
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            {t('timeUnit', { ns: 'library' })}
                          </label>
                          <select
                            value={editingTemplate.studyDefaults.timeUnit}
                            onChange={(e) => setEditingTemplate({
                              ...editingTemplate,
                              studyDefaults: {
                                ...editingTemplate.studyDefaults,
                                timeUnit: e.target.value
                              }
                            })}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                          >
                            <option value="seconds">{t('seconds', { ns: 'common' })}</option>
                            <option value="minutes">{t('minutes', { ns: 'common' })}</option>
                          </select>
                        </div>
                        {['shiftMinutes', 'contingency', 'pointsPerHour', 'normalActivity', 'optimalActivity'].map(field => (
                          <div key={field}>
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              {t(field, { ns: 'library' })}
                            </label>
                            <input
                              type="number"
                              value={editingTemplate.studyDefaults[field as keyof typeof editingTemplate.studyDefaults]}
                              onChange={(e) => setEditingTemplate({
                                ...editingTemplate,
                                studyDefaults: {
                                  ...editingTemplate.studyDefaults,
                                  [field]: parseFloat(e.target.value) || 0
                                }
                              })}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Botones de acción */}
                    <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                      <button
                        type="button"
                        onClick={handleCancelEdit}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                      >
                        {t('cancel', { ns: 'common' })}
                      </button>
                      <button
                        type="button"
                        onClick={handleSaveTemplate}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                      >
                        <Save className="w-4 h-4" />
                        <span>{t('save', { ns: 'common' })}</span>
                      </button>
                    </div>
                  </div>
                ) : (
                  /* Modo vista */
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-medium text-gray-900">
                          {template.name}
                        </h3>
                        {template.isDefault && (
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        )}
                      </div>
                      {template.description && (
                        <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                      )}
                      <div className="text-xs text-gray-500 mt-2">
                        {t('created', { ns: 'common' })}: {new Date(template.created_at).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {onSelectTemplate && (
                        <button
                          type="button"
                          onClick={() => handleSelectAndClose(template.id)}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                        >
                          {t('select', { ns: 'common' })}
                        </button>
                      )}
                      
                      {!template.isDefault && (
                        <button
                          type="button"
                          onClick={() => handleSetDefault(template.id)}
                          className="p-2 text-gray-400 hover:text-yellow-500 transition-colors"
                          title={t('setAsDefault', { ns: 'library' })}
                        >
                          <StarOff className="w-4 h-4" />
                        </button>
                      )}

                      <button
                        type="button"
                        onClick={() => handleDuplicate(template)}
                        className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                        title={t('duplicate', { ns: 'library' })}
                      >
                        <Copy className="w-4 h-4" />
                      </button>

                      <button
                        type="button"
                        onClick={() => setEditingTemplate(template)}
                        className="p-2 text-gray-400 hover:text-green-500 transition-colors"
                        title={t('edit', { ns: 'common' })}
                      >
                        <Edit2 className="w-4 h-4" />
                      </button>

                      {templates.length > 1 && (
                        <button
                          type="button"
                          onClick={() => setConfirmDelete(template.id)}
                          className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                          title={t('delete', { ns: 'common' })}
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Modal de confirmación de eliminación */}
        {confirmDelete && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-60">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
              <div className="flex items-center space-x-3 mb-4">
                <AlertCircle className="w-6 h-6 text-red-500" />
                <h3 className="text-lg font-medium text-gray-900">
                  {t('confirmDelete', { ns: 'common' })}
                </h3>
              </div>
              <p className="text-gray-600 mb-6">
                {t('confirmDeleteTemplate', { ns: 'library' })}
              </p>
              <div className="flex items-center justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setConfirmDelete(null)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  {t('cancel', { ns: 'common' })}
                </button>
                <button
                  type="button"
                  onClick={() => handleDelete(confirmDelete)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  {t('delete', { ns: 'common' })}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
