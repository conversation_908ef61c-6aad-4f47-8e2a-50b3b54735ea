import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Upload, FileSpreadsheet, AlertCircle, CheckCircle, Loader2, Download } from 'lucide-react';
import * as XLSX from 'xlsx';
import { downloadExcelTemplate, mapElementTypeFromExcel, parseFrequencyFromExcel, cleanNumericValue } from '../../utils/excelTemplate';

interface ExcelImporterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (studyData: {
    name: string;
    company: string;
    date: string;
    elements: any[];
  }) => Promise<void>;
  isImporting?: boolean;
}

interface ExcelRow {
  [key: string]: any;
}

interface ColumnMapping {
  description: string;
  time: string;
  activity: string;
  frequency: string;
  type: string;
  repetitionType: string;
  supplements: string;
  repetitions: string;
  cycles: string;
}

export const ExcelImporterModal: React.FC<ExcelImporterModalProps> = ({
  isOpen,
  onClose,
  onImport,
  isImporting = false
}) => {
  const { t } = useTranslation(['library', 'common']);
  const [file, setFile] = useState<File | null>(null);
  const [excelData, setExcelData] = useState<ExcelRow[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [step, setStep] = useState<'upload' | 'mapping' | 'preview'>('upload');
  const [studyInfo, setStudyInfo] = useState({
    name: '',
    company: '',
    date: new Date().toISOString().split('T')[0]
  });
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({
    description: '',
    time: '',
    activity: '',
    frequency: '',
    type: '',
    repetitionType: '',
    supplements: '',
    repetitions: '',
    cycles: ''
  });
  const [errors, setErrors] = useState<string[]>([]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (!uploadedFile) return;

    // Validar tipo de archivo
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    
    if (!validTypes.includes(uploadedFile.type)) {
      setErrors([t('invalidFileType', { ns: 'library' })]);
      return;
    }

    setFile(uploadedFile);
    setErrors([]);

    // Leer archivo Excel
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convertir a JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
        
        if (jsonData.length < 2) {
          setErrors([t('fileNeedsHeaders', { ns: 'library' })]);
          return;
        }

        // Primera fila como headers
        const fileHeaders = jsonData[0].map((header: any) => String(header || '').trim());
        const rows = jsonData.slice(1).map(row => {
          const rowObj: ExcelRow = {};
          fileHeaders.forEach((header, index) => {
            rowObj[header] = row[index] || '';
          });
          return rowObj;
        }).filter(row => Object.values(row).some(val => val !== ''));

        setHeaders(fileHeaders);
        setExcelData(rows);
        setStep('mapping');
        
        // Auto-mapear columnas comunes
        autoMapColumns(fileHeaders);
        
      } catch (error) {
        console.error('Error reading Excel file:', error);
        setErrors([t('errorReadingFile', { ns: 'library' })]);
      }
    };
    
    reader.readAsArrayBuffer(uploadedFile);
  }, [t]);

  const autoMapColumns = (fileHeaders: string[]) => {
    const mapping: Partial<ColumnMapping> = {};
    
    fileHeaders.forEach(header => {
      const lowerHeader = header.toLowerCase();
      
      if (lowerHeader.includes('descripci') || lowerHeader.includes('description')) {
        mapping.description = header;
      } else if (lowerHeader.includes('tiempo') || lowerHeader.includes('time')) {
        mapping.time = header;
      } else if (lowerHeader.includes('actividad') || lowerHeader.includes('activity')) {
        mapping.activity = header;
      } else if (lowerHeader.includes('frecuencia') || lowerHeader.includes('frequency')) {
        mapping.frequency = header;
      } else if (lowerHeader.includes('tipo') || lowerHeader.includes('type')) {
        mapping.type = header;
      } else if (lowerHeader.includes('suplemento') || lowerHeader.includes('supplement')) {
        mapping.supplements = header;
      } else if (lowerHeader.includes('repeticion') || lowerHeader.includes('repetition')) {
        mapping.repetitions = header;
      } else if (lowerHeader.includes('ciclo') || lowerHeader.includes('cycle')) {
        mapping.cycles = header;
      }
    });
    
    setColumnMapping(prev => ({ ...prev, ...mapping }));
  };

  const validateMapping = () => {
    const newErrors: string[] = [];
    
    if (!columnMapping.description) {
      newErrors.push(t('descriptionColumnRequired', { ns: 'library' }));
    }
    
    if (!studyInfo.name.trim()) {
      newErrors.push(t('studyNameRequired', { ns: 'library' }));
    }
    
    if (!studyInfo.company.trim()) {
      newErrors.push(t('companyRequired', { ns: 'library' }));
    }
    
    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handlePreview = () => {
    if (!validateMapping()) return;
    setStep('preview');
  };

  const processElements = () => {
    return excelData.map((row, index) => {
      // Procesar frecuencia usando la utilidad
      const frequencyStr = row[columnMapping.frequency] || '1/1';
      const { repetitions, cycles } = parseFrequencyFromExcel(frequencyStr);

      // Limpiar valores numéricos
      const time = cleanNumericValue(row[columnMapping.time], 0);
      const activity = cleanNumericValue(row[columnMapping.activity], 100);
      const supplementPercentage = cleanNumericValue(row[columnMapping.supplements], 0);

      const element = {
        id: crypto.randomUUID(),
        name: row[columnMapping.description] || `Elemento ${index + 1}`,
        description: row[columnMapping.description] || '',
        time: time,
        activity: activity,
        type: mapElementTypeFromExcel(row[columnMapping.type]),
        position: index + 1,
        repetition_type: cycles > 1 ? 'frequency' : 'repetitive',
        frequency_cycles: cycles,
        frequency_repetitions: repetitions,
        supplements: supplementPercentage > 0 ? [{
          id: crypto.randomUUID(),
          percentage: supplementPercentage,
          is_forced: true,
          points: {},
          factor_selections: {}
        }] : []
      };

      return element;
    });
  };

  const handleImport = async () => {
    try {
      const elements = processElements();
      await onImport({
        ...studyInfo,
        elements
      });
      handleClose();
    } catch (error) {
      console.error('Error importing Excel:', error);
      setErrors([t('errorImporting', { ns: 'library' })]);
    }
  };

  const handleClose = () => {
    setFile(null);
    setExcelData([]);
    setHeaders([]);
    setStep('upload');
    setStudyInfo({
      name: '',
      company: '',
      date: new Date().toISOString().split('T')[0]
    });
    setColumnMapping({
      description: '',
      time: '',
      activity: '',
      frequency: '',
      type: '',
      repetitionType: '',
      supplements: '',
      repetitions: '',
      cycles: ''
    });
    setErrors([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FileSpreadsheet className="w-6 h-6 text-green-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              {t('importFromExcel', { ns: 'library' })}
            </h2>
          </div>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            disabled={isImporting}
            title={t('close', { ns: 'common' })}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Errores */}
          {errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-red-800 mb-1">
                    {t('errorsFound', { ns: 'library' })}
                  </h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Step 1: Upload */}
          {step === 'upload' && (
            <div className="text-center py-12">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {t('uploadExcelFile', { ns: 'library' })}
                </h3>
                <p className="text-gray-500 mb-4">
                  {t('supportedFormats', { ns: 'library' })}: .xlsx, .xls
                </p>
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="excel-upload"
                />
                <label
                  htmlFor="excel-upload"
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors cursor-pointer"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {t('selectFile', { ns: 'common' })}
                </label>

                {/* Botón para descargar plantilla */}
                <button
                  type="button"
                  onClick={() => downloadExcelTemplate(true)}
                  className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  {t('downloadTemplate', { ns: 'library' })}
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Mapping */}
          {step === 'mapping' && (
            <div className="space-y-6">
              {/* Información del estudio */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {t('studyInformation', { ns: 'library' })}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('studyName', { ns: 'library' })} *
                    </label>
                    <input
                      type="text"
                      value={studyInfo.name}
                      onChange={(e) => setStudyInfo(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder={t('studyNamePlaceholder', { ns: 'library' })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('company', { ns: 'common' })} *
                    </label>
                    <input
                      type="text"
                      value={studyInfo.company}
                      onChange={(e) => setStudyInfo(prev => ({ ...prev, company: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder={t('companyPlaceholder', { ns: 'library' })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('date', { ns: 'common' })}
                    </label>
                    <input
                      type="date"
                      value={studyInfo.date}
                      onChange={(e) => setStudyInfo(prev => ({ ...prev, date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      title={t('studyDate', { ns: 'library' })}
                    />
                  </div>
                </div>
              </div>

              {/* Mapeo de columnas */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {t('columnMapping', { ns: 'library' })}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries({
                    description: t('elementDescription', { ns: 'library' }),
                    time: t('observedTime', { ns: 'library' }),
                    activity: t('observedActivity', { ns: 'library' }),
                    type: t('elementType', { ns: 'library' }),
                    repetitionType: t('repetitionType', { ns: 'library' }),
                    supplements: t('supplements', { ns: 'library' }),
                    repetitions: t('repetitionsPerCycle', { ns: 'library' }),
                    cycles: t('frequencyCycles', { ns: 'library' })
                  }).map(([key, label]) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {label} {key === 'description' && '*'}
                      </label>
                      <select
                        value={columnMapping[key as keyof ColumnMapping]}
                        onChange={(e) => setColumnMapping(prev => ({
                          ...prev,
                          [key]: e.target.value
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        title={`${t('selectColumn', { ns: 'library' })} - ${label}`}
                      >
                        <option value="">{t('selectColumn', { ns: 'library' })}</option>
                        {headers.map(header => (
                          <option key={header} value={header}>{header}</option>
                        ))}
                      </select>
                    </div>
                  ))}
                </div>
              </div>

              {/* Vista previa de datos */}
              {excelData.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    {t('dataPreview', { ns: 'library' })} ({excelData.length} {t('rows', { ns: 'library' })})
                  </h3>
                  <div className="overflow-x-auto border border-gray-200 rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {headers.slice(0, 5).map(header => (
                            <th key={header} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {excelData.slice(0, 3).map((row, index) => (
                          <tr key={index}>
                            {headers.slice(0, 5).map(header => (
                              <td key={header} className="px-4 py-2 text-sm text-gray-900">
                                {String(row[header] || '')}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Botones */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setStep('upload')}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  {t('back', { ns: 'common' })}
                </button>
                <button
                  type="button"
                  onClick={handlePreview}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  {t('preview', { ns: 'library' })}
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Preview */}
          {step === 'preview' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <h3 className="text-lg font-medium text-gray-900">
                  {t('readyToImport', { ns: 'library' })}
                </h3>
              </div>

              {/* Resumen del estudio */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">{t('studyInformation', { ns: 'library' })}</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">{t('name', { ns: 'common' })}:</span>
                    <span className="ml-2 font-medium">{studyInfo.name}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">{t('company', { ns: 'common' })}:</span>
                    <span className="ml-2 font-medium">{studyInfo.company}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">{t('elements', { ns: 'library' })}:</span>
                    <span className="ml-2 font-medium">{excelData.length}</span>
                  </div>
                </div>
              </div>

              {/* Vista previa de elementos */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">{t('elementsPreview', { ns: 'library' })}</h4>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {processElements().slice(0, 10).map((element, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg">
                      <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-semibold">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{element.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{element.time}s</span>
                          <span>{element.activity}%</span>
                          <span>{element.type}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {excelData.length > 10 && (
                    <p className="text-sm text-gray-500 text-center py-2">
                      {t('andMoreElements', { count: excelData.length - 10, ns: 'library' })}
                    </p>
                  )}
                </div>
              </div>

              {/* Botones */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setStep('mapping')}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  disabled={isImporting}
                >
                  {t('back', { ns: 'common' })}
                </button>
                <button
                  type="button"
                  onClick={handleImport}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  disabled={isImporting}
                >
                  {isImporting && <Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isImporting ? t('importing', { ns: 'library' }) : t('importStudy', { ns: 'library' })}</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
