{"name": "industrial-time-study", "private": true, "version": "42.1.9", "type": "module", "scripts": {"dev": "vite --host", "dev:mobile": "vite --config vite.config.mobile.ts --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "update-version": "node scripts/update-version.cjs"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.2", "@mui/material": "^5.15.11", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.4.0", "@supabase/supabase-js": "^2.39.7", "@tippyjs/react": "^4.2.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/nodemailer": "^6.4.17", "browser-image-compression": "^2.0.2", "canvg": ">=4.0.3", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^3.3.1", "dhtmlx-gantt": "^9.0.10", "dompurify": "3.2.4", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.18.2", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^7.2.0", "jspdf": ">=3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "micro": "^10.0.1", "nodemailer": "^6.9.10", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-i18next": "^14.0.5", "react-markdown": "^9.1.0", "react-router-dom": "^6.22.1", "react-toastify": "^11.0.5", "regenerator-runtime": "^0.14.1", "remark-emoji": "^5.0.1", "remark-gfm": "^4.0.1", "stripe": "^14.17.0", "tailwind-merge": "^2.6.0", "tippy.js": "^6.3.7", "vis-data": "^7.1.9", "vis-timeline": "^7.7.4", "vis-util": "^5.0.7", "zustand": "^4.5.1"}, "devDependencies": {"@eslint/js": "^8.56.0", "@eslint/plugin-kit": "^0.2.3", "@tailwindcss/typography": "^0.5.10", "@types/exceljs": "^0.5.3", "@types/i18next": "^12.1.0", "@types/react": "^18.2.58", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "canvg": ">=4.0.3", "cross-env": "^7.0.3", "cross-spawn": "^7.0.3", "esbuild": "^0.25.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "jspdf": ">=3.0.1", "postcss": "^8.4.35", "sharp": "^0.33.2", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^6.2.2", "vite-plugin-pwa": "^0.21.1", "workbox-window": "^7.0.0"}}