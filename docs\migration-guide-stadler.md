# 🚀 Guía de Migración - Proyecto Stadler

## Resumen
Esta guía te ayudará a migrar completamente la aplicación Cronometras del dominio `app.cronometras.com` al nuevo dominio `stadler.cronometras.com`.

## 📋 Prerequisitos

### 1. Crear nuevo proyecto en Supabase
1. Ve a [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Crea un nuevo proyecto llamado "Cronometras Stadler"
3. Guarda la URL del proyecto y las claves de API

### 2. Variables de Entorno Necesarias
Crea un archivo `.env` con las siguientes variables:

```env
# Proyecto Stadler - Supabase
STADLER_SUPABASE_URL=https://tu-proyecto-stadler.supabase.co
STADLER_SUPABASE_SERVICE_KEY=tu_clave_secreta_servicio

# URLs de la aplicación
VITE_APP_URL=https://stadler.cronometras.com
VITE_API_URL=https://api.stadler.cronometras.com
VITE_DEV_URL=https://localhost:5173

# Supabase (para el cliente)
VITE_PUBLIC_SUPABASE_URL=https://tu-proyecto-stadler.supabase.co
VITE_PUBLIC_SUPABASE_ANON_KEY=tu_clave_publica

# Stripe (configurar nuevas claves para el dominio Stadler)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
VITE_STRIPE_SECRET_KEY=sk_test_...
```

## 🗄️ Migración de Base de Datos

### Paso 1: Ejecutar Script de Migración
```bash
# Desde la raíz del proyecto
STADLER_SUPABASE_URL=tu_url STADLER_SUPABASE_SERVICE_KEY=tu_clave node scripts/migrate_to_stadler.js
```

### Paso 2: Verificar Estructura
El script migrará automáticamente:

#### Tablas Principales:
- `user_study_limits` - Límites de créditos por usuario
- `library_elements` - Elementos de biblioteca reutilizables
- `studies` - Estudios de tiempo
- `user_devices` - Dispositivos registrados por usuario
- `organizations` - Organizaciones (si aplica)
- `webhook_logs` - Logs de webhooks de Stripe

#### Funciones:
- `update_updated_at_column()` - Actualización automática de timestamps
- Funciones RPC para organizaciones
- Funciones de validación de créditos

#### Políticas RLS:
- Políticas de acceso por usuario
- Políticas de administrador (<EMAIL>)
- Políticas de elementos compartidos
- Políticas de organizaciones

#### Índices:
- Índices optimizados para consultas frecuentes
- Índices de usuario para todas las tablas principales

### Paso 3: Configurar Storage
```sql
-- El script ya incluye esto, pero puedes verificar manualmente:
INSERT INTO storage.buckets (id, name, public) 
VALUES ('public', 'public', true) 
ON CONFLICT DO NOTHING;
```

## 🔧 Edge Functions

### Desplegar Functions manualmente:
```bash
# Asegúrate de tener Supabase CLI instalado
supabase login

# Conectar al proyecto Stadler
supabase link --project-ref tu-proyecto-stadler-id

# Desplegar cada función
supabase functions deploy stripe-webhook
supabase functions deploy stripe-payment
supabase functions deploy stripe-payment-v2
supabase functions deploy stripe-cancel
```

### Configurar Variables de Entorno en Edge Functions:
En el dashboard de Supabase > Edge Functions > Settings:
```
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## 💳 Configuración de Stripe

### 1. Configurar Webhooks
- URL: `https://tu-proyecto-stadler.supabase.co/functions/v1/stripe-webhook`
- Eventos: `checkout.session.completed`, `invoice.payment_succeeded`

### 2. Actualizar Dominios Autorizados
- Añadir `stadler.cronometras.com` en los dominios autorizados
- Configurar URLs de éxito y cancelación

## 🌐 Configuración DNS

### Registros A/CNAME necesarios:
```
stadler.cronometras.com -> tu-servidor-ip
api.stadler.cronometras.com -> tu-servidor-api-ip
```

## 🔐 Configuración de Autenticación

### En Supabase Dashboard > Authentication > Settings:
1. **Site URL**: `https://stadler.cronometras.com`
2. **Redirect URLs**: 
   - `https://stadler.cronometras.com/**`
   - `https://localhost:5173/**` (para desarrollo)

## 📊 Migración de Datos (Opcional)

Si necesitas migrar datos existentes:

### 1. Exportar Datos del Proyecto Original
```sql
-- Conectar al proyecto original
SELECT * FROM user_study_limits;
SELECT * FROM library_elements WHERE is_shared = true;
-- etc.
```

### 2. Importar al Nuevo Proyecto
```sql
-- Usar el SQL Editor en el dashboard de Supabase Stadler
INSERT INTO user_study_limits (user_id, user_email, total_credits, used_credits)
VALUES (...);
```

## ✅ Lista de Verificación Final

### Base de Datos:
- [ ] Todas las tablas creadas correctamente
- [ ] Políticas RLS funcionando
- [ ] Funciones e índices aplicados
- [ ] Storage bucket configurado

### Autenticación:
- [ ] URLs de redirect configuradas
- [ ] Dominios autorizados
- [ ] Cookies funcionando con nuevo dominio

### Pagos:
- [ ] Webhooks de Stripe configurados
- [ ] Edge Functions desplegadas
- [ ] Variables de entorno configuradas

### Aplicación:
- [ ] Variables de entorno actualizadas
- [ ] URLs de API corregidas
- [ ] Compilación exitosa
- [ ] Deploy en nuevo dominio

## 🐛 Solución de Problemas Comunes

### Error: "exec_sql function not found"
```sql
-- Crear función temporalmente en Supabase
CREATE OR REPLACE FUNCTION exec_sql(query text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE query;
  RETURN '{"success": true}'::json;
END;
$$;
```

### Error: RLS policies conflicting
- Revisar que las políticas no se dupliquen
- Usar `DROP POLICY IF EXISTS` antes de crear nuevas

### Error: Storage bucket permissions
```sql
-- Verificar permisos del bucket
UPDATE storage.buckets SET public = true WHERE id = 'public';
```

## 📞 Soporte
Si encuentras problemas durante la migración, revisa:
1. Los logs del script de migración
2. Los logs de Supabase Dashboard
3. Las variables de entorno
4. La configuración de DNS

¡La migración debería completarse sin problemas siguiendo esta guía! 