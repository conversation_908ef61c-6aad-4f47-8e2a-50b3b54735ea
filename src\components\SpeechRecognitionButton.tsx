import React from 'react';
import { Mic } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';

interface SpeechRecognitionButtonProps {
  onTranscript: (text: string) => void;
  className?: string;
}

export const SpeechRecognitionButton: React.FC<SpeechRecognitionButtonProps> = ({
  onTranscript,
  className = ''
}) => {
  const { i18n } = useTranslation('cronoSeguido');
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    isSupported,
    updateLanguage
  } = useSpeechRecognition();

  React.useEffect(() => {
    if (transcript) {
      onTranscript(transcript);
    }
  }, [transcript, onTranscript]);

  // Actualizar el idioma del reconocimiento de voz cuando cambie el idioma de la aplicación
  React.useEffect(() => {
    updateLanguage(i18n.language);
  }, [i18n.language, updateLanguage]);

  if (!isSupported) {
    return null;
  }

  return (
    <button
      className={`p-2 rounded-full bg-blue-500 hover:bg-blue-600 text-white ${
        isListening ? 'animate-pulse' : ''
      } ${className}`}
      onMouseDown={startListening}
      onMouseUp={stopListening}
      onMouseLeave={stopListening}
      type="button"
    >
      <Mic className="w-5 h-5" />
    </button>
  );
};