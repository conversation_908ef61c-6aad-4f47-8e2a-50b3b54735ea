# Página de Método

## Descripción
Componente para la gestión de elementos del estudio.

## Estructura
```
MethodPage
├── MethodList
├── MethodForm
├── DeleteConfirmModal
└── ActionButton
```

## Flujo de Datos
1. Carga de elementos
2. Creación/Edición
3. Reordenamiento
4. Eliminación
5. Integración con biblioteca

## Interacciones
- Gestión CRUD de elementos
- Drag & Drop para reordenar
- Validación de datos
- Integración con biblioteca

## Estados Principales
```typescript
{
  elements: WorkElement[];
  showForm: boolean;
  editingElement: WorkElement | null;
  showDeleteModal: boolean;
}
```

## Hooks Utilizados
- useMethodStore
- useStudyStore
- useDragAndDrop