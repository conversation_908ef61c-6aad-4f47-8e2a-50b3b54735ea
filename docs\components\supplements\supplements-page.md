# Página de Suplementos

## Descripción
Componente para la gestión de suplementos por elemento.

## Estructura
```
SupplementsPage
├── SupplementsTable
├── SupplementsFactors
├── SupplementsForce
├── SupplementsMachine
└── CopySupplementsModal
```

## Flujo de Datos
1. Selección de tabla
2. Configuración de factores
3. Cálculo de puntos
4. Conversión a porcentaje
5. Persistencia de datos

## Interacciones
- Selección de factores
- Cálculos automáticos
- Copia entre elementos
- Reseteo de valores

## Estados Principales
```typescript
{
  selectedTable: 'oit' | 'tal' | null;
  selectedElement: WorkElement | null;
  activeTab: 'points' | 'force' | 'machine';
  points: Record<string, number>;
  factorSelections: Record<string, Selection>;
}
```

## Hooks Utilizados
- useSupplementStore
- useTalTablesStore
- useMethodStore