-- Eliminar todas las políticas existentes
drop policy if exists "Organization admins can manage members" on organization_members;
drop policy if exists "Users can view members of their organizations" on organization_members;
drop policy if exists "Users can view their own memberships" on organization_members;
drop policy if exists "Organization owners can manage all members" on organization_members;
drop policy if exists "Allow member creation through join request process" on organization_members;
drop policy if exists "Users can create organizations" on organizations;
drop policy if exists "Organization members can view their organizations" on organizations;

-- Políticas simples para organizations
create policy "Users can view organizations they created"
    on organizations for select
    to authenticated
    using (created_by = auth.uid());

create policy "Users can create organizations"
    on organizations for insert
    to authenticated
    with check (created_by = auth.uid());

-- Políticas simples para organization_members
create policy "Users can view organization memberships"
    on organization_members for select
    to authenticated
    using (true);

create policy "Users can manage their own memberships"
    on organization_members for all
    to authenticated
    using (user_id = auth.uid());

create policy "Organization creators can manage members"
    on organization_members for all
    to authenticated
    using (
        exists (
            select 1 from organizations
            where id = organization_members.organization_id
            and created_by = auth.uid()
        )
    );
