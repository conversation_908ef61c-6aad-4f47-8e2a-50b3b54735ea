# useTimeRecords Hook

## Descripción
Hook para gestionar y calcular estadísticas de registros de tiempo.

## Parámetros
```typescript
interface TimeRecord {
  id: string;
  time: number;
  activity: number;
  timestamp: string;
  comment?: string;
}

function useTimeRecords(
  records: TimeRecord[],
  localRecords: TimeRecord[] = []
)
```

## Funcionalidades
- Cálculo de estadísticas en tiempo real
- Combinación de registros locales y remotos
- Cálculos de promedios y totales
- Validación de datos

## Retorno
```typescript
{
  totalTime: number;
  averageTime: number;
  recordCount: number;
  averageActivity: number;
}
```

## Uso
```typescript
const stats = useTimeRecords(records, localRecords);

console.log(`Tiempo promedio: ${stats.averageTime}`);
console.log(`Actividad promedio: ${stats.averageActivity}`);
```