import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, Filter, X, Building2, Calendar, User } from 'lucide-react';

interface LibrarySearchBarProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters: {
    company?: string;
    dateFrom?: string;
    dateTo?: string;
    hasElements?: boolean;
  };
  onFiltersChange: (filters: LibrarySearchBarProps['filters']) => void;
  availableCompanies: string[];
}

export const LibrarySearchBar: React.FC<LibrarySearchBarProps> = ({
  searchTerm,
  onSearchChange,
  filters,
  onFiltersChange,
  availableCompanies
}) => {
  const { t } = useTranslation(['library', 'common']);
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    setShowFilters(false);
  };

  const handleClearFilters = () => {
    const emptyFilters = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const hasActiveFilters = Object.keys(filters).length > 0;
  const activeFilterCount = Object.values(filters).filter(value => 
    value !== undefined && value !== '' && value !== null
  ).length;

  return (
    <div className="space-y-4">
      {/* Barra de búsqueda principal */}
      <div className="flex items-center space-x-3">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
            placeholder={t('searchStudiesAndElements', { ns: 'library' })}
          />
          {searchTerm && (
            <button
              onClick={() => onSearchChange('')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>

        {/* Botón de filtros */}
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`
            relative px-4 py-2 border rounded-lg font-medium transition-colors
            ${hasActiveFilters 
              ? 'border-purple-300 bg-purple-50 text-purple-700' 
              : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
            }
          `}
        >
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4" />
            <span>{t('filters', { ns: 'common' })}</span>
            {activeFilterCount > 0 && (
              <span className="bg-purple-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </div>
        </button>
      </div>

      {/* Panel de filtros expandible */}
      {showFilters && (
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Filtro por empresa */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Building2 className="w-4 h-4 inline mr-1" />
                {t('company', { ns: 'common' })}
              </label>
              <select
                value={localFilters.company || ''}
                onChange={(e) => setLocalFilters(prev => ({
                  ...prev,
                  company: e.target.value || undefined
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="">{t('allCompanies', { ns: 'library' })}</option>
                {availableCompanies.map(company => (
                  <option key={company} value={company}>
                    {company}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro por fecha desde */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                {t('dateFrom', { ns: 'library' })}
              </label>
              <input
                type="date"
                value={localFilters.dateFrom || ''}
                onChange={(e) => setLocalFilters(prev => ({
                  ...prev,
                  dateFrom: e.target.value || undefined
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>

            {/* Filtro por fecha hasta */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                {t('dateTo', { ns: 'library' })}
              </label>
              <input
                type="date"
                value={localFilters.dateTo || ''}
                onChange={(e) => setLocalFilters(prev => ({
                  ...prev,
                  dateTo: e.target.value || undefined
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
          </div>

          {/* Filtros adicionales */}
          <div className="mt-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={localFilters.hasElements || false}
                onChange={(e) => setLocalFilters(prev => ({
                  ...prev,
                  hasElements: e.target.checked || undefined
                }))}
                className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <span className="text-sm text-gray-700">
                {t('onlyStudiesWithElements', { ns: 'library' })}
              </span>
            </label>
          </div>

          {/* Botones de acción */}
          <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={handleClearFilters}
              className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              {t('clearFilters', { ns: 'common' })}
            </button>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowFilters(false)}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                {t('cancel', { ns: 'common' })}
              </button>
              <button
                onClick={handleApplyFilters}
                className="px-4 py-2 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700 transition-colors"
              >
                {t('applyFilters', { ns: 'common' })}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Indicadores de filtros activos */}
      {hasActiveFilters && (
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-600">{t('activeFilters', { ns: 'common' })}:</span>
          {filters.company && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {filters.company}
              <button
                onClick={() => onFiltersChange({ ...filters, company: undefined })}
                className="ml-1 hover:text-purple-600"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
          {(filters.dateFrom || filters.dateTo) && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {filters.dateFrom && filters.dateTo 
                ? `${filters.dateFrom} - ${filters.dateTo}`
                : filters.dateFrom 
                  ? `Desde ${filters.dateFrom}`
                  : `Hasta ${filters.dateTo}`
              }
              <button
                onClick={() => onFiltersChange({ 
                  ...filters, 
                  dateFrom: undefined, 
                  dateTo: undefined 
                })}
                className="ml-1 hover:text-purple-600"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
          {filters.hasElements && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {t('withElements', { ns: 'library' })}
              <button
                onClick={() => onFiltersChange({ ...filters, hasElements: undefined })}
                className="ml-1 hover:text-purple-600"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};
