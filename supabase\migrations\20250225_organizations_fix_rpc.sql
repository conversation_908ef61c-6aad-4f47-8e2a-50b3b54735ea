-- Drop existing policies
drop policy if exists "Organization admins can manage members" on organization_members;
drop policy if exists "Users can view members of their organizations" on organization_members;
drop policy if exists "Users can view their own memberships" on organization_members;
drop policy if exists "Organization owners can manage all members" on organization_members;
drop policy if exists "Allow member creation through join request process" on organization_members;

-- Create a function to get user organizations
create or replace function get_user_organizations(p_user_id uuid)
returns setof organizations
language sql
security definer
set search_path = public
as $$
    select distinct o.*
    from organizations o
    inner join organization_members om on om.organization_id = o.id
    where om.user_id = p_user_id;
$$;
