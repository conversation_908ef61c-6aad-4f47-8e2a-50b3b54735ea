import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { Header } from '../components/Header';
import { useStudyStore } from '../store/studyStore';
import { useCreditStore } from '../store/creditStore';
import { Study } from '../types';
import { NoCreditsModal } from '../components/modals/NoCreditsModal';
import StudyForm from '../components/StudyForm';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl';
import { toast } from '../components/ui/use-toast';

export const StudyPage: React.FC = () => {
  const { t } = useTranslation(['study', 'common']);
  const location = useLocation();
  const navigate = useNavigate();
  const [showNoCreditsModal, setShowNoCreditsModal] = useState(false);
  const { createStudy, updateStudy, setSelectedStudy, selectedStudy } = useStudyStore();
  const { credits, studyLimits, fetchCredits, checkCredits } = useCreditStore();
  const [isLoading, setIsLoading] = useState(false);
  
  // Obtener el estudio desde la URL y usarlo como fuente principal de datos
  const studyFromUrl = useStudyFromUrl();
  
  // Determinar qué estudio usar: el de la URL si está disponible, o el seleccionado en el store
  const study = studyFromUrl || selectedStudy;

  // Check if we're creating a new study based on URL
  const isNewStudy = !location.pathname.includes('/study/');

  useEffect(() => {
    if (isNewStudy) {
      setSelectedStudy(null);
    }
  }, [isNewStudy, setSelectedStudy]);

  useEffect(() => {
    if (isNewStudy) {
      // Comprobar créditos al entrar en la página de nuevo estudio
      const verifyCredits = async () => {
        console.log('🔍 Verifying credits for new study...');
        const creditCheck = await checkCredits();
        console.log('✅ Credit check result:', creditCheck);
        
        if (!creditCheck.hasAvailableCredits) {
          console.log('❌ No credits available');
          setShowNoCreditsModal(true);
        } else {
          console.log('✅ Credits available:', {
            monthly: creditCheck.monthlyCredits,
            extra: creditCheck.extraCredits,
            total: creditCheck.remainingCredits
          });
        }
      };
      verifyCredits();
    }
  }, [isNewStudy, checkCredits]);

  // Cargar créditos al montar el componente
  useEffect(() => {
    const loadCredits = async () => {
      console.log('Loading credits...');
      await fetchCredits();
      console.log('Credits loaded:', { credits, studyLimits });
    };
    loadCredits();
  }, [fetchCredits]);

  const handleSubmit = async (studyData: Partial<Study>) => {
    console.log('🚀 Starting study submission...');
    setIsLoading(true);
    try {
      if (study && !isNewStudy) {
        // Para actualizaciones, esperar a que termine antes de navegar
        const updatedStudy = await updateStudy(study.id, studyData);
        console.log('✅ Study updated successfully:', updatedStudy);
        // Navegar a la página principal con el ID del estudio actualizado
        navigate('/', { state: { selectStudyId: study.id } });
      } else {
        // Verificar créditos antes de crear un nuevo estudio
        const creditCheck = await checkCredits();
        console.log('💳 Credit check before creation:', creditCheck);
        
        if (!creditCheck.hasAvailableCredits) {
          console.log('❌ No credits available for creation');
          setShowNoCreditsModal(true);
          setIsLoading(false);
          return;
        }

        // Para nuevos estudios, esperar a que se cree antes de navegar
        try {
          const folderId = (location.state as { folderId?: string })?.folderId;
          const studyPayload = {
            ...studyData,
            ...(folderId && { folder_id: folderId }),
          };
          const newStudy = await createStudy(studyPayload);
          console.log('✅ Study created successfully:', newStudy);
          
          if (newStudy && newStudy.id) {
            // Establecer explícitamente el estudio seleccionado antes de navegar
            setSelectedStudy(newStudy);
            // Navegar a la página principal con el ID del nuevo estudio y forzar la selección
            navigate('/', { state: { selectStudyId: newStudy.id, forceSelect: true } });
          } else {
            console.error('❌ El estudio se creó pero no tiene un ID válido');
            setIsLoading(false);
            toast({
              title: t('study:createError'),
              description: t('study:createErrorDescription'),
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error('❌ Error creating study:', error);
          setIsLoading(false);
          toast({
            title: t('study:createError'),
            description: error instanceof Error ? error.message : t('study:createErrorDescription'),
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error('❌ Error saving study:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={isNewStudy ? t('study:new') : t('study:edit')}
        subtitle={study?.required_info?.name || ''}
        showSearch={false}
        showActions={false}
        onBack={() => navigate('/')}
      />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {isNewStudy && studyLimits && (
            <div className="text-right">
              <span className="text-sm text-gray-600">
                {t('study:credits')}: {studyLimits.subscription_plan ? '∞' : (
                  `${studyLimits.monthly_credits - studyLimits.used_monthly_credits} + ${studyLimits.extra_credits - studyLimits.used_extra_credits}`
                )}
              </span>
            </div>
          )}
          <StudyForm 
            onSubmit={handleSubmit} 
            loading={isLoading} 
            className="bg-white rounded-lg shadow-lg p-6"
          />
        </div>
      </main>

      <NoCreditsModal
        isOpen={showNoCreditsModal}
        onClose={() => {
          setShowNoCreditsModal(false);
          navigate('/');
        }}
        onNavigateToProfile={() => {
          setShowNoCreditsModal(false);
          navigate('/profile');
        }}
      />
    </div>
  );
};