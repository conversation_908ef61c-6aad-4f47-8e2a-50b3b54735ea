import * as XLSX from 'xlsx';

/**
 * Datos de ejemplo para la plantilla de Excel
 */
export const exampleData = [
  {
    'Descripción': '10- Elemento de prueba, 1.',
    'Tipo': 'machineTypes.MP',
    'Frecuencia': '1/1',
    'Tiempo Observado (segundos)': 6.017,
    'Actividad': 108,
    'Suplementos (%)': 15.0,
    'Tiempo Final (segundos)': 7.497
  },
  {
    'Descripción': '20- Elemento de prueba, 2.',
    'Tipo': 'machineTypes.MP',
    'Frecuencia': '1/1',
    'Tiempo Observado (segundos)': 6.277,
    'Actividad': 107,
    'Suplementos (%)': 15.0,
    'Tiempo Final (segundos)': 7.700
  },
  {
    'Descripción': '30- Elemento de prueba, 3.',
    'Tipo': 'machineTypes.MM',
    'Frecuencia': '1/1',
    'Tiempo Observado (segundos)': 30.429,
    'Actividad': 108,
    'Suplementos (%)': 15.0,
    'Tiempo Final (segundos)': 37.910
  },
  {
    'Descripción': '40- Elemento frecuencial 1.',
    'Tipo': 'machineTypes.MP',
    'Frecuencia': '1/150',
    'Tiempo Observado (segundos)': 35.560,
    'Actividad': 110,
    'Suplementos (%)': 15.0,
    'Tiempo Final (segundos)': 0.300
  },
  {
    'Descripción': '50- Elemento de máquina.',
    'Tipo': 'machineTypes.TM',
    'Frecuencia': '1/1',
    'Tiempo Observado (segundos)': 36.000,
    'Actividad': 100,
    'Suplementos (%)': 33.0,
    'Tiempo Final (segundos)': 47.880
  }
];

/**
 * Instrucciones para el usuario
 */
export const instructions = [
  'INSTRUCCIONES PARA IMPORTAR ESTUDIOS DESDE EXCEL:',
  '',
  '1. COLUMNAS REQUERIDAS:',
  '   - Descripción: Descripción del elemento (OBLIGATORIO)',
  '   - Tiempo Observado: Tiempo en segundos (recomendado)',
  '   - Actividad: Actividad observada (recomendado)',
  '',
  '2. COLUMNAS OPCIONALES:',
  '   - Tipo: Tipo de elemento (MP=Máquina Parada, MM=Máquina Marcha, TM=Tiempo Máquina)',
  '   - Frecuencia: Formato "repeticiones/ciclos" (ej: 1/1, 1/150)',
  '   - Suplementos (%): Porcentaje de suplementos',
  '',
  '3. TIPOS DE ELEMENTO SOPORTADOS:',
  '   - machineTypes.MP o MP = Máquina Parada',
  '   - machineTypes.MM o MM = Máquina en Marcha', 
  '   - machineTypes.TM o TM = Tiempo Máquina',
  '',
  '4. FORMATO DE FRECUENCIA:',
  '   - 1/1 = Elemento repetitivo (cada ciclo)',
  '   - 1/150 = Elemento frecuencial (1 vez cada 150 ciclos)',
  '',
  '5. NOTAS IMPORTANTES:',
  '   - La primera fila debe contener los nombres de las columnas',
  '   - Las filas vacías se ignorarán automáticamente',
  '   - Los valores numéricos pueden usar punto o coma decimal',
  '   - El sistema auto-detectará las columnas por nombre',
  '',
  'EJEMPLO DE DATOS:'
];

/**
 * Genera y descarga una plantilla de Excel
 */
export const downloadExcelTemplate = (includeExampleData: boolean = true) => {
  const workbook = XLSX.utils.book_new();
  
  if (includeExampleData) {
    // Hoja con datos de ejemplo
    const exampleSheet = XLSX.utils.json_to_sheet(exampleData);
    
    // Ajustar ancho de columnas
    const columnWidths = [
      { wch: 30 }, // Descripción
      { wch: 20 }, // Tipo
      { wch: 12 }, // Frecuencia
      { wch: 25 }, // Tiempo Observado
      { wch: 10 }, // Actividad
      { wch: 15 }, // Suplementos
      { wch: 20 }  // Tiempo Final
    ];
    exampleSheet['!cols'] = columnWidths;
    
    XLSX.utils.book_append_sheet(workbook, exampleSheet, 'Ejemplo');
  }
  
  // Hoja con plantilla vacía
  const emptyTemplate = [
    {
      'Descripción': '',
      'Tipo': '',
      'Frecuencia': '1/1',
      'Tiempo Observado (segundos)': '',
      'Actividad': 100,
      'Suplementos (%)': 15,
      'Repeticiones por Ciclo': 1,
      'Ciclos de Frecuencia': 1
    }
  ];
  
  const templateSheet = XLSX.utils.json_to_sheet(emptyTemplate);
  templateSheet['!cols'] = [
    { wch: 40 }, // Descripción
    { wch: 20 }, // Tipo
    { wch: 12 }, // Frecuencia
    { wch: 25 }, // Tiempo Observado
    { wch: 10 }, // Actividad
    { wch: 15 }, // Suplementos
    { wch: 20 }, // Repeticiones
    { wch: 20 }  // Ciclos
  ];
  
  XLSX.utils.book_append_sheet(workbook, templateSheet, 'Plantilla');
  
  // Hoja con instrucciones
  const instructionsData = instructions.map(line => ({ 'Instrucciones': line }));
  const instructionsSheet = XLSX.utils.json_to_sheet(instructionsData);
  instructionsSheet['!cols'] = [{ wch: 80 }];
  
  XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instrucciones');
  
  // Descargar archivo
  const fileName = `plantilla_importacion_estudios_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

/**
 * Valida si un archivo Excel tiene la estructura correcta
 */
export const validateExcelStructure = (data: any[], headers: string[]): string[] => {
  const errors: string[] = [];
  
  // Verificar que hay headers
  if (headers.length === 0) {
    errors.push('El archivo no tiene encabezados');
    return errors;
  }
  
  // Verificar que hay datos
  if (data.length === 0) {
    errors.push('El archivo no contiene datos');
    return errors;
  }
  
  // Buscar columna de descripción
  const hasDescription = headers.some(header => 
    header.toLowerCase().includes('descripci') || 
    header.toLowerCase().includes('description')
  );
  
  if (!hasDescription) {
    errors.push('No se encontró una columna de descripción. Debe incluir una columna con "Descripción" o "Description" en el nombre.');
  }
  
  // Verificar que al menos algunas filas tienen descripción
  const rowsWithDescription = data.filter(row => {
    const descriptionColumn = headers.find(header => 
      header.toLowerCase().includes('descripci') || 
      header.toLowerCase().includes('description')
    );
    return descriptionColumn && row[descriptionColumn]?.toString().trim();
  });
  
  if (rowsWithDescription.length === 0) {
    errors.push('Ninguna fila tiene descripción. Al menos una fila debe tener descripción del elemento.');
  }
  
  // Advertencias sobre columnas recomendadas
  const hasTime = headers.some(header => 
    header.toLowerCase().includes('tiempo') || 
    header.toLowerCase().includes('time')
  );
  
  if (!hasTime) {
    errors.push('Advertencia: No se encontró columna de tiempo. Se recomienda incluir tiempos observados.');
  }
  
  return errors;
};

/**
 * Mapea tipos de elemento desde diferentes formatos
 */
export const mapElementTypeFromExcel = (type: string): string => {
  if (!type) return 'machine-stopped';
  
  const normalizedType = type.toLowerCase().replace(/[^a-z]/g, '');
  
  if (normalizedType.includes('mp') || normalizedType.includes('parada') || normalizedType.includes('stopped')) {
    return 'machine-stopped';
  } else if (normalizedType.includes('mm') || normalizedType.includes('marcha') || normalizedType.includes('running')) {
    return 'machine-running';
  } else if (normalizedType.includes('tm') || normalizedType.includes('tiempo') || normalizedType.includes('time')) {
    return 'machine-time';
  }
  
  return 'machine-stopped';
};

/**
 * Parsea frecuencia desde formato "1/150" o similar
 */
export const parseFrequencyFromExcel = (frequency: string): { repetitions: number; cycles: number } => {
  if (!frequency || typeof frequency !== 'string') {
    return { repetitions: 1, cycles: 1 };
  }
  
  const parts = frequency.split('/');
  if (parts.length === 2) {
    const repetitions = parseInt(parts[0].trim()) || 1;
    const cycles = parseInt(parts[1].trim()) || 1;
    return { repetitions, cycles };
  }
  
  return { repetitions: 1, cycles: 1 };
};

/**
 * Limpia y valida valores numéricos del Excel
 */
export const cleanNumericValue = (value: any, defaultValue: number = 0): number => {
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    // Reemplazar comas por puntos para decimales
    const cleaned = value.replace(',', '.');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  
  return defaultValue;
};
