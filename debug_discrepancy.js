// Debug script para analizar discordancias entre ciclo normal del reporte y suplementos

const studyData = {
  "id": "30ae40a2-1df8-474f-8da7-48d73e0991b4",
  "user_id": "39f03a05-4532-49bb-a0e8-396b78f17671",
  "required_info": "{\"date\":\"2025-05-14\",\"name\":\"2\",\"company\":\"2\",\"activity_scale\":{\"normal\":100,\"optimal\":133}}",
  "supplements": {
    "elements": {},
    "__machine_cycle_data__": {
      "baseTime": 0,
      "timestamp": "2025-05-25T09:26:13.016Z",
      "inactivityTime": 0,
      "totalCycleTime": 0,
      "calculationCase": "case1",
      "fatigueSupplement": 0,
      "personalNeedsSupplement": 0
    },
    "0fb5be68-6e4a-4f78-9ca6-455cec0a6012": {
      "points": {},
      "is_forced": true,
      "percentage": 5,
      "factor_selections": {}
    },
    "547423a4-1851-4515-aa64-fb71713beb42": {
      "points": {
        "A1": 3,
        "A2": 5
      },
      "is_forced": false,
      "percentage": 11,
      "factor_selections": {
        "A1": {
          "index": 4,
          "intensity": "Mediano"
        },
        "A2": {
          "index": 4
        }
      }
    },
    "7363f32c-3653-4cb1-ab3a-e147c391a599": {
      "points": {
        "A1": 3,
        "A2": 5,
        "B2": 6,
        "B4": 4
      },
      "is_forced": false,
      "percentage": 12,
      "factor_selections": {
        "A1": {
          "index": 4,
          "intensity": "Mediano"
        },
        "A2": {
          "index": 4
        },
        "B2": {
          "index": 4
        },
        "B4": {
          "index": 5
        }
      }
    },
    "a3b9e827-0e87-4c47-a380-b2d45249a89d": {
      "points": {
        "A1": 3,
        "A2": 5
      },
      "is_forced": false,
      "percentage": 11,
      "factor_selections": {
        "A1": {
          "index": 4,
          "intensity": "Mediano"
        },
        "A2": {
          "index": 4
        }
      }
    }
  },
  "elements": [
    {
      "id": "a3b9e827-0e87-4c47-a380-b2d45249a89d",
      "time": 16.2,
      "type": "machine-stopped",
      "activity": 100,
      "position": 1,
      "description": "1-Trasladarse al cajón de las etiquetas + Buscarlas + Contarlas, comprobando que todas son del mismo modelo\n",
      "repetition_type": "repetitive",
      "frequency_cycles": 8,
      "frequency_repetitions": 1
    },
    {
      "id": "547423a4-1851-4515-aa64-fb71713beb42",
      "time": 15.84,
      "type": "machine-stopped",
      "activity": 105,
      "position": 2,
      "description": "2-Trasladarse al Panel y Marcar el modelo en el PDP + Comprobar el numero del carro\n",
      "repetition_type": "repetitive",
      "frequency_cycles": 8,
      "frequency_repetitions": 1
    },
    {
      "id": "7363f32c-3653-4cb1-ab3a-e147c391a599",
      "time": 46.8,
      "type": "machine-running",
      "activity": 110,
      "position": 3,
      "description": "3-Trasladarse  y Acercar carro / palet por hacer , con todas las piezas del modelo  ( Entre los 2 Operarios )\n",
      "repetition_type": "repetitive",
      "frequency_cycles": 8,
      "frequency_repetitions": 1
    },
    {
      "id": "0fb5be68-6e4a-4f78-9ca6-455cec0a6012",
      "type": "machine-time",
      "position": 3,
      "study_id": "30ae40a2-1df8-474f-8da7-48d73e0991b4",
      "description": "Tiempo de máquina, 1.",
      "repetition_type": "machine-type",
      "frequency_cycles": 1,
      "frequency_repetitions": 1
    }
  ]
};

// Parse required_info JSON
const requiredInfo = JSON.parse(studyData.required_info);
const activityScale = requiredInfo.activity_scale;

console.log('=== ANÁLISIS DE DISCORDANCIA ===');
console.log('Activity Scale:', activityScale);

// Calcular tiempo de elementos para el reporte
console.log('\n=== CÁLCULOS DEL REPORTE ===');

let machineStoppedTime = 0;
let machineRunningTime = 0;
let machineTime = 0;

studyData.elements.forEach(element => {
  if (element.id === "0fb5be68-6e4a-4f78-9ca6-455cec0a6012") {
    // Machine time element
    const observedTime = 45; // From timeRecords
    const averageActivity = 100;
    const frequencyFactor = element.frequency_repetitions / element.frequency_cycles;
    const activityFactor = averageActivity / activityScale.normal;
    const supplementsPercentage = studyData.supplements[element.id]?.percentage || 0;
    
    const finalTime = observedTime * frequencyFactor * activityFactor * (1 + supplementsPercentage / 100);
    machineTime += finalTime;
    
    console.log(`Machine Time Element ${element.id}:`);
    console.log(`  - Observed Time: ${observedTime}`);
    console.log(`  - Activity: ${averageActivity}`);
    console.log(`  - Frequency: ${element.frequency_repetitions}/${element.frequency_cycles} = ${frequencyFactor}`);
    console.log(`  - Activity Factor: ${averageActivity}/${activityScale.normal} = ${activityFactor}`);
    console.log(`  - Supplements: ${supplementsPercentage}%`);
    console.log(`  - Final Time: ${finalTime}`);
  } else if (element.type === "machine-stopped") {
    const observedTime = element.time;
    const averageActivity = element.activity;
    const frequencyFactor = element.frequency_repetitions / element.frequency_cycles;
    const activityFactor = averageActivity / activityScale.normal;
    const supplementsPercentage = studyData.supplements[element.id]?.percentage || 0;
    
    const finalTime = observedTime * frequencyFactor * activityFactor * (1 + supplementsPercentage / 100);
    machineStoppedTime += finalTime;
    
    console.log(`Machine Stopped Element ${element.id}:`);
    console.log(`  - Observed Time: ${observedTime}`);
    console.log(`  - Activity: ${averageActivity}`);
    console.log(`  - Frequency: ${element.frequency_repetitions}/${element.frequency_cycles} = ${frequencyFactor}`);
    console.log(`  - Activity Factor: ${averageActivity}/${activityScale.normal} = ${activityFactor}`);
    console.log(`  - Supplements: ${supplementsPercentage}%`);
    console.log(`  - Final Time: ${finalTime}`);
  } else if (element.type === "machine-running") {
    const observedTime = element.time;
    const averageActivity = element.activity;
    const frequencyFactor = element.frequency_repetitions / element.frequency_cycles;
    const activityFactor = averageActivity / activityScale.normal;
    const supplementsPercentage = studyData.supplements[element.id]?.percentage || 0;
    
    const finalTime = observedTime * frequencyFactor * activityFactor * (1 + supplementsPercentage / 100);
    machineRunningTime += finalTime;
    
    console.log(`Machine Running Element ${element.id}:`);
    console.log(`  - Observed Time: ${observedTime}`);
    console.log(`  - Activity: ${averageActivity}`);
    console.log(`  - Frequency: ${element.frequency_repetitions}/${element.frequency_cycles} = ${frequencyFactor}`);
    console.log(`  - Activity Factor: ${averageActivity}/${activityScale.normal} = ${activityFactor}`);
    console.log(`  - Supplements: ${supplementsPercentage}%`);
    console.log(`  - Final Time: ${finalTime}`);
  }
});

console.log('\n=== RESUMEN REPORTE ===');
console.log(`Machine Stopped Time: ${machineStoppedTime}`);
console.log(`Machine Running Time: ${machineRunningTime}`);
console.log(`Machine Time: ${machineTime}`);

const baseCycleReport = machineStoppedTime + Math.max(machineRunningTime, machineTime);
const normalCycleReport = baseCycleReport; // Sin contingencia en este ejemplo

console.log(`Base Cycle (Report): ${baseCycleReport}`);
console.log(`Normal Cycle (Report): ${normalCycleReport}`);

// Verificar datos de suplementos
console.log('\n=== DATOS DE SUPLEMENTOS ===');
const machineCycleData = studyData.supplements['__machine_cycle_data__'];
console.log('Machine Cycle Data:', machineCycleData);

if (machineCycleData && machineCycleData.totalCycleTime) {
  console.log(`Total Cycle Time (Supplements): ${machineCycleData.totalCycleTime}`);
  
  const difference = Math.abs(normalCycleReport - machineCycleData.totalCycleTime);
  const threshold = 1.0;
  
  console.log(`Difference: ${difference}`);
  console.log(`Threshold: ${threshold}`);
  console.log(`Should detect discrepancy: ${difference > threshold}`);
} else {
  console.log('❌ NO HAY DATOS DE SUPLEMENTOS - ESTE ES EL PROBLEMA');
  console.log('Los datos __machine_cycle_data__ están vacíos o no existen');
  console.log('totalCycleTime es:', machineCycleData?.totalCycleTime);
}

console.log('\n=== DIAGNÓSTICO ===');
if (!machineCycleData || machineCycleData.totalCycleTime === 0) {
  console.log('🔍 PROBLEMA IDENTIFICADO:');
  console.log('- Los datos de __machine_cycle_data__ no tienen totalCycleTime calculado');
  console.log('- Esto indica que no se ha ejecutado el cálculo de suplementos de máquina');
  console.log('- La página de report busca estos datos para detectar discordancias');
  console.log('- Solución: Ejecutar el cálculo de suplementos de máquina primero');
} 