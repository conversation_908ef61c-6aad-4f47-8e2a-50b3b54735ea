import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Study } from '../types/study';
import { TimeUnit } from '../types/index';
import { useStudyStore } from '../store/studyStore';
import { useCreditStore } from '../store/creditStore';
import { useAuthStore } from '../store/authStore';
import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';
import { NoCreditsModal } from './modals/NoCreditsModal';
import { UserProfile } from '../types/profile';
import { AlertCircle, X } from 'lucide-react';

interface StudyFormProps {
  onSubmit?: (study: Partial<Study>) => Promise<void>;
  loading?: boolean;
  className?: string;
}

const StudyForm: React.FC<StudyFormProps> = ({ onSubmit, loading, className }) => {
  const { t } = useTranslation('study');
  const navigate = useNavigate();
  const { selectedStudy, createStudy, updateStudy, setSelectedStudy, cloneStudy } = useStudyStore();
  const { checkCredits } = useCreditStore();
  const { user } = useAuthStore();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  const [formData, setFormData] = useState({
    studyNumber: '',
    name: '',
    company: '',
    date: '',
    operator: '',
    section: '',
    reference: '',
    machine: '',
    tools: '',
    technician: '',
    activityScale: {
      normal: 100,
      optimal: 133
    },
    visibleEnBiblioteca: false,
    customUnits: {
      enableSquareMeters: false,
      enableLinearMeters: false,
      enableCubicMeters: false,
      enableKilos: false,
      enablePerimeter: false,
      squareMeters: {
        length: 0,
        width: 0
      },
      linearMeters: {
        length: 0
      },
      cubicMeters: {
        length: 0,
        width: 0,
        height: 0
      },
      kilos: {
        weight: 0
      },
      perimeter: {
        length: 0,
        width: 0
      }
    }
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [showNoCreditsModal, setShowNoCreditsModal] = useState(false);
  const [isNewStudy, setIsNewStudy] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isSharedStudy, setIsSharedStudy] = useState(false);

  // Fetch user profile to get default activity scale
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (error) throw error;
        
        // Verificar que data no es null y convertirlo a UserProfile
        if (data && typeof data === 'object') {
          const profileData: UserProfile = {
            id: String(data.id || ''),
            email: String(data.email || ''),
            company_name: data.company_name as string | null,
            logo_url: data.logo_url as string | undefined,
            created_at: String(data.created_at || ''),
            updated_at: String(data.updated_at || ''),
            default_time_unit: (data.default_time_unit as TimeUnit) || 'min' as TimeUnit,
            default_language: String(data.default_language || 'es'),
            default_contingency: Number(data.default_contingency || 0),
            minutes_per_shift: Number(data.minutes_per_shift || 480),
            default_normal_activity: data.default_normal_activity !== null && data.default_normal_activity !== undefined ? Number(data.default_normal_activity) : undefined,
            default_optimal_activity: data.default_optimal_activity !== null && data.default_optimal_activity !== undefined ? Number(data.default_optimal_activity) : undefined,
            companies_list: data.companies_list ? (Array.isArray(data.companies_list) ? data.companies_list : []) : [],
            default_company: data.default_company as string | null
          };
          setUserProfile(profileData);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };
    
    fetchUserProfile();
  }, [user]);

  useEffect(() => {
    // Determinar si estamos en modo nuevo estudio basado en la URL
    const isNew = !window.location.pathname.includes('/study/');
    setIsNewStudy(isNew);

    if (isNew) {
      // Si es un nuevo estudio, limpiar el formulario y usar la escala de actividad por defecto del usuario
      setFormData(prev => ({
        studyNumber: '',
        name: '',
        company: userProfile?.default_company || '', // Usar empresa por defecto del perfil
        date: new Date().toISOString().split('T')[0], // Fecha actual por defecto
        operator: '',
        section: '',
        reference: '',
        machine: '',
        tools: '',
        technician: '',
        activityScale: {
          normal: userProfile?.default_normal_activity && [60, 100].includes(Number(userProfile.default_normal_activity)) ? Number(userProfile.default_normal_activity) : 100,
          optimal: userProfile?.default_optimal_activity && [80, 133].includes(Number(userProfile.default_optimal_activity)) ? Number(userProfile.default_optimal_activity) : 133
        },
        visibleEnBiblioteca: false, // Por defecto desactivado para nuevos estudios
        customUnits: {
          enableSquareMeters: false,
          enableLinearMeters: false,
          enableCubicMeters: false,
          enableKilos: false,
          enablePerimeter: false,
          squareMeters: {
            length: 0,
            width: 0
          },
          linearMeters: {
            length: 0
          },
          cubicMeters: {
            length: 0,
            width: 0,
            height: 0
          },
          kilos: {
            weight: 0
          },
          perimeter: {
            length: 0,
            width: 0
          }
        }
      }));
      return;
    }

    // Si no es un nuevo estudio y hay un estudio seleccionado, rellenar el formulario
    if (!isNew && selectedStudy) {
      // Verificar si el usuario es propietario del estudio
      if (user && selectedStudy.user_id !== user.id) {
        setIsSharedStudy(true);
      } else {
        setIsSharedStudy(false);
      }
      
      setFormData(prev => ({
        ...prev,
        studyNumber: selectedStudy.optional_info?.study_number || '',
        name: selectedStudy.required_info?.name || '',
        company: selectedStudy.required_info?.company || '',
        date: selectedStudy.required_info?.date || '',
        operator: selectedStudy.optional_info?.operator || '',
        section: selectedStudy.optional_info?.section || '',
        reference: selectedStudy.optional_info?.reference || '',
        machine: selectedStudy.optional_info?.machine || '',
        tools: selectedStudy.optional_info?.tools || '',
        technician: selectedStudy.optional_info?.technician || '',
        activityScale: selectedStudy.required_info?.activity_scale || {
          normal: userProfile?.default_normal_activity && [60, 100].includes(Number(userProfile.default_normal_activity)) ? Number(userProfile.default_normal_activity) : 100,
          optimal: userProfile?.default_optimal_activity && [80, 133].includes(Number(userProfile.default_optimal_activity)) ? Number(userProfile.default_optimal_activity) : 133
        },
        visibleEnBiblioteca: selectedStudy.optional_info?.visibleEnBiblioteca || false,
        customUnits: {
          enableSquareMeters: selectedStudy.optional_info?.customUnits?.enableSquareMeters || false,
          enableLinearMeters: selectedStudy.optional_info?.customUnits?.enableLinearMeters || false,
          enableCubicMeters: selectedStudy.optional_info?.customUnits?.enableCubicMeters || false,
          enableKilos: selectedStudy.optional_info?.customUnits?.enableKilos || false,
          enablePerimeter: (selectedStudy.optional_info?.customUnits as any)?.enablePerimeter || false,
          squareMeters: selectedStudy.optional_info?.customUnits?.squareMeters || { length: 0, width: 0 },
          linearMeters: selectedStudy.optional_info?.customUnits?.linearMeters || { length: 0 },
          cubicMeters: selectedStudy.optional_info?.customUnits?.cubicMeters || { length: 0, width: 0, height: 0 },
          kilos: selectedStudy.optional_info?.customUnits?.kilos || { weight: 0 },
          perimeter: (selectedStudy.optional_info?.customUnits as any)?.perimeter || { length: 0, width: 0 }
        }
      }));
    }
  }, [selectedStudy, window.location.pathname, userProfile, user]);

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    if (type === 'checkbox') {
      if (name.startsWith('customUnits.')) {
        const unitKey = name.split('.')[1];
        setFormData(prev => ({
          ...prev,
          customUnits: {
            ...prev.customUnits,
            [unitKey]: checked
          }
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          [name]: checked
        }));
      }
    } else if (name === 'activityScaleNormal') {
      const normalActivity = parseInt(value);
      const optimalActivity = normalActivity === 60 ? 80 : 133;
      setFormData(prev => ({
        ...prev,
        activityScale: {
          normal: normalActivity,
          optimal: optimalActivity
        }
      }));
    } else if (name === 'activityScaleOptimal') {
      const optimalActivity = parseInt(value);
      const normalActivity = optimalActivity === 80 ? 60 : 100;
      setFormData(prev => ({
        ...prev,
        activityScale: {
          normal: normalActivity,
          optimal: optimalActivity
        }
      }));
    } else if (name.startsWith('customUnits.')) {
      // Manejar campos de unidades personalizadas
      const pathParts = name.split('.');
      const unitType = pathParts[1]; // squareMeters, linearMeters, etc.
      const field = pathParts[2]; // length, width, height, weight
      
      setFormData(prev => {
        const newCustomUnits = { ...prev.customUnits };
        
        // Asegurar que el unitType existe como objeto
        if (unitType === 'squareMeters') {
          newCustomUnits.squareMeters = {
            ...newCustomUnits.squareMeters,
            [field]: parseFloat(value) || 0
          };
        } else if (unitType === 'linearMeters') {
          newCustomUnits.linearMeters = {
            ...newCustomUnits.linearMeters,
            [field]: parseFloat(value) || 0
          };
        } else if (unitType === 'cubicMeters') {
          newCustomUnits.cubicMeters = {
            ...newCustomUnits.cubicMeters,
            [field]: parseFloat(value) || 0
          };
        } else if (unitType === 'kilos') {
          newCustomUnits.kilos = {
            ...newCustomUnits.kilos,
            [field]: parseFloat(value) || 0
          };
        } else if (unitType === 'perimeter') {
          newCustomUnits.perimeter = {
            ...newCustomUnits.perimeter,
            [field]: parseFloat(value) || 0
          };
        }
        
        return {
          ...prev,
          customUnits: newCustomUnits
        };
      });
    } else {
      // Actualizar otros campos normalmente
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setFormError(null);
      setIsLoading(true);
      
      // Validar campos requeridos
      if (!formData.name || !formData.company || !formData.date) {
        setFormError(t('errors.requiredFields'));
        setIsLoading(false);
        return;
      }

      const studyData: Partial<Study> = {
        required_info: {
          name: formData.name,
          company: formData.company,
          date: formData.date,
          activity_scale: {
            normal: formData.activityScale.normal,
            optimal: formData.activityScale.optimal
          }
        },
        optional_info: {
          study_number: formData.studyNumber,
          operator: formData.operator,
          section: formData.section,
          reference: formData.reference,
          machine: formData.machine,
          tools: formData.tools,
          technician: formData.technician,
          visibleEnBiblioteca: formData.visibleEnBiblioteca,
          customUnits: formData.customUnits
        }
      };

      // Si hay un onSubmit prop, usarlo
      if (onSubmit) {
        await onSubmit(studyData);
        return;
      }

      if (isNewStudy) {
        // Verificar si hay suficientes créditos para crear un nuevo estudio
        const hasCredits = await checkCredits();
        if (!hasCredits) {
          setShowNoCreditsModal(true);
          setIsLoading(false);
          return;
        }

        // Crear nuevo estudio
        const newStudy = await createStudy({
          ...studyData,
          time_records: {},
          crono_seguido_records: [],
          elements: [],
          supplements: {}
        });
        
        if (newStudy) {
          toast.success(t('success.created'));
          setSelectedStudy(newStudy);
          navigate(`/study/${newStudy.id}`);
        }
      } else if (selectedStudy) {
        // Actualizar estudio existente manteniendo todos los datos
        const updatedStudyData = {
          required_info: {
            name: formData.name,
            company: formData.company,
            date: formData.date,
            activity_scale: {
              normal: formData.activityScale.normal,
              optimal: formData.activityScale.optimal
            }
          },
          optional_info: {
            study_number: formData.studyNumber,
            operator: formData.operator,
            section: formData.section,
            reference: formData.reference,
            machine: formData.machine,
            tools: formData.tools,
            technician: formData.technician,
            visibleEnBiblioteca: formData.visibleEnBiblioteca,
            customUnits: formData.customUnits
          }
        };

        const updatedStudy = await updateStudy(selectedStudy.id, updatedStudyData);
        
        if (updatedStudy) {
          toast.success(t('success.updated'));
          setSelectedStudy(updatedStudy);
          navigate(`/study/${updatedStudy.id}`);
        }
      }
    } catch (error) {
      console.error('Error al guardar el estudio:', error);
      setFormError(t('saveError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {formError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            {formError}
          </div>
        )}
        
        {isSharedStudy && (
          <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{t('permission_error.message')}</span>
            <button
              onClick={() => setIsSharedStudy(false)}
              className="ml-auto text-amber-500 hover:text-amber-700"
              type="button"
              aria-label={t('common:close', 'Cerrar')}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">{t('name')}</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder={t('namePlaceholder')}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 bg-green-50"
            />
          </div>
          
          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700">{t('company')}</label>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleChange}
              placeholder={t('companyPlaceholder')}
              required
              list="companiesList"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 bg-green-50"
            />
            <datalist id="companiesList">
              {userProfile?.companies_list?.map((company, index) => (
                <option key={index} value={company} />
              ))}
            </datalist>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="studyNumber" className="block text-sm font-medium text-gray-700">{t('studyNumber')}</label>
            <input
              type="text"
              id="studyNumber"
              name="studyNumber"
              value={formData.studyNumber}
              onChange={handleChange}
              placeholder={t('studyNumberPlaceholder')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>
          
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700">{t('date')}</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 bg-green-50"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="operator" className="block text-sm font-medium text-gray-700">{t('operator')}</label>
            <input
              type="text"
              id="operator"
              name="operator"
              value={formData.operator}
              onChange={handleChange}
              placeholder={t('operatorPlaceholder')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>
          
          <div>
            <label htmlFor="section" className="block text-sm font-medium text-gray-700">{t('section')}</label>
            <input
              type="text"
              id="section"
              name="section"
              value={formData.section}
              onChange={handleChange}
              placeholder={t('sectionPlaceholder')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>
        </div>

        <div>
          <label htmlFor="reference" className="block text-sm font-medium text-gray-700">{t('reference')}</label>
          <input
            type="text"
            id="reference"
            name="reference"
            value={formData.reference}
            onChange={handleChange}
            placeholder={t('referencePlaceholder')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="machine" className="block text-sm font-medium text-gray-700">{t('machine')}</label>
            <input
              type="text"
              id="machine"
              name="machine"
              value={formData.machine}
              onChange={handleChange}
              placeholder={t('machinePlaceholder')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>
          
          <div>
            <label htmlFor="tools" className="block text-sm font-medium text-gray-700">{t('tools')}</label>
            <input
              type="text"
              id="tools"
              name="tools"
              value={formData.tools}
              onChange={handleChange}
              placeholder={t('toolsPlaceholder')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            />
          </div>
        </div>

        <div>
          <label htmlFor="technician" className="block text-sm font-medium text-gray-700">{t('technician')}</label>
          <input
            type="text"
            id="technician"
            name="technician"
            value={formData.technician}
            onChange={handleChange}
            placeholder={t('technicianPlaceholder')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="activityScaleNormal" className="block text-sm font-medium text-gray-700">{t('normalActivity')}</label>
            <select
              id="activityScaleNormal"
              name="activityScaleNormal"
              value={formData.activityScale.normal}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 bg-green-50"
            >
              <option value={60}>60</option>
              <option value={100}>100</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="activityScaleOptimal" className="block text-sm font-medium text-gray-700">{t('optimalActivity')}</label>
            <select
              id="activityScaleOptimal"
              name="activityScaleOptimal"
              value={formData.activityScale.optimal}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 bg-green-50"
            >
              <option value={80}>80</option>
              <option value={133}>133</option>
            </select>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="visibleEnBiblioteca" className="flex items-center text-sm font-medium text-gray-700">
            <input
              type="checkbox"
              id="visibleEnBiblioteca"
              name="visibleEnBiblioteca"
              checked={formData.visibleEnBiblioteca}
              onChange={handleChange}
              className="h-4 w-4 text-amber-600 border-gray-300 rounded focus:ring-amber-500 mr-2"
              disabled={isSharedStudy}
              aria-describedby="visibleEnBibliotecaHelpText"
            />
            <span id="visibleEnBibliotecaHelpText">{t('study:includeInLibrary')}</span>
          </label>
        </div>

        {/* Sección de Unidades de Medida Personalizadas */}
        <div className="bg-gray-50 p-6 rounded-lg border">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('study:customUnits.title')}
          </h3>
          <p className="text-sm text-gray-600 mb-6">
            {t('study:customUnits.description')}
          </p>

          {/* Checkboxes para habilitar diferentes unidades */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="customUnits.enableSquareMeters"
                checked={formData.customUnits.enableSquareMeters}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2"
              />
              <span className="text-sm font-medium text-gray-700">{t('study:customUnits.bySquareMeter')}</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                name="customUnits.enableLinearMeters"
                checked={formData.customUnits.enableLinearMeters}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2"
              />
              <span className="text-sm font-medium text-gray-700">{t('study:customUnits.byLinearMeter')}</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                name="customUnits.enableCubicMeters"
                checked={formData.customUnits.enableCubicMeters}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2"
              />
              <span className="text-sm font-medium text-gray-700">{t('study:customUnits.byCubicMeter')}</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                name="customUnits.enableKilos"
                checked={formData.customUnits.enableKilos}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2"
              />
              <span className="text-sm font-medium text-gray-700">{t('study:customUnits.byKilograms')}</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                name="customUnits.enablePerimeter"
                checked={formData.customUnits.enablePerimeter}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2"
              />
              <span className="text-sm font-medium text-gray-700">{t('study:customUnits.byPerimeter')}</span>
            </label>
          </div>

          {/* Campos condicionales para cada unidad */}
          {formData.customUnits.enableSquareMeters && (
            <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="text-sm font-medium text-blue-900 mb-3">{t('study:customUnits.configuration.squareMeter')}</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.length')}</label>
                  <input
                    type="number"
                    name="customUnits.squareMeters.length"
                    value={formData.customUnits.squareMeters.length}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Ej: 2.5"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.width')}</label>
                  <input
                    type="number"
                    name="customUnits.squareMeters.width"
                    value={formData.customUnits.squareMeters.width}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Ej: 1.5"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {t('study:customUnits.configuration.total')}: {(formData.customUnits.squareMeters.length * formData.customUnits.squareMeters.width).toFixed(3)} m²
              </p>
            </div>
          )}

          {formData.customUnits.enableLinearMeters && (
            <div className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="text-sm font-medium text-green-900 mb-3">{t('study:customUnits.configuration.linearMeter')}</h4>
              <div>
                <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.length')}</label>
                <input
                  type="number"
                  name="customUnits.linearMeters.length"
                  value={formData.customUnits.linearMeters.length}
                  onChange={handleChange}
                  step="0.001"
                  min="0"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
                  placeholder="Ej: 5.0"
                />
              </div>
            </div>
          )}

          {formData.customUnits.enableCubicMeters && (
            <div className="mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
              <h4 className="text-sm font-medium text-purple-900 mb-3">{t('study:customUnits.configuration.cubicMeter')}</h4>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.length')}</label>
                  <input
                    type="number"
                    name="customUnits.cubicMeters.length"
                    value={formData.customUnits.cubicMeters.length}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                    placeholder="Ej: 2.0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.width')}</label>
                  <input
                    type="number"
                    name="customUnits.cubicMeters.width"
                    value={formData.customUnits.cubicMeters.width}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                    placeholder="Ej: 1.5"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.height')}</label>
                  <input
                    type="number"
                    name="customUnits.cubicMeters.height"
                    value={formData.customUnits.cubicMeters.height}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                    placeholder="Ej: 1.0"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {t('study:customUnits.configuration.total')}: {(formData.customUnits.cubicMeters.length * formData.customUnits.cubicMeters.width * formData.customUnits.cubicMeters.height).toFixed(3)} m³
              </p>
            </div>
          )}

          {formData.customUnits.enableKilos && (
            <div className="mb-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <h4 className="text-sm font-medium text-yellow-900 mb-3">{t('study:customUnits.configuration.kilograms')}</h4>
              <div>
                <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.weight')}</label>
                <input
                  type="number"
                  name="customUnits.kilos.weight"
                  value={formData.customUnits.kilos.weight}
                  onChange={handleChange}
                  step="0.001"
                  min="0"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-yellow-500 focus:ring-yellow-500"
                  placeholder="Ej: 10.5"
                />
              </div>
            </div>
          )}

          {formData.customUnits.enablePerimeter && (
            <div className="mb-4 p-4 bg-pink-50 rounded-lg border border-pink-200">
              <h4 className="text-sm font-medium text-pink-900 mb-3">{t('study:customUnits.configuration.perimeter')}</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.length')}</label>
                  <input
                    type="number"
                    name="customUnits.perimeter.length"
                    value={formData.customUnits.perimeter.length}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                    placeholder="Ej: 10.0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">{t('study:customUnits.configuration.width')}</label>
                  <input
                    type="number"
                    name="customUnits.perimeter.width"
                    value={formData.customUnits.perimeter.width}
                    onChange={handleChange}
                    step="0.001"
                    min="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                    placeholder="Ej: 5.0"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {t('study:customUnits.configuration.total')}: {(formData.customUnits.perimeter.length + formData.customUnits.perimeter.width) * 2} metros
              </p>
            </div>
          )}
        </div>

        <button 
          type="submit" 
          disabled={loading || isLoading}
          className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          {selectedStudy ? t('update') : t('create')}
        </button>
      </form>

      {/* Modal de créditos insuficientes */}
      <NoCreditsModal
        isOpen={showNoCreditsModal}
        onClose={() => setShowNoCreditsModal(false)}
        onNavigateToProfile={() => {
          setShowNoCreditsModal(false);
          navigate('/profile');
        }}
      />
    </div>
  );
};

export default StudyForm;