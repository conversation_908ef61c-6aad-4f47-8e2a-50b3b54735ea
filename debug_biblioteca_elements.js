// Archivo de diagnóstico para la biblioteca de elementos
// Ejecutar en la consola del navegador cuando estés en la aplicación

async function debugBiblioteca() {
    console.log('=== DIAGNÓSTICO DE BIBLIOTECA DE ELEMENTOS ===');
    
    // Importar supabase
    const { supabase } = await import('./src/lib/supabase.ts');
    
    try {
        // 1. Verificar usuario actual
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError) {
            console.error('❌ Error obteniendo usuario:', userError);
            return;
        }
        
        if (!user) {
            console.error('❌ No hay usuario autenticado');
            return;
        }
        
        console.log('✅ Usuario autenticado:', user.email);
        
        // 2. Obtener todos los estudios del usuario
        const { data: allStudies, error: studiesError } = await supabase
            .from('studies')
            .select('id, required_info, optional_info, elements')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
            
        if (studiesError) {
            console.error('❌ Error obteniendo estudios:', studiesError);
            return;
        }
        
        console.log(`📊 Total de estudios del usuario: ${allStudies?.length || 0}`);
        
        // 3. Analizar cada estudio
        const studiesAnalysis = allStudies?.map(study => {
            let visibleEnBiblioteca = false;
            let optionalInfoType = 'undefined';
            
            if (study.optional_info) {
                optionalInfoType = typeof study.optional_info;
                
                if (typeof study.optional_info === 'string') {
                    try {
                        const info = JSON.parse(study.optional_info);
                        visibleEnBiblioteca = info.visibleEnBiblioteca === true;
                    } catch {
                        visibleEnBiblioteca = false;
                    }
                } else if (typeof study.optional_info === 'object') {
                    visibleEnBiblioteca = study.optional_info.visibleEnBiblioteca === true;
                }
            }
            
            return {
                id: study.id,
                name: study.required_info?.name || 'Sin nombre',
                visibleEnBiblioteca,
                optionalInfoType,
                elementsCount: study.elements?.length || 0,
                hasElements: (study.elements?.length || 0) > 0
            };
        }) || [];
        
        console.log('📋 Análisis de estudios:');
        console.table(studiesAnalysis);
        
        // 4. Filtrar estudios visibles en biblioteca
        const visibleStudies = studiesAnalysis.filter(s => s.visibleEnBiblioteca);
        console.log(`📚 Estudios marcados como visibles en biblioteca: ${visibleStudies.length}`);
        
        if (visibleStudies.length > 0) {
            console.log('📚 Estudios visibles:');
            console.table(visibleStudies);
            
            // 5. Analizar elementos en estudios visibles
            const elementsInLibrary = [];
            for (const visibleStudy of visibleStudies) {
                const fullStudy = allStudies.find(s => s.id === visibleStudy.id);
                if (fullStudy?.elements && fullStudy.elements.length > 0) {
                    fullStudy.elements.forEach(element => {
                        elementsInLibrary.push({
                            studyId: fullStudy.id,
                            studyName: fullStudy.required_info?.name || 'Sin nombre',
                            elementId: element.id,
                            elementName: element.name || 'Sin nombre',
                            elementDescription: element.description || 'Sin descripción'
                        });
                    });
                }
            }
            
            console.log(`🔍 Total de elementos disponibles en biblioteca: ${elementsInLibrary.length}`);
            if (elementsInLibrary.length > 0) {
                console.log('🔍 Elementos en biblioteca:');
                console.table(elementsInLibrary.slice(0, 10)); // Mostrar solo los primeros 10
            }
        } else {
            console.log('⚠️  No hay estudios marcados como visibles en biblioteca');
            console.log('💡 Para marcar un estudio como visible:');
            console.log('   1. Ve a la página de edición del estudio');
            console.log('   2. Marca el checkbox "Incluir en Biblioteca"');
            console.log('   3. Guarda el estudio');
        }
        
        // 6. Simular la búsqueda como lo hace MethodForm
        console.log('\n=== SIMULANDO BÚSQUEDA DEL METHODFORM ===');
        
        const { data: methodFormStudies, error: methodError } = await supabase
            .from('studies')
            .select('id, required_info, optional_info, elements, time_records, supplements')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
            
        if (methodError) {
            console.error('❌ Error en búsqueda MethodForm:', methodError);
            return;
        }
        
        const methodFormFiltered = methodFormStudies?.filter(study => {
            if (!study || typeof study !== 'object' || !study.optional_info) return false;
            
            if (typeof study.optional_info === 'string') {
                try {
                    const info = JSON.parse(study.optional_info);
                    return info.visibleEnBiblioteca === true;
                } catch {
                    return false;
                }
            }
            
            return study.optional_info.visibleEnBiblioteca === true;
        }) || [];
        
        console.log(`🔧 MethodForm encontraría: ${methodFormFiltered.length} estudios`);
        
        // Contar elementos totales que MethodForm vería
        let totalMethodFormElements = 0;
        methodFormFiltered.forEach(study => {
            if (study.elements && Array.isArray(study.elements)) {
                totalMethodFormElements += study.elements.length;
            }
        });
        
        console.log(`🔧 Total elementos que MethodForm vería: ${totalMethodFormElements}`);
        
        // 7. Recomendaciones
        console.log('\n=== RECOMENDACIONES ===');
        
        if (visibleStudies.length === 0) {
            console.log('🔧 PROBLEMA: No tienes estudios marcados como visibles en biblioteca');
            console.log('📝 SOLUCIÓN:');
            console.log('   1. Ve a la lista de estudios (/studies)');
            console.log('   2. Selecciona un estudio que tenga elementos');
            console.log('   3. Haz clic en "Editar"');
            console.log('   4. Marca el checkbox "Incluir en Biblioteca"');
            console.log('   5. Guarda el estudio');
        } else if (totalMethodFormElements === 0) {
            console.log('🔧 PROBLEMA: Los estudios visibles no tienen elementos');
            console.log('📝 SOLUCIÓN: Crea elementos en los estudios que están marcados como visibles en biblioteca');
        } else {
            console.log('✅ Todo parece estar configurado correctamente');
            console.log('🔧 Si aún no ves elementos en MethodForm, intenta:');
            console.log('   1. Refrescar la página');
            console.log('   2. Cerrar y abrir el modal de selección de biblioteca');
            console.log('   3. Verificar la consola de errores de JavaScript');
        }
        
    } catch (error) {
        console.error('❌ Error durante el diagnóstico:', error);
    }
}

// Función para marcar un estudio como visible en biblioteca
async function marcarEstudioVisibleEnBiblioteca(studyId) {
    const { supabase } = await import('./src/lib/supabase.ts');
    
    try {
        // Obtener el estudio actual
        const { data: study, error: getError } = await supabase
            .from('studies')
            .select('*')
            .eq('id', studyId)
            .single();
            
        if (getError) {
            console.error('❌ Error obteniendo estudio:', getError);
            return;
        }
        
        // Actualizar optional_info
        const updatedOptionalInfo = {
            ...(study.optional_info || {}),
            visibleEnBiblioteca: true
        };
        
        const { data, error: updateError } = await supabase
            .from('studies')
            .update({ optional_info: updatedOptionalInfo })
            .eq('id', studyId)
            .select()
            .single();
            
        if (updateError) {
            console.error('❌ Error actualizando estudio:', updateError);
            return;
        }
        
        console.log(`✅ Estudio ${studyId} marcado como visible en biblioteca`);
        return data;
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

// Función para listar estudios con elementos
async function listarEstudiosConElementos() {
    const { supabase } = await import('./src/lib/supabase.ts');
    
    try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            console.error('❌ No hay usuario autenticado');
            return;
        }
        
        const { data: studies, error } = await supabase
            .from('studies')
            .select('id, required_info, elements, optional_info')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
            
        if (error) {
            console.error('❌ Error:', error);
            return;
        }
        
        const studiesWithElements = studies?.filter(s => s.elements && s.elements.length > 0).map(study => ({
            id: study.id,
            name: study.required_info?.name || 'Sin nombre',
            elementCount: study.elements?.length || 0,
            visibleEnBiblioteca: study.optional_info?.visibleEnBiblioteca || false
        })) || [];
        
        console.log('📋 Estudios con elementos:');
        console.table(studiesWithElements);
        
        return studiesWithElements;
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

// Exportar funciones para uso en consola
window.debugBiblioteca = debugBiblioteca;
window.marcarEstudioVisibleEnBiblioteca = marcarEstudioVisibleEnBiblioteca;
window.listarEstudiosConElementos = listarEstudiosConElementos;

console.log('🔧 Herramientas de diagnóstico cargadas. Usa:');
console.log('   debugBiblioteca() - Diagnóstico completo');
console.log('   listarEstudiosConElementos() - Ver estudios con elementos');
console.log('   marcarEstudioVisibleEnBiblioteca("ID_DEL_ESTUDIO") - Marcar estudio como visible'); 