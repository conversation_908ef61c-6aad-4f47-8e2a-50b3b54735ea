# Corrección de Recursión Infinita en Políticas de Seguridad

Este archivo contiene instrucciones para aplicar la migración que corrige el problema de recursión infinita en las políticas de seguridad para las relaciones "organization_members", "organizations" y "studies".

## Problema

Se ha detectado un error de recursión infinita en las políticas de seguridad. Este error ocurre cuando:

1. La política de seguridad para la tabla "studies" consulta la tabla "organization_members" para verificar si un usuario es miembro de una organización.
2. La política de seguridad para la tabla "organization_members" a su vez consulta la tabla "studies", creando así una recursión infinita.
3. De manera similar, hay recursión entre las políticas de "organizations" y "organization_members".

El error se manifiesta como:

```
Error fetching studies: {code: '42P17', details: null, hint: null, message: 'infinite recursion detected in policy for relation "organization_members"'}
```

Y también:

```
Detectada recursión infinita en políticas de seguridad al verificar organizaciones. Asumiendo que existen.
```

## Solución

La solución consiste en:

1. Eliminar las políticas problemáticas
2. Crear funciones de ayuda que no generen recursión
3. Implementar nuevas políticas más eficientes que utilicen estas funciones
4. Añadir índices para mejorar el rendimiento

Las funciones de ayuda creadas son:
- `is_organization_member`: Verifica si un usuario es miembro de una organización
- `get_user_organizations`: Obtiene todas las organizaciones a las que pertenece un usuario

## Instrucciones para aplicar la migración

### Opción 1: Aplicar a través del panel de administración de Supabase

1. Accede al panel de administración de Supabase
2. Ve a la sección "SQL Editor"
3. Crea un nuevo script y copia el contenido del archivo `20250227_fix_recursion_studies.sql`
4. Ejecuta el script

### Opción 2: Aplicar a través de la CLI de Supabase

Si tienes configurada la CLI de Supabase, puedes aplicar la migración con el siguiente comando:

```bash
supabase db push --db-url=<URL_DE_TU_BD>
```

## Verificación

Para verificar que la migración se ha aplicado correctamente, puedes ejecutar las siguientes consultas:

```sql
-- Verificar políticas de studies
SELECT * FROM pg_policies WHERE tablename = 'studies';

-- Verificar políticas de organizations
SELECT * FROM pg_policies WHERE tablename = 'organizations';

-- Verificar políticas de organization_members
SELECT * FROM pg_policies WHERE tablename = 'organization_members';

-- Verificar funciones creadas
SELECT proname, prosrc FROM pg_proc WHERE proname IN ('is_organization_member', 'get_user_organizations');
```

Deberías ver las nuevas políticas con sufijo "v2" y no deberías ver las antiguas políticas que fueron eliminadas.

## Rollback

Si necesitas revertir la migración, puedes ejecutar el siguiente script:

```sql
-- Eliminar las nuevas políticas
DROP POLICY IF EXISTS "Users can view shared organization studies v2" ON studies;
DROP POLICY IF EXISTS "Users can view their organizations v2" ON organizations;
DROP POLICY IF EXISTS "Users can view members of their organizations v2" ON organization_members;

-- Eliminar las funciones de ayuda
DROP FUNCTION IF EXISTS is_organization_member(uuid, uuid);
DROP FUNCTION IF EXISTS get_user_organizations(uuid);

-- Restaurar las políticas originales
CREATE POLICY "Users can view shared organization studies"
    ON studies FOR SELECT
    TO authenticated
    USING (
        auth.uid() = user_id
        OR (
            is_shared = true
            AND exists (
                select 1 from organization_members
                where organization_id = studies.organization_id
                and user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Organization members can view their organizations"
    ON organizations FOR SELECT
    TO authenticated
    USING (
        exists (
            select 1 from organization_members
            where organization_id = organizations.id
            and user_id = auth.uid()
        )
        or auth.uid() = created_by
    );

CREATE POLICY "Users can view members of their organizations"
    ON organization_members FOR SELECT
    TO authenticated
    USING (
        exists (
            select 1 from organization_members om
            where om.organization_id = organization_members.organization_id
            and om.user_id = auth.uid()
        )
    );
