import { create } from 'zustand';
import i18next from 'i18next';

interface TableData {
  value: number;
  descripcion: string[];
  puntos: number[];
  [key: string]: any;
}

interface Tables {
  A1: TableData & {
    Reducido: {
      descripcion: string[];
      puntos: number[];
    };
    Mediano: {
      descripcion: string[];
      puntos: number[];
    };
    Intenso: {
      descripcion: string[];
      puntos: number[];
    };
  };
  A2: TableData;
  A3: TableData;
  A4: TableData;
  A5: TableData;
  B1: TableData;
  B2: TableData;
  B3: TableData;
  B4: TableData;
  C1: TableData & {
    temperature: number;
    humidity: number;
  };
  C2: TableData;
  C3: TableData;
  C4: TableData;
  C5: TableData;
  C6: TableData;
  [key: string]: TableData;
}

interface SupplementTablesState {
  tables: Tables;
  tableType: 'tal' | 'oit';
  updateValue: (table: string, value: number, selections?: number[]) => void;
  updateDescriptions: () => void;
  setTableType: (type: 'tal' | 'oit') => void;
  getFactorPoints: (factor: string) => number[];
  getFactorDescriptions: (factor: string) => string[];
}

const getFactorPoints = (factor: string, tableType: 'tal' | 'oit'): number[] => {
  if (tableType === 'tal') {
    switch (factor) {
      case 'A1-Reducido':
        return [0,0,0,0,3,6,7,8,9,10,11,12,13,14,14,15,16,16,17,18,19,19,20,21,22,22,23,23,24,25,26,26,27,27,28,28,29,30,31,31,32,32,33,34,34,35,35,36,36,37,38,38,39,39,40,41,41,42,42,43,43,43,44,44,45,46,46,47,47,48,48,49,50,50,50,51,51,52,52,53,54,54,54,55,55,56,56,57,58,58,58,59,59,60,60,60,61,62,62,63,63,63,64,65,65,66,66,66,67,67,68,68,68,68,69,70,71,71,71,72,72,73,73,73,74,74,75,75,76,76,77,77,77,78,78,78,79,80,80,81,81,82,82,82,83,83,84,84,84,85];
      case 'A1-Mediano':
        return [0,0,0,0,3,6,8,10,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,32,33,34,35,36,37,38,39,39,40,41,41,42,43,44,45,46,46,47,48,49,50,50,51,51,52,53,54,54,55,56,56,57,58,59,59,60,61,61,62,63,64,64,65,65,66,67,68,69,70,70,71,72,72,72,73,73,74,74,75,76,76,77,78,79,79,80,80,81,82,82,83,84,85,86,86,87,88,88,88,89,90,91,92,93,94,95,95,96,96,97,97,97,98,98,98,99,99,99,100,100,100,101,101,102,102,103,104,105,106,107,108,109,109,109,110,110,111,112,112,112,113];
      case 'A1-Intenso':
        return [0,0,0,3,8,11,13,15,17,18,20,21,22,24,25,27,28,29,30,31,33,34,35,37,38,39,40,41,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,88,89,90,91,92,93,94,94,95,96,97,98,99,100,101,101,102,103,104,105,105,106,107,108,109,110,110,111,112,113,114,115,115,116,117,118,119,119,120,121,122,123,124,124,125,126,127,128,128,129,130,130,131,132,133,134,135,136,136,137,137,138,139,140,141,142,142,143,143,144,145,146,147,148,148,149];
      case 'A2':
        return [0,2,2,4,5,6,8,8,10,12,16,16];
      case 'A3':
        return [1,2,2,2,4,4,6,8,8,15];
      case 'A4':
        return [1,2,3,4,5,6,7,8,9,10];
      case 'A5':
        return [1,2,2,3,3,4,4,5,5,6,8,15,20];
      case 'B1':
        return [0,0,1,1,2,2,3,4,4,5,5,6,6,6,7,7,8,10,10,10,10,15,15];
      case 'B2':
        return [0,3,5,5,6,8,11];
      case 'B3':
        return [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20];
      case 'B4':
        return [1,1,2,3,3,4,5,5,6,7,7,8,9,9,10];
      case 'C1':
          // Matriz de puntos para temperatura y humedad
          const temperaturePoints = {
            "23": [0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3],
            "24": [6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8],
            "25": [6, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
            "26": [6, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
            "27": [7, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
            "28": [7, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
            "29": [8, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
            "30": [9, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
            "31": [9, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12],
            "32": [9, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12]
          };
        return [];  // Los puntos se manejan en el componente
      case 'C2':
        return [0,0,1,3,14];
      case 'C3':
        return [0,1,1,1,5,6,10];
      case 'C4':
        return [0,0,0,1,2,4,6,7,10,11,12];
      case 'C5':
        return [0,0,0,1,2,3,3,4,5,7,10,10];
      case 'C6':
        return [0,1,2,4,5,10,10,10];
      default:
        return [];
    }
  } else {
    // OIT tables
    switch (factor) {
      case 'A1-Reducido':
        return [0,0,0,0,3,6,7,8,9,10,11,12,13,14,14,15,16,16,17,18,19,19,20,21,22,22,23,23,24,25,26,26,27,27,28,28,29,30,31,31,32,32,33,34,34,35,35,36,36,37,38,38,39,39,40,41,41,42,42,43,43,43,44,44,45,46,46,47,47,48,48,49,50,50,50,51,51,52,52,53,54,54,54,55,55,56,56,57,58,58,58,59,59,60,60,60,61,62,62,63,63,63,64,65,65,66,66,66,67,67,68,68,68,68,69,70,71,71,71,72,72,73,73,73,74,74,75,75,76,76,77,77,77,78,78,78,79,80,80,81,81,82,82,82,83,83,84,84,84,85];
      case 'A1-Mediano':
        return [0,0,0,0,3,6,8,10,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,32,33,34,35,36,37,38,39,39,40,41,41,42,43,44,45,46,46,47,48,49,50,50,51,51,52,53,54,54,55,56,56,57,58,59,59,60,61,61,62,63,64,64,65,65,66,67,68,69,70,70,71,72,72,72,73,73,74,74,75,76,76,77,78,79,79,80,80,81,82,82,83,84,85,86,86,87,88,88,88,89,90,91,92,93,94,95,95,96,96,97,97,97,98,98,98,99,99,99,100,100,100,101,101,102,102,103,104,105,106,107,108,109,109,109,110,110,111,112,112,112,113];
      case 'A1-Intenso':
        return [0,0,0,3,8,11,13,15,17,18,20,21,22,24,25,27,28,29,30,31,33,34,35,37,38,39,40,41,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,88,89,90,91,92,93,94,94,95,96,97,98,99,100,101,101,102,103,104,105,105,106,107,108,109,110,110,111,112,113,114,115,115,116,117,118,119,119,120,121,122,123,124,124,125,126,127,128,128,129,130,130,131,132,133,134,135,136,136,137,137,138,139,140,141,142,142,143,143,144,145,146,147,148,148,149];
      case 'A2':
        return [0,2,4,5,6,8,10,12,16];
      case 'A3':
        return [1,2,2,4,4,4,6,8,15];
      case 'A4':
        return [1,2,3,4,5,6,7,8,9,10];
      case 'A5':
        return [1,2,2,3,5,8,15,20];
      case 'B1':
        return [0,0,1,1,2,2,3,4,4,5,5,6,6,7,7,8,10,10,10,10,15,15];
      case 'B2':
        return [0,3,5,5,6,8,11];
      case 'B3':
        return [0,2,2,2,4,4,8,10,10,14];
      case 'C1':
          // Matriz de puntos para temperatura y humedad
          const temperaturePoints = {
            "23": [0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3],
            "24": [6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8],
            "25": [6, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
            "26": [6, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
            "27": [7, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
            "28": [7, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
            "29": [8, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
            "30": [9, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
            "31": [9, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12],
            "32": [9, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12]
          };
        return [];  // Los puntos se manejan en el componente
      case 'B4':
        return [0,0,1,2,2,4,5,9,10];
      case 'C2':
        return [0,0,1,3,14];
      case 'C3':
        return [0,1,1,1,5,6,10];
      case 'C4':
        return [0,0,0,1,2,4,6,10,11,12];
      case 'C5':
        return [0,0,1,2,4,5,7,10,10];
      case 'C6':
        return [0,1,2,4,5,10];
      default:
        return [];
    }
  }
};

export const useSupplementTablesStore = create<SupplementTablesState>((set, get) => ({
  tables: {
    A1: {
      value: 0,
      descripcion: [],
      puntos: [],
      Reducido: {
        descripcion: [],
        puntos: []
      },
      Mediano: {
        descripcion: [],
        puntos: []
      },
      Intenso: {
        descripcion: [],
        puntos: []
      }
    },
    A2: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    A3: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    A4: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    A5: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    B1: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    B2: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    B3: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    B4: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    C1: {
      value: 0,
      descripcion: [],
      puntos: [],
      temperature: 0,
      humidity: 0
    },
    C2: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    C3: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    C4: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    C5: {
      value: 0,
      descripcion: [],
      puntos: []
    },
    C6: {
      value: 0,
      descripcion: [],
      puntos: []
    }
  },
  tableType: 'tal',

  setTableType: (type) => set({ tableType: type }),

  updateValue: (table, value, selections) => {
    set((state) => ({
      tables: {
        ...state.tables,
        [table]: {
          ...state.tables[table],
          value,
          selections
        }
      }
    }));
  },

  getFactorPoints: (factor) => {
    const state = get();
    return getFactorPoints(factor, state.tableType);
  },

  getFactorDescriptions: (factor) => {
    const state = get();
    return i18next.t(`${state.tableType}.${factor}.description`, { returnObjects: true }) as string[];
  },

  updateDescriptions: () => {
    set((state) => {
      const tables = { ...state.tables };
      Object.keys(tables).forEach((factor) => {
        if (factor === 'A1') {
          if (state.tableType === 'tal') {
            ['Reducido', 'Mediano', 'Intenso'].forEach((intensity) => {
              const intensityDescriptions = i18next.t(`${state.tableType}.${factor}.description.${intensity}`, { returnObjects: true }) as string[];
              tables[factor][intensity].descripcion = intensityDescriptions;
              tables[factor][intensity].puntos = getFactorPoints(`${factor}-${intensity}`, state.tableType);
            });
          } else {
            const descriptions = i18next.t(`${state.tableType}.${factor}.description`, { returnObjects: true }) as string[];
            tables[factor].descripcion = descriptions;
            tables[factor].puntos = getFactorPoints(factor, state.tableType);
          }
        } else {
          const descriptions = i18next.t(`${state.tableType}.${factor}.description`, { returnObjects: true }) as string[];
          tables[factor].descripcion = descriptions;
          tables[factor].puntos = getFactorPoints(factor, state.tableType);
        }
      });

      return { tables };
    });
  }
}));
