/**
 * Utilidad para formatear números según el idioma del usuario
 * En español, los decimales se separan con coma (,)
 * En inglés, los decimales se separan con punto (.)
 */

import i18n from '../i18n';

export interface NumberFormatOptions {
  decimals?: number;
  forceLocale?: string;
}

export interface ExcelNumberFormat {
  value: number;
  format: string;
}

/**
 * Formatea un número para exportación a Excel respetando el formato regional
 * Devuelve el valor numérico y el formato de celda para Excel
 * @param value - El número a formatear
 * @param options - Opciones de formato
 * @returns Objeto con valor numérico y formato de celda
 */
export function formatNumberForExcel(value: number, options: NumberFormatOptions = {}): ExcelNumberFormat {
  const { decimals = 2, forceLocale } = options;
  
  // Usar el idioma forzado o el actual de i18n
  const currentLanguage = forceLocale || i18n.language || 'es';
  
  // Crear el formato de celda según el idioma y decimales
  let format: string;
  
  if (currentLanguage === 'es') {
    // Formato español: usa coma como separador decimal
    if (decimals === 0) {
      format = '#.##0';
    } else {
      const decimalsString = '0'.repeat(decimals);
      format = `#.##0,${decimalsString}`;
    }
  } else {
    // Formato inglés: usa punto como separador decimal
    if (decimals === 0) {
      format = '#,##0';
    } else {
      const decimalsString = '0'.repeat(decimals);
      format = `#,##0.${decimalsString}`;
    }
  }
  
  return {
    value: value,
    format: format
  };
}

/**
 * Formatea un número para mostrar en pantalla (siempre usa punto)
 * @param value - El número a formatear
 * @param decimals - Número de decimales
 * @returns El número formateado para mostrar
 */
export function formatNumberForDisplay(value: number, decimals: number = 2): string {
  return value.toFixed(decimals);
}

/**
 * Convierte un string con formato regional a número
 * @param value - El string a convertir
 * @returns El número convertido
 */
export function parseLocalizedNumber(value: string): number {
  // Reemplazar coma por punto para parsing
  const normalizedValue = value.replace(',', '.');
  return parseFloat(normalizedValue);
}

/**
 * Obtiene el separador decimal según el idioma
 * @param language - Idioma (opcional, usa el actual si no se especifica)
 * @returns ',' para español, '.' para otros idiomas
 */
export function getDecimalSeparator(language?: string): string {
  const currentLanguage = language || i18n.language || 'es';
  return currentLanguage === 'es' ? ',' : '.';
}

/**
 * Formatea un porcentaje para Excel
 * @param value - El valor porcentual (ej: 15 para 15%)
 * @param options - Opciones de formato
 * @returns Objeto con valor decimal y formato de porcentaje
 */
export function formatPercentageForExcel(value: number, options: NumberFormatOptions = {}): ExcelNumberFormat {
  const { decimals = 1, forceLocale } = options;
  const currentLanguage = forceLocale || i18n.language || 'es';
  
  // Convertir a decimal (15% = 0.15)
  const decimalValue = value / 100;
  
  // Crear formato de porcentaje según el idioma
  let format: string;
  
  if (currentLanguage === 'es') {
    // Formato español: 15,0%
    if (decimals === 0) {
      format = '0%';
    } else {
      const decimalsString = '0'.repeat(decimals);
      format = `0,${'0'.repeat(decimals)}%`;
    }
  } else {
    // Formato inglés: 15.0%
    if (decimals === 0) {
      format = '0%';
    } else {
      const decimalsString = '0'.repeat(decimals);
      format = `0.${'0'.repeat(decimals)}%`;
    }
  }
  
  return {
    value: decimalValue,
    format: format
  };
}

/**
 * Información del formato actual
 */
export function getCurrentFormatInfo() {
  const language = i18n.language || 'es';
  const separator = getDecimalSeparator(language);
  
  const sampleFormat = formatNumberForExcel(1234.567, { decimals: 2 });
  
  return {
    language,
    decimalSeparator: separator,
    isSpanish: language === 'es',
    example: `${sampleFormat.value} (formato: ${sampleFormat.format})`,
    sampleFormat: sampleFormat.format
  };
}

/**
 * Función auxiliar para mantener compatibilidad con código existente
 * Devuelve solo el string formateado
 */
export function formatNumberAsString(value: number, options: NumberFormatOptions = {}): string {
  const { decimals = 2, forceLocale } = options;
  const currentLanguage = forceLocale || i18n.language || 'es';
  
  const formattedNumber = value.toFixed(decimals);
  
  if (currentLanguage === 'es') {
    return formattedNumber.replace('.', ',');
  }
  
  return formattedNumber;
} 