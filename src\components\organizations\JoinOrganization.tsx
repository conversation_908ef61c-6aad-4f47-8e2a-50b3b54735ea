import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';

export const JoinOrganization: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [inviteCode, setInviteCode] = useState('');
  const { isLoading, sendJoinRequest } = useOrganizationStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await sendJoinRequest(inviteCode);
      
      toast({
        title: t('joinOrganization.requestSent'),
        description: t('joinOrganization.requestSentDescription'),
      });
      setInviteCode('');
    } catch (error) {
      toast({
        title: t('errors.joinOrganization'),
        description: error instanceof Error ? error.message : t('errors.joinOrganization'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-4">
      <p className="text-gray-600">
        {t('joinOrganization.description')}
      </p>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Input
            value={inviteCode}
            onChange={(e) => setInviteCode(e.target.value)}
            placeholder={t('joinOrganization.inviteCodePlaceholder')}
            disabled={isLoading}
          />
        </div>

        <Button
          type="submit"
          disabled={!inviteCode.trim() || isLoading}
        >
          {isLoading ? t('joinOrganization.sending') : t('joinOrganization.send')}
        </Button>
      </form>
    </div>
  );
};
