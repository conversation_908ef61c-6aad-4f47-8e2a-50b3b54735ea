# Estructura de la Base de Datos

## Tablas Principales

### 1. user_devices
Gestiona los dispositivos autorizados por usuario (sistema de licencias).

```sql
create table public.user_devices (
  id uuid primary key,
  user_id uuid references auth.users(id),
  user_email text,
  device_id text,
  device_name text,
  platform text,
  last_used_at timestamptz,
  created_at timestamptz,
  updated_at timestamptz
)
```

- **Propósito**: Control de acceso por dispositivo
- **Restricciones**: Un usuario solo puede tener un dispositivo registrado
- **Índices**: user_id, device_id, user_email

### 2. library_elements
Biblioteca de elementos de trabajo reutilizables.

```sql
create table public.library_elements (
  id uuid primary key,
  user_id uuid references auth.users(id),
  description text,
  type text,
  frequency_repetitions integer,
  frequency_cycles integer,
  repetition_type text,
  is_shared boolean,
  time_records jsonb,
  time_stats jsonb,
  supplements jsonb,
  created_at timestamptz,
  updated_at timestamptz
)
```

- **Propósito**: Almacenar elementos comunes para reutilización
- **Campos JSONB**:
  - time_records: Registros de tiempo históricos
  - time_stats: Estadísticas calculadas
  - supplements: Configuración de suplementos

### 3. studies
Tabla principal de estudios de tiempos.

```sql
create table public.studies (
  id uuid primary key,
  user_id uuid references auth.users(id),
  info jsonb,
  elements jsonb,
  created_at timestamptz,
  updated_at timestamptz
)
```

#### Estructura del campo info
```json
{
  "required": {
    "name": "string",
    "company": "string",
    "date": "date",
    "activity_scale": {
      "normal": number,
      "optimal": number
    }
  },
  "optional": {
    "study_number": "string?",
    "operator": "string?",
    "section": "string?",
    "reference": "string?",
    "norm_number": "string?",
    "machine": "string?",
    "tools": "string?",
    "technician": "string?"
  }
}
```

#### Estructura del campo elements
```json
[
  {
    "id": "uuid",
    "description": "string",
    "type": "machine-stopped|machine-running|machine-time",
    "frequency_repetitions": number,
    "frequency_cycles": number,
    "repetition_type": "repetitive|frequency|machine",
    "position": number,
    "library_element_id": "uuid?",
    "time_records": [
      {
        "id": "uuid",
        "time": number,
        "activity": number,
        "timestamp": "datetime",
        "comment": "string?"
      }
    ],
    "supplements": {
      "points": { [factor: string]: number },
      "percentage": number,
      "is_forced": boolean,
      "factor_selections": {
        [factor: string]: {
          "index"?: number,
          "indices"?: number[],
          "intensity"?: string,
          "temperature"?: number,
          "humidity"?: number
        }
      }
    }
  }
]
```

## Políticas de Seguridad (RLS)

### user_devices
- Select: Solo el propietario puede ver sus dispositivos
- Insert: Solo el propietario puede registrar dispositivos
- Update: Solo el propietario puede actualizar sus dispositivos
- Delete: Solo el propietario puede eliminar sus dispositivos

### library_elements
- Select: El propietario puede ver sus elementos y los compartidos (is_shared = true)
- Insert: Solo el propietario puede crear elementos
- Update: Solo el propietario puede actualizar sus elementos
- Delete: Solo el propietario puede eliminar sus elementos

### studies
- Select: Solo el propietario puede ver sus estudios
- Insert: Solo el propietario puede crear estudios
- Update: Solo el propietario puede actualizar sus estudios
- Delete: Solo el propietario puede eliminar sus estudios

## Índices

### user_devices
```sql
create index idx_user_devices_user_id on public.user_devices(user_id);
create index idx_user_devices_user_email on public.user_devices(user_email);
create index idx_user_devices_device_id on public.user_devices(device_id);
```

### library_elements
```sql
create index idx_library_elements_user_id on public.library_elements(user_id);
```

### studies
```sql
create index idx_studies_user_id on public.studies(user_id);
create index idx_studies_company on public.studies((info->'required'->>'company'));
create index idx_studies_date on public.studies((info->'required'->>'date'));
create index idx_studies_name on public.studies((info->'required'->>'name'));
create index idx_studies_info_gin on public.studies using gin (info jsonb_path_ops);
create index idx_studies_elements_gin on public.studies using gin (elements jsonb_path_ops);
```