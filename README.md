hola

## Limpieza de estudios huérfanos

Si hay estudios en la base de datos que pertenecen a miembros que ya han sido expulsados de la organización pero que aún mantienen referencias a la organización, puedes limpiarlos de dos maneras:

1. **Desde la interfaz de usuario**: Los propietarios de la organización verán un botón "Limpiar estudios huérfanos" en la sección de miembros de la organización.

2. **Usando el script de limpieza**:
   ```bash
   # Configurar las variables de entorno
   export SUPABASE_URL=tu_url_de_supabase
   export SUPABASE_KEY=tu_clave_de_supabase
   
   # Ejecutar el script con el ID de la organización
   node scripts/clean-orphaned-studies.js tu_id_de_organizacion
   ```

Esta funcionalidad garantiza que cuando un miembro es expulsado de una organización:
- Sus estudios dejan de ser visibles para la organización
- El miembro expulsado no puede ver estudios de otros miembros
