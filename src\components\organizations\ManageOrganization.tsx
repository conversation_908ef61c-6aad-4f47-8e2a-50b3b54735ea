import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';
import { OrganizationMembersList } from './OrganizationMembersList';
import { JoinRequestsList } from './JoinRequestsList';

export const ManageOrganization: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { 
    currentOrganization,
    fetchMembers,
    fetchJoinRequests,
    error 
  } = useOrganizationStore();

  useEffect(() => {
    if (currentOrganization) {
      fetchMembers(currentOrganization.id);
      fetchJoinRequests(currentOrganization.id);
    }
  }, [currentOrganization, fetchMembers, fetchJoinRequests]);

  if (!currentOrganization) {
    return null;
  }

  if (error) {
    return (
      <div className="p-4">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold">{currentOrganization.name}</h2>
        {currentOrganization.description && (
          <p className="mt-2 text-gray-600">{currentOrganization.description}</p>
        )}
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">{t('members.title')}</h3>
        <OrganizationMembersList />
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">{t('joinRequests.title')}</h3>
        <JoinRequestsList />
      </div>
    </div>
  );
};
