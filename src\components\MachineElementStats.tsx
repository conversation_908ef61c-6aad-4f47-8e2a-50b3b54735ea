import React from 'react';
import { useTranslation } from 'react-i18next';
import { TimeRecord, ElementInstance } from '../types';

interface MachineElementStatsProps {
  totalTime?: number;
  averageTime?: number;
  recordCount?: number;
  averageActivity?: number;
  timeRecords?: TimeRecord[];
  element?: ElementInstance;
  onStatsCalculated?: (stats: {
    totalTime: number;
    averageTime: number;
    recordCount: number;
    averageActivity: number;
  }) => void;
}

export const MachineElementStats: React.FC<MachineElementStatsProps> = ({
  totalTime = 0,
  averageTime = 0,
  recordCount = 0,
  averageActivity = 0,
  timeRecords = [],
  element,
  onStatsCalculated
}) => {
  const { t } = useTranslation();

  React.useEffect(() => {
    if (timeRecords.length > 0 && onStatsCalculated) {
      const calculatedTotalTime = timeRecords.reduce((sum, record) => sum + record.time, 0);
      const calculatedAverageTime = calculatedTotalTime / timeRecords.length;
      const calculatedAverageActivity = timeRecords.reduce((sum, record) => sum + (record.activity || 0), 0) / timeRecords.length;

      onStatsCalculated({
        totalTime: calculatedTotalTime,
        averageTime: calculatedAverageTime,
        recordCount: timeRecords.length,
        averageActivity: calculatedAverageActivity
      });
    }
  }, [timeRecords, onStatsCalculated]);

  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6 overflow-x-auto">
      <div className="flex space-x-6 min-w-max">
        <div>
          <div className="text-sm text-gray-600">{t('takes', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{recordCount}</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('averageTime', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{averageTime.toFixed(2)}s</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('totalTime', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{totalTime.toFixed(2)}s</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">{t('averageActivity', { ns: 'frequency' })}</div>
          <div className="text-lg font-bold">{averageActivity.toFixed(0)}</div>
        </div>
      </div>
    </div>
  );
};
