import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { ElementInstance, Study } from '../types';
import i18n from '../i18n';
import { useCreditStore } from './creditStore';

interface ElementSearchState {
  searchResults: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  selectedElements: Array<{
    element: ElementInstance;
    studyName: string;
    studyId: string;
  }>;
  isLoading: boolean;
  error: string | null;
  fetchAllElements: () => Promise<void>;
  searchElements: (query: string) => Promise<void>;
  toggleElementSelection: (element: ElementInstance, studyName: string, studyId: string) => void;
  clearSelection: () => void;
  createNewStudyFromSelection: (study: Study) => Promise<Study>;
  addAveragedElement: (element: ElementInstance) => void;
}

export const useElementSearchStore = create<ElementSearchState>((set, get) => ({
  searchResults: [],
  selectedElements: [],
  isLoading: false,
  error: null,

  addAveragedElement: (averagedElement) => {
    // Generar un ID único para el estudio
    const studyId = 'averaged-' + crypto.randomUUID();
    const studyName = i18n.t('averagedElement', { ns: 'library' }) || 'Elemento Promediado';
    
    // Asegurarse de que el elemento tenga todos los campos necesarios
    const completeElement = {
      ...averagedElement,
      id: averagedElement.id || crypto.randomUUID()
    };
    
    // Añadir a resultados de búsqueda y a seleccionados (reemplazando los anteriores)
    set(state => ({
      ...state,
      searchResults: [
        {
          element: completeElement,
          studyName,
          studyId
        },
        ...state.searchResults
      ],
      // Seleccionar SOLO el nuevo elemento promediado
      selectedElements: [{
        element: completeElement,
        studyName,
        studyId
      }]
    }));
  },

  fetchAllElements: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      // Get all organization memberships for the user
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        throw membershipError;
      }

      let query = supabase
        .from('studies')
        .select('id, required_info, optional_info, elements, time_records, supplements, user_id, organization_id');

      if (memberships && memberships.length > 0) {
        // Get all organization IDs where the user is a member
        const orgIds = memberships
          .filter(m => m && typeof m === 'object' && 'organization_id' in m)
          .map(m => m.organization_id);
        console.log('User is member of organizations:', orgIds);
        
        // Include studies that either belong to the user OR are in organizations where the user is a member
        query = query.or(`user_id.eq.${user.id},organization_id.in.(${orgIds.join(',')})`);
      } else {
        // If not in any organization, only get user's own studies
        query = query.eq('user_id', user.id);
      }

      const { data: studies, error: studiesError } = await query;

      if (studiesError) throw studiesError;

      // Filtrar estudios para mostrar solo los que tienen visibleEnBiblioteca=true
      const visibleStudies = studies.filter(study => 
        study && typeof study === 'object' && 
        study.optional_info && 
        typeof study.optional_info === 'object' && 
        study.optional_info.visibleEnBiblioteca === true
      );

      // Si no hay estudios visibles, retornar lista vacía
      if (visibleStudies.length === 0) {
        set({ searchResults: [], isLoading: false });
        return;
      }

      // Procesar cada estudio para obtener sus elementos
      const results = visibleStudies.flatMap(study => {
        if (!study || typeof study !== 'object') return [];
        
        const elements = study.elements as ElementInstance[] || [];
        
        // Si no hay elementos, añadir un placeholder para asegurar que el estudio aparezca
        if (elements.length === 0) {
          return [{
            element: {
              id: 'placeholder-' + crypto.randomUUID(),
              name: '',
              description: '',
              type: '',
              position: 0,
              repetition_type: '',
              frequency_cycles: 0,
              frequency_repetitions: 0,
              isPlaceholder: true
            },
            studyName: study.required_info && 'name' in study.required_info ? study.required_info.name : 'Sin nombre',
            studyId: study.id || ''
          }];
        }
        
        return elements.map(element => {
          // Obtener todos los registros de tiempo del elemento
          const timeRecords = study.time_records && element.id ? study.time_records[element.id] || [] : [];
          if (timeRecords.length > 0) {
            // Usar el primer registro para mostrar en la biblioteca, pero guardar todos
            element.time = timeRecords[0].time;
            element.activity = timeRecords[0].activity;
            element.timeRecords = timeRecords; // Guardar todos los registros
          }

          // Obtener los suplementos del elemento
          const elementSupplement = study.supplements && element.id ? study.supplements[element.id] : undefined;
          if (elementSupplement) {
            element.supplements = [{
              id: element.id,
              name: element.name,
              description: element.description,
              percentage: elementSupplement.percentage,
              is_forced: elementSupplement.is_forced,
              points: elementSupplement.points,
              factor_selections: elementSupplement.factor_selections
            }];
          }

          return {
            element,
            studyName: study.required_info && 'name' in study.required_info ? study.required_info.name : 'Sin nombre',
            studyId: study.id || ''
          };
        });
      });

      set({ searchResults: results, isLoading: false });
    } catch (error) {
      console.error('Error fetching elements:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  searchElements: async (query: string) => {
    if (!query.trim()) {
      get().fetchAllElements();
      return;
    }

    set({ isLoading: true, error: null });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      // Get all organization memberships for the user
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching memberships:', membershipError);
        throw membershipError;
      }

      let supabaseQuery = supabase
        .from('studies')
        .select('id, required_info, optional_info, elements, time_records, supplements, user_id, organization_id');

      if (memberships && memberships.length > 0) {
        // Get all organization IDs where the user is a member
        const orgIds = memberships
          .filter(m => m && typeof m === 'object' && 'organization_id' in m)
          .map(m => m.organization_id);
        
        // Include studies that either belong to the user OR are in organizations where the user is a member
        supabaseQuery = supabaseQuery.or(`user_id.eq.${user.id},organization_id.in.(${orgIds.join(',')})`);
      } else {
        // If not in any organization, only get user's own studies
        supabaseQuery = supabaseQuery.eq('user_id', user.id);
      }

      const { data: studies, error: studiesError } = await supabaseQuery;

      if (studiesError) throw studiesError;

      // Filtrar estudios para mostrar solo los que tienen visibleEnBiblioteca=true
      const visibleStudies = studies.filter(study => 
        study && typeof study === 'object' && 
        study.optional_info && 
        typeof study.optional_info === 'object' && 
        study.optional_info.visibleEnBiblioteca === true
      );
      
      // Si no hay estudios visibles, retornar lista vacía
      if (visibleStudies.length === 0) {
        set({ searchResults: [], isLoading: false });
        return;
      }
      
      const searchQuery = query.toLowerCase();

      // Filtrar por nombre de estudio o contenido de elementos
      const matchingStudies = visibleStudies.filter(study => {
        // Verificar si el nombre del estudio coincide con la búsqueda
        const studyName = study.required_info && 'name' in study.required_info ? study.required_info.name : '';
        const studyMatchesSearch = studyName.toLowerCase().includes(searchQuery);
        
        if (studyMatchesSearch) {
          return true;
        }
        
        // Verificar si algún elemento coincide con la búsqueda
        const elements = study.elements as ElementInstance[] || [];
        return elements.some(element => 
          element.name?.toLowerCase().includes(searchQuery) ||
          element.description?.toLowerCase().includes(searchQuery)
        );
      });

      // Procesar cada estudio para obtener sus elementos
      const results = matchingStudies.flatMap(study => {
        if (!study || typeof study !== 'object') return [];
        
        const elements = study.elements as ElementInstance[] || [];
        const studyName = study.required_info && 'name' in study.required_info ? study.required_info.name : 'Sin nombre';
        const studyMatchesSearch = studyName.toLowerCase().includes(searchQuery);
        
        // Si no hay elementos, añadir un placeholder para asegurar que el estudio aparezca
        if (elements.length === 0) {
          return [{
            element: {
              id: 'placeholder-' + crypto.randomUUID(),
              name: '',
              description: '',
              type: '',
              position: 0,
              repetition_type: '',
              frequency_cycles: 0,
              frequency_repetitions: 0,
              isPlaceholder: true
            },
            studyName,
            studyId: study.id || ''
          }];
        }
        
        // Si la búsqueda coincide con el estudio, mostrar todos los elementos
        // De lo contrario, filtrar los elementos que coinciden con la búsqueda
        const filteredElements = studyMatchesSearch 
          ? elements 
          : elements.filter(element =>
          element.name?.toLowerCase().includes(searchQuery) ||
          element.description?.toLowerCase().includes(searchQuery)
            );
        
        return filteredElements.map(element => {
          // Obtener todos los registros de tiempo del elemento
          const timeRecords = study.time_records && element.id ? study.time_records[element.id] || [] : [];
          if (timeRecords.length > 0) {
            // Usar el primer registro para mostrar en la biblioteca, pero guardar todos
            element.time = timeRecords[0].time;
            element.activity = timeRecords[0].activity;
            element.timeRecords = timeRecords; // Guardar todos los registros
          }

          // Obtener los suplementos del elemento
          const elementSupplement = study.supplements && element.id ? study.supplements[element.id] : undefined;
          if (elementSupplement) {
            element.supplements = [{
              id: element.id,
              name: element.name,
              description: element.description,
              percentage: elementSupplement.percentage,
              is_forced: elementSupplement.is_forced,
              points: elementSupplement.points,
              factor_selections: elementSupplement.factor_selections
            }];
          }

          return {
            element,
            studyName,
            studyId: study.id || ''
          };
        });
      });

      set({ searchResults: results, isLoading: false });
    } catch (error) {
      console.error('Error searching elements:', error);
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  toggleElementSelection: (element: ElementInstance, studyName: string, studyId: string) => {
    set(state => {
      // Crear una clave única para el elemento usando su ID y el ID del estudio
      const elementKey = `${element.id}-${studyId}`;
      
      // Verificar si el elemento ya está seleccionado
      const isSelected = state.selectedElements.some(
        sel => `${sel.element.id}-${sel.studyId}` === elementKey
      );

      // Si ya está seleccionado, lo quitamos
      if (isSelected) {
        return {
          selectedElements: state.selectedElements.filter(
            sel => `${sel.element.id}-${sel.studyId}` !== elementKey
          )
        };
      }

      // Asegurarse de que los suplementos estén correctamente incluidos
      // Esto es importante para preservar los suplementos cuando se crea un nuevo estudio
      console.log('Elemento seleccionado con suplementos:', element.name, element.supplements);

      // Si no está seleccionado, lo añadimos
      return {
        selectedElements: [...state.selectedElements, { element, studyName, studyId }]
      };
    });
  },

  clearSelection: () => {
    set({ selectedElements: [] });
  },

  createNewStudyFromSelection: async (study: Study) => {
    try {
      // Consumir un crédito antes de crear el estudio
      const consumeCredit = useCreditStore.getState().consumeCredit;
      const creditConsumed = await consumeCredit();
      
      if (!creditConsumed) {
        throw new Error(i18n.t('study:no_credits.message'));
      }

      const { data, error } = await supabase
        .from('studies')
        .insert([study])
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating new study:', error);
      throw error;
    }
  }
}));
