# Time Record Store

## Descripción
Gestiona los registros de tiempo y sus estadísticas.

## Estado
```typescript
interface TimeRecordState {
  records: Record<string, TimeRecord[]>;
  isLoading: boolean;
  error: string | null;
  fetchRecords: (elementId: string) => Promise<void>;
  saveRecords: (newRecords: Omit<TimeRecord, 'id'>[]) => Promise<void>;
  updateRecord: (recordId: string, updatedRecord: Partial<TimeRecord>) => Promise<void>;
  deleteRecord: (recordId: string) => Promise<void>;
  deleteAllRecords: (elementId: string) => Promise<void>;
}
```

## Funcionalidades

### 1. Gestión de Registros
- Carga de registros por elemento
- Guardado de nuevos registros
- Actualización de registros existentes
- Eliminación individual y masiva

### 2. Estadísticas
- Cálculo de promedios
- Desviaciones estándar
- Validación de outliers
- Proyecciones

### 3. Persistencia
- Almacenamiento en Supabase
- Caché local
- Sincronización automática

## Uso
```typescript
const { 
  records, 
  saveRecords, 
  deleteRecord 
} = useTimeRecordStore();

// Guardar registro
await saveRecords([{
  elementId,
  time: 2.5,
  activity: 100,
  timestamp: new Date().toISOString()
}]);

// Eliminar registro
await deleteRecord(recordId);
```