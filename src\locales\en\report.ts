export default {
  title: 'Report',
    noStudySelected: 'No study selected',
    timeUnit: 'Time Unit',
    shiftMinutes: 'Minutes per Shift',
    contingency: 'Contingency',
    activityScale: 'Activity Scale',
    identifyingData: 'Identifying Data',
    units: {
      seconds: 'seconds',
      minutes: 'minutes',
      mmm: 'MMM',
      cmm: 'CMM',
      tmu: 'TMU',
      dmh: 'DMH',
      cyclesPerShift: 'cycles/shift',
      cyclesPerHour: 'cycles/hour',
      hours: 'hours',
      points: 'points'
    },
    elementDetails: {
      title: 'Element Details',
      description: 'Description',
      type: 'Type',
      frequency: 'Frequency',
      observedTime: 'Observed Time',
      activity: 'Activity',
      supplements: 'Supplements',
      finalTime: 'Final Time',
      repetitions: 'Repetitions',
      cycles: 'Cycles',
      repetitionType: 'Repetition Type',
      addToLibrary: 'Add to Library',
      frequencyFormat: '{{repetitions}} repetitions, {{cycles}} cycles {{each}}',
      totalPoints: 'Points',
      conversionPercentage: 'Percentage',
      forcedSupplements: 'Forced',
      recordNumber: 'Record Number',
      time: 'Time',
      observations: 'Observations'
    },
    results: {
      title: 'RESULTS',
      normalCycle: 'Normal Cycle',
      optimalCycle: 'Optimal Cycle',
      contingencyTime: 'Contingency Time',
      totalK: 'K Total',
      normalProduction: 'Normal Production per Shift',
      optimalProduction: 'Optimal Production per Shift',
      normalProductionPerHour: 'Normal Production per Hour',
      optimalProductionPerHour: 'Optimal Production per Hour',
      normalSaturation: 'Normal Saturation',
      optimalSaturation: 'Optimal Saturation',
      maxActivity: 'Maximum Activity',
      machineStoppedTime: 'Machine Stopped Time',
      machineRunningTime: 'Machine Running Time',
      machineTime: 'Machine Time',
      valueHour: 'Hour Value',
      valueMinute: 'Minute Value',
      valuePoint: 'Point Value'
    },
    
    // Short versions for Excel
    resultShort: {
      normalCycle: 'Normal Cycle',
      optimalCycle: 'Optimal Cycle',
      contingencyTime: 'Contingency Time',
      totalK: 'K Total',
      normalProduction: 'Normal Prod/Shift',
      optimalProduction: 'Optimal Prod/Shift',
      normalProductionPerHour: 'Normal Prod/Hour',
      optimalProductionPerHour: 'Optimal Prod/Hour',
      normalSaturation: 'Normal Saturation',
      optimalSaturation: 'Optimal Saturation',
      maxActivity: 'Max Activity',
      valueHour: 'Hour Value',
      valueMinute: 'Minute Value',
      valuePoint: 'Point Value'
    },

    // Labels for custom units
    customUnits: {
      normalTime: 'Normal Time',
      optimalTime: 'Optimal Time',
      normalProdShift: 'Normal Prod/Shift',
      optimalProdShift: 'Optimal Prod/Shift',
      normalProdHour: 'Normal Prod/Hour',
      optimalProdHour: 'Optimal Prod/Hour',
      valueHour: 'Value/Hour',
      valueMinute: 'Value/Min',
      valuePoint: 'Value/Point'
    },

    descriptions: {
      normalCycle: 'Normal Cycle',
      optimalCycle: 'Optimal Cycle',
      contingencyTime: 'Contingency Time',
      totalK: 'Total K',
      normalProduction: 'Normal Production per Shift',
      optimalProduction: 'Optimal Production per Shift',
      normalProductionPerHour: 'Normal Production per Hour',
      optimalProductionPerHour: 'Optimal Production per Hour',
      normalSaturation: 'Normal Saturation',
      optimalSaturation: 'Optimal Saturation',
      maxActivity: 'Maximum Activity',
      machineStoppedTime: 'Machine Stopped Time',
      machineRunningTime: 'Machine Running Time',
      machineTime: 'Machine Time',
      valueHour: 'Hour Value',
      valueMinute: 'Minute Value',
      valuePoint: 'Point Value'
    },
    calculations: {
      normalCycle: 'Machine Stopped + (the longest of Machine Running or Machine Time)',
      optimalCycle: 'Optimized Machine Stopped + The longest of Machine Time or Optimized Machine Running Time',
      contingencyTime: 'Additional time for contingencies, calculated as: Normal Cycle × Contingency Percentage',
      totalK: 'The total sum of all applied supplements, expressed as time per cycle',
      normalProduction: 'Expected production per shift considering the normal cycle: (Minutes per Shift / Normal Cycle)',
      optimalProduction: 'Expected production per shift considering the optimal cycle: (Minutes per Shift / Optimal Cycle)',
      normalProductionPerHour: 'Normal production per hour: (1 hour / Normal Cycle)',
      optimalProductionPerHour: 'Optimal production per hour: (1 hour / Optimal Cycle)',
      normalSaturation: '(Machine Stopped + Machine Running) / Normal Cycle',
      optimalSaturation: '(Machine Stopped + Machine Running) / Optimal Cycle',
      maxActivity: 'Normal Cycle / Optimal Cycle',
      valueHour: 'Standard time value in hours',
      valueMinute: 'Standard time value in minutes',
      valuePoint: 'Standard time value in points: one hour equals 100 points.'
    },

    machineTypes: {
      'machine-stopped': 'Machine Stopped',
      'machine-running': 'Machine Running',
      'machine-time': 'Machine Time'
    },
    sections: {
      production: 'PRODUCTION',
      saturation: 'SATURATION',
      machineTimes: 'TIME TYPE IN RELATION TO THE MACHINE'
    },
    types: {
      'machine-stopped': 'MS',
      'machine-running': 'MR',
      'machine-time': 'MT'
    },
    table: {
      description: 'Description',
      type: 'Type',
      frequency: 'Frequency',
      observedTime: 'Observed Time',
      activity: 'Activity',
      supplements: 'Supplements',
      finalTime: 'Final Time'
    },
    actions: {
      exportPDF: 'Export to PDF',
      exportExcel: 'Export to Excel'
    },
    settings: {
      title: 'CONFIGURATION',
      timeUnit: 'Time unit',
      shiftMinutes: 'Minutes per shift',
      contingency: 'Contingency',
      contingencyDescription: 'Contingency is a percentage that is added to the total time to account for unexpected events.',
      contingencyPlaceholder: 'Enter contingency percentage',
      contingencyError: 'Contingency must be between 0 and 100',
      contingencyUnit: '%'
    },
    cycleDiscrepancy: {
      title: 'Normal Cycle Discrepancy',
      message: 'The normal cycle calculation differs from the one obtained in the fatigue calculation. Please review the waiting times.',
      reportValue: 'Report value',
      supplementsValue: 'Supplements value',
      difference: 'Difference',
      selectSource: 'Select cycle source',
      useReportCycle: 'Use report cycle',
      useSupplementsCycle: 'Use supplements cycle',
      tooltip: 'The normal cycle calculated in the report does not consider machine waiting times, while the supplements calculation does consider them according to different time ranges.',
      warningIcon: '⚠️'
    },

    charts: {
      title: 'Visual Process Analysis',
      noData: 'Insufficient data to display charts',
      
      machineTypes: {
        title: 'Distribution by Machine Type',
        stopped: 'Machine Stopped',
        running: 'Machine Running',
        time: 'Machine Time'
      },
      
      saturation: {
        title: 'Saturation/Normal Performance',
        used: 'Used Time',
        unused: 'Unused Time',
        label: 'Saturation'
      },
      
      elements: {
        title: 'Time by Element',
        finalTime: 'Final Time'
      },
      
      supplements: {
        title: 'Supplements by Element',
        finalTime: 'Final Time',
        percentage: 'Supplements Percentage (%)'
      },
      
      activity: {
        title: 'Activity by Element',
        average: 'Average Activity (%)'
      },
      
      cycleDistribution: {
        title: 'Cycle Time Distribution',
        work: 'Work Time',
        rest: 'Rest Time'
      },
      
      summary: {
        title: 'Statistical Summary',
        totalElements: 'Total Elements',
        optimalSaturation: 'Optimal Saturation',
        saturation: 'Normal Saturation',
        maxActivity: 'Maximum Activity'
      },

      flowchartSymbols: {
        title: 'Operation Type Analysis',
        distribution: 'Distribution by Operation Type',
        percentages: 'Percentages by Operation Type',
        count: 'Number of Operations',
        countTitle: 'Number of Operations by Type',
        types: 'Operation Types',
        summary: 'Operation Type Analysis',
        typesUsed: 'Types Used',
        totalOperations: 'Total Operations',
        mostCommon: 'Most Common',
        avgTime: 'Average Time',
        element: 'element',
        elements: 'elements'
      }
    }
  };
