# Supplements Force Component

## Descripción
Control para suplementos forzados.

## Props
```typescript
interface SupplementsForceProps {
  element: WorkElement;
  onSaved: () => void;
  supplement?: {
    points: Record<string, number>;
    percentage: number;
    isForced: boolean;
  };
}
```

## Características
- Input directo de porcentaje
- Anulación de cálculos por factores
- Validación de rango (0-100%)
- Persistencia del valor forzado
- Indicador visual de estado forzado

## Uso
```tsx
<SupplementsForce
  element={currentElement}
  onSaved={handleSave}
  supplement={currentSupplement}
/>
```