-- <PERSON><PERSON>, crear una tabla temporal con los registros que queremos mantener
CREATE TEMP TABLE temp_limits AS
WITH RankedLimits AS (
  SELECT *,
         ROW_NUMBER() OVER (
           PARTITION BY user_id 
           ORDER BY created_at DESC
         ) as rn
  FROM user_study_limits
)
SELECT id, user_id, user_email, monthly_credits, extra_credits, 
       used_monthly_credits, used_extra_credits, reset_date, 
       created_at, updated_at
FROM RankedLimits
WHERE rn = 1;

-- Luego, eliminar todos los registros de la tabla original
DELETE FROM user_study_limits;

-- Finalmente, insertar los registros únicos de vuelta
INSERT INTO user_study_limits (
  id, user_id, user_email, monthly_credits, extra_credits,
  used_monthly_credits, used_extra_credits, reset_date,
  created_at, updated_at
)
SELECT * FROM temp_limits;

-- Limpiar
DROP TABLE temp_limits;
