# Action Button Component

## Descripción
Botón de acción principal con retroalimentación táctil y visual.

## Props
```typescript
interface ActionButtonProps {
  onClick: () => void;
  icon?: React.ReactNode;
  label?: string;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}
```

## Características
- Feedback táctil en móviles
- Animación de pulsación
- Soporte para iconos
- Variantes de estilo
- Estado deshabilitado

## Subcomponentes
- `TouchFeedback`: Wrapper para feedback táctil

## Uso
```tsx
<ActionButton
  icon={<Plus />}
  label="Añadir Elemento"
  variant="primary"
  onClick={handleAdd}
  disabled={false}
/>
```