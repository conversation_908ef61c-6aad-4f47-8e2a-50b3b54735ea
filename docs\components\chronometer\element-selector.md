# Element Selector Component

## Descripción
Selector de elementos repetitivos para cronometrar.

## Props
```typescript
interface ElementSelectorProps {
  elements: WorkElement[];
  selectedIndex: number;
  onSelect: (index: number) => void;
  disabled?: boolean;
}
```

## Características
- Lista horizontal desplazable
- Indicador de elemento actual
- Navegación por índice
- Soporte para gestos táctiles
- Estado deshabilitado durante medición

## Uso
```tsx
<ElementSelector
  elements={repetitiveElements}
  selectedIndex={currentElementIndex}
  onSelect={handleElementSelect}
  disabled={isRunning}
/>
```