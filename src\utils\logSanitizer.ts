/**
 * Sanitiza strings para logging seguro removiendo caracteres especiales y limitando la longitud
 * @param input - El string a sanitizar
 * @param maxLength - Longitud máxima permitida (default: 1000)
 * @returns String sanitizado
 */
export function sanitizeForLog(input: unknown, maxLength: number = 1000): string {
  if (input === null || input === undefined) {
    return '[null]';
  }

  let str = String(input);
  
  // Remover caracteres especiales que podrían afectar los logs
  str = str.replace(/[\n\r\t\v\f\b]/g, ' ');
  
  // Remover caracteres de control y no imprimibles
  str = str.replace(/[\x00-\x1F\x7F-\x9F]/g, '');
  
  // Limitar la longitud
  if (str.length > maxLength) {
    str = str.substring(0, maxLength) + '...';
  }
  
  return str;
}
