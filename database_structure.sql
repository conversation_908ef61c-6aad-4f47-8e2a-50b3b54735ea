-- ================================================
-- ESTRUCTURA DE BASE DE DATOS PARA CRONOMETRAS
-- Migración completa con RLS para organizaciones
-- ================================================

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ================================================
-- TABLAS PRINCIPALES
-- ================================================

-- Tabla de perfiles de usuario
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    avatar_url TEXT,
    logo_url TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de organizaciones
CREATE TABLE IF NOT EXISTS organizations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES profiles(id) NOT NULL,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de miembros de organizaciones
CREATE TABLE IF NOT EXISTS organization_members (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    permissions JSONB DEFAULT '{}',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);

-- Tabla de carpetas
CREATE TABLE IF NOT EXISTS folders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
    color VARCHAR(7) DEFAULT '#8B5CF6',
    icon VARCHAR(50) DEFAULT 'folder',
    is_shared BOOLEAN DEFAULT false,
    study_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de estudios
CREATE TABLE IF NOT EXISTS studies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
    
    -- Información del estudio
    required_info JSONB NOT NULL DEFAULT '{}',
    optional_info JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    
    -- Datos del estudio
    elements JSONB DEFAULT '[]',
    time_records JSONB DEFAULT '{}',
    supplements JSONB DEFAULT '{}',
    crono_seguido_records JSONB DEFAULT '[]',
    
    -- Estado y configuración
    status VARCHAR(50) DEFAULT 'draft',
    is_shared BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================
-- HABILITAR ROW LEVEL SECURITY
-- ================================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE studies ENABLE ROW LEVEL SECURITY;

-- ================================================
-- FUNCIONES AUXILIARES
-- ================================================

-- Función para verificar membresía en organización
CREATE OR REPLACE FUNCTION user_belongs_to_organization(user_uuid UUID, org_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members 
        WHERE user_id = user_uuid 
        AND organization_id = org_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================
-- POLÍTICAS RLS PARA PROFILES
-- ================================================

-- Ver y editar perfil propio
CREATE POLICY "Users can manage own profile" ON profiles
    FOR ALL USING (auth.uid() = id);

-- Ver perfiles de miembros de la misma organización
CREATE POLICY "Organization members can view profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organization_members om1
            JOIN organization_members om2 ON om1.organization_id = om2.organization_id
            WHERE om1.user_id = auth.uid()
            AND om2.user_id = profiles.id
        )
    );

-- ================================================
-- POLÍTICAS RLS PARA ORGANIZATIONS
-- ================================================

-- Ver organizaciones propias o donde soy miembro
CREATE POLICY "Users can view accessible organizations" ON organizations
    FOR SELECT USING (
        owner_id = auth.uid() OR
        user_belongs_to_organization(auth.uid(), id)
    );

-- Solo propietario puede modificar
CREATE POLICY "Organization owners can manage" ON organizations
    FOR ALL USING (owner_id = auth.uid());

-- Crear organizaciones
CREATE POLICY "Users can create organizations" ON organizations
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- ================================================
-- POLÍTICAS RLS PARA ORGANIZATION_MEMBERS
-- ================================================

-- Ver miembros de organizaciones accesibles
CREATE POLICY "Users can view organization members" ON organization_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        user_belongs_to_organization(auth.uid(), organization_id)
    );

-- Gestión de miembros por propietarios/admins
CREATE POLICY "Organization admins can manage members" ON organization_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id AND om.user_id = auth.uid()
            WHERE o.id = organization_members.organization_id
            AND (o.owner_id = auth.uid() OR om.role IN ('owner', 'admin'))
        )
    );

-- ================================================
-- POLÍTICAS RLS PARA FOLDERS
-- ================================================

-- Ver carpetas propias, de organización, o compartidas
CREATE POLICY "Users can view accessible folders" ON folders
    FOR SELECT USING (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        is_shared = true
    );

-- Solo propietario puede gestionar sus carpetas
CREATE POLICY "Users can manage own folders" ON folders
    FOR ALL USING (user_id = auth.uid());

-- ================================================
-- POLÍTICAS RLS PARA STUDIES
-- ================================================

-- Ver estudios propios, de organización, o compartidos
CREATE POLICY "Users can view accessible studies" ON studies
    FOR SELECT USING (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        is_shared = true
    );

-- Solo propietario puede crear y actualizar estudios
CREATE POLICY "Users can create own studies" ON studies
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own studies" ON studies
    FOR UPDATE USING (user_id = auth.uid());

-- Solo propietario puede eliminar estudios
CREATE POLICY "Users can delete own studies" ON studies
    FOR DELETE USING (user_id = auth.uid());

-- ================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- ================================================

-- Índices para folders
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON folders(user_id);
CREATE INDEX IF NOT EXISTS idx_folders_org_id ON folders(organization_id);
CREATE INDEX IF NOT EXISTS idx_folders_parent ON folders(parent_folder_id);

-- Índices para studies  
CREATE INDEX IF NOT EXISTS idx_studies_user_id ON studies(user_id);
CREATE INDEX IF NOT EXISTS idx_studies_org_id ON studies(organization_id);
CREATE INDEX IF NOT EXISTS idx_studies_folder_id ON studies(folder_id);
CREATE INDEX IF NOT EXISTS idx_studies_created_at ON studies(created_at);

-- ================================================
-- TRIGGERS PARA UPDATED_AT
-- ================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_organizations_updated_at
    BEFORE UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_folders_updated_at
    BEFORE UPDATE ON folders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_studies_updated_at
    BEFORE UPDATE ON studies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 