import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import Stripe from 'https://esm.sh/stripe@12.18.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Precios de los planes
const PRICES = {
  CREDITS: 'price_1QifAFLE2XlCZ8RrJGYWuWxP', // 30 EUR por crédito
  MONTHLY: 'price_1Qif9lLE2XlCZ8RrkhrlbuGi', // 100 EUR por mes
  YEARLY: 'price_1Qif9QLE2XlCZ8RrqgpPWnxD',  // 1000 EUR por año
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { mode, priceId, userId, credits } = await req.json()

    if (!userId) {
      throw new Error('User ID is required')
    }

    let session

    if (mode === 'subscription') {
      // Crear sesión de suscripción
      session = await stripe.checkout.sessions.create({
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [{
          price: priceId === 'yearly' ? PRICES.YEARLY : PRICES.MONTHLY,
          quantity: 1,
        }],
        success_url: 'https://localhost:5173/profile?status=success&session_id={CHECKOUT_SESSION_ID}',
        cancel_url: 'https://localhost:5173/profile?status=cancelled',
        client_reference_id: userId,
      })
    } else {
      // Crear sesión de pago por créditos
      if (!credits || credits <= 0) {
        throw new Error('Number of credits is required')
      }

      session = await stripe.checkout.sessions.create({
        mode: 'payment',
        payment_method_types: ['card'],
        line_items: [{
          price: PRICES.CREDITS,
          quantity: credits,
        }],
        success_url: 'https://localhost:5173/profile?status=success&session_id={CHECKOUT_SESSION_ID}',
        cancel_url: 'https://localhost:5173/profile?status=cancelled',
        client_reference_id: userId,
      })
    }

    return new Response(
      JSON.stringify({ sessionId: session.id, url: session.url }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error creating session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
