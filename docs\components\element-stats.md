# Element Stats Component

## Descripción
Muestra estadísticas en tiempo real para elementos de trabajo.

## Props
```typescript
interface ElementStatsProps {
  totalTime: number;
  averageTime: number;
  recordCount: number;
  averageActivity: number;
}
```

## Características
- Tiempo total acumulado
- Tiempo promedio
- Contador de tomas
- Actividad promedio
- Cálculo de tomas restantes
- Actualización en tiempo real

## Cálculos
- Tomas restantes: `max(0, 30 - recordCount)`
- Fiabilidad estadística: 95%
- Fórmula: `n = (40 * σ² / e²)`

## Uso
```tsx
<ElementStats
  totalTime={120.5}
  averageTime={12.05}
  recordCount={10}
  averageActivity={100}
/>
```