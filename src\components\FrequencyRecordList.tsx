import React from 'react';
import { useTranslation } from 'react-i18next';
import { MessageSquare, Trash2 } from 'lucide-react';
import { WorkElement, TimeRecord } from '../types';

interface FrequencyRecordListProps {
  element: WorkElement;
  records: TimeRecord[];
  onDeleteRecord: (recordId: string) => void;
  onEditRecord: (record: TimeRecord) => void;
  onAddComment: (record: TimeRecord) => void;
  onDeleteAll: () => void;
}

export const FrequencyRecordList: React.FC<FrequencyRecordListProps> = ({
  element,
  records,
  onDeleteRecord,
  onEditRecord,
  onAddComment,
  onDeleteAll
}) => {
  const { t } = useTranslation(['frequency', 'common']);

  // Sort records in reverse chronological order
  const sortedRecords = [...records].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold">{element.description}</h3>
        <button
          onClick={onDeleteAll}
          className="p-2 text-red-500 hover:text-red-700 transition-colors"
          title={t('deleteAll', { ns: 'common' })}
        >
          <Trash2 className="w-5 h-5" />
        </button>
      </div>
      
      {sortedRecords.length === 0 ? (
        <div className="text-center py-8 text-gray-600">
          {t('noRecords', { ns: 'frequency' })}
        </div>
      ) : (
        <div className="space-y-2">
          {sortedRecords.map((record, index) => (
            <div
              key={record.id}
              onClick={() => onEditRecord(record)}
              className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer animate-slide-down"
              style={{
                animationDelay: `${index * 50}ms`,
                animationFillMode: 'backwards'
              }}
            >
              <div className="flex items-center">
                <button
                  className="p-2 rounded-full bg-green-100 mr-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddComment(record);
                  }}
                  title={t('addComment', { ns: 'common' })}
                >
                  <MessageSquare className="w-4 h-4 text-green-600" />
                </button>
                <div>
                  <div className="font-mono">{record.time.toFixed(2)}</div>
                  <div className="text-sm text-gray-500">
                    {new Date(record.timestamp).toLocaleTimeString()}
                  </div>
                  {record.comment && (
                    <div className="flex items-start gap-1 mt-1">
                      <span className="text-blue-500 text-xs font-medium">💬</span>
                      <div className="text-sm text-blue-700 bg-blue-50 px-2 py-1 rounded-md border-l-2 border-blue-300">
                        {record.comment}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <span className="font-bold mr-4">{record.activity}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteRecord(record.id);
                  }}
                  className="p-2 text-red-500 hover:text-red-700"
                  title={t('delete', { ns: 'common' })}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};