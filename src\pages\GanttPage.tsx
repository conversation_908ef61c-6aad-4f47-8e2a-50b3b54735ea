import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Header } from '../components/Header';
import { useTranslation } from 'react-i18next';
import { TimeUnit } from '../types/index';
import { Timeline, DataSet } from 'vis-timeline/standalone';
import type { TimelineOptions } from 'vis-timeline/standalone';
import 'vis-timeline/styles/vis-timeline-graph2d.css';

// Interfaz para las tareas recibidas del estado de la ruta
interface ReceivedTask {
  id: string;
  name: string;
  start_date: string; 
  duration: number; // Duración en segundos
  progress: number;
  timeUnit: TimeUnit;
  type?: string; // Tipo de tarea, puede ser 'maquina_parada' u otros
  concurrent?: boolean; // Indica si es una tarea concurrente
  parentId?: string; // ID de la tarea con la que es concurrente
}

// Interfaz para el estado recibido de la navegación
interface ReceivedState {
  tasks?: ReceivedTask[];
  studyName?: string;
  studyTimeUnit?: TimeUnit;
  studyId?: string; // Añadir ID del estudio
}

// Interfaz para los items del timeline
interface TimelineItem {
  id: string;
  content: string;
  start: Date;
  end: Date;
  title: string;
  group: number;
  className?: string;
  taskData: {
    originalDuration: number;
    timeUnit: TimeUnit;
    type?: string;
    concurrent?: boolean;
    parentId?: string;
  };
}

// Interfaz para grupos del timeline
interface TimelineGroup {
  id: number;
  content: string;
}

// Tipo para un item recuperado del timeline
type TimelineItemType = {
  id: string;
  content: string;
  start: Date;
  end: Date;
  taskData?: {
    type?: string;
    concurrent?: boolean;
    parentId?: string;
  };
  [key: string]: any;
};

// Función helper para convertir duraciones a segundos
const convertDurationToSeconds = (duration: number, unit: TimeUnit): number => {
  switch (unit) {
    case 'hours':
      return duration * 3600;
    case 'minutes':
      return duration * 60;
    case 'seconds':
      return duration;
    default:
      console.warn(`Unidad de tiempo desconocida: ${unit}`);
      return duration;
  }
};

// Función helper para formatear duración según unidad
const formatDuration = (seconds: number, unit: TimeUnit): string => {
  switch (unit) {
    case 'hours':
      return `${(seconds / 3600).toFixed(2)} horas`;
    case 'minutes':
      return `${(seconds / 60).toFixed(2)} minutos`;
    case 'seconds':
      return `${seconds.toFixed(2)} segundos`;
    default:
      return `${seconds.toFixed(2)} segundos`;
  }
};

const GanttPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation(['gantt', 'common']);
  const timelineContainer = useRef<HTMLDivElement>(null);
  const timelineInstance = useRef<Timeline | null>(null);
  
  const [tasks, setTasks] = useState<ReceivedTask[]>([]);
  const [timelineItems, setTimelineItems] = useState<TimelineItem[]>([]);
  const [timelineGroups, setTimelineGroups] = useState<TimelineGroup[]>([]);
  const [totalDuration, setTotalDuration] = useState(0);
  const [originalDuration, setOriginalDuration] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [studyId, setStudyId] = useState<string | undefined>(undefined); // Estado para guardar el ID del estudio

  const receivedState = location.state as ReceivedState | undefined;

  const studyName = receivedState?.studyName || 'Estudio sin nombre';
  const studyTimeUnit = receivedState?.studyTimeUnit || 'seconds';

  // Guardar el ID del estudio cuando se carga el componente
  useEffect(() => {
    // Intentar obtener el ID del estudio del estado de navegación
    if (receivedState?.studyId) {
      console.log("[GanttPage] ID del estudio recibido:", receivedState.studyId);
      setStudyId(receivedState.studyId);
    } else {
      // Intentar extraer el ID del estudio de la URL de referencia
      const referrer = document.referrer;
      if (referrer) {
        const match = referrer.match(/\/study\/([^\/]+)\/report/);
        if (match && match[1]) {
          console.log("[GanttPage] ID del estudio extraído de la URL:", match[1]);
          setStudyId(match[1]);
        } else {
          // Intentar extraer de la URL actual (por si viene como parámetro)
          const urlParams = new URLSearchParams(window.location.search);
          const urlStudyId = urlParams.get('studyId');
          if (urlStudyId) {
            console.log("[GanttPage] ID del estudio extraído de parámetros URL:", urlStudyId);
            setStudyId(urlStudyId);
          } else {
            console.log("[GanttPage] No se pudo determinar el ID del estudio");
          }
        }
      }
    }
  }, [receivedState]);

  // Inicializar las tareas cuando cambia el estado recibido
  useEffect(() => {
    if (receivedState?.tasks && receivedState.tasks.length > 0) {
      console.log("Tareas recibidas:", receivedState.tasks);
      
      // Identificar tareas concurrentes y asociarlas con sus tareas principales
      const processedTasks = receivedState.tasks.map(task => {
        // Marcar como concurrente si tiene "concurrente" en el tipo o nombre
        const isConcurrent = 
          task.type?.toLowerCase().includes('concurrente') || 
          task.name?.toLowerCase().includes('concurrente');
        
        return {
          ...task,
          concurrent: isConcurrent,
        };
      });
      
      // Calcular la duración total en segundos para ajustar la vista
      const totalDurationInSeconds = processedTasks.reduce((total, task) => {
        // Si es concurrente, no suma a la duración total
        if (task.concurrent) return total;
        return total + convertDurationToSeconds(task.duration, task.timeUnit);
      }, 0);
      
      setTotalDuration(totalDurationInSeconds);
      setOriginalDuration(totalDurationInSeconds);
      
      setTasks(processedTasks);
      
      // Convertir tareas al formato de vis-timeline
      convertTasksToTimelineItems(processedTasks);
    }
  }, [receivedState?.tasks]);

  // Convertir tareas al formato de vis-timeline
  const convertTasksToTimelineItems = (tasks: ReceivedTask[]) => {
    if (!tasks || tasks.length === 0) return;
    
    // Inspeccionar las tareas recibidas
    console.log("[GanttPage] Tareas originales recibidas:", JSON.parse(JSON.stringify(tasks)));
    
    // Buscar si hay alguna tarea que pueda ser concurrente basada en su nombre
    const potencialmenteConcurrentes = tasks.filter(task => {
      const nombre = task.name?.toLowerCase() || '';
      return nombre.includes('pistón') || 
             nombre.includes('piston') || 
             nombre.includes('interior') || 
             nombre.includes('concurrente') ||
             nombre.includes('paralelo');
    });
    
    if (potencialmenteConcurrentes.length > 0) {
      console.log("[GanttPage] Tareas potencialmente concurrentes encontradas:", 
        potencialmenteConcurrentes.map(t => `${t.name} (concurrent=${t.concurrent})`));
    } else {
      console.log("[GanttPage] No se encontraron tareas que parezcan concurrentes por su nombre");
    }
    
    const baseDate = new Date();
    baseDate.setHours(0, 0, 0, 0);

    // Clasificación de tareas
    const tmpTasks: ReceivedTask[] = [];
    const tmList: ReceivedTask[] = []; // Puede haber varios, pero solo uno es "el principal"
    const tmcTasks: ReceivedTask[] = [];
    const tmmTasks: ReceivedTask[] = [];
    const tfTasks: ReceivedTask[] = [];
    const otherIdentifiedTasks: ReceivedTask[] = []; // Tareas que caen en otras clasificaciones específicas

    // Primero identificamos todas las tareas concurrentes
    // Las tareas concurrentes tienen prioridad sobre otras clasificaciones
    tasks.forEach(task => {
      const nameLower = task.name?.toLowerCase() || "";
      const typeLower = task.type?.toLowerCase() || "";
      
      // TMC: Tiempo Máquina Concurrente - Palabras clave más amplias para detectar tareas concurrentes
      if (
        nameLower.includes('concurrente') || 
        typeLower.includes('concurrente') || 
        task.concurrent || 
        nameLower.includes('pistón') || 
        nameLower.includes('piston') || 
        nameLower.includes('interior') ||
        nameLower.includes('paralelo') ||
        nameLower.includes('simultáneo') ||
        nameLower.includes('simultaneo')
      ) {
        // Si encontramos una tarea que parece ser concurrente, pero no está marcada como tal, la marcamos
        if (!task.concurrent) {
          console.log(`[GanttPage] Tarea detectada como concurrente por nombre: "${task.name}"`);
          task.concurrent = true;
        }
        
        if (!tmcTasks.find(t => t.id === task.id)) {
          tmcTasks.push({...task, concurrent: true});
        }
      }
    });
    
    // Buscar la tarea principal de máquina (TM)
    let tmPrincipal: ReceivedTask | null = null;
    tasks.forEach(task => {
      const nameLower = task.name?.toLowerCase() || "";
      const typeLower = task.type?.toLowerCase() || "";
      
      // Buscar la tarea principal de máquina (por nombre o tipo)
      if (
        (nameLower.includes('máquina principal') || typeLower.includes('máquina principal') || 
         nameLower.includes('maquina principal') || typeLower.includes('maquina principal') ||
         nameLower === 'máquina' || typeLower === 'máquina' ||
         nameLower === 'maquina' || typeLower === 'maquina') &&
        !task.concurrent
      ) {
        tmPrincipal = task;
        console.log(`[GanttPage] Detectada tarea principal de máquina: "${task.name}"`);
      }
    });
    
    // Luego procesamos el resto de las tareas, evitando duplicados con las concurrentes
    tasks.forEach(task => {
      // Si ya está clasificada como concurrente, no la procesamos de nuevo
      if (tmcTasks.some(t => t.id === task.id)) return;
      
      const nameLower = task.name?.toLowerCase() || "";
      const typeLower = task.type?.toLowerCase() || "";
      let classified = false;

      // TMP: Tiempo Máquina Parada
      if (nameLower.includes('parada') || typeLower.includes('parada')) {
        tmpTasks.push(task); 
        classified = true;
      }
      // TF: Tiempo Frecuencial
      if (nameLower.includes('frecuencial') || typeLower.includes('frecuencial')) {
        tfTasks.push(task); 
        classified = true;
      }
      // TMM: Tiempo Máquina en Marcha (términos ampliados para mejor detección)
      if (
        nameLower.includes('marcha') || 
        typeLower.includes('marcha') || 
        nameLower.includes('introducir') ||
        nameLower.includes('recoger') ||
        nameLower.includes('embalar') ||
        nameLower.includes('colocar') ||
        (typeLower.includes('máquina') && !nameLower.includes('parada')) ||
        (typeLower.includes('maquina') && !nameLower.includes('parada'))
      ) {
        console.log(`[GanttPage] Detectada tarea de máquina en marcha: "${task.name}"`);
        if (!tmmTasks.find(t => t.id === task.id)) {
          tmmTasks.push({...task, type: task.type || 'máquina en marcha'});
        }
        classified = true;
      }
      // TM: Tiempo Máquina (Principal u otros)
      if (nameLower.includes('máquina') || typeLower.includes('máquina') || nameLower.includes('maquina') || typeLower.includes('maquina') || nameLower.includes('principal') || typeLower.includes('principal')) {
        // Si ya está en una lista más específica (TMM, TMC, TMP), no lo añadimos como TM genérico
        if (!tmpTasks.find(t=>t.id === task.id) && !tmmTasks.find(t=>t.id === task.id)) {
          if (!tmList.find(t => t.id === task.id)) {
            console.log(`[GanttPage] Detectada tarea de máquina principal: "${task.name}"`);
            tmList.push(task);
            
            // Si aún no tenemos una tarea principal identificada, usar esta
            if (!tmPrincipal) {
              tmPrincipal = task;
            }
          }
        }
        classified = true;
      }
      // Si no fue clasificada específicamente pero no es una de las ya filtradas
      if (!classified) {
        const alreadyInOtherList = tmpTasks.some(t => t.id === task.id) ||
                                 tfTasks.some(t => t.id === task.id) ||
                                 tmmTasks.some(t => t.id === task.id) ||
                                 tmList.some(t => t.id === task.id);
        if (!alreadyInOtherList && !otherIdentifiedTasks.find(t => t.id === task.id)) {
          console.log(`[GanttPage] Tarea no clasificada específicamente: "${task.name}"`);
          otherIdentifiedTasks.push(task);
        }
      }
    });
    
    // Si no hay TM principal pero hay tareas de máquina en marcha, usar la primera como principal
    if (!tmPrincipal && tmmTasks.length > 0) {
      tmPrincipal = tmmTasks[0];
      console.log(`[GanttPage] No se encontró TM principal. Usando primera tarea de máquina en marcha como referencia: "${tmPrincipal.name}"`);
    }
    
    console.log("[GanttPage] Tareas clasificadas:", {
      tmp: tmpTasks.map(t=>t.name),
      tmPrincipal: tmPrincipal?.name,
      tmc: tmcTasks.map(t=>t.name),
      tmm: tmmTasks.map(t=>t.name),
      tf: tfTasks.map(t=>t.name),
      otras: otherIdentifiedTasks.map(t=>t.name)
    });

    // Asignación de Grupos (una fila por tarea)
    const timelineGroups: TimelineGroup[] = [];
    const taskToGroupMap = new Map<string, number>();
    let groupIdCounter = 1;
    tasks.forEach(task => {
      if (!taskToGroupMap.has(task.id)) {
        taskToGroupMap.set(task.id, groupIdCounter);
        timelineGroups.push({ id: groupIdCounter, content: '' }); // content: task.name para debug
        groupIdCounter++;
      }
    });
    setTimelineGroups(timelineGroups);

    const items: TimelineItem[] = [];
    let currentTmpEndTime = new Date(baseDate); // t=0 inicialmente

    // 1. Tiempos de Máquina Parada (TMP)
    tmpTasks.forEach(task => {
      const duration = convertDurationToSeconds(task.duration, task.timeUnit);
      const startDate = new Date(currentTmpEndTime);
      const endDate = new Date(startDate.getTime() + duration * 1000);
      items.push({
        id: task.id, content: task.name, start: startDate, end: endDate,
        title: `${task.name} (${duration}s)`,
        group: taskToGroupMap.get(task.id)!,
        className: 'timeline-item maquina-parada',
        taskData: { ...task, originalDuration: duration, concurrent: false }
      });
      currentTmpEndTime = endDate;
    });

    // Determinar inicio del Tiempo Máquina (TM)
    const tmStartTime = new Date(currentTmpEndTime); // TM comienza después del último TMP, o en t=0 si no hay TMP
    let tmEndTime = new Date(tmStartTime); // Si no hay TM principal, este es el punto de referencia para "después de TM"

    // 2. Tiempo Máquina (TM) Principal
    if (tmPrincipal) {
      const duration = convertDurationToSeconds(tmPrincipal.duration, tmPrincipal.timeUnit);
      const endDate = new Date(tmStartTime.getTime() + duration * 1000);
      
      console.log(`[GanttPage] Creando tarea principal: "${tmPrincipal.name}" (inicio: ${tmStartTime.toISOString()}, fin: ${endDate.toISOString()})`);
      
      items.push({
        id: tmPrincipal.id, content: tmPrincipal.name, start: tmStartTime, end: endDate,
        title: `${tmPrincipal.name} (${duration}s)`,
        group: taskToGroupMap.get(tmPrincipal.id)!,
        className: 'timeline-item maquina-principal',
        taskData: { ...tmPrincipal, originalDuration: duration, concurrent: false }
      });
      tmEndTime = endDate;
    }

    // 3. Tiempo Máquina Concurrente (TMC) - Inicia con TM
    tmcTasks.forEach(task => {
      const duration = convertDurationToSeconds(task.duration, task.timeUnit);
      const startDate = new Date(tmStartTime);
      const endDate = new Date(startDate.getTime() + duration * 1000);
      
      console.log(`[GanttPage] Agregando tarea concurrente: "${task.name}" (duración: ${duration}s, inicio: ${startDate.toISOString()})`);
      
      items.push({
        id: task.id, content: task.name, start: startDate, end: endDate,
        title: `${task.name} (${duration}s) - Concurrente`,
        group: taskToGroupMap.get(task.id)!,
        className: 'timeline-item maquina-concurrente',
        taskData: { ...task, originalDuration: duration, concurrent: true }
      });
    });

    // 4. Tiempos Máquina en Marcha (TMM)
    // La primera tarea de máquina en marcha debe coincidir con el tiempo inicial de la máquina principal
    // Las siguientes se encadenan a partir del fin de la anterior
    if (tmmTasks.length > 0) {
      console.log(`[GanttPage] Procesando ${tmmTasks.length} tareas de máquina en marcha`);
      
      // Iniciar desde el fin de la tarea principal (si existe) o desde el inicio de tiempo de máquina
      let currentTime = tmStartTime;
      if (tmPrincipal) {
        // Calculamos cuándo termina la tarea principal
        const tmDuration = convertDurationToSeconds(tmPrincipal.duration, tmPrincipal.timeUnit);
        const tmEndTime = new Date(tmStartTime.getTime() + tmDuration * 1000);
        console.log(`[GanttPage] Tarea principal termina en: ${tmEndTime.toISOString()}`);
        // Las tareas de máquina en marcha empiezan después de la principal
        currentTime = tmEndTime;
      }
      
      // Procesar cada tarea de máquina en marcha, evitando duplicados
      tmmTasks.forEach((task, index) => {
        // Si esta tarea es la misma que ya usamos como principal, saltarla
        if (tmPrincipal && task.id === tmPrincipal.id) {
          console.log(`[GanttPage] Saltando tarea de máquina en marcha: "${task.name}" (ya usada como tarea principal)`);
          return;
        }
        
        const duration = convertDurationToSeconds(task.duration, task.timeUnit);
        const startDate = new Date(currentTime);
        const endDate = new Date(startDate.getTime() + duration * 1000);
        
        console.log(`[GanttPage] Tarea en marcha #${index + 1}: "${task.name}" (duración: ${duration}s, inicio: ${startDate.toISOString()}, fin: ${endDate.toISOString()})`);
        
        items.push({
          id: task.id, content: task.name, start: startDate, end: endDate,
          title: `${task.name} (${duration}s)`,
          group: taskToGroupMap.get(task.id)!,
          className: 'timeline-item maquina-marcha',
          taskData: { ...task, originalDuration: duration, concurrent: false }
        });
        
        // Actualizar tiempo para la siguiente tarea
        currentTime = endDate;
      });
    }
    
    // Calcular el punto de inicio para Tiempos Frecuenciales (TF)
    // Debe ser después de que TODOS los TMP, TM, TMC, y TMM hayan terminado.
    let latestPreTfEndTime = new Date(baseDate);
    if (items.length > 0) {
      const allEndTimes = items.map(item => item.end.getTime());
      latestPreTfEndTime = new Date(Math.max(...allEndTimes));
    } else {
      // Si no hay items previos (muy raro, pero por si acaso)
      latestPreTfEndTime = new Date(baseDate);
    }
    console.log("[GanttPage] Inicio calculado para TF:", latestPreTfEndTime);

    // 5. Tiempos Frecuenciales (TF)
    let currentTfEndTime = new Date(latestPreTfEndTime);
    tfTasks.forEach(task => {
      const duration = convertDurationToSeconds(task.duration, task.timeUnit);
      const startDate = new Date(currentTfEndTime);
      const endDate = new Date(startDate.getTime() + duration * 1000);
      
      console.log(`[GanttPage] Agregando tarea frecuencial: "${task.name}" (duración: ${duration}s)`);
      
      items.push({
        id: task.id, content: task.name, start: startDate, end: endDate,
        title: `${task.name} (${duration}s)`,
        group: taskToGroupMap.get(task.id)!,
        className: 'timeline-item tiempo-frecuencial',
        taskData: { 
          ...task, 
          originalDuration: duration, 
          concurrent: false,
          type: task.type || 'frecuencial' // Asegurar que tenga un tipo para identificarlo
        }
      });
      currentTfEndTime = endDate;
    });
    
    // 6. Otras Tareas (si las hay, se encadenan después de los TF o lo que haya sido último)
    let lastOverallEndTime = new Date(currentTfEndTime); // Inicia donde terminaron los TF (o TMPs/TM/TMMs si no hay TF)
     if (items.length > 0 && otherIdentifiedTasks.length > 0) {
        const allEndTimes = items.map(item => item.end.getTime());
        lastOverallEndTime = new Date(Math.max(...allEndTimes));
    }

    otherIdentifiedTasks.forEach(task => {
        const duration = convertDurationToSeconds(task.duration, task.timeUnit);
        const startDate = new Date(lastOverallEndTime);
        const endDate = new Date(startDate.getTime() + duration * 1000);
        items.push({
            id: task.id, content: task.name, start: startDate, end: endDate,
            title: `${task.name} (${duration}s)`,
            group: taskToGroupMap.get(task.id)!,
            className: 'timeline-item other-task',
            taskData: { ...task, originalDuration: duration, concurrent: false }
        });
        lastOverallEndTime = endDate;
    });

    console.log("[GanttPage] Items finales para el timeline:", JSON.parse(JSON.stringify(items)));
    setTimelineItems(items);
  };

  // Inicializar y configurar timeline
  useEffect(() => {
    // Si no hay contenedor o items, no hay nada que hacer
    if (!timelineContainer.current || timelineItems.length === 0) return;
    
    // Limpiar timeline existente si hay uno
    if (timelineInstance.current) {
      try {
        timelineInstance.current.destroy();
      } catch (error) {
        console.warn('Error al destruir el timeline anterior:', error);
      }
      timelineInstance.current = null;
    }
    
    // Crear datasets con los items y grupos
    const items = new DataSet<TimelineItemType>(timelineItems as any);
    const groups = new DataSet<TimelineGroup>(timelineGroups);
    
    // Calcular rango de tiempo para visualización
    let minTime: Date | null = null;
    let maxTime: Date | null = null;
    
    timelineItems.forEach(item => {
      if (!minTime || item.start < minTime) {
        minTime = new Date(item.start);
      }
      
      if (!maxTime || item.end > maxTime) {
        maxTime = new Date(item.end);
      }
    });
    
    // Opciones del timeline
    const options: TimelineOptions = {
      editable: {
        updateTime: true,
        updateGroup: false,
        remove: false,
        overrideItems: false
      },
      multiselect: false,
      margin: {
        item: {
          horizontal: 5
        }
      },
      orientation: 'top',
      // Extender significativamente el rango de tiempo visualizable
      min: minTime ? new Date((minTime as Date).getTime() - (60 * 1000)) : undefined, // 1 minuto antes del primer elemento
      max: maxTime ? new Date((maxTime as Date).getTime() + (120 * 1000)) : undefined, // 2 minutos después del último elemento
      format: {
        minorLabels: {
          millisecond: 'SSS',
          second: 'HH:mm:ss',
          minute: 'HH:mm:ss',
          hour: 'HH:mm',
          weekday: 'ddd D',
          day: 'D',
          week: 'w',
          month: 'MMM',
          year: 'YYYY'
        },
        majorLabels: {
          millisecond: 'HH:mm:ss',
          second: 'D MMMM',
          minute: 'ddd D MMMM',
          hour: 'ddd D MMMM',
          weekday: 'MMMM YYYY',
          day: 'MMMM YYYY',
          week: 'MMMM YYYY',
          month: 'YYYY',
          year: ''
        }
      },
      timeAxis: { 
        scale: 'second' as any, 
        step: 5 // Mostrar marcas cada 5 segundos para no saturar
      },
      // Definir niveles de zoom preestablecidos
      zoomMin: 500, // Mínimo zoom (milisegundos)
      zoomMax: 1000 * 60 * 60 * 24, // Máximo zoom (1 día)
      // Define snap como una función que toma un date y devuelve otro date
      snap: (date: Date, scale: string, step: number) => {
        const milliseconds = date.getMilliseconds();
        const remainder = milliseconds % 500; // 500ms = 0.5 segundos
        
        // Redondear al medio segundo más cercano
        if (remainder < 250) {
          date.setMilliseconds(milliseconds - remainder);
        } else {
          date.setMilliseconds(milliseconds + (500 - remainder));
        }
        
        return date;
      },
      moveable: true,
      zoomable: true,
      showCurrentTime: false,
      showMajorLabels: false, // Ocultar etiquetas de tiempo principales para evitar fechas innecesarias
      showMinorLabels: true, // Mantener etiquetas de tiempo secundarias
      tooltip: {
        followMouse: true,
        overflowMethod: 'cap'
      },
      // Configuración para mantener visible el elemento mientras se arrastra
      onMoving: (item: any, callback: Function) => {
        // Garantizar actualización en tiempo real durante el arrastre
        try {
          // Usar directamente el item proporcionado para calcular tiempo
          // Esta función es llamada continuamente durante el arrastre
          
          // Obtener el tiempo final actual directamente
          const allCurrentItems = items.get();
          if (allCurrentItems && item) {
            // Filtrar y encontrar el último elemento
            const itemsArray = Array.isArray(allCurrentItems) ? allCurrentItems : [allCurrentItems];
            
            // Crear una lista temporal con todos los elementos (excepto el que se mueve) más el elemento actualizado
            const updatedItemsList = itemsArray
              .filter(existingItem => existingItem.id !== item.id)
              .concat(item);
            
            // Encontrar el elemento más tardío (excluyendo concurrentes)
            const nonConcurrentItems = updatedItemsList.filter(i => !i.taskData?.concurrent);
            
            if (nonConcurrentItems.length > 0) {
              // Obtener extremos de tiempo
              let earliestTime = Number.POSITIVE_INFINITY;
              let latestTime = 0;
              
              nonConcurrentItems.forEach(i => {
                if (i.start && i.start.getTime() < earliestTime) {
                  earliestTime = i.start.getTime();
                }
                if (i.end && i.end.getTime() > latestTime) {
                  latestTime = i.end.getTime();
                }
              });
              
              if (earliestTime !== Number.POSITIVE_INFINITY && latestTime > 0) {
                const newDuration = Math.round((latestTime - earliestTime) / 1000);
                // Forzar actualización inmediata
                setTotalDuration(newDuration);
              }
            }
          }
        } catch (error) {
          console.error('Error en onMoving:', error);
        }
        
        // Si el elemento está fuera del área visible, ajustar la ventana para mostrarlo
        if (timelineInstance.current) {
          const currentWindow = timelineInstance.current.getWindow();
          const visibleWidth = currentWindow.end.getTime() - currentWindow.start.getTime();
          
          // Si el extremo final del elemento está fuera de la vista, mover la ventana
          if (item.end.getTime() > currentWindow.end.getTime()) {
            const newStart = new Date(item.end.getTime() - visibleWidth);
            const newEnd = new Date(item.end.getTime() + (visibleWidth * 0.2)); // Añadir un margen
            timelineInstance.current.setWindow(newStart, newEnd);
          }
          
          // Si el extremo inicial del elemento está fuera de la vista, mover la ventana
          if (item.start.getTime() < currentWindow.start.getTime()) {
            const newStart = new Date(item.start.getTime() - (visibleWidth * 0.2)); // Añadir un margen
            const newEnd = new Date(item.start.getTime() + visibleWidth);
            timelineInstance.current.setWindow(newStart, newEnd);
          }
        }
        
        // Marcar visualmente el elemento que se está moviendo
        document.querySelectorAll('.vis-item.moving').forEach(el => {
          el.classList.remove('moving');
        });
        
        // Buscar el elemento en el DOM y aplicar la clase
        setTimeout(() => {
          const movingEl = document.querySelector(`.vis-item[data-id="${item.id}"]`);
          if (movingEl) {
            movingEl.classList.add('moving');
          }
        }, 0);
        
        callback(item); // Importante: pasar el item procesado de vuelta al callback
      }
    };
    
    try {
      // Crear y guardar la instancia del timeline
      const timeline = new Timeline(timelineContainer.current, items, groups, options);
      timelineInstance.current = timeline;
      
      // Evento cuando se mueve o redimensiona un item
      timeline.on('timeChanged', (event: any) => {
        try {
          setIsDragging(false); // Terminó el arrastre
          const { id, time } = event;
          const itemData = items.get(id);
          
          if (!itemData) return;
          
          // Convertir el resultado a un solo item si es un array
          const item: TimelineItemType = Array.isArray(itemData) ? itemData[0] : itemData;
          
          // No permitir mover tareas concurrentes o principales independientemente
          if (item.taskData?.concurrent || item.taskData?.type?.toLowerCase().includes('principal')) {
            // Ignorar el cambio
            return;
          }
          
          if (item) {
            const start = time.start;
            const end = time.end;
            const durationInSeconds = Math.round((end.getTime() - start.getTime()) / 1000);
            const itemContent = item.content || 'Tarea';
            
            // Actualizar item con nuevas fechas
            items.update({
              id,
              start,
              end,
              title: `${itemContent} (${durationInSeconds}s)`
            });
            
            // Obtener todos los items
            const allItems = items.get();
            const itemsArray = Array.isArray(allItems) ? allItems : [allItems];
            
            // Clasificar por tipo
            const marchaItems = itemsArray.filter(i => i.taskData?.type?.toLowerCase().includes('marcha'));
            const concurrenteItems = itemsArray.filter(i => i.taskData?.concurrent || i.taskData?.type?.toLowerCase().includes('concurrente'));
            const principalItems = itemsArray.filter(i => i.taskData?.type?.toLowerCase().includes('principal'));
            const otherItems = itemsArray.filter(i => 
              !i.taskData?.type?.toLowerCase().includes('marcha') && 
              !i.taskData?.concurrent && 
              !i.taskData?.type?.toLowerCase().includes('concurrente') && 
              !i.taskData?.type?.toLowerCase().includes('principal')
            );
            
            // Si se movió una tarea de máquina en marcha
            if (item.taskData?.type?.toLowerCase().includes('marcha')) {
              // Ordenar las tareas de marcha por grupo (para mantener el orden)
              const sortedMarchaItems = [...marchaItems].sort((a, b) => {
                return a.group - b.group;
              });
              
              // Encontrar el índice del item que se movió
              const movedItemIndex = sortedMarchaItems.findIndex(i => i.id === id);
              
              if (movedItemIndex !== -1) {
                // La primera tarea de marcha determina el inicio común
                if (movedItemIndex === 0) {
                  const newCommonStart = start;
                  
                  // Actualizar concurrentes y principal para que comiencen con la primera tarea de marcha
                  [...concurrenteItems, ...principalItems].forEach(concurrentItem => {
                    const duration = concurrentItem.end.getTime() - concurrentItem.start.getTime();
                    items.update({
                      id: concurrentItem.id,
                      start: newCommonStart,
                      end: new Date(newCommonStart.getTime() + duration)
                    });
                  });
                  
                  // Reencadenar el resto de tareas de marcha
                  let lastEndTime = end;
                  for (let i = 1; i < sortedMarchaItems.length; i++) {
                    const nextItem = sortedMarchaItems[i];
                    const duration = nextItem.end.getTime() - nextItem.start.getTime();
                    
                    const newStart = new Date(lastEndTime);
                    const newEnd = new Date(newStart.getTime() + duration);
                    
                    items.update({
                      id: nextItem.id,
                      start: newStart,
                      end: newEnd
                    });
                    
                    lastEndTime = newEnd;
                  }
                } else {
                  // Si no es la primera, solo reencadenar desde la movida en adelante
                  let lastEndTime = end;
                  
                  for (let i = movedItemIndex + 1; i < sortedMarchaItems.length; i++) {
                    const nextItem = sortedMarchaItems[i];
                    const duration = nextItem.end.getTime() - nextItem.start.getTime();
                    
                    const newStart = new Date(lastEndTime);
                    const newEnd = new Date(newStart.getTime() + duration);
                    
                    items.update({
                      id: nextItem.id,
                      start: newStart,
                      end: newEnd
                    });
                    
                    lastEndTime = newEnd;
                  }
                }
              }
            }
            
            // Recalcular tiempo total - ahora usando las funciones auxiliares
            const latestEndTime = calcLatestEndTime(items);
            const earliestStartTime = calcEarliestStartTime(items);
            if (latestEndTime > 0 && earliestStartTime > 0) {
              const newTotalDuration = Math.round((latestEndTime - earliestStartTime) / 1000);
              setTotalDuration(newTotalDuration);
            }
            
            // Ajustar la ventana para mostrar todos los elementos
            if (timelineInstance.current) {
              timelineInstance.current.fit({
                animation: {
                  duration: 300,
                  easingFunction: 'easeInOutQuad'
                }
              });
            }
          }
        } catch (error) {
          console.error('Error al procesar el evento timeChanged:', error);
        }
      });
      
      // Actualizar duración total en tiempo real durante movimientos
      timeline.on('moving', (event: any) => {
        try {
          setIsDragging(true);
          
          // Verificar que tenemos un evento válido con id, start y end
          if (!event || !event.id || !event.start || !event.end) {
            console.warn('[GanttPage] Evento moving incompleto:', event);
            return;
          }
          
          // Obtener los datos actuales
          const allItems = items.get();
          if (!allItems || allItems.length === 0) return;
          
          // Crear una copia temporal del dataset para no afectar el original durante el cálculo
          const tempItems = new DataSet<TimelineItemType>(allItems);
          
          // Actualizar temporalmente el item en movimiento en la copia
          tempItems.update({
            id: event.id,
            start: event.start,
            end: event.end
          });
          
          // Aplicar clase visual al elemento que se está moviendo
          const movingItem = document.querySelector(`.vis-item[data-id="${event.id}"]`);
          if (movingItem) {
            movingItem.classList.add('moving');
          }
          
          // Recalcular tiempo total con la posición actual del elemento en movimiento
          const latestEndTime = calcLatestEndTime(tempItems);
          const earliestStartTime = calcEarliestStartTime(tempItems);
          
          if (latestEndTime > 0 && earliestStartTime > 0) {
            const newTotalDuration = Math.round((latestEndTime - earliestStartTime) / 1000);
            
            // Forzar actualización inmediata del estado con función callback
            setTotalDuration(prevDuration => {
              console.log(`[GanttPage] Actualizando tiempo durante arrastre: ${prevDuration}s -> ${newTotalDuration}s`);
              return newTotalDuration;
            });
            
            // Identificar y resaltar el elemento que marca el final del ciclo
            document.querySelectorAll('.vis-item.cycle-end').forEach(el => {
              el.classList.remove('cycle-end');
            });
            
            // Encontrar el elemento que marca el final del ciclo
            const regularItems = Array.isArray(allItems) ? allItems : [allItems];
            regularItems
              .filter(item => !item.taskData?.concurrent)
              .forEach(item => {
                if (item.end && Math.abs(item.end.getTime() - latestEndTime) < 100) {
                  const endItem = document.querySelector(`.vis-item[data-id="${item.id}"]`);
                  if (endItem) {
                    endItem.classList.add('cycle-end');
                  }
                }
              });
          }
        } catch (error) {
          console.error('Error al actualizar duración durante movimiento:', error);
        }
      });

      // Detectar cuando termina el arrastre
      timeline.on('movementFinished', () => {
        setIsDragging(false);
        
        // Quitar clase 'moving' de todos los elementos
        document.querySelectorAll('.vis-item.moving').forEach(el => {
          el.classList.remove('moving');
        });
        
        // Recalcular una vez más el tiempo total
        const latestEndTime = calcLatestEndTime(items);
        const earliestStartTime = calcEarliestStartTime(items);
        if (latestEndTime > 0 && earliestStartTime > 0) {
          const newTotalDuration = Math.round((latestEndTime - earliestStartTime) / 1000);
          setTotalDuration(newTotalDuration);
          
          // Resaltar el elemento que marca el final del ciclo
          const allItems = items.get();
          const itemsArray = Array.isArray(allItems) ? allItems : [allItems];
          const regularItems = itemsArray.filter(item => !item.taskData?.concurrent);
          
          // Quitar clase 'cycle-end' de todos los elementos
          document.querySelectorAll('.vis-item.cycle-end').forEach(el => {
            el.classList.remove('cycle-end');
          });
          
          // Encontrar el elemento que marca el final del ciclo
          regularItems.forEach(item => {
            if (item.end && Math.abs(item.end.getTime() - latestEndTime) < 100) {
              const endItem = document.querySelector(`.vis-item[data-id="${item.id}"]`);
              if (endItem) {
                endItem.classList.add('cycle-end');
              }
            }
          });
        }
        
        // Ajustar la ventana una vez terminado el movimiento
        if (timelineInstance.current) {
          timelineInstance.current.fit({
            animation: {
              duration: 500,
              easingFunction: 'easeOutQuad'
            }
          });
        }
      });
      
      // Actualizar duración total cuando cambia el zoom
      timeline.on('rangechange', () => {
        // Recalcular tiempo total
        recalculateTotalDuration(items);
      });
      
      // Inicial cálculo de duración total
      recalculateTotalDuration(items);
      
      // Resaltar inicialmente el elemento que marca el final del ciclo
      const latestEndTime = calcLatestEndTime(items);
      if (latestEndTime > 0) {
        const allItems = items.get();
        const itemsArray = Array.isArray(allItems) ? allItems : [allItems];
        const regularItems = itemsArray.filter(item => !item.taskData?.concurrent);
        
        // Encontrar el elemento que marca el final del ciclo
        regularItems.forEach(item => {
          if (item.end && Math.abs(item.end.getTime() - latestEndTime) < 100) {
            setTimeout(() => {
              const endItem = document.querySelector(`.vis-item[data-id="${item.id}"]`);
              if (endItem) {
                endItem.classList.add('cycle-end');
              }
            }, 500); // Breve retardo para asegurar que los elementos DOM estén listos
          }
        });
      }
    } catch (error) {
      console.error('Error al inicializar el timeline:', error);
    }
    
    // Limpiar al desmontar
    return () => {
      if (timelineInstance.current) {
        try {
          timelineInstance.current.destroy();
        } catch (error) {
          console.warn('Error al destruir el timeline durante cleanup:', error);
        }
        timelineInstance.current = null;
      }
    };
  }, [timelineItems, timelineGroups]);

  // Función para calcular el tiempo más temprano
  const calcEarliestStartTime = (items: DataSet<TimelineItemType>) => {
    try {
      const allItems = items.get();
      if (!allItems || allItems.length === 0) return 0;
      
      const itemsArray = Array.isArray(allItems) ? allItems : [allItems];
      
      // Filtrar elementos que no son concurrentes, usando la misma lógica que calcLatestEndTime
      const regularItems = itemsArray.filter(item => {
        // Verificar explícitamente si es una tarea concurrente
        const isConcurrent = item.taskData?.concurrent === true;
        
        // Verificar si es un elemento frecuencial por su clase o nombre
        const isFrequential = 
          (item.className && item.className.includes('frecuencial')) ||
          (item.content && item.content.toLowerCase().includes('frecuencial'));
        
        // Incluimos los elementos frecuenciales y los no concurrentes
        return !isConcurrent || isFrequential;
      });
      
      if (regularItems.length === 0) return 0;
      
      // Ordenar elementos por tiempo de inicio
      const sortedByStartTime = [...regularItems].sort((a, b) => {
        if (!a.start || !b.start) return 0;
        return a.start.getTime() - b.start.getTime();
      });
      
      // Tomar el primer elemento (el que comienza más temprano)
      const firstItem = sortedByStartTime[0];
      
      if (!firstItem || !firstItem.start) return 0;
      
      // Para depuración
      console.log(`[GanttPage] Elemento que marca el inicio del ciclo: "${firstItem.content}" (comienza en: ${new Date(firstItem.start).toISOString()})`);
      
      return firstItem.start.getTime();
    } catch (error) {
      console.error('Error al calcular el tiempo más temprano:', error);
      return 0;
    }
  };
  
  // Función para calcular el tiempo más tardío
  const calcLatestEndTime = (items: DataSet<TimelineItemType>) => {
    try {
      const allItems = items.get();
      if (!allItems || allItems.length === 0) return 0;
      
      const itemsArray = Array.isArray(allItems) ? allItems : [allItems];
      
      // Filtrar elementos que no son concurrentes y que no son de tipo "tiempo-frecuencial"
      // Los elementos frecuenciales deben contarse para el tiempo total, pero otros concurrentes no
      const regularItems = itemsArray.filter(item => {
        // Verificar explícitamente si es una tarea concurrente
        const isConcurrent = item.taskData?.concurrent === true;
        
        // Verificar si es un elemento frecuencial por su clase o nombre
        const isFrequential = 
          (item.className && item.className.includes('frecuencial')) ||
          (item.content && item.content.toLowerCase().includes('frecuencial'));
        
        // Incluimos los elementos frecuenciales y los no concurrentes
        return !isConcurrent || isFrequential;
      });
      
      if (regularItems.length === 0) return 0;
      
      // Ordenar elementos por tiempo de finalización
      const sortedByEndTime = [...regularItems].sort((a, b) => {
        if (!a.end || !b.end) return 0;
        return a.end.getTime() - b.end.getTime();
      });
      
      // Tomar el último elemento (el que termina más tarde)
      const lastItem = sortedByEndTime[sortedByEndTime.length - 1];
      
      if (!lastItem || !lastItem.end) return 0;
      
      // Para depuración
      console.log(`[GanttPage] Elemento que marca el final del ciclo: "${lastItem.content}" (termina en: ${new Date(lastItem.end).toISOString()})`);
      
      return lastItem.end.getTime();
    } catch (error) {
      console.error('Error al calcular el tiempo más tardío:', error);
      return 0;
    }
  };

  // Función para recalcular la duración total
  const recalculateTotalDuration = (items: DataSet<TimelineItemType>) => {
    try {
      // Utilizar las funciones auxiliares para calcular los tiempos
      const earliestTime = calcEarliestStartTime(items);
      const latestTime = calcLatestEndTime(items);
      
      // Salir si no pudimos determinar los tiempos
      if (earliestTime === 0 || latestTime === 0) return;
      
      // Calcular duración total en segundos
      const newTotalDuration = Math.round((latestTime - earliestTime) / 1000);
      
      // Actualizar la duración total
      setTotalDuration(newTotalDuration);
      
      // Si el timeline existe y no estamos arrastrando, ajustar el rango visible
      // No ajustar durante el arrastre para evitar saltos visuales
      if (timelineInstance.current && !isDragging) {
        timelineInstance.current.fit({
          animation: false
        });
      }
    } catch (error) {
      console.error('Error al recalcular la duración total:', error);
    }
  };

  // Manejar el caso sin tareas
  if (!tasks || tasks.length === 0) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Header
          title={t('gantt:title', { defaultValue: 'Diagrama de Gantt' })}
          subtitle={studyName || t('gantt:noStudyName', { defaultValue: 'Estudio no especificado' })}
          showSearch={false}
        />
        <main className="container mx-auto px-4 py-6">
          <p>{t('gantt:noTasks', { defaultValue: 'No hay tareas para mostrar en el diagrama de Gantt.' })}</p>
          {studyTimeUnit && <p>{t('gantt:studyTimeUnit', { defaultValue: 'Unidad de tiempo del estudio:' })} {studyTimeUnit}</p>}
        </main>
      </div>
    );
  }

  // Guardar cambios y volver al informe
  const handleSaveAndReturn = () => {
    // Si tenemos un ID de estudio, volver a la página del informe de ese estudio
    if (studyId) {
      console.log("[GanttPage] Volviendo al informe del estudio:", studyId);
      navigate(`/study/${studyId}/report`);
    } else {
      // Si no hay ID, simplemente volver a la página anterior
      console.log("[GanttPage] No hay ID de estudio, volviendo a la página anterior");
      navigate(-1);
    }
  };

  // Formatear información del tiempo de ciclo
  const formatCycleTimeInfo = () => {
    // Aplicar una clase adicional para resaltar cambios durante el arrastre
    const highlightClass = isDragging ? 'animate-pulse font-bold' : '';
    
    if (totalDuration === originalDuration) {
      return (
        <span className={highlightClass}>
          {formatDuration(totalDuration, 'seconds')}
        </span>
      );
    }
    
    const difference = totalDuration - originalDuration;
    const prefix = difference > 0 ? '+' : '';
    
    return (
      <span className={`flex items-center ${highlightClass}`}>
        <span className="font-semibold">{formatDuration(totalDuration, 'seconds')}</span>
        <span className={`ml-2 text-sm ${difference > 0 ? 'text-red-500' : 'text-green-500'}`}>
          ({prefix}{difference}s)
        </span>
      </span>
    );
  };

  return (
    <div className="w-full flex flex-col">
      <Header
        title={t('gantt:title', { defaultValue: 'Diagrama de Gantt' })}
        subtitle={`${studyName}`}
        showSearch={false}
      />
      {/* Panel de información del tiempo de ciclo */}
      <div className="bg-white shadow-md py-2 px-4 flex justify-between items-center z-10">
        <div className="flex items-center space-x-4">
          <div>
            <span className="text-sm text-gray-500">{t('gantt:originalCycleTime', { defaultValue: 'Tiempo de ciclo original:' })}</span>
            <div className="font-medium">{formatDuration(originalDuration, 'seconds')}</div>
          </div>
          <div className="border-l border-gray-300 pl-4">
            <span className="text-sm text-gray-500">{t('gantt:currentCycleTime', { defaultValue: 'Tiempo de ciclo actual:' })}</span>
            <div className={`font-medium ${isDragging ? 'text-blue-600 animate-pulse text-lg transition-all duration-200' : ''}`}>{formatCycleTimeInfo()}</div>
          </div>
        </div>
        {isDragging && (
          <div className="text-blue-600 text-sm font-medium animate-pulse">
            Actualizando en tiempo real...
          </div>
        )}
      </div>
      {/* Eliminamos los botones para información y leyenda flotante */}
      
      {/* Estilos CSS inline para personalizar la apariencia del timeline */}
      <style>
        {`
          body {
            margin: 0;
            padding: 0;
            overflow-y: auto;
            overflow-x: hidden;
          }
          .timeline-item {
            background-color: #6366f1;
            color: white;
            border-radius: 4px;
            border-color: #4f46e5;
          }
          .maquina-principal {
            background-color: #ef4444;
            border-color: #dc2626;
            z-index: 1;
          }
          .maquina-parada {
            background-color: #ef4444;
            border-color: #dc2626;
            z-index: 1;
          }
          .maquina-marcha {
            background-color: #6B9AFF;
            border-color: #477EF5;
            border-width: 2px;
            z-index: 2;
          }
          .maquina-concurrente {
            background-color: #FF96B6; /* Color rosa similar al de la tabla */
            border-color: #E74C7A;
            border-width: 2px;
            border-style: dashed;
            z-index: 10; /* Mayor z-index para asegurar que esté por encima */
            opacity: 0.9;
          }
          .vis-timeline {
            border: none !important;
            font-family: inherit;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: visible !important;
            max-width: none !important;
            transform-origin: top left;
          }
          .vis-item-content {
            padding: 5px;
            font-weight: 500;
          }
          .vis-time-axis .vis-text {
            color: #475569;
            font-size: 0.85rem;
            padding: 4px 0;
          }
          .vis-time-axis .vis-text.vis-minor {
            font-weight: normal;
          }
          .vis-time-axis .vis-text.vis-major {
            font-weight: bold;
          }
          .vis-time-axis .vis-text span {
            display: inline-block;
            padding: 0 3px;
          }
          .vis-labelset .vis-label {
            color: #1f2937;
            font-weight: 500;
          }
          .vis-panel {
            border: none !important;
            background: white;
            overflow: visible !important;
          }
          .vis-content {
            background: white;
          }
          /* Ocultar completamente la columna izquierda */
          .vis-left {
            width: 0 !important;
            display: none !important;
          }
          /* Centrar el contenido de los elementos */
          .vis-item {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
          }
          /* Hacer que el centro ocupe todo el ancho disponible */
          .vis-center {
            left: 0 !important;
            width: 100% !important;
            overflow: visible !important;
          }
          .timeline-container {
            width: 100% !important;
            height: 500px !important; /* Altura fija para permitir scroll */
            overflow: auto !important;
            padding: 0;
            margin: 0 0 20px 0;
            position: relative !important; /* Para que .vis-timeline se posicione dentro */
          }
          /* Permitir scroll y mejorar la navegación del timeline */
          .vis-panel.vis-center, 
          .vis-panel.vis-bottom, 
          .vis-panel.vis-top {
            overflow: visible !important;
          }
          /* Asegurar que los controles de navegación estén visibles */
          .vis-navigation {
            z-index: 10;
          }
          /* Mejorar visibilidad durante el arrastre */
          .vis-item.vis-selected {
            z-index: 999 !important; 
            border-color: #2563eb !important;
            box-shadow: 0 0 8px rgba(37, 99, 235, 0.6) !important;
          }
          /* Estilo para el elemento que se está moviendo */
          .vis-item.moving {
            box-shadow: 0 0 12px rgba(59, 130, 246, 0.8) !important;
            border-width: 2px !important;
            border-style: dashed !important;
            opacity: 0.9 !important;
          }
          /* Resaltar mejor el tiempo que marca el final del ciclo */
          .vis-item.cycle-end {
            border-right: 3px solid #f97316 !important;
            border-right-style: double !important;
          }
          /* Estilo para elementos frecuenciales */
          .tiempo-frecuencial {
            background-color: #14B8A6 !important; /* Color teal */
            border-color: #0D9488 !important;
            border-width: 2px !important;
            z-index: 5 !important;
            color: white !important;
            font-weight: bold !important;
          }
          /* Asegurar que la línea de tiempo se extiende suficientemente */
          .vis-foreground, .vis-background {
            min-width: 150% !important;
          }
          /* Permitir que el área de scroll se extienda más allá */
          .vis-shadow, .vis-ruler, .vis-loading {
            min-width: 2000px !important;
          }
          .bottom-bar {
            position: fixed;
            bottom: 0;
            right: 0;
            padding: 10px 20px;
            background: white;
            z-index: 1000;
          }
        `}
      </style>
      
      <div className="timeline-container" ref={timelineContainer}>
        {/* Área principal del timeline que ocupa todo el espacio disponible */}
      </div>

      {/* Contenedor separado para la leyenda con altura fija para garantizar visibilidad */}
      <div className="mt-8 pt-4 border-t-2 border-gray-200">
        <div className="bg-blue-50 border-2 border-blue-200 shadow-lg rounded-lg p-6 m-4 mb-32">
          <h3 className="text-2xl font-bold mb-6 text-blue-800">{t('gantt:legendTitle', { defaultValue: 'Leyenda' })}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-center bg-white p-3 rounded-lg shadow">
              <div className="w-10 h-8 bg-red-500 rounded mr-4"></div>
              <span className="text-lg font-medium">{t('gantt:machineStopped', { defaultValue: 'Máquina parada' })}</span>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow">
              <div className="w-10 h-8 bg-red-500 rounded mr-4"></div>
              <span className="text-lg font-medium">{t('gantt:mainMachine', { defaultValue: 'Máquina principal' })}</span>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow">
              <div className="w-10 h-8 bg-blue-400 rounded mr-4"></div>
              <span className="text-lg font-medium">{t('gantt:machineRunning', { defaultValue: 'Máquina en marcha' })}</span>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow">
              <div className="w-10 h-8 bg-pink-400 rounded border-dashed border-2 border-pink-600 mr-4"></div>
              <span className="text-lg font-medium">{t('gantt:concurrentMachine', { defaultValue: 'Máquina concurrente' })}</span>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow">
              <div className="w-10 h-8 bg-teal-500 rounded mr-4"></div>
              <span className="text-lg font-medium">{t('gantt:frequentialTime', { defaultValue: 'Tiempo frecuencial' })}</span>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow">
              <div className="w-10 h-8 border-r-4 border-orange-500 rounded mr-4"></div>
              <span className="text-lg font-medium">{t('gantt:cycleEndMarker', { defaultValue: 'Marca fin de ciclo' })}</span>
            </div>
          </div>
          <div className="mt-8 p-4 bg-white rounded-lg shadow text-lg text-gray-700 space-y-3">
            <p className="font-medium">{t('gantt:zoomHelp', { defaultValue: 'Zoom: Utiliza los botones + y - o mantén CTRL mientras usas la rueda del ratón.' })}</p>
            <p className="font-medium">{t('gantt:dragHelp', { defaultValue: 'Arrastrar: Haz clic y arrastra un elemento para moverlo.' })}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GanttPage;