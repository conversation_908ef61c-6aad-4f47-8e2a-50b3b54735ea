# Documentación: Cálculo de Suplementos Normalizados

## Descripción General

El sistema de cálculo de suplementos normalizados permite calcular de forma precisa el tiempo total de ciclo considerando suplementos por necesidades personales y fatiga, aplicando diferentes casos según el tiempo de inactividad de la máquina.

## Tipos de Elementos

### 1. Machine-Running (MM - Máquina en Marcha)
- **Descripción**: Elementos donde el operario trabaja mientras la máquina está en funcionamiento
- **Característica**: Trabajo interior - operario activo durante funcionamiento de máquina
- **Aplicación de suplementos**: Se aplican necesidades personales y fatiga

### 2. Machine-Stopped (MP - Máquina Parada)
- **Descripción**: Elementos donde el operario trabaja mientras la máquina está detenida
- **Característica**: Trabajo exterior - operario activo con máquina parada
- **Aplicación de suplementos**: Se aplican necesidades personales y fatiga

### 3. Machine-Time (TM - Tiempo de Máquina)
- **Descripción**: Tiempo puro de máquina donde el operario solo supervisa
- **Característica**: Tiempo de espera - operario en supervisión/espera
- **Aplicación de suplementos**: Solo necesidades personales (fatiga = 0%)

## Separación de Suplementos

### Regla Fundamental
Todo porcentaje de suplementos se separa en:
- **5% fijo**: Necesidades personales (siempre aplicado)
- **Resto del %**: Fatiga (porcentaje total - 5%)

### Ejemplo de Separación
```
Suplementos totales: 15%
├── Necesidades personales: 5% (fijo)
└── Fatiga: 10% (15% - 5%)

Tiempo normalizado: 6.83875 seg
├── NP: 6.83875 × 5% = 0.3419375 seg
└── Fatiga: 6.83875 × 10% = 0.683875 seg
Total suplementos: 1.0258125 seg
```

## Proceso de Cálculo

### Paso 1: Normalización de Tiempos por Elemento

Para cada elemento se calcula:

```
Tiempo Normalizado = Tiempo Observado × (Actividad / Escala Normal) × Factor Frecuencia

Donde:
- Tiempo Observado: Tiempo promedio registrado
- Actividad: Actividad apreciada del elemento
- Escala Normal: Escala de actividad del estudio (normalmente 100)
- Factor Frecuencia: Repeticiones / Ciclos
```

### Paso 2: Cálculo de Suplementos por Elemento

```
Necesidades Personales = Tiempo Normalizado × 5%
Fatiga = Tiempo Normalizado × (Porcentaje Total - 5%)
Total Suplementos = Necesidades Personales + Fatiga
Tiempo Final = Tiempo Normalizado + Total Suplementos
```

### Paso 3: Agrupación por Tipos

```
Total MP = Σ (Tiempos finales de elementos machine-stopped)
Total MM = Σ (Tiempos finales de elementos machine-running)  
Total TM = Σ (Tiempos finales de elementos machine-time)
```

### Paso 4: Cálculo del Ciclo Base

```
Ciclo Normal = MP + MAX(MM, TM)

Explicación:
- MP siempre se suma (trabajo con máquina parada)
- Se toma el máximo entre MM y TM porque ocurren simultáneamente
```

### Paso 5: Cálculo del Tiempo de Espera

```
Tiempo de Espera = MAX(0, TM - MM)

Si TM > MM: Hay tiempo de espera (inactividad)
Si TM ≤ MM: No hay tiempo de espera
```

## Casos de Aplicación de Suplementos

### Caso 1: Tiempo de Espera ≤ 30 segundos
- **Condición**: `Tiempo de Espera ≤ 30 seg`
- **Aplicación**: 
  - ✅ Necesidades personales: Se aplican fuera del ciclo
  - ✅ Fatiga: Se aplica fuera del ciclo
- **Fórmula**: `Ciclo Final = Ciclo Normal`

### Caso 2: Tiempo de Espera entre 30-90 segundos
- **Condición**: `30 seg < Tiempo de Espera < 91 seg`
- **Aplicación**:
  - ✅ Necesidades personales: Se aplican fuera del ciclo
  - ⚠️ Fatiga: Parcialmente absorbida
- **Cálculo de absorción**:
  ```
  Capacidad Absorción = (Tiempo Espera - 30) × 1.5
  Suplementos Absorbidos = MIN(Total Suplementos, Capacidad Absorción)
  Ciclo Final = Ciclo Normal - Suplementos Absorbidos
  ```

### Caso 3: Tiempo de Espera entre 91-600 segundos
- **Condición**: `91 seg ≤ Tiempo de Espera ≤ 600 seg`
- **Aplicación**:
  - ✅ Necesidades personales: Se aplican fuera del ciclo
  - ❌ Fatiga: Completamente absorbida (= 0)
- **Fórmula**: `Ciclo Final = Ciclo Normal - Total Fatiga`

### Caso 4: Tiempo de Espera > 600 segundos
- **Condición**: `Tiempo de Espera > 600 seg`
- **Aplicación**:
  - ❌ Necesidades personales: Completamente absorbidas (= 0)
  - ❌ Fatiga: Completamente absorbida (= 0)
- **Fórmula**: `Ciclo Final = Ciclo Normal - Total Suplementos`

## Cálculo de Fatiga Ponderada

### Propósito
Determinar el porcentaje de fatiga promedio considerando solo elementos de trabajo activo (MM + MP).

### Proceso

1. **Filtrar elementos activos**: Solo MM y MP (excluir TM)
2. **Calcular tiempo normalizado** por elemento
3. **Calcular peso** de cada elemento:
   ```
   Peso = Tiempo Normalizado Elemento / Suma Total Normalizada
   ```
4. **Calcular fatiga ponderada**:
   ```
   Fatiga Ponderada = Σ(Peso × Porcentaje Suplementos)
   ```
5. **Ajustar por necesidades personales**:
   ```
   Fatiga Final = Fatiga Ponderada - 5%
   ```

### Aplicación Correcta
El porcentaje se aplica sobre el **tiempo total de elementos activos**, NO sobre el tiempo base completo:

```
✅ CORRECTO: Tiempo Elementos Activos × Fatiga Final%
❌ INCORRECTO: Tiempo Base Completo × Fatiga Final%
```

## Ejemplo Completo de Cálculo

### Datos de Entrada
```
MM1: 20.00 seg, 120% actividad, 15% suplementos
MP1: 5.471 seg, 125% actividad, 25% suplementos  
MP2: 1.247 seg, 103% actividad, 15% suplementos
TM1: 45.00 seg, 100% actividad, 5% suplementos
TM2: 10.00 seg, 100% actividad, 5% suplementos
```

### Paso 1: Normalización
```
MM1: 20.00 × (120/100) × 1 = 24.00 seg
MP1: 5.471 × (125/100) × 1 = 6.84 seg
MP2: 1.247 × (103/100) × 1 = 1.28 seg
TM1: 45.00 × (100/100) × 1 = 45.00 seg
TM2: 10.00 × (100/100) × 1 = 10.00 seg
```

### Paso 2: Separación de Suplementos
```
MM1: NP = 24.00 × 5% = 1.20 seg, Fatiga = 24.00 × 10% = 2.40 seg
MP1: NP = 6.84 × 5% = 0.34 seg, Fatiga = 6.84 × 20% = 1.37 seg
MP2: NP = 1.28 × 5% = 0.06 seg, Fatiga = 1.28 × 10% = 0.13 seg
TM1: NP = 45.00 × 5% = 2.25 seg, Fatiga = 45.00 × 0% = 0.00 seg
TM2: NP = 10.00 × 5% = 0.50 seg, Fatiga = 10.00 × 0% = 0.00 seg
```

### Paso 3: Tiempos Finales
```
MM1: 24.00 + 1.20 + 2.40 = 27.60 seg
MP1: 6.84 + 0.34 + 1.37 = 8.55 seg
MP2: 1.28 + 0.06 + 0.13 = 1.47 seg
TM1: 45.00 + 2.25 + 0.00 = 47.25 seg
TM2: 10.00 + 0.50 + 0.00 = 10.50 seg
```

### Paso 4: Agrupación
```
Total MP = 8.55 + 1.47 = 10.02 seg
Total MM = 27.60 seg
Total TM = 47.25 + 10.50 = 57.75 seg
```

### Paso 5: Ciclo Normal
```
Ciclo Normal = MP + MAX(MM, TM)
Ciclo Normal = 10.02 + MAX(27.60, 57.75)
Ciclo Normal = 10.02 + 57.75 = 67.77 seg
```

### Paso 6: Tiempo de Espera
```
Tiempo Espera = MAX(0, TM - MM)
Tiempo Espera = MAX(0, 57.75 - 27.60) = 30.15 seg
```

### Paso 7: Aplicación de Caso
Como `30.15 seg > 30 seg` y `< 91 seg` → **Caso 2**

```
Capacidad Absorción = (30.15 - 30) × 1.5 = 0.225 seg
Total Suplementos = 4.36 + 3.90 = 8.26 seg
Suplementos Absorbidos = MIN(8.26, 0.225) = 0.225 seg
Ciclo Final = 67.77 - 0.225 = 67.55 seg
```

### Resultado Final
```
Tiempo Base: 59.30 seg (sin suplementos aplicados)
Necesidades Personales: 4.36 seg
Fatiga: 3.90 seg  
Ciclo Total: 67.55 seg
```

## Validaciones y Consistencia

### Verificaciones del Sistema
1. **Tiempo base**: Debe ser positivo
2. **Suplementos**: No pueden ser negativos
3. **Consistencia entre pestañas**: Todos los valores deben coincidir
4. **Casos**: Se aplica automáticamente según tiempo de espera

### Troubleshooting Común
- **Valores inconsistentes**: Verificar que todos los elementos tengan registros
- **Cálculos incorrectos**: Confirmar que la actividad y frecuencia están bien configuradas
- **Casos mal aplicados**: Revisar el tiempo de espera calculado

## Notas Técnicas

### Precisión
- Todos los cálculos mantienen 3 decimales de precisión
- Solo se redondea para visualización final
- Los cálculos internos usan números de punto flotante completos

### Performance
- Los cálculos se ejecutan con debounce de 500ms
- Se evitan recálculos innecesarios
- Los resultados se guardan automáticamente

### Compatibilidad
- Los estudios existentes mantienen compatibilidad
- Los nuevos cálculos no afectan datos previos
- La separación de suplementos es transparente al usuario 