import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSupplementTypeStore } from '../../store/supplementTypeStore';

interface SupplementsTableProps {
  onSelectTable: (table: 'oit' | 'tal') => void;
}

export const SupplementsTable: React.FC<SupplementsTableProps> = ({ onSelectTable }) => {
  const { t } = useTranslation(['supplements']);
  const setSelectedType = useSupplementTypeStore((state) => state.setSelectedType);

  const handleSelectTable = (type: 'oit' | 'tal') => {
    setSelectedType(type);
    onSelectTable(type);
  };

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4">
          <img
            src="/oit-logo.png"
            alt="OIT"
            className="w-16 h-16 object-contain"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold">{t('oitTables', { ns: 'supplements' })}</h3>
            <p className="text-sm text-gray-600">{t('oitDescription', { ns: 'supplements' })}</p>
          </div>
        </div>
        <button
          onClick={() => handleSelectTable('oit')}
          className="mt-4 w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600"
        >
          {t('openTables', { ns: 'supplements' })}
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4">
          <img
            src="/tal-logo.png"
            alt="TAL"
            className="w-16 h-16 object-contain"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold">{t('talTables', { ns: 'supplements' })}</h3>
            <p className="text-sm text-gray-600">{t('talDescription', { ns: 'supplements' })}</p>
          </div>
        </div>
        <button
          onClick={() => handleSelectTable('tal')}
          className="mt-4 w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600"
        >
          {t('openTables', { ns: 'supplements' })}
        </button>
      </div>
    </div>
  );
};