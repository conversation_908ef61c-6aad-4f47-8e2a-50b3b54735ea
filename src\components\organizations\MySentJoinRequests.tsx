import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useOrganizationStore } from '../../store/organizationStore';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { Spinner } from '../ui/Spinner'; // Updated import path and using named import
import { Button } from '../ui/button';
import { LoadingState, SentRequestSkeleton } from '../ui/skeleton';
import { RequestCard } from '../ui/enhanced-card';

interface JoinRequest {
  id: string;
  organization_id: string;
  status: string;
  created_at: string;
  processed_at?: string;
}

export const MySentJoinRequests: React.FC = () => {
  const { t, i18n } = useTranslation(['common']);
  const { 
    mySentJoinRequests, 
    fetchMySentJoinRequests, 
    getOrganizationName 
  } = useOrganizationStore();
  const [organizationNames, setOrganizationNames] = useState<Record<string, string>>({});
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingError, setLoadingError] = useState<string>('');
  const [isLoadingNames, setIsLoadingNames] = useState(false);

  // Cargar las solicitudes al montar el componente
  useEffect(() => {
    const loadRequests = async () => {
      setIsLoading(true);
      try {
        await fetchMySentJoinRequests();
      } catch (err) {
        console.error('Error al cargar solicitudes:', err);
        setError(t('joinRequests.errorLoading'));
      } finally {
        setIsLoading(false);
      }
    };
    loadRequests();
  }, [fetchMySentJoinRequests, t]);

  // Obtener los nombres de las organizaciones por separado
  useEffect(() => {
    const fetchOrganizationNames = async () => {
      if (!mySentJoinRequests) return;
      
      setIsLoadingNames(true);
      try {
        const names: Record<string, string> = {};
        
        for (const request of mySentJoinRequests) {
          try {
            // Intentar obtener el nombre con reintentos
            let retries = 0;
            const maxRetries = 2;
            let name = '';
            
            while (retries <= maxRetries) {
              try {
                name = await getOrganizationName(request.organization_id);
                break; // Si no hay error, salimos del bucle
              } catch (error) {
                console.error(`Intento ${retries + 1} fallido para obtener nombre de organización ${request.organization_id}:`, error);
                retries++;
                
                if (retries > maxRetries) {
                  throw error; // Si agotamos los reintentos, propagamos el error
                }
                
                // Esperar un poco antes de reintentar
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }
            
            names[request.organization_id] = name || `Org. ${request.organization_id.substring(0, 6)}`;
          } catch (error) {
            console.error(`Error al obtener nombre para organización ${request.organization_id}:`, error);
            // Usar un nombre genérico pero único basado en el ID
            names[request.organization_id] = `Organización ${request.organization_id.substring(0, 6)}`;
          }
        }
        
        setOrganizationNames(names);
      } catch (error) {
        console.error('Error al obtener nombres de organizaciones:', error);
        setLoadingError(t('joinRequests.errorLoadingNames'));
      } finally {
        setIsLoadingNames(false);
      }
    };

    fetchOrganizationNames();
  }, [mySentJoinRequests, getOrganizationName, t]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{t('joinRequests.mySentRequests')}</h3>
      
      {isLoading && (
        <LoadingState 
          message={t('joinRequests.loadingSentRequests', { defaultValue: 'Loading your sent requests...' })} 
          icon="📤" 
          variant="default"
        />
      )}
      
      {error && (
        <div className="text-red-500">
          {error}
        </div>
      )}
      
      {!isLoading && !error && mySentJoinRequests.length === 0 && (
        <div className="text-center py-8 px-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border-2 border-dashed border-purple-200">
          <div className="max-w-sm mx-auto">
            {/* Icono ilustrativo */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-3xl">📝</span>
              </div>
            </div>
            
            {/* Texto principal */}
            <h4 className="text-lg font-semibold text-purple-900 mb-2">
              {t('joinRequests.noSentRequests', { defaultValue: 'No sent requests' })}
            </h4>
            <p className="text-purple-700 text-sm mb-4 leading-relaxed">
              {t('joinRequests.joinInvitation', { defaultValue: 'Want to join an existing organization? Ask an administrator for the invitation code and send your request.' })}
            </p>
            
            {/* Botón de acción */}
            <Button 
              variant="outline" 
              size="sm"
              className="border-purple-300 text-purple-700 hover:bg-purple-50 font-medium"
              onClick={() => {
                // Activar el tab de unirse
                const joinTab = document.querySelector('[value="join"]') as HTMLElement;
                joinTab?.click();
              }}
            >
              <span className="mr-2">🔗</span>
              Unirse a Organización
            </Button>
          </div>
        </div>
      )}
      
      {isLoadingNames && (
        <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600 mr-3"></div>
          <span className="text-purple-700 font-medium">{t('joinRequests.loadingOrganizationNames', { defaultValue: 'Loading organization names...' })}</span>
        </div>
      )}
      
      {loadingError && (
        <div className="text-red-500">
          {loadingError}
        </div>
      )}
      
      {!isLoading && !error && mySentJoinRequests.length > 0 && (
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {mySentJoinRequests.map((request) => (
            <RequestCard
              key={request.id}
              request={{
                ...request,
                user_email: t('joinRequests.yourRequest', { defaultValue: 'Your request' }),
                organization_name: organizationNames[request.organization_id] || t('joinRequests.unknownOrganization')
              }}
              type="outgoing"
              className="h-full"
            />
          ))}
        </div>
      )}
    </div>
  );
};
