<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup Sistema</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            padding: 12px 24px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #status {
            margin-top: 20px;
            color: #666;
        }
        .spinner {
            display: none;
            width: 24px;
            height: 24px;
            margin: 10px auto;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sistema de Backup</h1>
        <button id="backupBtn" onclick="realizarBackup()">Realizar Backup</button>
        <div id="spinner" class="spinner"></div>
        <div id="status"></div>
    </div>

    <script>
        async function realizarBackup() {
            const button = document.getElementById('backupBtn');
            const spinner = document.getElementById('spinner');
            const statusElement = document.getElementById('status');
            
            try {
                // Deshabilitar botón y mostrar spinner
                button.disabled = true;
                spinner.style.display = 'block';
                statusElement.textContent = 'Realizando backup...';
                
                const response = await fetch('/api/backup', {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    throw new Error('Error en el backup');
                }

                // Obtener el blob del archivo
                const blob = await response.blob();
                
                // Crear URL para descarga
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('content-disposition').split('filename=')[1];
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                statusElement.textContent = '¡Backup completado exitosamente! El archivo se está descargando.';
            } catch (error) {
                statusElement.textContent = 'Error al realizar el backup: ' + error.message;
            } finally {
                // Rehabilitar botón y ocultar spinner
                button.disabled = false;
                spinner.style.display = 'none';
            }
        }
    </script>
</body>
</html>