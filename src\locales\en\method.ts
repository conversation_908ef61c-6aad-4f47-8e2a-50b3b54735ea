export default {
  title: 'Method',
  noStudySelected: 'No study selected',
  addElement: 'Add Element',
  element: 'Element',
  new: 'New Element',
  edit: 'Edit Element',
  description: 'Description',
  descriptionPlaceholder: 'Enter element description',
  type: 'In function of its relationship with the machine',
  types: {
    'machine-stopped': 'MS',
    'machine-running': 'MR', 
    'machine-time': 'MT',
    'machine-type': 'Machine',
    'repetitive-type': 'Repetitive',
    'repetitive': 'Repetitive',
    'frequency-type': 'Frequency'
  },
  typesLong: {
    'machine-stopped': 'Machine Stopped',
    'machine-running': 'Machine Running',
    'machine-time': 'Machine Time',
    'machine': 'Machine',
    'repetitive': 'Repetitive',
    'frequency': 'Frequency'
  },
  repetitions: 'Repetitions',
  cycles: 'Cycles',
  frequency: {
    title: 'Frequency',
    repetitions: 'Repetitions',
    cycles: 'Cycles',
    each: 'each'
  },
  repetitionType: 'In function of its regularity',
  repetitionTypes: {
    'repetitive': 'Repetitive',
    'frequency': 'Frequency',
    'machine': 'Machine'
  },
  addToLibrary: 'Add to Library',
  frequencyFormat: '{{repetitions}} repetitions, {{cycles}} cycles {{each}}',
  observations: 'Observations',
  saveSuccess: 'Method saved successfully',
  saveError: 'Error saving method',
  deleteSuccess: 'Element deleted successfully',
  deleteError: 'Error deleting element',
  confirmDelete: 'Are you sure you want to delete this element?',
  noElements: 'No elements available',
  loadError: 'Error loading elements',
  name: 'Machine elements, {{number}}',
  save: 'Save',
  cancel: 'Cancel',
  dragAndDrop: 'Drag and drop to reorder',
  deleteElementTitle: 'Delete Element',
  deleteElementConfirmation: 'Are you sure you want to delete this element?',
  speech: {
    noSpeech: 'No speech detected',
    noMicrophone: 'No microphone found',
    notAllowed: 'Speech recognition not allowed',
    error: 'Speech recognition error',
    listening: 'Listening...',
    pressToSpeak: 'Press to speak'
  },
  selectFromLibrary: 'Select from Library',
  selectElements: 'Select Elements'
};
