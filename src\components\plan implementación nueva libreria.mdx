Voy a analizar en profundidad la aplicación para entender la estructura actual de la biblioteca y generar un plan completo para implementar los cambios solicitados.
Search files...

Read file: src/pages/LibraryPage.tsx

Ran tool

Read file: src/components/library/LibraryElementList.tsx

Search files...

Basándome en mi análisis profundo de la aplicación, he identificado la arquitectura actual y los cambios necesarios. Aquí está el **PLAN COMPLETO** para implementar la nueva biblioteca de elementos organizados por estudios:

## 📋 PLAN DE IMPLEMENTACIÓN

### **FASE 1: Rediseño de la Interfaz de Biblioteca**

#### 1.1 Crear nueva estructura de componentes
- **Crear `StudyLibraryList.tsx`**: Lista principal de estudios marcados como biblioteca
  - Mostrar estudios con `optional_info.visibleEnBiblioteca = false`
  - Tarjetas expandibles/colapsables por estudio
  - Contador de elementos por estudio
  - Estado de expansión individual por estudio

- **<PERSON>rear `StudyElementsCompactList.tsx`**: Lista compacta de elementos por estudio
  - Elementos numerados (1, 2, 3...)
  - Checkbox para selección múltiple
  - Información resumida: descripción, tiempo, tipo
  - Indicador visual de elementos ya seleccionados

- **Modificar `LibraryPage.tsx`**: 
  - Reemplazar la vista actual de elementos planos
  - Implementar navegación por estudios
  - Mantener estado de selecciones globales
  - Panel lateral para elementos seleccionados

#### 1.2 Sistema de estado mejorado
- **Modificar `elementSearchStore.ts`**:
  - Cambiar estructura de datos de elementos planos a agrupados por estudio
  - Mantener estado de estudios expandidos/colapsados
  - Gestionar selecciones por estudio
  - Funciones para cambiar entre estudios manteniendo selecciones

### **FASE 2: Gestión de Selecciones Multi-Estudio**

#### 2.1 Lógica de selección avanzada
- **Estado de selecciones persistente**: Mantener elementos seleccionados al cambiar entre estudios
- **Panel de elementos seleccionados**: 
  - Lista lateral/inferior con elementos seleccionados
  - Organizado por estudio de origen
  - Opción de eliminar selecciones individuales
  - Contador total de elementos seleccionados

#### 2.2 Funcionalidad de colapso/expansión
- **Solo un estudio expandido a la vez**: Al abrir uno, colapsar el anterior
- **Animaciones suaves** para transiciones
- **Estado persistente** durante la sesión

### **FASE 3: Importador de Excel**

#### 3.1 Crear componente `ExcelImporterModal.tsx`
- **Carga de archivo Excel**: Soporte para .xlsx y .xls
- **Vista previa de datos**: Mostrar primeras filas del Excel
- **Mapeo de columnas**:
  ```
  - Descripción del elemento
  - Tiempo observado promedio  
  - Actividad observada promedio
  - Frecuencia del elemento
  - Tipo de elemento (máquina parada/marcha/tiempo)
  - Tipo de repetición (repetitivo/frecuencial/máquina)
  - Símbolo de diagrama de flujo
  - Suplementos aplicados
  - Repeticiones por ciclo
  - Ciclos de frecuencia
  ```

#### 3.2 Procesamiento de datos
- **Validación de datos**: Verificar tipos y rangos válidos
- **Creación de estudio**: Generar estudio completo con elementos
- **Configuración automática**: Asignar configuraciones por defecto
- **Marcado para biblioteca**: Automáticamente marcar como `visibleEnBiblioteca = true`

#### 3.3 Integración con biblioteca existente
- **Utilizar estructura actual**: Aprovechar `elementSearchStore` existente
- **Compatibilidad**: Mantener funcionamiento con sistema actual
- **Consumo de créditos**: Integrar con sistema de créditos existente, cada estudio creado desde la bilblioteca consume 1 crédito como hasta ahora.

### **FASE 4: Mejoras de UX/UI**

#### 4.1 Diseño responsivo mejorado
- **Vista móvil**: Navegación por pestañas para estudios
- **Vista tablet**: Panel lateral colapsable
- **Vista desktop**: Dos paneles side-by-side

#### 4.2 Funcionalidades adicionales
- **Búsqueda por estudio**: Filtrar estudios por nombre/empresa
- **Búsqueda por elemento**: Buscar dentro de elementos de estudios expandidos
- **Ordenamiento**: Por fecha, nombre, número de elementos
- **Filtros**: Por empresa, tipo de estudio, fecha de creación

### **FASE 5: Refactorización y Optimización**

#### 5.1 Optimización de rendimiento
- **Carga lazy**: Cargar elementos solo cuando se expande un estudio
- **Virtualización**: Para listas largas de elementos
- **Memoización**: Optimizar re-renders innecesarios

#### 5.2 Mantenimiento de compatibilidad
- **Migración gradual**: Mantener funcionalidad actual mientras se implementa nueva
- **Tests**: Asegurar que funcionalidad existente no se rompa
- **Documentación**: Actualizar documentación de componentes

## 🗂️ ESTRUCTURA DE ARCHIVOS PROPUESTA

```
src/
├── components/
│   ├── library/
│   │   ├── StudyLibraryList.tsx          [NUEVO]
│   │   ├── StudyElementsCompactList.tsx  [NUEVO]
│   │   ├── ExcelImporterModal.tsx        [NUEVO]
│   │   ├── SelectedElementsPanel.tsx     [NUEVO]
│   │   ├── LibraryPage.tsx               [MODIFICAR]
│   │   └── LibraryElementList.tsx        [MANTENER]
├── store/
│   ├── elementSearchStore.ts             [MODIFICAR]
│   └── excelImportStore.ts               [NUEVO]
├── utils/
│   ├── excelProcessor.ts                 [NUEVO]
│   └── libraryDataTransform.ts           [NUEVO]
└── types/
    └── excelImport.ts                    [NUEVO]
```

## 🔄 FLUJO DE USUARIO PROPUESTO

1. **Entrada a biblioteca** → Lista de estudios marcados como biblioteca
2. **Clic en estudio** → Expansión con lista numerada de elementos
3. **Selección múltiple** → Checkboxes para marcar elementos deseados
4. **Cambio de estudio** → Mantener selecciones, colapsar anterior
5. **Panel de seleccionados** → Vista consolidada de elementos elegidos
6. **Crear nuevo estudio** → Usar elementos seleccionados
7. **Importar Excel** → Botón especial para cargar paquetes completos

## ⚠️ CONSIDERACIONES TÉCNICAS

- **Compatibilidad**: Mantener funcionamiento actual durante transición
- **Rendimiento**: Implementar carga lazy para estudios con muchos elementos  
- **Datos**: Aprovechar estructura actual de `studies` table
- **Créditos**: Integrar importador con sistema de créditos existente
- **Responsive**: Diseño que funcione en móvil, tablet y desktop

Este plan mantiene la arquitectura existente mientras introduce las nuevas funcionalidades solicitadas de manera incremental y compatible.