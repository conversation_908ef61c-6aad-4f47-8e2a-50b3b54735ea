import { NextApiRequest, NextApiResponse } from 'next';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(import.meta.env.VITE_STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

const SUBSCRIPTION_PRICES = {
  monthly: import.meta.env.VITE_STRIPE_MONTHLY_PRICE_ID,
  annual: import.meta.env.VITE_STRIPE_ANNUAL_PRICE_ID,
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, userEmail, plan, paymentMethodId } = req.body;

    if (!userId || !userEmail || !plan || !paymentMethodId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const priceId = SUBSCRIPTION_PRICES[plan as keyof typeof SUBSCRIPTION_PRICES];
    
    if (!priceId) {
      return res.status(400).json({ error: 'Invalid subscription plan' });
    }

    // Crear o recuperar el cliente de Stripe
    let customer;
    const existingCustomers = await stripe.customers.list({
      email: userEmail,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
      // Actualizar el método de pago por defecto
      await stripe.customers.update(customer.id, {
        default_payment_method: paymentMethodId,
      });
    } else {
      customer = await stripe.customers.create({
        email: userEmail,
        payment_method: paymentMethodId,
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
    }

    // Crear la suscripción
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: {
        payment_method_types: ['card'],
        save_default_payment_method: 'on_subscription',
      },
      expand: ['latest_invoice.payment_intent'],
    });

    return res.status(200).json({
      subscriptionId: subscription.id,
      clientSecret: (subscription.latest_invoice as Stripe.Invoice)
        .payment_intent?.client_secret,
      subscription: subscription,
    });
  } catch (error) {
    console.error('Error creating subscription:', error);
    return res.status(500).json({ error: 'Error creating subscription' });
  }
}
