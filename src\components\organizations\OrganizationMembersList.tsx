import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import { useOrganizationStore } from '../../store/organizationStore';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export const OrganizationMembersList: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { members, currentOrganization, isLoading } = useOrganizationStore();

  const handlePromote = async (memberId: string) => {
    // TODO: Implementar promoción de miembros
    toast({
      title: t('members.promoted'),
      description: t('members.promotedDescription'),
    });
  };

  const handleDemote = async (memberId: string) => {
    // TODO: Implementar degradación de miembros
    toast({
      title: t('members.demoted'),
      description: t('members.demotedDescription'),
    });
  };

  const handleRemove = async (memberId: string) => {
    // TODO: Implementar eliminación de miembros
    toast({
      title: t('members.removed'),
      description: t('members.removedDescription'),
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!members.length) {
    return (
      <p className="text-gray-500">{t('members.noMembers')}</p>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.email')}
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.role')}
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.joinDate')}
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('members.actions')}
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {members.map((member) => (
            <tr key={member.user_id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{member.user_email}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                  ${member.role === 'owner' ? 'bg-purple-100 text-purple-800' : 
                    member.role === 'admin' ? 'bg-blue-100 text-blue-800' : 
                    'bg-green-100 text-green-800'}`}>
                  {t(`members.${member.role}`)}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {format(new Date(member.created_at), 'PPP', { locale: es })}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex space-x-2">
                  {member.role !== 'owner' && (
                    <>
                      {member.role === 'member' ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePromote(member.user_id)}
                        >
                          {t('members.promote')}
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDemote(member.user_id)}
                        >
                          {t('members.demote')}
                        </Button>
                      )}
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRemove(member.user_id)}
                      >
                        {t('members.remove')}
                      </Button>
                    </>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
