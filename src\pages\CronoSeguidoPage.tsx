import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Play, Pause, Square, ArrowUp, ArrowDown, X, AlertCircle, Plus, Trash2, Save, ChevronUp, ChevronDown, Mic } from 'lucide-react';
import { Header } from '../components/Header';
import { StatsDisplay } from '../components/StatsDisplay';
import { EditTimeModal } from '../components/EditTimeModal';
import { AddToMethodModal } from '../components/AddToMethodModal';
import { DeleteConfirmModal } from '../components/DeleteConfirmModal';
import { BulkMethodModal } from '../components/BulkMethodModal';
import { BulkAddToMethodConfigModal } from '../components/BulkAddToMethodConfigModal';
import { playSoundAndVibrate } from '../utils/sounds';
import { useStudyStore } from '../store/studyStore';
import { useStudyFromUrl } from '../hooks/useStudyFromUrl';
import { useAuthStore } from '../store/authStore';
import { CronoSeguidoRecord } from '../types/index';

interface CronoSeguidoPageProps {
  onBack: () => void;
}

export const CronoSeguidoPage: React.FC<CronoSeguidoPageProps> = ({ onBack }) => {
  const { t } = useTranslation(['cronoSeguido', 'common', 'study']);
  const selectedStudy = useStudyStore(state => state.selectedStudy);
  const { updateStudy, updateCronoSeguido } = useStudyStore();
  useStudyFromUrl(); // Add this hook to handle study from URL
  const { user } = useAuthStore();
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [time, setTime] = useState(0);
  const [activity, setActivity] = useState(selectedStudy?.required_info?.activity_scale?.normal || 100);
  const [records, setRecords] = useState<CronoSeguidoRecord[]>(selectedStudy?.crono_seguido_records || []);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const [showAddToMethodModal, setShowAddToMethodModal] = useState(false);
  const [showBulkMethodModal, setShowBulkMethodModal] = useState(false);
  const [showBulkConfigModal, setShowBulkConfigModal] = useState(false);
  const [editingRecord, setEditingRecord] = useState<CronoSeguidoRecord | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeId, setSelectedTimeId] = useState<string | null>(null);
  const [lastAddedRecord, setLastAddedRecord] = useState<CronoSeguidoRecord | null>(null);
  const [isSharedStudy, setIsSharedStudy] = useState(false);
  
  // Estados para el reconocimiento de voz no bloqueante
  const [isListeningForDescription, setIsListeningForDescription] = useState(false);
  const [listeningRecordId, setListeningRecordId] = useState<string | null>(null);
  const recognitionRef = useRef<any | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Estados para manejo de doble clic en eliminación
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<string | null>(null);
  const deleteClickTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const timerRef = useRef<number>();
  const startTimeRef = useRef<number>();

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      // Limpiar reconocimiento de voz y timeouts
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      // Limpiar timeouts de eliminación
      deleteClickTimeouts.current.forEach(timeout => clearTimeout(timeout));
      deleteClickTimeouts.current.clear();
    };
  }, []);

  // Función para iniciar reconocimiento de voz no bloqueante
  const startVoiceRecognition = (recordId: string) => {
    try {
      
      // Detener reconocimiento previo si existe
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }

      // Limpiar timeout previo
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Verificar soporte del navegador
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      if (!SpeechRecognition) {
        console.error('❌ Tu navegador no soporta el reconocimiento de voz');
        alert('Tu navegador no soporta el reconocimiento de voz. Usa Chrome, Edge o Safari para esta función.');
        return false;
      }

      // Verificar si tenemos permisos de micrófono (si está disponible)
      if (navigator.permissions) {
        navigator.permissions.query({ name: 'microphone' as any }).then((result) => {
          if (result.state === 'denied') {
            alert('Permisos de micrófono denegados. Por favor, permite el acceso al micrófono en la configuración del navegador.');
          }
        }).catch(() => {
          // Silently handle permission check errors
        });
      }

      const recognition = new SpeechRecognition();
      recognition.lang = 'es-ES';
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        setIsListeningForDescription(true);
        setListeningRecordId(recordId);
      };

      recognition.onresult = (event: any) => {
        const last = event.results.length - 1;
        const text = event.results[last][0].transcript;
        
        // Actualizar la descripción del registro específico
        updateRecordDescription(recordId, text);
        
        // Detener el reconocimiento
        setIsListeningForDescription(false);
        setListeningRecordId(null);
      };

      recognition.onerror = (event: any) => {
        setIsListeningForDescription(false);
        setListeningRecordId(null);
      };

      recognition.onend = () => {
        setIsListeningForDescription(false);
        setListeningRecordId(null);
      };

      recognitionRef.current = recognition;
      
      // Intentar iniciar el reconocimiento
      try {
        recognition.start();
        
        // Auto-detener después de 8 segundos
        timeoutRef.current = setTimeout(() => {
          if (recognitionRef.current) {
            recognitionRef.current.stop();
          }
        }, 8000);
        
        return true;
      } catch (startError) {
        setIsListeningForDescription(false);
        setListeningRecordId(null);
        return false;
      }

    } catch (err) {
      setIsListeningForDescription(false);
      setListeningRecordId(null);
      return false;
    }
  };

  // Función para actualizar la descripción de un registro específico
  const updateRecordDescription = async (recordId: string, description: string) => {
    if (!selectedStudy) return;

    // Usar setRecords con función callback para obtener el estado más actualizado
    setRecords(currentRecords => {
      // Buscar el registro en el estado actual
      const recordIndex = currentRecords.findIndex(r => r.id === recordId);
      
      if (recordIndex === -1) {
        // Buscar en selectedStudy como fallback
        const studyRecords = selectedStudy.crono_seguido_records || [];
        const studyRecord = studyRecords.find((r: any) => r.id === recordId);
        
        if (studyRecord) {
          const updatedRecord = { ...studyRecord, description };
          const newRecords = [updatedRecord, ...currentRecords];
          
          // Guardar en base de datos de forma asíncrona
          updateCronoSeguido(selectedStudy.id, newRecords).catch(console.error);
          
          return newRecords;
        } else {
          return currentRecords; // No cambiar el estado si no se encuentra
        }
      }

      // Actualizar el registro encontrado
      const updatedRecords = currentRecords.map(record =>
        record.id === recordId
          ? { ...record, description }
          : record
      );

      // Guardar en base de datos de forma asíncrona
      updateCronoSeguido(selectedStudy.id, updatedRecords).catch(console.error);

      return updatedRecords;
    });
  };

  useEffect(() => {
    if (selectedStudy?.crono_seguido_records) {
      // Ensure each record has a valid ID
      const recordsWithIds = selectedStudy.crono_seguido_records.map((record: any) => ({
        ...record,
        id: record.id || crypto.randomUUID()
      }));

      // Solo actualizar si es la carga inicial (records está vacío) o si hay más registros en la DB que localmente
      if (records.length === 0 || recordsWithIds.length > records.length) {
      setRecords(recordsWithIds);
      }

      // If we added IDs, update the study to persist them
      if (recordsWithIds.some((record: any) => !selectedStudy.crono_seguido_records.find((r: any) => r.id === record.id))) {
        updateCronoSeguido(selectedStudy.id, recordsWithIds);
      }
    } else if (records.length === 0) {
      setRecords([]);
    }
  }, [selectedStudy]);

  useEffect(() => {
    if (selectedStudy && user) {
      const isOwner = selectedStudy.user_id === user.id;
      setIsSharedStudy(!isOwner);
      
      // Limpiar errores al cambiar de estudio
      setError(null);
    }
  }, [selectedStudy, user, t]);

  const handleStart = async () => {
    await playSoundAndVibrate('start', 200);
    // Si estaba pausado, ajustamos startTimeRef para mantener el tiempo acumulado
    startTimeRef.current = isPaused ? Date.now() - time : Date.now();
    timerRef.current = window.setInterval(() => {
      if (startTimeRef.current) {
        setTime(Date.now() - startTimeRef.current);
      }
    }, 10);
    setIsRunning(true);
    setIsPaused(false);
  };

  const handlePause = async () => {
    await playSoundAndVibrate('stop', 100);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setIsPaused(true);
    setIsRunning(false);
  };

  const handleStop = async () => {
    await playSoundAndVibrate('stop', [100, 50, 100]);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setTime(0);
    startTimeRef.current = undefined;
    setIsRunning(false);
    setIsPaused(false);
  };

  const handleLap = async () => {
    if (!isRunning) return;

    await playSoundAndVibrate('lap', 150);

    const record: CronoSeguidoRecord = {
      id: Math.random().toString(36).substring(2) + Date.now().toString(36),
      time: time / 1000,
      activity,
      description: t('noDescription', { ns: 'cronoSeguido' }),
      timestamp: new Date().toISOString(),
      addedToMethod: false
    };

    const updatedRecords = [record, ...records];
    
    setRecords(updatedRecords);
    setTime(0);
    startTimeRef.current = Date.now();
    setLastAddedRecord(record);

    // Iniciar reconocimiento de voz inmediatamente (aprovecha la acción del usuario)
    startVoiceRecognition(record.id);

    // Guardar en base de datos en paralelo
    if (selectedStudy) {
      updateCronoSeguido(selectedStudy.id, updatedRecords).catch(console.error);
    }
  };

  const handleActivityChange = (increment: boolean) => {
    if (!selectedStudy) return;

    const { normal, optimal } = selectedStudy.required_info.activity_scale;
    const step = 5;
    const min = Math.max(normal * 0.5);
    const max = optimal;

    setActivity((prev: number) => {
      const newActivity = increment ? prev + step : prev - step;
      return Math.min(Math.max(newActivity, min), max);
    });
  };

  const handleUpdateRecord = async (record: CronoSeguidoRecord, data: { time: number; activity: number; description: string; comment?: string }) => {
    const updatedRecords = records.map(r =>
      r.id === record.id
        ? { ...r, ...data }
        : r
    );
    setRecords(updatedRecords);
    setEditingRecord(null);
    setShowEditModal(false);

    if (selectedStudy) {
      await updateCronoSeguido(selectedStudy.id, updatedRecords);
    }
  };

  const handleDeleteRecord = async (recordId: string) => {
    const recordToDelete = records.find(r => r.id === recordId);
    if (!recordToDelete || !selectedStudy) return;

    const updatedRecords = records.filter(r => r.id !== recordId);
    setRecords(updatedRecords);

    await updateCronoSeguido(selectedStudy.id, updatedRecords);
  };

  // Función para manejar eliminación con doble clic
  const handleDeleteClick = (recordId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    const timeouts = deleteClickTimeouts.current;
    
    if (timeouts.has(recordId)) {
      // Es un doble clic - eliminar directamente sin confirmación
      clearTimeout(timeouts.get(recordId));
      timeouts.delete(recordId);
      handleDeleteRecord(recordId);
      console.log('🗑️ Eliminación directa por doble clic');
    } else {
      // Es un primer clic - mostrar confirmación después de 300ms
      const timeout = setTimeout(() => {
        timeouts.delete(recordId);
        setRecordToDelete(recordId);
        setShowDeleteConfirm(true);
        console.log('❓ Mostrando confirmación por clic simple');
      }, 300);
      
      timeouts.set(recordId, timeout);
    }
  };

  // Función para confirmar eliminación
  const confirmDeleteRecord = () => {
    if (recordToDelete) {
      handleDeleteRecord(recordToDelete);
      setRecordToDelete(null);
      setShowDeleteConfirm(false);
    }
  };

  // Función para cancelar eliminación
  const cancelDeleteRecord = () => {
    setRecordToDelete(null);
    setShowDeleteConfirm(false);
  };

  const handleDeleteAllRecords = async () => {
    if (!selectedStudy) return;

    setRecords([]);
    setShowDeleteAllModal(false);

    await updateCronoSeguido(selectedStudy.id, []);
  };

  const handleAddToMethod = async (data: {
    isNewElement: boolean;
    elementId?: string;
    type: string;
    repetitionType: string;
    frequencyRepetitions: number;
    frequencyCycles: number;
  }) => {
    if (!selectedTimeId || !selectedStudy) return;

    // Verificar si el usuario tiene permisos para editar este estudio
    if (isSharedStudy) {
      setError(t('permission_error.message', { ns: 'study' }));
      setShowAddToMethodModal(false);
      setSelectedTimeId(null);
      return;
    }

    const record = records.find(r => r.id === selectedTimeId);
    if (!record) return;

    try {
      if (data.isNewElement) {
        // Create new element
        const newElement = {
          id: Math.random().toString(36).substring(2) + Date.now().toString(36),
          description: record.description,
          type: data.type,
          repetition_type: data.repetitionType,
          frequency_repetitions: data.frequencyRepetitions,
          frequency_cycles: data.frequencyCycles,
          position: selectedStudy.elements.length
        };

        // Add time record
        const newTimeRecord = {
          id: Math.random().toString(36).substring(2) + Date.now().toString(36),
          elementId: newElement.id,
          time: record.time,
          activity: record.activity,
          timestamp: record.timestamp,
          comment: record.comment || record.description
        };

        const updatedStudy = await updateStudy(selectedStudy.id, {
          ...selectedStudy,
          elements: [...selectedStudy.elements, newElement],
          time_records: {
            ...selectedStudy.time_records,
            [newElement.id]: [newTimeRecord]
          }
        });

        if (updatedStudy) {
          // Update crono seguido record
          const updatedRecords = records.map(r =>
            r.id === selectedTimeId
              ? { ...r, addedToMethod: true, elementId: newElement.id }
              : r
          );
          setRecords(updatedRecords);
          await updateCronoSeguido(selectedStudy.id, updatedRecords);
        } else {
          setError(t('addToMethodError', { ns: 'common' }));
        }
      } else if (data.elementId) {
        // Verificar que el elemento exista
        const elementExists = selectedStudy.elements.some((e: any) => e.id === data.elementId);
        if (!elementExists) {
          console.error('El elemento seleccionado no existe en el estudio');
          setError(t('elementNotFound', { ns: 'common' }));
          return;
        }

        // Add time to existing element
        const existingRecords = selectedStudy.time_records[data.elementId] || [];
        const newTimeRecord = {
          id: Math.random().toString(36).substring(2) + Date.now().toString(36),
          elementId: data.elementId,
          time: record.time,
          activity: record.activity,
          timestamp: record.timestamp,
          comment: record.comment || record.description
        };

        // Crear una copia de los time_records para asegurar que se detecta el cambio
        const updatedTimeRecords = {
          ...selectedStudy.time_records,
          [data.elementId]: [...existingRecords, newTimeRecord]
        };

        console.log('Actualizando time_records:', updatedTimeRecords);
        
        const updatedStudy = await updateStudy(selectedStudy.id, {
          ...selectedStudy,
          time_records: updatedTimeRecords
        });

        if (updatedStudy) {
          console.log('Estudio actualizado correctamente:', updatedStudy);
          
          // Update crono seguido record
          const updatedRecords = records.map(r =>
            r.id === selectedTimeId
              ? { ...r, addedToMethod: true, elementId: data.elementId }
              : r
          );
          setRecords(updatedRecords);
          await updateCronoSeguido(selectedStudy.id, updatedRecords);
        } else {
          console.error('No se pudo actualizar el estudio');
          setError(t('addToMethodError', { ns: 'common' }));
        }
      }

      setShowAddToMethodModal(false);
      setSelectedTimeId(null);

    } catch (error) {
      console.error('Error adding to method:', error);
      setError(t('addToMethodError', { ns: 'common' }));
    }
  };

  const handleRemoveFromMethod = async (record: CronoSeguidoRecord) => {
    if (!selectedStudy || !record.elementId) return;

    // Verificar si el usuario tiene permisos para editar este estudio
    if (isSharedStudy) {
      setError(t('permission_error.message', { ns: 'study' }));
      return;
    }

    try {
      // Remove element from method if it was created for this record
      const element = selectedStudy.elements.find((e: any) => e.id === record.elementId);
      if (element) {
        const updatedElements = selectedStudy.elements.filter((e: any) => e.id !== record.elementId);
        const { [record.elementId]: _, ...updatedTimeRecords } = selectedStudy.time_records;

        await updateStudy(selectedStudy.id, {
          ...selectedStudy,
          elements: updatedElements,
          time_records: updatedTimeRecords
        });
      }

      // Update crono seguido record
      const updatedRecords = records.map(r =>
        r.id === record.id
          ? { ...r, addedToMethod: false, elementId: undefined }
          : r
      );
      setRecords(updatedRecords);
      await updateCronoSeguido(selectedStudy.id, updatedRecords);

    } catch (error) {
      console.error('Error removing from method:', error);
      setError(t('removeFromMethodError', { ns: 'common' }));
    }
  };

  const handleBulkTransfer = async () => {
    if (!selectedStudy) return;

    // Verificar si el usuario tiene permisos para editar este estudio
    if (isSharedStudy) {
      setError(t('permission_error.message', { ns: 'study' }));
      setShowBulkMethodModal(false);
      return;
    }

    try {
      // Get records not yet in method and reverse them to maintain chronological order
      const recordsToTransfer = records
        .filter(r => !r.addedToMethod)
        .reverse(); // Reverse to maintain original chronological order

      // Si no hay registros para transferir, mostrar un mensaje y salir
      if (recordsToTransfer.length === 0) {
        setError(t('noRecordsToTransfer', { ns: 'common' }));
        setShowBulkMethodModal(false);
        return;
      }

      // Create new elements for each record
      const newElements = recordsToTransfer.map((record, index) => ({
        id: Math.random().toString(36).substring(2) + Date.now().toString(36),
        description: record.description,
        type: 'machine-stopped',
        repetition_type: 'repetitive',
        frequency_repetitions: 1,
        frequency_cycles: 1,
        position: selectedStudy.elements.length + index
      }));

      // Create time records for each element
      const newTimeRecords = { ...selectedStudy.time_records };
      
      // Asegurarse de que cada elemento tenga un registro de tiempo
      newElements.forEach((element, index) => {
        const record = recordsToTransfer[index];
        newTimeRecords[element.id] = [{
          id: Math.random().toString(36).substring(2) + Date.now().toString(36),
          elementId: element.id,
          time: record.time,
          activity: record.activity,
          timestamp: record.timestamp,
          comment: record.description
        }];
      });

      console.log("Elementos nuevos:", newElements);
      console.log("Time records nuevos:", newTimeRecords);

      // Update study with new elements and time records
      const updatedStudy = await updateStudy(selectedStudy.id, {
        ...selectedStudy,
        elements: [...selectedStudy.elements, ...newElements],
        time_records: newTimeRecords
      });

      if (!updatedStudy) {
        setError(t('bulkTransferError', { ns: 'common' }));
        return;
      }

      // Mark records as added to method
      const updatedRecords = records.map(record => {
        if (!record.addedToMethod) {
          const index = recordsToTransfer.findIndex(r => r.id === record.id);
          if (index !== -1 && index < newElements.length) {
            const newElement = newElements[index];
            return { ...record, addedToMethod: true, elementId: newElement.id };
          }
        }
        return record;
      });

      setRecords(updatedRecords);
      await updateCronoSeguido(selectedStudy.id, updatedRecords);
      setShowBulkMethodModal(false);

    } catch (error) {
      console.error('Error in bulk transfer:', error);
      setError(t('bulkTransferError', { ns: 'common' }));
    }
  };

  const handleConfirmBulkConfig = async (data: { recordsToAdd: any[], recordsToRemove: CronoSeguidoRecord[] }) => {
    if (!selectedStudy) return;

    // Verificar si el usuario tiene permisos para editar este estudio
    if (isSharedStudy) {
      setError(t('permission_error.message', { ns: 'study' }));
      setShowBulkConfigModal(false);
      return;
    }

    try {
      const { recordsToAdd, recordsToRemove } = data;

      // Primero eliminar elementos si es necesario
      if (recordsToRemove.length > 0) {
        for (const record of recordsToRemove) {
          if (record.elementId) {
            // Eliminar elemento del método
            const element = selectedStudy.elements.find((e: any) => e.id === record.elementId);
            if (element) {
              const updatedElements = selectedStudy.elements.filter((e: any) => e.id !== record.elementId);
              const { [record.elementId]: _, ...updatedTimeRecords } = selectedStudy.time_records;

              await updateStudy(selectedStudy.id, {
                ...selectedStudy,
                elements: updatedElements,
                time_records: updatedTimeRecords
              });

              // Actualizar el selectedStudy para las siguientes operaciones
              selectedStudy.elements = updatedElements;
              selectedStudy.time_records = updatedTimeRecords;
            }
          }
        }
      }

      // Filtrar solo los registros que no han sido agregados al método
      const recordsToTransfer = recordsToAdd.filter((r: any) => !r.addedToMethod);

      if (recordsToTransfer.length === 0 && recordsToRemove.length === 0) {
        setError(t('noRecordsToTransfer', { ns: 'common' }));
        setShowBulkConfigModal(false);
        return;
      }

      const newElements: any[] = [];
      const newTimeRecords = { ...selectedStudy.time_records };

      for (let i = 0; i < recordsToTransfer.length; i++) {
        const record = recordsToTransfer[i];
        const config = record.config;

        if (config.isNewElement) {
          // Crear nuevo elemento con la configuración especificada
          const newElement: any = {
            id: Math.random().toString(36).substring(2) + Date.now().toString(36),
            description: record.description || `Elemento ${i + 1}`,
            type: config.type,
            repetition_type: config.repetitionType,
            frequency_repetitions: config.frequencyRepetitions,
            frequency_cycles: config.frequencyCycles,
            flowchartSymbol: config.flowchartSymbol,
            position: selectedStudy.elements.length + newElements.length
          };

          newElements.push(newElement);

          // Crear registro de tiempo para el nuevo elemento
          newTimeRecords[newElement.id] = [{
            id: Math.random().toString(36).substring(2) + Date.now().toString(36),
            elementId: newElement.id,
            time: record.time,
            activity: record.activity,
            timestamp: record.timestamp,
            comment: record.description || record.comment
          }];
        } else {
          // Agregar tiempo a elemento existente
          if (config.elementId && selectedStudy.elements.find((e: any) => e.id === config.elementId)) {
            if (!newTimeRecords[config.elementId]) {
              newTimeRecords[config.elementId] = [];
            }

            newTimeRecords[config.elementId].push({
              id: Math.random().toString(36).substring(2) + Date.now().toString(36),
              elementId: config.elementId,
              time: record.time,
              activity: record.activity,
              timestamp: record.timestamp,
              comment: record.description || record.comment
            });
          }
        }
      }

      console.log("Elementos nuevos configurados:", newElements);
      console.log("Time records configurados:", newTimeRecords);

      // Actualizar estudio con nuevos elementos y registros de tiempo
      const updatedStudy = await updateStudy(selectedStudy.id, {
        ...selectedStudy,
        elements: [...selectedStudy.elements, ...newElements],
        time_records: newTimeRecords
      });

      if (!updatedStudy) {
        setError(t('bulkTransferError', { ns: 'common' }));
        return;
      }

      // Marcar registros como agregados al método o eliminados
      const updatedRecords = records.map((record: any) => {
        // Verificar si el registro fue eliminado
        const removedRecord = recordsToRemove.find((r: any) => r.id === record.id);
        if (removedRecord) {
          return { 
            ...record, 
            addedToMethod: false, 
            elementId: undefined 
          };
        }

        // Verificar si el registro fue agregado
        const configuredRecord = recordsToTransfer.find((r: any) => r.id === record.id);
        if (configuredRecord && !record.addedToMethod) {
          const newElement = newElements.find(e => e.description === record.description);
          return { 
            ...record, 
            addedToMethod: true, 
            elementId: newElement?.id || configuredRecord.config.elementId 
          };
        }
        return record;
      });

      setRecords(updatedRecords);
      await updateCronoSeguido(selectedStudy.id, updatedRecords);
      setShowBulkConfigModal(false);

    } catch (error) {
      console.error('Error in bulk config transfer:', error);
      setError(t('bulkTransferError', { ns: 'common' }));
    }
  };

  const handleVoiceTranscript = async (text: string) => {
    if (!lastAddedRecord || !selectedStudy) return;

    const updatedRecords = records.map(record =>
      record.id === lastAddedRecord.id
        ? { ...record, description: text }
        : record
    );

    setRecords(updatedRecords);
    await updateCronoSeguido(selectedStudy.id, updatedRecords);
    setLastAddedRecord(null);
  };

  const handleVoiceInputClose = () => {
    setIsListeningForDescription(false);
    setListeningRecordId(null);
  };

  const handleVoiceDescription = async (description: string) => {
    // Actualizar la descripción del último registro
    if (!lastAddedRecord || !selectedStudy) return;

    const updatedRecords = records.map(record =>
      record.id === lastAddedRecord.id
        ? { ...record, description }
        : record
    );

    setRecords(updatedRecords);
    await updateCronoSeguido(selectedStudy.id, updatedRecords);
    setLastAddedRecord(null);
    setIsListeningForDescription(false);
    setListeningRecordId(null);
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toFixed(2).padStart(5, '0')}`;
  };

  const handleMoveRecord = async (index: number, direction: 'up' | 'down') => {
    if (!records || !selectedStudy || 
        (direction === 'up' && index === 0) || 
        (direction === 'down' && index === records.length - 1)) {
      return;
    }

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    const newRecords = [...records];
    const [movedItem] = newRecords.splice(index, 1);
    newRecords.splice(newIndex, 0, movedItem);

    // Update local state immediately
    setRecords(newRecords);

    try {
      // Update in the background
      await updateCronoSeguido(selectedStudy.id, newRecords);
    } catch (error) {
      console.error('Error updating records order:', error);
      // Revert to original order on error
      setRecords(records);
    }
  };

  const handleAddRecord = async () => {
    if (!selectedStudy) return;

    const newRecord: CronoSeguidoRecord = {
      id: crypto.randomUUID(),
      time: time / 1000,
      activity,
      timestamp: new Date().toISOString(),
      description: '',
      addedToMethod: false
    };

    const updatedRecords = [...records, newRecord];
    setRecords(updatedRecords);
    setLastAddedRecord(newRecord);

    await updateCronoSeguido(selectedStudy.id, updatedRecords);
    handleStop();
  };

  if (!selectedStudy) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="mt-4">{t('noStudySelected', { ns: 'common' })}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Header
        title={t('title', { ns: 'cronoSeguido' })}
        subtitle={selectedStudy?.required_info?.name || t('noStudySelected', { ns: 'common' })}
        showSearch={false}
        showActions={true}
        onBack={onBack}
      />

      <main className="container mx-auto p-4 space-y-4">
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
              aria-label={t('close', { ns: 'common' })}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {isSharedStudy && (
          <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg flex items-center text-amber-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{t('permission_error.message', { ns: 'study' })}</span>
            <button
              onClick={() => setIsSharedStudy(false)}
              className="ml-auto text-amber-500 hover:text-amber-700"
              aria-label={t('close', { ns: 'common' })}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Indicador de reconocimiento de voz no bloqueante */}
        {isListeningForDescription && (
          <div className="fixed top-16 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-red-500 to-red-600 text-white px-8 py-4 rounded-2xl shadow-2xl flex items-center space-x-4 z-50 animate-pulse border-2 border-red-300">
            <div className="relative">
              <Mic className="w-6 h-6 animate-bounce" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full animate-ping"></div>
            </div>
            <div className="flex flex-col">
              <span className="font-bold text-lg">{t('listening', { ns: 'cronoSeguido' })}</span>
              <span className="text-red-100 text-sm">Dicta la descripción del tiempo</span>
            </div>
            <button
              onClick={() => {
                if (recognitionRef.current) {
                  recognitionRef.current.stop();
                }
                setIsListeningForDescription(false);
                setListeningRecordId(null);
              }}
              className="ml-2 text-white hover:text-red-200 bg-red-600 hover:bg-red-700 rounded-full p-2 transition-colors"
              title={t('close', { ns: 'common' })}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => handleActivityChange(false)}
              className="p-2 rounded-full hover:bg-gray-100"
              title={t('decreaseActivity', { ns: 'common' })}
            >
              <ArrowDown className="w-6 h-6" />
            </button>

            <div className="text-4xl font-mono font-bold">
              {(time / 1000).toFixed(2)}
            </div>

            <button
              onClick={() => handleActivityChange(true)}
              className="p-2 rounded-full hover:bg-gray-100"
              title={t('increaseActivity', { ns: 'common' })}
            >
              <ArrowUp className="w-6 h-6" />
            </button>
          </div>

          <div className="text-center mb-6">
            <span className="text-2xl font-bold">{activity}</span>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={handleStop}
              disabled={!isRunning && !isPaused}
              className="p-4 rounded-lg bg-red-500 text-white font-bold disabled:opacity-50"
              aria-label={t('stop', { ns: 'common' })}
            >
              <Square className="mx-auto" />
            </button>
            <button
              onClick={isRunning ? handlePause : handleStart}
              className={`p-4 rounded-lg text-white font-bold
                ${isRunning ? 'bg-yellow-500' : 'bg-green-500'}`}
              aria-label={isRunning ? t('pause', { ns: 'common' }) : t('start', { ns: 'common' })}
            >
              {isRunning ? <Pause className="mx-auto" /> : <Play className="mx-auto" />}
            </button>
          </div>

          <button
            onClick={handleLap}
            disabled={!isRunning}
            className="w-full mt-4 p-4 rounded-lg bg-blue-500 text-white font-bold disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            <Save className="w-5 h-5" />
            <span>{t('nextElement', { ns: 'cronoSeguido' })}</span>
          </button>
        </div>

        <div className="mt-4 p-4 bg-white rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2">{t('stats', { ns: 'cronoSeguido' })}</h3>
          <div className="grid grid-cols-2 gap-4">
            <div key="total-time">
              <p className="text-gray-600">{t('totalTime', { ns: 'cronoSeguido' })}</p>
              <p className="text-xl font-semibold">{formatTime(records.reduce((sum, record) => sum + record.time, 0))}</p>
            </div>
            <div key="total-takes">
              <p className="text-gray-600">{t('totalTakes', { ns: 'cronoSeguido' })}</p>
              <p className="text-xl font-semibold">{records.length}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-between my-4">
          <button
            onClick={() => setShowDeleteAllModal(true)}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 flex items-center space-x-2"
          >
            <Trash2 className="w-5 h-5" />
            <span>{t('deleteAll', { ns: 'cronoSeguido' })}</span>
          </button>

          <button
            onClick={() => setShowBulkConfigModal(true)}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center space-x-2"
            disabled={records.length === 0 || isSharedStudy}
            title={isSharedStudy ? t('common:errors.sharedStudyAction') : ''}
          >
            <Save className="w-5 h-5" />
            <span>{t('addToMethod', { ns: 'cronoSeguido' })}</span>
          </button>
        </div>

        {records && records.length > 0 ? (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={record.id} className="flex items-center gap-2">
                <div className="flex flex-col text-gray-400 p-2">
                  <button 
                    onClick={() => handleMoveRecord(index, 'up')}
                    disabled={index === 0}
                    className="p-1 hover:text-gray-600 disabled:opacity-30"
                    aria-label={t('moveUp', { ns: 'common' })}
                  >
                    <ChevronUp className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => handleMoveRecord(index, 'down')}
                    disabled={index === records.length - 1}
                    className="p-1 hover:text-gray-600 disabled:opacity-30"
                    aria-label={t('moveDown', { ns: 'common' })}
                  >
                    <ChevronDown className="w-5 h-5" />
                  </button>
                </div>
                <div
                  onClick={() => {
                    setEditingRecord(record);
                    setShowEditModal(true);
                  }}
                  className={`flex-1 bg-white rounded-lg shadow p-4 cursor-pointer hover:bg-gray-50 animate-slide-down ${
                    record.addedToMethod ? 'bg-green-50' : ''
                  }`}
                  style={{
                    animationDelay: `${index * 50}ms`,
                    animationFillMode: 'backwards'
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!record.addedToMethod) {
                            setSelectedTimeId(record.id);
                            setShowAddToMethodModal(true);
                          } else {
                            handleRemoveFromMethod(record);
                          }
                        }}
                        className={`p-2 rounded-full ${
                          record.addedToMethod
                            ? 'bg-red-100 text-red-600 hover:bg-red-200'
                            : 'bg-green-100 text-green-600 hover:bg-green-200'
                        }`}
                      >
                        {record.addedToMethod ? <Trash2 className="w-4 h-4" /> : <Plus className="w-4 h-4" />}
                      </button>
                      <div className="flex-1">
                        <div className="text-gray-600">{record.description}</div>
                        {record.comment && (
                          <div className="flex items-start gap-1 mt-1">
                            <span className="text-blue-500 text-xs font-medium">💬</span>
                            <div className="text-sm text-blue-700 bg-blue-50 px-2 py-1 rounded-md border-l-2 border-blue-300">
                              {record.comment}
                            </div>
                          </div>
                        )}
                        <div className="text-sm text-gray-500">
                          {new Date(record.timestamp).toLocaleTimeString()}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {record.time.toFixed(2)}s
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="font-bold mr-4">{record.activity}</span>
                      
                      {/* Botón para dictar descripción */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (isListeningForDescription && listeningRecordId === record.id) {
                            // Si ya está escuchando para este registro, detener
                            if (recognitionRef.current) {
                              recognitionRef.current.stop();
                            }
                          } else {
                            // Iniciar reconocimiento para este registro
                            startVoiceRecognition(record.id);
                          }
                        }}
                        className={`p-2 rounded-full mr-2 ${
                          isListeningForDescription && listeningRecordId === record.id
                            ? 'bg-red-100 text-red-600 animate-pulse'
                            : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                        }`}
                        title={
                          isListeningForDescription && listeningRecordId === record.id
                            ? t('listening', { ns: 'cronoSeguido' })
                            : t('addVoiceDescription', { ns: 'cronoSeguido' })
                        }
                      >
                        <Mic className="w-4 h-4" />
                      </button>

                      {/* Botón de eliminación con doble clic */}
                      <button
                        onClick={(e) => handleDeleteClick(record.id, e)}
                        className="p-2 text-red-500 hover:text-red-700"
                        aria-label={t('delete', { ns: 'common' })}
                        title={t('deleteRecordHint', { ns: 'cronoSeguido' })}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : null}

        {showEditModal && editingRecord && (
          <EditTimeModal
            onClose={() => {
              setShowEditModal(false);
              setEditingRecord(null);
            }}
            onSave={(data) => handleUpdateRecord(editingRecord, data)}
            initialData={{
              time: editingRecord.time,
              activity: editingRecord.activity,
              description: editingRecord.description,
              comment: editingRecord.comment
            }}
          />
        )}

        {showAddToMethodModal && selectedTimeId && (
          <AddToMethodModal
            onClose={() => {
              setShowAddToMethodModal(false);
              setSelectedTimeId(null);
            }}
            onSave={handleAddToMethod}
            timeId={selectedTimeId}
          />
        )}

        <DeleteConfirmModal
          isOpen={showDeleteAllModal}
          onClose={() => setShowDeleteAllModal(false)}
          onConfirm={handleDeleteAllRecords}
          title={t('deleteAllTitle', { ns: 'cronoSeguido' })}
          message={t('deleteAllConfirmation', { ns: 'cronoSeguido' })}
        />

        <BulkMethodModal
          isOpen={showBulkMethodModal}
          onClose={() => setShowBulkMethodModal(false)}
          onConfirm={handleBulkTransfer}
        />

        <BulkAddToMethodConfigModal
          isOpen={showBulkConfigModal}
          onClose={() => setShowBulkConfigModal(false)}
          onConfirm={handleConfirmBulkConfig}
          records={records}
          loading={false}
        />

        {/* Modal de confirmación de eliminación individual */}
        <DeleteConfirmModal
          isOpen={showDeleteConfirm}
          onClose={cancelDeleteRecord}
          onConfirm={confirmDeleteRecord}
          title={t('deleteRecord', { ns: 'cronoSeguido' })}
          message={t('confirmDeleteRecord', { ns: 'cronoSeguido', defaultValue: '¿Estás seguro de que deseas eliminar este registro? Esta acción no se puede deshacer.' })}
        />

      </main>
    </div>
  );
};