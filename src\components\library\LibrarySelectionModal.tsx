import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Calculator, Search, ArrowLeft } from 'lucide-react';
import { ElementInstance } from '../../types';
import { useElementSearchStore } from '../../store/elementSearchStore';

interface LibrarySelectionModalProps {
  onClose: () => void;
  onSelectElement: (element: ElementInstance) => void;
}

export const LibrarySelectionModal: React.FC<LibrarySelectionModalProps> = ({
  onClose,
  onSelectElement
}) => {
  const { t } = useTranslation(['library', 'common']);
  const {
    searchResults,
    selectedElements,
    isLoading,
    searchElements,
    toggleElementSelection,
    clearSelection
  } = useElementSearchStore();

  const [activeTab, setActiveTab] = useState<'library' | 'average'>('library');
  const [searchQuery, setSearchQuery] = useState('');
  const [averagedElement, setAveragedElement] = useState<ElementInstance | null>(null);
  const [selectedStudyId, setSelectedStudyId] = useState<string | null>(null);
  const [visibleStudies, setVisibleStudies] = useState<{id: string, name: string}[]>([]);
  const [initialLoadDone, setInitialLoadDone] = useState(false);

  useEffect(() => {
    setSelectedStudyId(null);
    searchElements('');
    setInitialLoadDone(true);
    return () => {
      clearSelection();
      setInitialLoadDone(false);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps 
  }, []);

  useEffect(() => {
    if (searchResults.length > 0 || initialLoadDone) {
      const uniqueStudies = new Map<string, {id: string, name: string}>();
      searchResults.forEach(({ studyId, studyName, element }) => {
        if (element && !(element as any).isPlaceholder && !uniqueStudies.has(studyId)) {
          uniqueStudies.set(studyId, { id: studyId, name: studyName });
        } else if (!uniqueStudies.has(studyId) && searchResults.some(sr => sr.studyId === studyId)) {
          uniqueStudies.set(studyId, { id: studyId, name: studyName });
        }
      });
      setVisibleStudies(Array.from(uniqueStudies.values()));
    }
  }, [searchResults, initialLoadDone]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchQuery.trim() !== '' || initialLoadDone) {
      searchElements(searchQuery);
      }
    }, 300);
    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery, searchElements, initialLoadDone]);

  const handleCreateAverage = () => {
    if (selectedElements.length === 0) return;
    const totalTime = selectedElements.reduce((sum, sel) => sum + (sel.element.time || 0), 0);
    const averageTime = totalTime / selectedElements.length;
    const totalSupplements = selectedElements.reduce((sum, sel) => sum + (sel.element.supplements?.[0]?.percentage || 0), 0);
    const averageSupplements = totalSupplements / selectedElements.length;
    const descriptions = selectedElements.map(sel => `${sel.element.name}: ${sel.element.description}`);
    const combinedDescription = `Elemento promediado de:\n${descriptions.join('\n')}`;
    const newAveragedElement: ElementInstance = {
      id: crypto.randomUUID(),
      name: 'Promedio de elementos',
      description: combinedDescription,
      type: 'machine-stopped',
      time: averageTime,
      position: 0,
      repetition_type: 'repetitive',
      frequency_cycles: 1,
      frequency_repetitions: 1,
      supplements: [{
        percentage: averageSupplements,
        is_forced: true,
        points: {},
        factor_selections: {}
      }],
      sourceElements: selectedElements.map(sel => ({ id: sel.element.id, name: sel.element.name, description: sel.element.description, time: sel.element.time }))
    };
    setAveragedElement(newAveragedElement);
    clearSelection();
  };

  const handleSelectElement = (element: ElementInstance) => {
    onSelectElement(element);
    onClose();
  };

  const handleSelectAveragedElement = () => {
    if (averagedElement) {
      onSelectElement(averagedElement);
      onClose();
    }
  };

  const getStudyElements = (studyIdToFilter: string) => {
    return searchResults.filter(result => result.studyId === studyIdToFilter && !(result.element as any).isPlaceholder);
  };
  
  const handleBackToStudies = () => setSelectedStudyId(null);

  const renderStudiesView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {visibleStudies.map((study) => {
        const elementsInStudy = getStudyElements(study.id);
        if (elementsInStudy.length > 0 || searchResults.some(sr => sr.studyId === study.id && (sr.element as any).isPlaceholder)) {
            return (
              <div
                key={study.id}
                onClick={() => setSelectedStudyId(study.id)}
                className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 cursor-pointer"
              >
                <h3 className="font-semibold">{study.name}</h3>
                <p className="text-sm text-gray-500 mt-2">
                  {elementsInStudy.length} {t('elements', { ns: 'library' })}
                </p>
              </div>
            );
        }
        return null;
       })}
    </div>
  );

  const renderStudyElementsView = () => {
    if (!selectedStudyId) return null;
    const elementsToRender = getStudyElements(selectedStudyId);

    if (elementsToRender.length === 0) {
        return <div className="text-center py-8 text-gray-500">{t('noElementsInStudy', { ns: 'library' })}</div>;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {elementsToRender.map(({ element, studyName, studyId }) => {
          const isSelected = selectedElements.some(sel => sel.element.id === element.id && sel.studyId === studyId);
          return (
            <div
              key={`${element.id}-${studyId}`}
              className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200"
            >
              <h3 className="font-semibold mb-1">{element.name || element.description.substring(0, 50) + '...'}</h3>
              <p className="text-sm text-gray-600 mb-2 truncate" style={{ maxHeight: '3em', overflow: 'hidden' }}>{element.description}</p>
              <div className="text-xs text-gray-500 mb-3">
                {element.time !== undefined && (
                  <p>{t('baseTime', { ns: 'library' })}: {element.time.toFixed(2)}</p>
                )}
              </div>
              <div className="flex space-x-2 justify-end">
                <button
                  onClick={() => handleSelectElement(element)}
                  className="px-3 py-1 rounded bg-green-500 text-white hover:bg-green-600 text-sm"
                >
                  {t('common:select', { ns: 'common' })}
                </button>
                <button
                  onClick={() => toggleElementSelection(element, studyName, studyId)}
                  className={`px-3 py-1 rounded text-sm ${isSelected 
                    ? 'bg-amber-100 text-amber-600 hover:bg-amber-200'
                    : 'bg-amber-500 text-white hover:bg-amber-600'
                  }`}
                >
                  {isSelected ? t('common:selected') : t('sendToAverage', { ns: 'library' })}
                </button>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              {selectedStudyId && (
                <button 
                  onClick={handleBackToStudies}
                  className="mr-3 text-gray-500 hover:text-gray-700"
                  title={t('common:back')}
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
              )}
            <h2 className="text-2xl font-bold">
                {selectedStudyId 
                  ? visibleStudies.find(study => study.id === selectedStudyId)?.name || t('title', { ns: 'library' })
                  : t('title', { ns: 'library' })
                }
            </h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              title={t('common:close')}
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6 flex-grow overflow-auto">
          <div className="flex space-x-4 mb-6">
            <button
              onClick={() => {
                setActiveTab('library');
                setSelectedStudyId(null);
              }}
              className={`px-4 py-2 rounded-lg font-medium ${activeTab === 'library' ? 'bg-amber-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              {t('libraryTab', { ns: 'library' })}
            </button>
            <button
              onClick={() => setActiveTab('average')}
              className={`px-4 py-2 rounded-lg font-medium ${activeTab === 'average' ? 'bg-amber-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              {t('averageTab', { ns: 'library' })}
            </button>
          </div>

          {activeTab === 'library' && (
            <div className="overflow-y-auto max-h-[50vh]">
              {isLoading ? (
                <div className="text-center py-8 text-gray-500">{t('loading', { ns: 'library' })}</div>
              ) : !selectedStudyId ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {visibleStudies.map((study) => (
                    <div
                      key={study.id}
                      onClick={() => setSelectedStudyId(study.id)}
                      className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 cursor-pointer"
                    >
                      <h3 className="font-semibold">{study.name}</h3>
                      <p className="text-sm text-gray-500 mt-2">
                        {getStudyElements(study.id).length} {t('elements', { ns: 'library' })}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getStudyElements(selectedStudyId).map(({ element, studyName, studyId }) => (
                    <div
                      key={`${element.id}-${studyId}`}
                      className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200"
                    >
                      <h3 className="font-semibold mb-1">{element.name || element.description.substring(0, 50) + '...'}</h3>
                      <p className="text-sm text-gray-600 mb-2 truncate" style={{ maxHeight: '3em', overflow: 'hidden' }}>{element.description}</p>
                      <div className="text-xs text-gray-500 mb-3">
                        {element.time !== undefined && (
                          <p>{t('baseTime', { ns: 'library' })}: {element.time.toFixed(2)}</p>
                        )}
                      </div>
                      <div className="flex space-x-2 justify-end">
                        <button
                          onClick={() => handleSelectElement(element)}
                          className="px-3 py-1 rounded bg-green-500 text-white hover:bg-green-600 text-sm"
                        >
                          {t('common:select', { ns: 'common' })}
                        </button>
                <button
                          onClick={() => toggleElementSelection(element, studyName, studyId)}
                          className={`px-3 py-1 rounded text-sm ${selectedElements.some(sel => sel.element.id === element.id && sel.studyId === studyId) 
                            ? 'bg-amber-100 text-amber-600 hover:bg-amber-200'
                            : 'bg-amber-500 text-white hover:bg-amber-600'
                          }`}
                        >
                          {selectedElements.some(sel => sel.element.id === element.id && sel.studyId === studyId) ? t('common:selected') : t('sendToAverage', { ns: 'library' })}
                </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'average' && (
            <div className="overflow-y-auto max-h-[calc(90vh - 150px)]">
              {selectedElements.length > 0 && (
            <div className="mb-6 p-4 bg-white rounded-lg shadow border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">{t('selectedElements', { count: selectedElements.length, ns: 'library' })}</h3>
                    <button onClick={handleCreateAverage} className="bg-amber-500 text-white px-4 py-2 rounded hover:bg-amber-600 flex items-center text-sm">
                      <Calculator className="w-4 h-4 mr-2" />{t('calculateAverage', { ns: 'library' })}
                </button>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {selectedElements.map(({ element, studyName, studyId }) => (
                      <div key={`${element.id}-${studyId}`} className="flex items-center justify-between bg-white p-3 rounded-lg shadow border border-gray-100">
                    <div>
                          <p className="font-medium text-sm">{element.name || element.description}</p>
                          <p className="text-xs text-gray-600">{t('fromStudy', { name: studyName, ns: 'library' })}</p>
                    </div>
                        <button onClick={() => toggleElementSelection(element, studyName, studyId)} className="text-gray-500 hover:text-red-500" title={t('common:remove')}>
                          <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
              {averagedElement && (
            <div className="mb-6 p-4 bg-white rounded-lg shadow border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">{t('averagedElement', { ns: 'library' })}</h3>
                    <button onClick={handleSelectAveragedElement} className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 text-sm">
                  {t('common:select', { ns: 'common' })}
                </button>
              </div>
                  <div className="space-y-1 text-sm">
                <p><strong>{t('name', { ns: 'common' })}:</strong> {averagedElement.name}</p>
                <p><strong>{t('description', { ns: 'common' })}:</strong> {averagedElement.description}</p>
                <p><strong>{t('baseTime', { ns: 'library' })}:</strong> {averagedElement.time.toFixed(2)}</p>
                    <p><strong>{t('totalTakes', { ns: 'library' })}:</strong> {averagedElement.sourceElements?.length || 0}</p>
                {averagedElement.supplements && averagedElement.supplements.length > 0 && (
                      <div><strong>{t('supplements', { ns: 'common' })}:</strong>
                        <ul className="ml-4 list-disc">{averagedElement.supplements.map((sup: any, idx: number) => <li key={idx}>{sup.percentage.toFixed(2)}%</li>)}</ul>
                  </div>
                )}
              </div>
            </div>
          )}
              {selectedElements.length === 0 && !averagedElement && (
                  <div className="text-center py-8 text-gray-500">{t('noElementsToAverage', {ns: 'library'})}</div>
              )}
            </div>
          )}
        </div>

        <div className="p-4 border-t flex justify-end">
          <button onClick={onClose} className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm">
            {t('common:cancel')}
          </button>
        </div>
      </div>
    </div>
  );
};