import React from 'react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useOrganizationStore } from '@/store/organizationStore';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface CleanOrphanedStudiesButtonProps {
  organizationId: string;
}

export function CleanOrphanedStudiesButton({ organizationId }: CleanOrphanedStudiesButtonProps) {
  const { t } = useTranslation();
  const { cleanOrphanedStudies, isLoading } = useOrganizationStore();

  const handleCleanStudies = async () => {
    if (!organizationId) return;
    await cleanOrphanedStudies(organizationId);
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCleanStudies}
            disabled={isLoading}
            className="text-xs"
          >
            {isLoading 
              ? t('organization.cleaningOrphanedStudies')
              : t('organization.cleanOrphanedStudies')
            }
          </Button>
        </TooltipTrigger>
        <TooltipContent className="max-w-xs">
          <p>{t('organization.cleanOrphanedStudiesDescription')}</p>
          <p className="mt-1 text-xs text-muted-foreground">{t('organization.cleanOrphanedStudiesNote')}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
